<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExpenseType>
 */
class ExpenseTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Maintenance',
                'Cleaning',
                'Security',
                'Utilities',
                'Insurance',
                'Repairs',
                'Landscaping',
                'Administration'
            ]),
            'description' => fake()->optional()->sentence(),
        ];
    }
}
