<?php

namespace Database\Factories;

use App\Models\Building;
use App\Models\User;
use App\Models\ExpenseType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Expense>
 */
class ExpenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $dueDate = fake()->dateTimeBetween('-1 month', '+2 months');
        
        return [
            'expense_type_id' => ExpenseType::factory(),
            'user_id' => User::factory(),
            'building_id' => Building::factory(),
            'amount' => fake()->randomFloat(2, 10, 1000),

            'due_date' => $dueDate,
            'month' => $dueDate->format('m'),
            'year' => $dueDate->format('Y'),
            'notes' => fake()->optional()->sentence(),
            'is_automatic' => fake()->boolean(30), // 30% chance of being automatic
            'is_archived' => false,
        ];
    }

    /**
     * Indicate that the expense is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_archived' => true,
            'archived_at' => now(),
            'archived_by' => User::factory(),
            'archive_reason' => fake()->sentence(),
        ]);
    }

    /**
     * Indicate that the expense is automatic.
     */
    public function automatic(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_automatic' => true,
        ]);
    }
}
