<?php

namespace Database\Factories;

use App\Models\ReportTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReportTemplate>
 */
class ReportTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['financial', 'operational', 'analytics', 'custom'];
        $category = fake()->randomElement($categories);
        
        return [
            'name' => fake()->words(3, true) . ' Report',
            'slug' => fake()->unique()->slug(),
            'description' => fake()->sentence(),
            'category' => $category,
            'data_sources' => $this->getDataSourcesForCategory($category),
            'fields' => $this->getFieldsForCategory($category),
            'filters' => $this->getFiltersForCategory($category),
            'grouping_options' => $this->getGroupingOptionsForCategory($category),
            'chart_options' => ['bar', 'line', 'pie', 'table'],
            'default_config' => [
                'fields' => ['amount', 'created_at'],
                'filters' => [],
                'grouping' => [],
                'chart_type' => 'bar',
            ],
            'is_system_template' => true,
            'is_active' => true,
        ];
    }

    /**
     * Get data sources for a specific category.
     */
    private function getDataSourcesForCategory(string $category): array
    {
        switch ($category) {
            case 'financial':
                return ['expenses', 'incomes'];
            case 'operational':
                return ['users', 'notifications'];
            case 'analytics':
                return ['expenses', 'users', 'report_analytics'];
            default:
                return ['expenses'];
        }
    }

    /**
     * Get fields for a specific category.
     */
    private function getFieldsForCategory(string $category): array
    {
        $baseFields = [
            ['key' => 'id', 'type' => 'number', 'label' => 'ID'],
            ['key' => 'created_at', 'type' => 'date', 'label' => 'Created Date'],
        ];

        switch ($category) {
            case 'financial':
                return array_merge($baseFields, [
                    ['key' => 'amount', 'type' => 'currency', 'label' => 'Amount'],
                    ['key' => 'expense_type_name', 'type' => 'string', 'label' => 'Expense Type'],
                    ['key' => 'user_name', 'type' => 'string', 'label' => 'User Name'],
                    ['key' => 'apartment_number', 'type' => 'string', 'label' => 'Apartment'],
                    ['key' => 'notes', 'type' => 'text', 'label' => 'Notes'],
                ]);
            case 'operational':
                return array_merge($baseFields, [
                    ['key' => 'user_name', 'type' => 'string', 'label' => 'User Name'],
                    ['key' => 'user_email', 'type' => 'string', 'label' => 'Email'],
                    ['key' => 'apartment_number', 'type' => 'string', 'label' => 'Apartment'],
                    ['key' => 'role', 'type' => 'string', 'label' => 'Role'],
                ]);
            default:
                return $baseFields;
        }
    }

    /**
     * Get filters for a specific category.
     */
    private function getFiltersForCategory(string $category): array
    {
        $baseFilters = [
            ['key' => 'date_from', 'type' => 'date', 'label' => 'From Date'],
            ['key' => 'date_to', 'type' => 'date', 'label' => 'To Date'],
        ];

        switch ($category) {
            case 'financial':
                return array_merge($baseFilters, [
                    ['key' => 'expense_type_id', 'type' => 'select', 'label' => 'Expense Type'],
                    ['key' => 'user_id', 'type' => 'select', 'label' => 'User'],
                    ['key' => 'amount_min', 'type' => 'number', 'label' => 'Minimum Amount'],
                    ['key' => 'amount_max', 'type' => 'number', 'label' => 'Maximum Amount'],
                ]);
            case 'operational':
                return array_merge($baseFilters, [
                    ['key' => 'user_id', 'type' => 'select', 'label' => 'User'],
                    ['key' => 'role', 'type' => 'select', 'label' => 'Role'],
                ]);
            default:
                return $baseFilters;
        }
    }

    /**
     * Get grouping options for a specific category.
     */
    private function getGroupingOptionsForCategory(string $category): array
    {
        switch ($category) {
            case 'financial':
                return [
                    ['key' => 'expense_type_id', 'label' => 'By Expense Type'],
                    ['key' => 'user_id', 'label' => 'By User'],
                    ['key' => 'month', 'label' => 'By Month'],
                ];
            case 'operational':
                return [
                    ['key' => 'role', 'label' => 'By Role'],
                    ['key' => 'apartment_number', 'label' => 'By Apartment'],
                ];
            default:
                return [];
        }
    }

    /**
     * Create a financial template.
     */
    public function financial(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'financial',
            'data_sources' => ['expenses', 'incomes'],
        ]);
    }

    /**
     * Create an operational template.
     */
    public function operational(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'operational',
            'data_sources' => ['users', 'notifications'],
        ]);
    }

    /**
     * Create an analytics template.
     */
    public function analytics(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'analytics',
            'data_sources' => ['expenses', 'users', 'report_analytics'],
        ]);
    }

    /**
     * Create an inactive template.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a custom (non-system) template.
     */
    public function custom(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_system_template' => false,
        ]);
    }
}
