<?php

namespace Database\Factories;

use App\Models\Package;
use Illuminate\Database\Eloquent\Factories\Factory;

class PackageFactory extends Factory
{
    protected $model = Package::class;

    public function definition()
    {
        return [
            'name' => $this->faker->words(2, true),
            'name_en' => $this->faker->words(2, true),
            'slug' => $this->faker->slug(),
            'description' => $this->faker->paragraph(),
            'description_en' => $this->faker->paragraph(),
            'price' => $this->faker->randomFloat(2, 0, 999.99),
            'annual_price' => $this->faker->randomFloat(2, 0, 999.99),
            'is_free' => false,
            'is_active' => true,
            'is_popular' => false,
            'max_neighbors' => $this->faker->numberBetween(10, 100),
            'max_admins' => $this->faker->numberBetween(1, 5),
            'storage_limit_gb' => $this->faker->numberBetween(1, 100),
            'email_notifications' => $this->faker->boolean(),
            'sms_notifications' => $this->faker->boolean(),
            'priority_support' => $this->faker->boolean(),
            'advanced_reporting' => $this->faker->boolean(),
            'multi_admin_enabled' => $this->faker->boolean(),
            'email_limit_per_month' => $this->faker->numberBetween(100, 1000),
            'email_limit_per_day' => $this->faker->numberBetween(10, 100),
            'export_limit_per_month' => $this->faker->numberBetween(10, 100),
            'max_custom_reports' => $this->faker->numberBetween(1, 10),
            'max_scheduled_reports' => $this->faker->numberBetween(1, 5),
            'custom_reports_enabled' => $this->faker->boolean(),
            'report_scheduling_enabled' => $this->faker->boolean(),
            'advanced_charts_enabled' => $this->faker->boolean(),
            'available_chart_types' => json_encode(['bar', 'line', 'pie']),
        ];
    }

    public function free()
    {
        return $this->state(function (array $attributes) {
            return [
                'price' => 0,
                'annual_price' => 0,
                'is_free' => true,
                'max_neighbors' => 10,
                'max_admins' => 1,
                'storage_limit_gb' => 1,
                'email_notifications' => false,
                'sms_notifications' => false,
                'priority_support' => false,
                'advanced_reporting' => false,
                'multi_admin_enabled' => false,
                'email_limit_per_month' => 50,
                'email_limit_per_day' => 5,
                'export_limit_per_month' => 5,
                'max_custom_reports' => 0,
                'max_scheduled_reports' => 0,
                'custom_reports_enabled' => false,
                'report_scheduling_enabled' => false,
                'advanced_charts_enabled' => false,
                'available_chart_types' => json_encode([]),
            ];
        });
    }

    public function premium()
    {
        return $this->state(function (array $attributes) {
            return [
                'price' => 99.99,
                'annual_price' => 999.99,
                'is_free' => false,
                'is_popular' => true,
                'max_neighbors' => 100,
                'max_admins' => 5,
                'storage_limit_gb' => 100,
                'email_notifications' => true,
                'sms_notifications' => true,
                'priority_support' => true,
                'advanced_reporting' => true,
                'multi_admin_enabled' => true,
                'email_limit_per_month' => 1000,
                'email_limit_per_day' => 100,
                'export_limit_per_month' => 100,
                'max_custom_reports' => 10,
                'max_scheduled_reports' => 5,
                'custom_reports_enabled' => true,
                'report_scheduling_enabled' => true,
                'advanced_charts_enabled' => true,
                'available_chart_types' => json_encode(['bar', 'line', 'pie', 'doughnut', 'radar']),
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
} 