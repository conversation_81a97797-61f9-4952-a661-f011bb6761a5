<?php

namespace Database\Factories;

use App\Models\Building;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Income>
 */
class IncomeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'building_id' => Building::factory(),
            'amount' => fake()->randomFloat(2, 50, 2000),

            'payment_date' => fake()->dateTimeBetween('-6 months', 'now'),
            'payment_method' => fake()->randomElement(['cash', 'bank_transfer', 'check']),
            'notes' => fake()->optional()->sentence(),
            'is_archived' => false,
        ];
    }

    /**
     * Indicate that the income is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_archived' => true,
            'archived_at' => now(),
            'archived_by' => User::factory(),
            'archive_reason' => fake()->sentence(),
        ]);
    }
}
