<?php

namespace Database\Factories;

use App\Models\PackageChangeRequest;
use App\Models\Building;
use App\Models\User;
use App\Models\Package;
use Illuminate\Database\Eloquent\Factories\Factory;

class PackageChangeRequestFactory extends Factory
{
    protected $model = PackageChangeRequest::class;

    public function definition()
    {
        return [
            'building_id' => Building::factory(),
            'requested_by_user_id' => User::factory(),
            'current_package_id' => Package::factory(),
            'requested_package_id' => Package::factory(),
            'billing_cycle' => $this->faker->randomElement(['monthly', 'annual']),
            'payment_method' => $this->faker->randomElement(['bank_transfer', 'cash', 'credit_card']),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'reason' => $this->faker->optional()->paragraph(),
            'admin_notes' => $this->faker->optional()->paragraph(),
            'approved_by_user_id' => null,
            'approved_at' => null,
            'rejected_at' => null,
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'approved_by_user_id' => null,
                'approved_at' => null,
                'rejected_at' => null,
            ];
        });
    }

    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'approved',
                'approved_by_user_id' => User::factory(),
                'approved_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
                'rejected_at' => null,
            ];
        });
    }

    public function rejected()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'rejected',
                'approved_by_user_id' => null,
                'approved_at' => null,
                'rejected_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
                'admin_notes' => $this->faker->paragraph(),
            ];
        });
    }

    public function monthly()
    {
        return $this->state(function (array $attributes) {
            return [
                'billing_cycle' => 'monthly',
            ];
        });
    }

    public function annual()
    {
        return $this->state(function (array $attributes) {
            return [
                'billing_cycle' => 'annual',
            ];
        });
    }

    public function bankTransfer()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'bank_transfer',
            ];
        });
    }

    public function cash()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'cash',
            ];
        });
    }

    public function creditCard()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'credit_card',
            ];
        });
    }
} 