<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Building;
use App\Models\BuildingExpenseType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BuildingExpense>
 */
class BuildingExpenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'building_expense_type_id' => BuildingExpenseType::factory(),
            'building_id' => Building::factory(),
            'amount' => $this->faker->randomFloat(2, 50, 1000),
            'currency' => 'JOD',
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'month' => $this->faker->numberBetween(1, 12),
            'year' => $this->faker->numberBetween(2023, 2025),
            'notes' => $this->faker->optional()->sentence(),
            'is_archived' => false,
        ];
    }
}
