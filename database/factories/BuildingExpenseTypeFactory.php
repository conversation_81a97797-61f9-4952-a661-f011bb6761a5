<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BuildingExpenseType>
 */
class BuildingExpenseTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement([
                'Utilities',
                'Maintenance',
                'Cleaning',
                'Security',
                'Insurance',
                'Property Tax',
                'Landscaping',
                'Equipment',
                'Administrative'
            ]),
            'description' => $this->faker->sentence(),
        ];
    }
}
