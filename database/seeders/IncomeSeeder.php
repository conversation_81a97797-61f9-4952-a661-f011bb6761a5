<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Income;
use App\Models\Building;
use App\Models\User;
use Carbon\Carbon;

class IncomeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $buildings = Building::all();

        if ($buildings->isEmpty()) {
            $this->command->warn('Buildings not found. Please run BuildingSeeder first.');
            return;
        }

        foreach ($buildings as $building) {
            $users = User::where('building_id', $building->id)->where('role', 'neighbor')->get();
            
            if ($users->isEmpty()) {
                continue;
            }

            // Create income records for the last 6 months
            for ($monthsBack = 0; $monthsBack < 6; $monthsBack++) {
                $date = Carbon::now()->subMonths($monthsBack);

                foreach ($users as $user) {
                    // 70% chance of payment each month (some users might miss payments)
                    if (rand(1, 100) <= 70) {
                        $paymentMethods = ['cash', 'bank_transfer', 'check'];
                        $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
                        
                        // Payment amount varies slightly from building monthly fee
                        $baseAmount = $building->monthly_fee ?? 100;
                        $amount = $baseAmount + rand(-10, 20); // Small variation
                        
                        $paymentDate = $date->copy()->addDays(rand(1, 28));

                        Income::create([
                            'user_id' => $user->id,
                            'building_id' => $building->id,
                            'amount' => $amount,
                            'payment_date' => $paymentDate,
                            'payment_method' => $paymentMethod,
                            'notes' => "Monthly payment from {$user->apartment_number} - {$paymentMethod}",
                            'is_archived' => false,
                        ]);
                    }
                }
            }

            // Create some additional payments (partial payments, late fees, etc.)
            $additionalPayments = rand(10, 25);
            for ($i = 0; $i < $additionalPayments; $i++) {
                $user = $users->random();
                $date = Carbon::now()->subDays(rand(1, 180));
                
                $paymentTypes = [
                    ['amount' => rand(20, 50), 'note' => 'Partial payment'],
                    ['amount' => rand(10, 30), 'note' => 'Late fee payment'],
                    ['amount' => rand(50, 150), 'note' => 'Maintenance contribution'],
                    ['amount' => rand(25, 75), 'note' => 'Utility payment'],
                ];
                
                $paymentType = $paymentTypes[array_rand($paymentTypes)];
                $paymentMethods = ['cash', 'bank_transfer', 'check'];

                Income::create([
                    'user_id' => $user->id,
                    'building_id' => $building->id,
                    'amount' => $paymentType['amount'],
                    'payment_date' => $date,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'notes' => $paymentType['note'] . " from {$user->apartment_number}",
                    'is_archived' => false,
                ]);
            }

            // Create some archived income records
            $archivedCount = rand(5, 15);
            for ($i = 0; $i < $archivedCount; $i++) {
                $user = $users->random();
                $date = Carbon::now()->subMonths(rand(6, 12));

                Income::create([
                    'user_id' => $user->id,
                    'building_id' => $building->id,
                    'amount' => rand(50, 200),
                    'payment_date' => $date,
                    'payment_method' => 'bank_transfer',
                    'notes' => "Archived payment from {$user->apartment_number}",
                    'is_archived' => true,
                    'archived_at' => $date->addDays(rand(30, 90)),
                    'archived_by' => User::where('building_id', $building->id)->where('role', 'admin')->first()?->id,
                    'archive_reason' => 'End of fiscal year archive',
                ]);
            }
        }

        $this->command->info('Created income records for ' . $buildings->count() . ' buildings');
    }
}
