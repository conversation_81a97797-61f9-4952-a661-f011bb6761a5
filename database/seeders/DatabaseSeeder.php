<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core data seeders
            BuildingSeeder::class,
            ExpenseTypeSeeder::class,
            UserSeeder::class,

            // Package system seeders
            PackageSeeder::class,

            // Advanced features seeders
            ReportTemplateSeeder::class,

            // Transaction data seeders
            ExpenseSeeder::class,
            IncomeSeeder::class,
        ]);

        $this->command->info('Database seeding completed successfully!');
    }
}
