<?php

namespace Database\Seeders;

use App\Models\ReportTemplate;
use Illuminate\Database\Seeder;

class ReportTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'Financial Summary Report',
                'slug' => 'financial-summary',
                'description' => 'Comprehensive financial overview with expenses, incomes, and balance analysis',
                'category' => 'financial',
                'data_sources' => ['expenses', 'incomes'],
                'fields' => [
                    ['key' => 'amount', 'label' => 'Amount', 'type' => 'currency'],
                    ['key' => 'created_at', 'label' => 'Date', 'type' => 'date'],
                    ['key' => 'expense_type_name', 'label' => 'Expense Type', 'type' => 'string'],
                    ['key' => 'user_name', 'label' => 'User Name', 'type' => 'string'],
                    ['key' => 'apartment_number', 'label' => 'Apartment', 'type' => 'string'],
                    ['key' => 'notes', 'label' => 'Notes', 'type' => 'text'],
                ],
                'filters' => [
                    ['key' => 'expense_type_id', 'label' => 'Expense Type', 'type' => 'select'],
                    ['key' => 'user_id', 'label' => 'User', 'type' => 'select'],
                    ['key' => 'amount_min', 'label' => 'Minimum Amount', 'type' => 'number'],
                    ['key' => 'amount_max', 'label' => 'Maximum Amount', 'type' => 'number'],
                ],
                'grouping_options' => [
                    ['key' => 'expense_type_id', 'label' => 'By Expense Type'],
                    ['key' => 'user_id', 'label' => 'By User'],
                    ['key' => 'month', 'label' => 'By Month'],
                    ['key' => 'year', 'label' => 'By Year'],
                ],
                'chart_options' => [
                    ['type' => 'bar', 'label' => 'Bar Chart'],
                    ['type' => 'line', 'label' => 'Line Chart'],
                    ['type' => 'pie', 'label' => 'Pie Chart'],
                ],
                'default_config' => [
                    'fields' => ['amount', 'created_at', 'expense_type_name', 'user_name'],
                    'filters' => [],
                    'grouping' => [],
                    'calculate_totals' => true,
                ],
                'is_system_template' => true,
            ],
            [
                'name' => 'Expense Analysis Report',
                'slug' => 'expense-analysis',
                'description' => 'Detailed expense analysis with payment tracking and categorization',
                'category' => 'financial',
                'data_sources' => ['expenses', 'payments'],
                'fields' => [
                    ['key' => 'amount', 'label' => 'Expense Amount', 'type' => 'currency'],
                    ['key' => 'due_date', 'label' => 'Due Date', 'type' => 'date'],
                    ['key' => 'expense_type_name', 'label' => 'Expense Type', 'type' => 'string'],
                    ['key' => 'user_name', 'label' => 'User Name', 'type' => 'string'],
                    ['key' => 'apartment_number', 'label' => 'Apartment', 'type' => 'string'],
                    ['key' => 'payment_status', 'label' => 'Payment Status', 'type' => 'string'],
                    ['key' => 'payment_amount', 'label' => 'Paid Amount', 'type' => 'currency'],
                    ['key' => 'month', 'label' => 'Month', 'type' => 'number'],
                    ['key' => 'year', 'label' => 'Year', 'type' => 'number'],
                ],
                'filters' => [
                    ['key' => 'expense_type_id', 'label' => 'Expense Type', 'type' => 'select'],
                    ['key' => 'user_id', 'label' => 'User', 'type' => 'select'],
                    ['key' => 'status', 'label' => 'Payment Status', 'type' => 'select'],
                    ['key' => 'month', 'label' => 'Month', 'type' => 'select'],
                    ['key' => 'year', 'label' => 'Year', 'type' => 'select'],
                ],
                'grouping_options' => [
                    ['key' => 'expense_type_id', 'label' => 'By Expense Type'],
                    ['key' => 'user_id', 'label' => 'By User'],
                    ['key' => 'status', 'label' => 'By Payment Status'],
                    ['key' => 'month', 'label' => 'By Month'],
                ],
                'chart_options' => [
                    ['type' => 'bar', 'label' => 'Bar Chart'],
                    ['type' => 'line', 'label' => 'Line Chart'],
                    ['type' => 'doughnut', 'label' => 'Doughnut Chart'],
                ],
                'default_config' => [
                    'fields' => ['amount', 'due_date', 'expense_type_name', 'user_name', 'payment_status'],
                    'filters' => [],
                    'grouping' => ['expense_type_id'],
                    'calculate_totals' => true,
                ],
                'is_system_template' => true,
            ],
            [
                'name' => 'Income Tracking Report',
                'slug' => 'income-tracking',
                'description' => 'Track income sources and payment patterns',
                'category' => 'financial',
                'data_sources' => ['incomes'],
                'fields' => [
                    ['key' => 'amount', 'label' => 'Income Amount', 'type' => 'currency'],
                    ['key' => 'payment_date', 'label' => 'Payment Date', 'type' => 'date'],
                    ['key' => 'payment_method', 'label' => 'Payment Method', 'type' => 'string'],
                    ['key' => 'user_name', 'label' => 'User Name', 'type' => 'string'],
                    ['key' => 'apartment_number', 'label' => 'Apartment', 'type' => 'string'],
                    ['key' => 'notes', 'label' => 'Notes', 'type' => 'text'],
                ],
                'filters' => [
                    ['key' => 'user_id', 'label' => 'User', 'type' => 'select'],
                    ['key' => 'payment_method', 'label' => 'Payment Method', 'type' => 'select'],
                    ['key' => 'amount_min', 'label' => 'Minimum Amount', 'type' => 'number'],
                    ['key' => 'amount_max', 'label' => 'Maximum Amount', 'type' => 'number'],
                ],
                'grouping_options' => [
                    ['key' => 'user_id', 'label' => 'By User'],
                    ['key' => 'payment_method', 'label' => 'By Payment Method'],
                    ['key' => 'DATE(payment_date)', 'label' => 'By Date'],
                ],
                'chart_options' => [
                    ['type' => 'bar', 'label' => 'Bar Chart'],
                    ['type' => 'line', 'label' => 'Line Chart'],
                    ['type' => 'area', 'label' => 'Area Chart'],
                ],
                'default_config' => [
                    'fields' => ['amount', 'payment_date', 'payment_method', 'user_name'],
                    'filters' => [],
                    'grouping' => ['payment_method'],
                    'calculate_totals' => true,
                ],
                'is_system_template' => true,
            ],
            [
                'name' => 'User Activity Report',
                'slug' => 'user-activity',
                'description' => 'Analyze user engagement and payment behavior',
                'category' => 'operational',
                'data_sources' => ['users', 'expenses', 'payments'],
                'fields' => [
                    ['key' => 'user_name', 'label' => 'User Name', 'type' => 'string'],
                    ['key' => 'user_email', 'label' => 'Email', 'type' => 'string'],
                    ['key' => 'apartment_number', 'label' => 'Apartment', 'type' => 'string'],
                    ['key' => 'total_expenses', 'label' => 'Total Expenses', 'type' => 'currency'],
                    ['key' => 'total_payments', 'label' => 'Total Payments', 'type' => 'currency'],
                    ['key' => 'payment_ratio', 'label' => 'Payment Ratio', 'type' => 'percentage'],
                ],
                'filters' => [
                    ['key' => 'user_id', 'label' => 'User', 'type' => 'select'],
                    ['key' => 'apartment_number', 'label' => 'Apartment', 'type' => 'text'],
                ],
                'grouping_options' => [
                    ['key' => 'apartment_number', 'label' => 'By Apartment'],
                    ['key' => 'user_id', 'label' => 'By User'],
                ],
                'chart_options' => [
                    ['type' => 'bar', 'label' => 'Bar Chart'],
                    ['type' => 'scatter', 'label' => 'Scatter Plot'],
                    ['type' => 'radar', 'label' => 'Radar Chart'],
                ],
                'default_config' => [
                    'fields' => ['user_name', 'apartment_number', 'total_expenses', 'total_payments'],
                    'filters' => [],
                    'grouping' => [],
                    'calculate_totals' => true,
                ],
                'is_system_template' => true,
            ],
            [
                'name' => 'Monthly Trends Analysis',
                'slug' => 'monthly-trends',
                'description' => 'Analyze financial trends over time with advanced visualizations',
                'category' => 'analytics',
                'data_sources' => ['expenses', 'incomes'],
                'fields' => [
                    ['key' => 'month', 'label' => 'Month', 'type' => 'number'],
                    ['key' => 'year', 'label' => 'Year', 'type' => 'number'],
                    ['key' => 'total_expenses', 'label' => 'Total Expenses', 'type' => 'currency'],
                    ['key' => 'total_incomes', 'label' => 'Total Incomes', 'type' => 'currency'],
                    ['key' => 'net_balance', 'label' => 'Net Balance', 'type' => 'currency'],
                    ['key' => 'expense_count', 'label' => 'Expense Count', 'type' => 'number'],
                    ['key' => 'income_count', 'label' => 'Income Count', 'type' => 'number'],
                ],
                'filters' => [
                    ['key' => 'year', 'label' => 'Year', 'type' => 'select'],
                    ['key' => 'month', 'label' => 'Month', 'type' => 'select'],
                ],
                'grouping_options' => [
                    ['key' => 'year', 'label' => 'By Year'],
                    ['key' => 'month', 'label' => 'By Month'],
                    ['key' => 'CONCAT(year, "-", LPAD(month, 2, "0"))', 'label' => 'By Year-Month'],
                ],
                'chart_options' => [
                    ['type' => 'line', 'label' => 'Line Chart'],
                    ['type' => 'area', 'label' => 'Area Chart'],
                    ['type' => 'bar', 'label' => 'Bar Chart'],
                ],
                'default_config' => [
                    'fields' => ['month', 'year', 'total_expenses', 'total_incomes', 'net_balance'],
                    'filters' => [],
                    'grouping' => ['year', 'month'],
                    'calculate_totals' => true,
                ],
                'is_system_template' => true,
            ],
        ];

        foreach ($templates as $templateData) {
            ReportTemplate::updateOrCreate(
                ['slug' => $templateData['slug']],
                $templateData
            );
        }
    }
}
