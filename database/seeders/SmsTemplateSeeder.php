<?php

namespace Database\Seeders;

use App\Models\Building;
use App\Models\SmsTemplate;
use App\Models\Notification;
use Illuminate\Database\Seeder;

class SmsTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all buildings to create templates for each
        $buildings = Building::all();

        if ($buildings->isEmpty()) {
            $this->command->warn('No buildings found. Creating system templates only.');
            $this->createSystemTemplates();
            return;
        }

        foreach ($buildings as $building) {
            $this->createTemplatesForBuilding($building);
        }

        $this->command->info('SMS templates seeded successfully!');
    }

    /**
     * Create system templates (not building-specific).
     */
    private function createSystemTemplates(): void
    {
        $systemTemplates = [
            [
                'name' => 'Payment Reminder (System)',
                'notification_type' => Notification::TYPE_PAYMENT_REMINDER,
                'template' => 'Hi {user_name}, you have a payment due for {expense_type}. Amount: {amount}. Due: {due_date}. Building: {building_name}',
                'variables' => ['user_name', 'expense_type', 'amount', 'due_date', 'building_name'],
            ],
            [
                'name' => 'Overdue Payment (System)',
                'notification_type' => Notification::TYPE_OVERDUE_PAYMENT,
                'template' => 'URGENT: {user_name}, your payment for {expense_type} is {days_overdue} days overdue. Amount: {amount}. Please pay immediately.',
                'variables' => ['user_name', 'expense_type', 'days_overdue', 'amount', 'building_name'],
            ],
            [
                'name' => 'General Announcement (System)',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => '{building_name}: {title} - {message}',
                'variables' => ['building_name', 'title', 'message'],
            ],
        ];

        foreach ($systemTemplates as $templateData) {
            SmsTemplate::create([
                'building_id' => null,
                'name' => $templateData['name'],
                'notification_type' => $templateData['notification_type'],
                'template' => $templateData['template'],
                'variables' => $templateData['variables'],
                'is_active' => true,
                'is_system_template' => true,
                'character_count' => mb_strlen($templateData['template']),
            ]);
        }
    }

    /**
     * Create templates for a specific building.
     */
    private function createTemplatesForBuilding(Building $building): void
    {
        $templates = [
            // Payment Reminder Templates
            [
                'name' => 'Payment Reminder - Standard',
                'notification_type' => Notification::TYPE_PAYMENT_REMINDER,
                'template' => 'Hi {user_name}, you have a payment due for {expense_type}. Amount: {amount}. Due: {due_date}. Building: {building_name}',
                'variables' => ['user_name', 'expense_type', 'amount', 'due_date', 'building_name'],
            ],
            [
                'name' => 'Payment Reminder - Friendly',
                'notification_type' => Notification::TYPE_PAYMENT_REMINDER,
                'template' => 'Hello {user_name}! Just a friendly reminder that your {expense_type} payment of {amount} is due on {due_date}. Thank you! - {building_name}',
                'variables' => ['user_name', 'expense_type', 'amount', 'due_date', 'building_name'],
            ],
            [
                'name' => 'Payment Reminder - Brief',
                'notification_type' => Notification::TYPE_PAYMENT_REMINDER,
                'template' => '{user_name}: {expense_type} payment {amount} due {due_date}. {building_name}',
                'variables' => ['user_name', 'expense_type', 'amount', 'due_date', 'building_name'],
            ],

            // Overdue Payment Templates
            [
                'name' => 'Overdue Payment - Urgent',
                'notification_type' => Notification::TYPE_OVERDUE_PAYMENT,
                'template' => 'URGENT: {user_name}, your payment for {expense_type} is {days_overdue} days overdue. Amount: {amount}. Please pay immediately. {building_name}',
                'variables' => ['user_name', 'expense_type', 'days_overdue', 'amount', 'building_name'],
            ],
            [
                'name' => 'Overdue Payment - Final Notice',
                'notification_type' => Notification::TYPE_OVERDUE_PAYMENT,
                'template' => 'FINAL NOTICE: {user_name}, your {expense_type} payment of {amount} is {days_overdue} days overdue. Immediate payment required. {building_name}',
                'variables' => ['user_name', 'expense_type', 'days_overdue', 'amount', 'building_name'],
            ],

            // General Announcement Templates
            [
                'name' => 'General Announcement - Standard',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => '{building_name}: {title} - {message}',
                'variables' => ['building_name', 'title', 'message'],
            ],
            [
                'name' => 'General Announcement - Formal',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => 'Dear Residents, {title}: {message} - Management, {building_name}',
                'variables' => ['building_name', 'title', 'message'],
            ],

            // Expense Created Templates
            [
                'name' => 'New Expense Created',
                'notification_type' => Notification::TYPE_EXPENSE_CREATED,
                'template' => 'New expense created: {expense_type} - {amount}. Due: {due_date}. Building: {building_name}',
                'variables' => ['expense_type', 'amount', 'due_date', 'building_name'],
            ],

            // Payment Received Templates
            [
                'name' => 'Payment Received - Thank You',
                'notification_type' => Notification::TYPE_PAYMENT_RECEIVED,
                'template' => 'Payment received: {amount} for {expense_type}. Thank you! Building: {building_name}',
                'variables' => ['amount', 'expense_type', 'building_name'],
            ],
            [
                'name' => 'Payment Received - Confirmation',
                'notification_type' => Notification::TYPE_PAYMENT_RECEIVED,
                'template' => 'Payment confirmation: We received your {amount} payment for {expense_type}. Receipt will be sent via email. {building_name}',
                'variables' => ['amount', 'expense_type', 'building_name'],
            ],

            // Income Received Templates
            [
                'name' => 'Income Recorded',
                'notification_type' => Notification::TYPE_INCOME_RECEIVED,
                'template' => 'Income of {amount} recorded for your account. Building: {building_name}',
                'variables' => ['amount', 'building_name'],
            ],

            // Emergency/Maintenance Templates
            [
                'name' => 'Emergency Notification',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => 'EMERGENCY: {message} Please follow safety instructions. {building_name}',
                'variables' => ['message', 'building_name'],
            ],
            [
                'name' => 'Maintenance Notice',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => 'MAINTENANCE: {title} scheduled. {message} Expected duration: Contact management for details. {building_name}',
                'variables' => ['title', 'message', 'building_name'],
            ],

            // Utility/Service Templates
            [
                'name' => 'Utility Service Notice',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => 'SERVICE NOTICE: {title}. {message} We apologize for any inconvenience. {building_name}',
                'variables' => ['title', 'message', 'building_name'],
            ],

            // Meeting/Event Templates
            [
                'name' => 'Meeting Reminder',
                'notification_type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'template' => 'MEETING REMINDER: {title} on {message}. Your attendance is important. {building_name}',
                'variables' => ['title', 'message', 'building_name'],
            ],
        ];

        foreach ($templates as $templateData) {
            // Check if template already exists for this building and type
            $existingTemplate = SmsTemplate::where('building_id', $building->id)
                ->where('notification_type', $templateData['notification_type'])
                ->where('name', $templateData['name'])
                ->first();

            if (!$existingTemplate) {
                SmsTemplate::create([
                    'building_id' => $building->id,
                    'name' => $templateData['name'],
                    'notification_type' => $templateData['notification_type'],
                    'template' => $templateData['template'],
                    'variables' => $templateData['variables'],
                    'is_active' => true,
                    'is_system_template' => false,
                    'character_count' => mb_strlen($templateData['template']),
                ]);
            }
        }

        $this->command->info("Created SMS templates for building: {$building->name}");
    }
}
