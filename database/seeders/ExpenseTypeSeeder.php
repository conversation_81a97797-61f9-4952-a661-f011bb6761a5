<?php

namespace Database\Seeders;

use App\Models\ExpenseType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $expenseTypes = [
            [
                'name' => 'Maintenance',
                'description' => 'Regular building maintenance and repairs',
            ],
            [
                'name' => 'Utilities',
                'description' => 'Common area utilities (water, electricity, etc.)',
            ],
            [
                'name' => 'Security',
                'description' => 'Building security services and equipment',
            ],
            [
                'name' => 'Cleaning',
                'description' => 'Cleaning services for common areas',
            ],
            [
                'name' => 'Insurance',
                'description' => 'Building insurance premiums',
            ],
            [
                'name' => 'Landscaping',
                'description' => 'Garden and landscape maintenance',
            ],
            [
                'name' => 'Administration',
                'description' => 'Administrative costs and management fees',
            ],
            [
                'name' => 'Repairs',
                'description' => 'Emergency repairs and fixes',
            ],
            [
                'name' => 'Elevator Maintenance',
                'description' => 'Elevator servicing and repairs',
            ],
            [
                'name' => 'Parking',
                'description' => 'Parking area maintenance and security',
            ],
            [
                'name' => 'Setup Fee',
                'description' => 'One-time setup fee for building registration',
            ],
            [
                'name' => 'Monthly Subscription',
                'description' => 'Monthly subscription fee per building',
            ],
        ];

        foreach ($expenseTypes as $type) {
            ExpenseType::create($type);
        }
    }
}
