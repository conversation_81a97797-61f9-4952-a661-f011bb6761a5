<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Building;

class BuildingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $buildings = [
            [
                'name' => 'Sunset Apartments',
                'address' => '123 Main Street',
                'city' => 'New York',
                'country' => 'USA',
                'postal_code' => '10001',
                'description' => 'Modern apartment complex in downtown Manhattan',
                'monthly_fee' => 250.00,
                'currency' => 'USD',
            ],
            [
                'name' => 'Ocean View Towers',
                'address' => '456 Beach Boulevard',
                'city' => 'Miami',
                'country' => 'USA',
                'postal_code' => '33101',
                'description' => 'Luxury high-rise with ocean views',
                'monthly_fee' => 350.00,
                'currency' => 'USD',
            ],
            [
                'name' => 'Garden Heights',
                'address' => '789 Garden Lane',
                'city' => 'Los Angeles',
                'country' => 'USA',
                'postal_code' => '90210',
                'description' => 'Family-friendly residential complex',
                'monthly_fee' => 200.00,
                'currency' => 'USD',
            ],
            [
                'name' => 'Downtown Plaza',
                'address' => '321 Business District',
                'city' => 'Chicago',
                'country' => 'USA',
                'postal_code' => '60601',
                'description' => 'Modern business district apartments',
                'monthly_fee' => 300.00,
                'currency' => 'USD',
            ],
            [
                'name' => 'Riverside Commons',
                'address' => '654 River Road',
                'city' => 'Portland',
                'country' => 'USA',
                'postal_code' => '97201',
                'description' => 'Eco-friendly apartments by the river',
                'monthly_fee' => 275.00,
                'currency' => 'USD',
            ]
        ];

        foreach ($buildings as $building) {
            Building::create($building);
        }

        // Create additional buildings using factory
        Building::factory()->count(5)->create();
    }
}
