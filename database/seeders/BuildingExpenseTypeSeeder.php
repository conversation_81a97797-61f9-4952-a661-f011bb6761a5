<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BuildingExpenseType;

class BuildingExpenseTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $buildingExpenseTypes = [
            [
                'name' => 'Utilities',
                'description' => 'Electricity, water, gas, and other utility expenses for the building'
            ],
            [
                'name' => 'Maintenance',
                'description' => 'General building maintenance and repairs'
            ],
            [
                'name' => 'Cleaning',
                'description' => 'Cleaning services for common areas'
            ],
            [
                'name' => 'Security',
                'description' => 'Security services and equipment'
            ],
            [
                'name' => 'Insurance',
                'description' => 'Building insurance premiums'
            ],
            [
                'name' => 'Property Tax',
                'description' => 'Property taxes and government fees'
            ],
            [
                'name' => 'Landscaping',
                'description' => 'Garden and landscaping maintenance'
            ],
            [
                'name' => 'Equipment',
                'description' => 'Purchase and maintenance of building equipment'
            ],
            [
                'name' => 'Administrative',
                'description' => 'Administrative and management expenses'
            ],
            [
                'name' => 'Other',
                'description' => 'Other miscellaneous building expenses'
            ]
        ];

        foreach ($buildingExpenseTypes as $type) {
            BuildingExpenseType::firstOrCreate(
                ['name' => $type['name']],
                $type
            );
        }
    }
}
