<?php

namespace Database\Seeders;

use App\Models\Package;
use Illuminate\Database\Seeder;

class UpdatePackagesWithExportSettingsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Updating packages with export settings...');

        // Define export settings for different package types
        $exportSettings = [
            'free' => [
                'exports_enabled' => false,
                'exports_per_month' => 0,
                'max_records_per_export' => 100,
                'export_formats' => [],
            ],
            'basic' => [
                'exports_enabled' => true,
                'exports_per_month' => 5,
                'max_records_per_export' => 500,
                'export_formats' => ['pdf'],
            ],
            'standard' => [
                'exports_enabled' => true,
                'exports_per_month' => 25,
                'max_records_per_export' => 2000,
                'export_formats' => ['pdf', 'excel'],
            ],
            'premium' => [
                'exports_enabled' => true,
                'exports_per_month' => null, // unlimited
                'max_records_per_export' => 10000,
                'export_formats' => ['pdf', 'excel'],
            ],
        ];

        // Update packages based on their slug or name
        foreach ($exportSettings as $packageType => $settings) {
            $packages = Package::where('slug', 'like', "%{$packageType}%")
                ->orWhere('name', 'like', "%{$packageType}%")
                ->get();

            foreach ($packages as $package) {
                $package->update($settings);
                $this->command->info("Updated package: {$package->name} with {$packageType} export settings");
            }
        }

        // If no packages were found by slug/name matching, update by price ranges
        $allPackages = Package::all();
        
        if ($allPackages->isNotEmpty()) {
            foreach ($allPackages as $package) {
                // Skip if already updated
                if ($package->exports_enabled !== null) {
                    continue;
                }

                // Determine package tier by price
                $price = $package->price;
                
                if ($price <= 0) {
                    // Free package
                    $package->update($exportSettings['free']);
                    $this->command->info("Updated free package: {$package->name}");
                } elseif ($price <= 50) {
                    // Basic package
                    $package->update($exportSettings['basic']);
                    $this->command->info("Updated basic package: {$package->name}");
                } elseif ($price <= 150) {
                    // Standard package
                    $package->update($exportSettings['standard']);
                    $this->command->info("Updated standard package: {$package->name}");
                } else {
                    // Premium package
                    $package->update($exportSettings['premium']);
                    $this->command->info("Updated premium package: {$package->name}");
                }
            }
        }

        $this->command->info('Package export settings update completed.');
    }
}
