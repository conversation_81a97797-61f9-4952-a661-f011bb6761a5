<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Package;

class AddFreePackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if free package already exists
        $freePackage = Package::where('slug', 'free')->first();
        
        if (!$freePackage) {
            // Create Free Package
            Package::create([
                'name' => 'الباقة المجانية',
                'slug' => 'free',
                'description' => 'باقة مجانية أساسية للمباني الصغيرة مع ميزات محدودة',
                'description_en' => 'Free basic package for small buildings with limited features',
                'price' => 0.00,
                'annual_price' => 0.00,
                'max_neighbors' => 10,
                'notifications_enabled' => true,
                'email_notifications_enabled' => false,
                'sms_notifications_enabled' => false,
                'priority_support' => false,
                'advanced_reporting' => false,
                'file_attachments_enabled' => false,
                'storage_limit_gb' => 1,
                'exports_enabled' => false,
                'exports_per_month' => 0,
                'max_records_per_export' => 0,
                'export_formats' => null,
                'features' => [
                    'basic_expense_tracking',
                    'basic_income_tracking',
                    'basic_user_management',
                    'basic_notifications'
                ],
                'limitations' => [
                    'no_email_notifications',
                    'no_sms_notifications',
                    'no_file_attachments',
                    'no_exports',
                    'no_advanced_reporting',
                    'limited_neighbors',
                    'limited_storage',
                    'community_support_only'
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 0, // First in order
                'billing_cycle' => 'monthly',
                'trial_days' => 0,
            ]);

            $this->command->info('Free package created successfully.');
        } else {
            $this->command->info('Free package already exists.');
        }

        // Also ensure all existing buildings without a package get assigned the free package
        $buildingsWithoutPackage = \App\Models\Building::whereNull('current_package_id')->get();
        
        if ($buildingsWithoutPackage->count() > 0) {
            $freePackage = Package::where('slug', 'free')->first();
            
            foreach ($buildingsWithoutPackage as $building) {
                $building->update(['current_package_id' => $freePackage->id]);
                $this->command->info("Assigned free package to building: {$building->name}");
            }
            
            $this->command->info("Assigned free package to {$buildingsWithoutPackage->count()} buildings.");
        } else {
            $this->command->info('All buildings already have packages assigned.');
        }
    }
}
