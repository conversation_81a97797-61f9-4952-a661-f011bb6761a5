<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Building;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all buildings
        $buildings = Building::all();

        if ($buildings->isEmpty()) {
            $this->command->warn('No buildings found. Please run BuildingSeeder first.');
            return;
        }

        // Create super admin (not tied to any building)
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password123'),
                'role' => 'super_admin',
                'building_id' => null,
                'apartment_number' => null,
                'phone_number' => '+1234567890',
                'email_verified_at' => now(),
            ]
        );

        // Create admin and neighbors for each building
        foreach ($buildings as $building) {
            // Create building admin
            $admin = User::firstOrCreate(
                ['email' => "admin.{$building->id}@lajnet.com"],
                [
                    'name' => "Admin {$building->name}",
                    'password' => Hash::make('password123'),
                    'role' => 'admin',
                    'building_id' => $building->id,
                    'apartment_number' => 'ADMIN',
                    'phone_number' => '+1234567' . str_pad($building->id, 3, '0', STR_PAD_LEFT),
                    'email_verified_at' => now(),
                ]
            );

            // Create neighbors for this building
            $apartmentNumbers = ['A101', 'A102', 'A201', 'A202', 'B101', 'B102', 'B201', 'B202'];
            
            foreach (array_slice($apartmentNumbers, 0, rand(4, 8)) as $index => $apartmentNumber) {
                User::firstOrCreate(
                    ['email' => "neighbor.{$building->id}.{$apartmentNumber}@lajnet.com"],
                    [
                        'name' => "Neighbor {$apartmentNumber} - {$building->name}",
                        'password' => Hash::make('password123'),
                        'role' => 'neighbor',
                        'building_id' => $building->id,
                        'apartment_number' => $apartmentNumber,
                        'phone_number' => '+1234567' . str_pad($building->id * 10 + $index, 3, '0', STR_PAD_LEFT),
                        'email_verified_at' => now(),
                    ]
                );
            }
        }

        // Create some additional test users using factories
        foreach ($buildings->take(3) as $building) {
            User::factory()->count(2)->neighbor()->create([
                'building_id' => $building->id,
            ]);
        }

        $this->command->info('Created users for ' . $buildings->count() . ' buildings');
    }
}
