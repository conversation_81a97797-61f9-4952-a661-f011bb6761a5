<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Package;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Basic Package
        Package::create([
            'name' => 'الباقة الأساسية',
            'slug' => 'basic',
            'description' => 'أداة إدارة  الأساسية للمباني الصغيرة',
            'description_en' => 'Core admin tool for small buildings',
            'price' => 9.99,
            'annual_price' => 99.90, // 20% discount (9.99 * 12 * 0.8)
            'max_neighbors' => 50,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => false,
            'custom_reports_enabled' => false,
            'max_custom_reports' => 0,
            'report_scheduling_enabled' => false,
            'max_scheduled_reports' => 0,
            'advanced_charts_enabled' => false,
            'available_chart_types' => null,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 5,
            'features' => [
                'core_saas_admin_tool',
                'expense_tracking',
                'income_tracking',
                'user_management',
                'basic_reports',
                'email_notifications',
                'file_attachments'
            ],
            'limitations' => [
                'no_sms_notifications',
                'no_phone_support',
                'no_advanced_reporting',
                'no_custom_reports',
                'limited_storage'
            ],
            'is_active' => true,
            'is_popular' => false,
            'sort_order' => 1,
            'billing_cycle' => 'both',
            'trial_days' => 14,
        ]);

        // Standard Package
        Package::create([
            'name' => 'الباقة القياسية',
            'slug' => 'standard',
            'description' => 'باقة قياسية مع تصدير التقارير ومتعدد المشرفين',
            'description_en' => 'Standard package with reports export and multi-admin support',
            'price' => 14.99,
            'annual_price' => 149.90, // 20% discount (14.99 * 12 * 0.8)
            'max_neighbors' => null, // unlimited
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => true,
            'custom_reports_enabled' => true,
            'max_custom_reports' => 10,
            'report_scheduling_enabled' => true,
            'max_scheduled_reports' => 5,
            'advanced_charts_enabled' => true,
            'available_chart_types' => ['bar', 'line', 'pie', 'doughnut'],
            'exports_enabled' => true,
            'exports_per_month' => 50,
            'max_records_per_export' => 5000,
            'export_formats' => ['pdf', 'excel'],
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 25,
            'features' => [
                'core_saas_admin_tool',
                'reports_export',
                'multi_admin_support',
                'unlimited_neighbors',
                'advanced_reports',
                'custom_reports',
                'report_scheduling',
                'advanced_charts',
                'email_notifications',
                'file_attachments',
                'expense_tracking',
                'income_tracking',
                'user_management',
                'payment_reminders'
            ],
            'limitations' => [
                'no_sms_notifications',
                'no_phone_support',
                'standard_support'
            ],
            'is_active' => true,
            'is_popular' => true,
            'sort_order' => 2,
            'billing_cycle' => 'both',
            'trial_days' => 14,
        ]);

        // Pro Package
        Package::create([
            'name' => 'الباقة الاحترافية',
            'slug' => 'pro',
            'description' => 'باقة احترافية مع الدعم الهاتفي والأرشفة وحزمة SMS صغيرة',
            'description_en' => 'Pro package with phone support, archive, and small SMS package',
            'price' => 24.99,
            'annual_price' => 249.90, // 20% discount (24.99 * 12 * 0.8)
            'max_neighbors' => null, // unlimited
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => true,
            'priority_support' => true,
            'advanced_reporting' => true,
            'custom_reports_enabled' => true,
            'max_custom_reports' => 50,
            'report_scheduling_enabled' => true,
            'max_scheduled_reports' => 20,
            'advanced_charts_enabled' => true,
            'available_chart_types' => ['bar', 'line', 'pie', 'doughnut', 'area', 'scatter', 'bubble'],
            'exports_enabled' => true,
            'exports_per_month' => null, // unlimited
            'max_records_per_export' => 10000,
            'export_formats' => ['pdf', 'excel'],
            'file_attachments_enabled' => true,
            'storage_limit_gb' => null, // unlimited
            'features' => [
                'core_saas_admin_tool',
                'reports_export',
                'multi_admin_support',
                'phone_support',
                'archive_feature',
                'small_sms_package',
                'unlimited_neighbors',
                'unlimited_storage',
                'advanced_reports',
                'custom_reports',
                'report_scheduling',
                'advanced_charts',
                'email_notifications',
                'sms_notifications',
                'file_attachments',
                'large_file_support',
                'expense_tracking',
                'income_tracking',
                'user_management',
                'payment_reminders',
                'priority_support',
                'data_archiving'
            ],
            'limitations' => [],
            'is_active' => true,
            'is_popular' => false,
            'sort_order' => 3,
            'billing_cycle' => 'both',
            'trial_days' => 30,
        ]);

        // Add-on Services Package (for separate booking)
        Package::create([
            'name' => 'الخدمات الإضافية',
            'slug' => 'addons',
            'description' => 'خدمات إضافية يتم حجزها بشكل منفصل من لوحة التحكم',
            'description_en' => 'Add-on services booked separately from dashboard',
            'price' => 0.00, // Variable pricing
            'annual_price' => 0.00,
            'max_neighbors' => null,
            'notifications_enabled' => false,
            'email_notifications_enabled' => false,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => false,
            'custom_reports_enabled' => false,
            'max_custom_reports' => 0,
            'report_scheduling_enabled' => false,
            'max_scheduled_reports' => 0,
            'advanced_charts_enabled' => false,
            'available_chart_types' => null,
            'file_attachments_enabled' => false,
            'storage_limit_gb' => 0,
            'features' => [
                'custom_integrations',
                'additional_sms_packages',
                'premium_support',
                'custom_reports',
                'api_access',
                'white_labeling'
            ],
            'limitations' => [
                'requires_separate_booking',
                'variable_pricing'
            ],
            'is_active' => true,
            'is_popular' => false,
            'sort_order' => 4,
            'billing_cycle' => 'both',
            'trial_days' => 0,
        ]);
    }
}
