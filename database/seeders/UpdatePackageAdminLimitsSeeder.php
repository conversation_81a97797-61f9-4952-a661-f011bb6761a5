<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Package;

class UpdatePackageAdminLimitsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define package configurations based on common naming patterns
        $packageConfigs = [
            // Basic/Free packages
            'basic' => [
                'multi_admin_enabled' => false,
                'max_admins' => 1,
            ],
            'free' => [
                'multi_admin_enabled' => false,
                'max_admins' => 1,
            ],
            
            // Standard packages
            'standard' => [
                'multi_admin_enabled' => true,
                'max_admins' => 3,
            ],
            
            // Premium/Pro packages
            'premium' => [
                'multi_admin_enabled' => true,
                'max_admins' => 5,
            ],
            'pro' => [
                'multi_admin_enabled' => true,
                'max_admins' => 5,
            ],
            
            // Enterprise packages
            'enterprise' => [
                'multi_admin_enabled' => true,
                'max_admins' => 10,
            ],
        ];

        // Update existing packages
        foreach (Package::all() as $package) {
            $slug = strtolower($package->slug);
            
            // Find matching configuration
            $config = null;
            foreach ($packageConfigs as $pattern => $packageConfig) {
                if (str_contains($slug, $pattern)) {
                    $config = $packageConfig;
                    break;
                }
            }
            
            // If no specific config found, use basic defaults
            if (!$config) {
                $config = $packageConfigs['basic'];
            }
            
            // Update the package
            $package->update($config);
            
            $this->command->info("Updated package '{$package->name}' (slug: {$package->slug}) with admin limits");
        }
        
        $this->command->info('All packages updated with admin limits!');
    }
}
