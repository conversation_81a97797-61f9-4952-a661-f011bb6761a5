<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Expense;
use App\Models\Building;
use App\Models\User;
use App\Models\ExpenseType;
use Carbon\Carbon;

class ExpenseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $buildings = Building::all();
        $expenseTypes = ExpenseType::all();

        if ($buildings->isEmpty() || $expenseTypes->isEmpty()) {
            $this->command->warn('Buildings or ExpenseTypes not found. Please run BuildingSeeder and ExpenseTypeSeeder first.');
            return;
        }

        foreach ($buildings as $building) {
            $users = User::where('building_id', $building->id)->where('role', 'neighbor')->get();
            
            if ($users->isEmpty()) {
                continue;
            }

            // Create expenses for the last 6 months
            for ($monthsBack = 0; $monthsBack < 6; $monthsBack++) {
                $date = Carbon::now()->subMonths($monthsBack);
                $month = $date->format('m');
                $year = $date->format('Y');

                foreach ($users as $user) {
                    // Create 1-3 expenses per user per month
                    $expenseCount = rand(1, 3);
                    
                    for ($i = 0; $i < $expenseCount; $i++) {
                        $expenseType = $expenseTypes->random();
                        
                        // Different expense amounts based on type
                        $amount = match($expenseType->name) {
                            'Setup Fee' => rand(50, 150), // Variable setup fee
                            'Monthly Subscription' => rand(10, 30), // Variable subscription based on package
                            'Maintenance' => rand(50, 200),
                            'Utilities' => rand(30, 150),
                            'Security' => rand(25, 100),
                            'Cleaning' => rand(20, 80),
                            'Insurance' => rand(40, 120),
                            'Landscaping' => rand(15, 60),
                            'Administration' => rand(10, 50),
                            'Repairs' => rand(100, 500),
                            'Elevator Maintenance' => rand(30, 150),
                            'Parking' => rand(20, 75),
                            default => rand(25, 100),
                        };

                        $dueDate = $date->copy()->addDays(rand(1, 28));

                        Expense::create([
                            'expense_type_id' => $expenseType->id,
                            'user_id' => $user->id,
                            'building_id' => $building->id,
                            'amount' => $amount,
                            'due_date' => $dueDate,
                            'month' => $month,
                            'year' => $year,
                            'notes' => "Monthly {$expenseType->name} for {$user->apartment_number}",
                            'is_automatic' => rand(0, 1) ? true : false,
                            'is_archived' => false,
                        ]);
                    }
                }
            }

            // Create some archived expenses
            $archivedCount = rand(5, 15);
            for ($i = 0; $i < $archivedCount; $i++) {
                $user = $users->random();
                $expenseType = $expenseTypes->random();
                $date = Carbon::now()->subMonths(rand(6, 12));

                Expense::create([
                    'expense_type_id' => $expenseType->id,
                    'user_id' => $user->id,
                    'building_id' => $building->id,
                    'amount' => rand(25, 200),
                    'due_date' => $date,
                    'month' => $date->format('m'),
                    'year' => $date->format('Y'),
                    'notes' => "Archived {$expenseType->name} for {$user->apartment_number}",
                    'is_automatic' => false,
                    'is_archived' => true,
                    'archived_at' => $date->addDays(rand(30, 90)),
                    'archived_by' => User::where('building_id', $building->id)->where('role', 'admin')->first()?->id,
                    'archive_reason' => 'Completed and archived',
                ]);
            }
        }

        $this->command->info('Created expenses for ' . $buildings->count() . ' buildings');
    }
}
