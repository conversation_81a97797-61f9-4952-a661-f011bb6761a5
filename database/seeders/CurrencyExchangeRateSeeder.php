<?php

namespace Database\Seeders;

use App\Models\CurrencyExchangeRate;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class CurrencyExchangeRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $effectiveDate = now();

        // Base rates against USD (approximate rates for demonstration)
        $usdRates = [
            'JOD' => 0.71,
            'ILS' => 3.75,
        ];

        // Create USD to other currency rates
        foreach ($usdRates as $currency => $rate) {
            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => 'USD',
                'to_currency' => $currency,
                'effective_date' => $effectiveDate,
                'provider' => 'manual',
            ], [
                'rate' => $rate,
                'is_active' => true,
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Initial exchange rates for demonstration'
                ]
            ]);

            // Create reverse rates (other currency to USD)
            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => $currency,
                'to_currency' => 'USD',
                'effective_date' => $effectiveDate,
                'provider' => 'manual',
            ], [
                'rate' => 1 / $rate,
                'is_active' => true,
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Initial exchange rates for demonstration'
                ]
            ]);
        }

        // Create some cross-currency rates for Middle Eastern currencies
        $crossRates = [
            ['JOD', 'ILS', 5.28]
        ];

        foreach ($crossRates as [$fromCurrency, $toCurrency, $rate]) {
            // Create forward rate
            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'effective_date' => $effectiveDate,
                'provider' => 'manual',
            ], [
                'rate' => $rate,
                'is_active' => true,
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Cross-currency rates for Middle Eastern currencies'
                ]
            ]);

            // Create reverse rate
            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => $toCurrency,
                'to_currency' => $fromCurrency,
                'effective_date' => $effectiveDate,
                'provider' => 'manual',
            ], [
                'rate' => 1 / $rate,
                'is_active' => true,
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Cross-currency rates for Middle Eastern currencies'
                ]
            ]);
        }

        // Create some historical rates (30 days ago) with slight variations
        $historicalDate = now()->subDays(30);
        
        foreach ($usdRates as $currency => $baseRate) {
            // Add some random variation (-5% to +5%)
            $variation = (rand(-500, 500) / 10000); // -0.05 to +0.05
            $historicalRate = $baseRate * (1 + $variation);

            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => 'USD',
                'to_currency' => $currency,
                'effective_date' => $historicalDate,
                'provider' => 'manual',
            ], [
                'rate' => $historicalRate,
                'is_active' => false, // Historical rates are not active
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Historical exchange rates for demonstration',
                    'variation' => $variation
                ]
            ]);

            CurrencyExchangeRate::updateOrCreate([
                'from_currency' => $currency,
                'to_currency' => 'USD',
                'effective_date' => $historicalDate,
                'provider' => 'manual',
            ], [
                'rate' => 1 / $historicalRate,
                'is_active' => false,
                'metadata' => [
                    'source' => 'seeder',
                    'note' => 'Historical exchange rates for demonstration',
                    'variation' => -$variation
                ]
            ]);
        }

        $this->command->info('Currency exchange rates seeded successfully!');
        $this->command->info('Created rates for ' . count($usdRates) . ' currencies against USD');
        $this->command->info('Created ' . count($crossRates) . ' cross-currency rate pairs');
        $this->command->info('Created historical rates for demonstration');
    }
}
