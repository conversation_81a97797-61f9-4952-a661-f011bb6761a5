<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('packages')) {
            Schema::table('packages', function (Blueprint $table) {
                if (!Schema::hasColumn('packages', 'email_limit_per_month')) {
                    $table->integer('email_limit_per_month')->nullable()->after('email_notifications_enabled')->comment('Monthly email limit, null for unlimited');
                }
                if (!Schema::hasColumn('packages', 'email_limit_per_day')) {
                    $table->integer('email_limit_per_day')->nullable()->after('email_limit_per_month')->comment('Daily email limit, null for unlimited');
                }
                if (!Schema::hasColumn('packages', 'email_quota_warnings_enabled')) {
                    $table->boolean('email_quota_warnings_enabled')->default(true)->after('email_limit_per_day')->comment('Enable quota warning emails');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('packages')) {
            Schema::table('packages', function (Blueprint $table) {
                if (Schema::hasColumn('packages', 'email_limit_per_month')) {
                    $table->dropColumn('email_limit_per_month');
                }
                if (Schema::hasColumn('packages', 'email_limit_per_day')) {
                    $table->dropColumn('email_limit_per_day');
                }
                if (Schema::hasColumn('packages', 'email_quota_warnings_enabled')) {
                    $table->dropColumn('email_quota_warnings_enabled');
                }
            });
        }
    }
};
