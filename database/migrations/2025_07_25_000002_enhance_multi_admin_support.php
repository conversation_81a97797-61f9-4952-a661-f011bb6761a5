<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add admin hierarchy and permission fields to users table
        Schema::table('users', function (Blueprint $table) {
            $table->enum('admin_level', ['primary', 'secondary'])->nullable()->after('role');
            $table->json('admin_permissions')->nullable()->after('admin_level');
            $table->timestamp('last_activity_at')->nullable()->after('admin_permissions');
            $table->boolean('is_active')->default(true)->after('last_activity_at');
            
            // Add indexes for admin queries
            $table->index(['role', 'building_id']);
            $table->index(['admin_level', 'building_id']);
            $table->index(['is_active', 'building_id']);
        });

        // Create admin activity log table
        Schema::create('admin_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->string('resource_type')->nullable(); // expense, income, user, etc.
            $table->unsignedBigInteger('resource_id')->nullable();
            $table->json('details')->nullable(); // Additional action details
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();

            // Indexes for efficient querying
            $table->index(['admin_id', 'created_at']);
            $table->index(['building_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['resource_type', 'resource_id']);
        });

        // Update admin invitations table to support admin levels
        Schema::table('admin_invitations', function (Blueprint $table) {
            $table->enum('admin_level', ['primary', 'secondary'])->default('secondary')->after('role');
            $table->json('permissions')->nullable()->after('admin_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role', 'building_id']);
            $table->dropIndex(['admin_level', 'building_id']);
            $table->dropIndex(['is_active', 'building_id']);
            $table->dropColumn(['admin_level', 'admin_permissions', 'last_activity_at', 'is_active']);
        });

        Schema::dropIfExists('admin_activity_logs');

        Schema::table('admin_invitations', function (Blueprint $table) {
            $table->dropColumn(['admin_level', 'permissions']);
        });
    }
};
