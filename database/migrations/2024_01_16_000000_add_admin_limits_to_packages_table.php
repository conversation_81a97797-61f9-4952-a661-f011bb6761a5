<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            // Add admin management fields
            $table->boolean('multi_admin_enabled')->default(false)->after('advanced_reporting');
            $table->integer('max_admins')->default(1)->after('multi_admin_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn([
                'multi_admin_enabled',
                'max_admins'
            ]);
        });
    }
};
