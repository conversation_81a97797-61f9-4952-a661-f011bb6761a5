<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('buildings')) {
            Schema::table('buildings', function (Blueprint $table) {
                if (!Schema::hasColumn('buildings', 'current_package_id')) {
                    // Add the column first without foreign key constraint
                    $table->unsignedBigInteger('current_package_id')->nullable()->after('id');
                }
                if (!Schema::hasColumn('buildings', 'package_updated_at')) {
                    $table->timestamp('package_updated_at')->nullable()->after('current_package_id');
                }
            });

            // Add foreign key constraint only if packages table exists
            if (Schema::hasTable('packages')) {
                Schema::table('buildings', function (Blueprint $table) {
                    $table->foreign('current_package_id')->references('id')->on('packages')->onDelete('set null');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('buildings')) {
            Schema::table('buildings', function (Blueprint $table) {
                // Drop foreign key constraint if it exists
                if (Schema::hasColumn('buildings', 'current_package_id')) {
                    try {
                        $table->dropForeign(['current_package_id']);
                    } catch (\Exception $e) {
                        // Foreign key might not exist, continue
                    }
                    $table->dropColumn('current_package_id');
                }
                if (Schema::hasColumn('buildings', 'package_updated_at')) {
                    $table->dropColumn('package_updated_at');
                }
            });
        }
    }
};
