<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for users table (search by name, email, apartment_number)
        Schema::table('users', function (Blueprint $table) {
            $table->index(['name']);
            $table->index(['email']);
            $table->index(['apartment_number']);
            $table->index(['building_id', 'name']);
            $table->index(['building_id', 'apartment_number']);
        });

        // Add indexes for expenses table (search by notes, amount, month, year)
        Schema::table('expenses', function (Blueprint $table) {
            $table->index(['amount']);
            $table->index(['month', 'year']);
            $table->index(['building_id', 'month', 'year']);
            $table->index(['user_id', 'month', 'year']);
            $table->index(['expense_type_id']);
            $table->index(['created_at']);
        });

        // Add indexes for incomes table (search by notes, amount, payment_date)
        Schema::table('incomes', function (Blueprint $table) {
            $table->index(['amount']);
            $table->index(['payment_date']);
            $table->index(['payment_method']);
            $table->index(['building_id', 'payment_date']);
            $table->index(['user_id', 'payment_date']);
            $table->index(['created_at']);
        });

        // Add indexes for payments table (search by amount, payment_date, payment_method)
        Schema::table('payments', function (Blueprint $table) {
            $table->index(['amount']);
            $table->index(['payment_date']);
            $table->index(['payment_method']);
            $table->index(['status']);
            $table->index(['expense_id', 'payment_date']);
            $table->index(['user_id', 'payment_date']);
            $table->index(['created_at']);
        });

        // Add indexes for buildings table (performance optimization)
        Schema::table('buildings', function (Blueprint $table) {
            $table->index(['current_package_id']);
            $table->index(['currency']);
            $table->index(['created_at']);
        });

        // Add indexes for packages table (performance optimization)
        if (Schema::hasTable('packages')) {
            Schema::table('packages', function (Blueprint $table) {
                $table->index(['is_active']);
                $table->index(['sort_order']);
                $table->index(['billing_cycle']);
            });
        }

        // Add indexes for subscriptions table (performance optimization)
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->index(['building_id', 'status']);
                $table->index(['package_id']);
                $table->index(['status']);
                $table->index(['starts_at']);
                $table->index(['ends_at']);
                $table->index(['trial_ends_at']);
                $table->index(['created_at']);
            });
        }

        // Add indexes for notifications table (performance optimization)
        if (Schema::hasTable('notifications')) {
            Schema::table('notifications', function (Blueprint $table) {
                // Only add indexes that don't already exist
                $table->index(['building_id', 'created_at']);
                // Skip user_id, read_at as it already exists from table creation
                $table->index(['type']);
                $table->index(['created_at']);
            });
        }

        // Add indexes for file_attachments table (performance optimization)
        if (Schema::hasTable('file_attachments')) {
            Schema::table('file_attachments', function (Blueprint $table) {
                $table->index(['attachable_type', 'attachable_id']);
                $table->index(['building_id']);
                $table->index(['file_size']);
                $table->index(['created_at']);
            });
        }

        // Add indexes for expense_types table (search by name)
        if (Schema::hasTable('expense_types')) {
            Schema::table('expense_types', function (Blueprint $table) {
                $table->index(['name']);
            });
        }

        // Add indexes for buildings table (search by name, address)
        Schema::table('buildings', function (Blueprint $table) {
            $table->index(['name']);
            $table->index(['address']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['email']);
            $table->dropIndex(['apartment_number']);
            $table->dropIndex(['building_id', 'name']);
            $table->dropIndex(['building_id', 'apartment_number']);
        });

        // Remove indexes from expenses table
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropIndex(['amount']);
            $table->dropIndex(['month', 'year']);
            $table->dropIndex(['building_id', 'month', 'year']);
            $table->dropIndex(['user_id', 'month', 'year']);
            $table->dropIndex(['expense_type_id']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from incomes table
        Schema::table('incomes', function (Blueprint $table) {
            $table->dropIndex(['amount']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['payment_method']);
            $table->dropIndex(['building_id', 'payment_date']);
            $table->dropIndex(['user_id', 'payment_date']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->dropIndex(['amount']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['payment_method']);
            $table->dropIndex(['status']);
            $table->dropIndex(['expense_id', 'payment_date']);
            $table->dropIndex(['user_id', 'payment_date']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from buildings table
        Schema::table('buildings', function (Blueprint $table) {
            $table->dropIndex(['current_package_id']);
            $table->dropIndex(['currency']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from packages table
        Schema::table('packages', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
            $table->dropIndex(['sort_order']);
            $table->dropIndex(['billing_cycle']);
        });

        // Remove indexes from subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['building_id', 'status']);
            $table->dropIndex(['package_id']);
            $table->dropIndex(['status']);
            $table->dropIndex(['starts_at']);
            $table->dropIndex(['ends_at']);
            $table->dropIndex(['trial_ends_at']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from notifications table
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropIndex(['building_id', 'created_at']);
            $table->dropIndex(['user_id', 'read_at']);
            $table->dropIndex(['type']);
            $table->dropIndex(['is_read']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from file_attachments table
        Schema::table('file_attachments', function (Blueprint $table) {
            $table->dropIndex(['attachable_type', 'attachable_id']);
            $table->dropIndex(['building_id']);
            $table->dropIndex(['file_size']);
            $table->dropIndex(['created_at']);
        });

        // Remove indexes from expense_types table
        Schema::table('expense_types', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['building_id', 'name']);
        });

        // Remove indexes from buildings table
        Schema::table('buildings', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['address']);
        });
    }
};
