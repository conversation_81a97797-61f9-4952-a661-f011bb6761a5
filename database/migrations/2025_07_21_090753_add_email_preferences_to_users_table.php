<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Email notification preferences
            $table->boolean('email_notifications_enabled')->default(true)->after('email');
            $table->boolean('email_payment_reminders')->default(true)->after('email_notifications_enabled');
            $table->boolean('email_expense_notifications')->default(true)->after('email_payment_reminders');
            $table->boolean('email_income_notifications')->default(true)->after('email_expense_notifications');
            $table->boolean('email_general_announcements')->default(true)->after('email_income_notifications');
            $table->boolean('email_overdue_notifications')->default(true)->after('email_general_announcements');

            // Email frequency preferences
            $table->enum('email_frequency', ['immediate', 'daily', 'weekly'])->default('immediate')->after('email_overdue_notifications');

            // Last email sent timestamp for frequency control
            $table->timestamp('last_email_sent_at')->nullable()->after('email_frequency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'email_notifications_enabled',
                'email_payment_reminders',
                'email_expense_notifications',
                'email_income_notifications',
                'email_general_announcements',
                'email_overdue_notifications',
                'email_frequency',
                'last_email_sent_at'
            ]);
        });
    }
};
