<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('package_id')->constrained()->onDelete('restrict');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who created the subscription

            // Subscription details
            $table->enum('status', ['active', 'inactive', 'cancelled', 'expired', 'trial'])->default('trial');
            $table->enum('billing_cycle', ['monthly', 'annual'])->default('monthly');
            $table->decimal('amount', 10, 2); // Amount paid for this subscription
            $table->string('currency', 3)->default('USD');

            // Subscription period
            $table->timestamp('starts_at');
            $table->timestamp('ends_at');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();

            // Payment tracking
            $table->timestamp('last_payment_at')->nullable();
            $table->timestamp('next_payment_due')->nullable();
            $table->integer('payment_failures')->default(0);
            $table->timestamp('last_payment_failure_at')->nullable();

            // Usage tracking
            $table->integer('current_neighbors_count')->default(0);
            $table->bigInteger('storage_used_bytes')->default(0);
            $table->integer('notifications_sent_this_month')->default(0);
            $table->integer('emails_sent_this_month')->default(0);
            $table->json('usage_stats')->nullable(); // Additional usage statistics

            // Auto-renewal settings
            $table->boolean('auto_renew')->default(true);
            $table->string('payment_method')->nullable(); // For future payment integration
            $table->json('payment_details')->nullable(); // Encrypted payment details

            // Metadata
            $table->json('metadata')->nullable(); // Additional subscription data
            $table->text('notes')->nullable(); // Admin notes

            $table->timestamps();

            // Indexes
            $table->index(['building_id', 'status']);
            $table->index(['status', 'ends_at']);
            $table->index(['next_payment_due']);
            $table->unique(['building_id', 'status'], 'unique_active_subscription');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
