<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('building_expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_expense_type_id')->constrained()->onDelete('cascade');
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->decimal('original_amount', 10, 4)->nullable();
            $table->string('original_currency', 3)->nullable();
            $table->decimal('exchange_rate', 10, 6)->nullable();
            $table->timestamp('exchange_rate_date')->nullable();
            $table->date('due_date');
            $table->string('month');
            $table->string('year');
            $table->text('notes')->nullable();
            $table->boolean('is_automatic')->default(false);
            $table->boolean('is_archived')->default(false);
            $table->timestamp('archived_at')->nullable();
            $table->foreignId('archived_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('archive_reason')->nullable();
            $table->timestamps();

            // Add indexes for performance
            $table->index(['building_id', 'month', 'year']);
            $table->index(['building_expense_type_id']);
            $table->index(['is_archived', 'building_id']);
            $table->index(['archived_at']);
            $table->index(['amount']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('building_expenses');
    }
};
