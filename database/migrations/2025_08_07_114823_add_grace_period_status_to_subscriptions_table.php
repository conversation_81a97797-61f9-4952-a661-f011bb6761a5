<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update the status enum to include grace_period
        DB::statement("ALTER TABLE subscriptions MODIFY COLUMN status ENUM('active', 'inactive', 'cancelled', 'expired', 'trial', 'grace_period') NOT NULL DEFAULT 'trial'");

        // Add any additional fields if needed for grace period tracking
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->timestamp('grace_period_started_at')->nullable()->after('ends_at');
            $table->text('grace_period_notes')->nullable()->after('grace_period_started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn(['grace_period_started_at', 'grace_period_notes']);
        });

        // Revert the status enum to original values
        DB::statement("ALTER TABLE subscriptions MODIFY COLUMN status ENUM('active', 'inactive', 'cancelled', 'expired', 'trial') NOT NULL DEFAULT 'trial'");
    }
};
