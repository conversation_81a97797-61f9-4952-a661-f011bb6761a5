<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        // Create admin activity log table
        Schema::create('admin_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained('users');
            $table->foreignId('building_id')->nullable();
            $table->string('action');
            $table->string('resource_type')->nullable(); // expense, income, user, etc.
            $table->unsignedBigInteger('resource_id')->nullable();
            $table->json('details')->nullable(); // Additional action details
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();

            // Indexes for efficient querying
            $table->index(['admin_id', 'created_at']);
            $table->index(['building_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['resource_type', 'resource_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
        // Drop admin activity logs table if it exists
        Schema::dropIfExists('admin_activity_logs');
    }
};
