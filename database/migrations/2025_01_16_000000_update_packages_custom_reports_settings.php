<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update Standard package to enable custom reports
        DB::table('packages')
            ->where('slug', 'standard')
            ->update([
                'custom_reports_enabled' => true,
                'max_custom_reports' => 10,
                'report_scheduling_enabled' => true,
                'max_scheduled_reports' => 5,
                'advanced_charts_enabled' => true,
                'available_chart_types' => json_encode(['bar', 'line', 'pie', 'doughnut']),
            ]);

        // Update Pro package to enable custom reports with higher limits
        DB::table('packages')
            ->where('slug', 'pro')
            ->update([
                'custom_reports_enabled' => true,
                'max_custom_reports' => 50,
                'report_scheduling_enabled' => true,
                'max_scheduled_reports' => 20,
                'advanced_charts_enabled' => true,
                'available_chart_types' => json_encode(['bar', 'line', 'pie', 'doughnut', 'area', 'scatter', 'bubble']),
            ]);

        // Update Basic package to disable custom reports (if not already set)
        DB::table('packages')
            ->where('slug', 'basic')
            ->update([
                'custom_reports_enabled' => false,
                'max_custom_reports' => 0,
                'report_scheduling_enabled' => false,
                'max_scheduled_reports' => 0,
                'advanced_charts_enabled' => false,
                'available_chart_types' => null,
            ]);

        // Update Add-ons package to disable custom reports (if not already set)
        DB::table('packages')
            ->where('slug', 'addons')
            ->update([
                'custom_reports_enabled' => false,
                'max_custom_reports' => 0,
                'report_scheduling_enabled' => false,
                'max_scheduled_reports' => 0,
                'advanced_charts_enabled' => false,
                'available_chart_types' => null,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert all packages to disable custom reports
        DB::table('packages')->update([
            'custom_reports_enabled' => false,
            'max_custom_reports' => 0,
            'report_scheduling_enabled' => false,
            'max_scheduled_reports' => 0,
            'advanced_charts_enabled' => false,
            'available_chart_types' => null,
        ]);
    }
}; 