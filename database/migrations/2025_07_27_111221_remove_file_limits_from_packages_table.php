<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn(['max_file_size_mb', 'max_files_per_record']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->integer('max_file_size_mb')->default(10)->after('file_attachments_enabled');
            $table->integer('max_files_per_record')->default(5)->after('max_file_size_mb');
        });
    }
};
