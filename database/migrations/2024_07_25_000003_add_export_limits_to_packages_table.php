<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('packages')) {
            Schema::table('packages', function (Blueprint $table) {
                if (!Schema::hasColumn('packages', 'exports_enabled')) {
                    $table->boolean('exports_enabled')->default(false)->after('advanced_reporting')->comment('Enable export functionality');
                }
                if (!Schema::hasColumn('packages', 'exports_per_month')) {
                    $table->integer('exports_per_month')->nullable()->after('exports_enabled')->comment('Monthly export limit, null for unlimited');
                }
                if (!Schema::hasColumn('packages', 'max_records_per_export')) {
                    $table->integer('max_records_per_export')->default(1000)->after('exports_per_month')->comment('Maximum records per export');
                }
                if (!Schema::hasColumn('packages', 'export_formats')) {
                    $table->json('export_formats')->nullable()->after('max_records_per_export')->comment('Allowed export formats (pdf, excel)');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('packages')) {
            Schema::table('packages', function (Blueprint $table) {
                $columns = ['exports_enabled', 'exports_per_month', 'max_records_per_export', 'export_formats'];
                foreach ($columns as $column) {
                    if (Schema::hasColumn('packages', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
};
