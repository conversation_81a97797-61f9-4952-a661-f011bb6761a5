<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Free, Standard, Premium
            $table->string('slug')->unique(); // free, standard, premium
            $table->text('description');
            $table->text('description_en')->nullable(); // English description
            $table->decimal('price', 10, 2); // Monthly price in SAR
            $table->decimal('annual_price', 10, 2)->nullable(); // Annual price (optional discount)

            // Package limits
            $table->integer('max_neighbors')->nullable(); // null = unlimited
            $table->boolean('notifications_enabled')->default(false);
            $table->boolean('email_notifications_enabled')->default(false);
            $table->boolean('sms_notifications_enabled')->default(false);
            $table->boolean('priority_support')->default(false);
            $table->boolean('advanced_reporting')->default(false);
            $table->boolean('file_attachments_enabled')->default(false);
            $table->integer('max_file_size_mb')->default(10); // Max file size in MB
            $table->integer('max_files_per_record')->default(5); // Max files per expense/income
            $table->integer('storage_limit_gb')->nullable(); // Storage limit in GB, null = unlimited

            // Package features (JSON for flexibility)
            $table->json('features')->nullable(); // Additional features as JSON
            $table->json('limitations')->nullable(); // Additional limitations as JSON

            // Package status and ordering
            $table->boolean('is_active')->default(true);
            $table->boolean('is_popular')->default(false); // Highlight as popular choice
            $table->integer('sort_order')->default(0); // Display order

            // Billing settings
            $table->enum('billing_cycle', ['monthly', 'annual', 'both'])->default('monthly');
            $table->integer('trial_days')->default(0); // Free trial days

            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
