<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // This migration fixes issues with admin invitation table references
        // by ensuring the admin_invitations table exists before trying to modify it
        
        // Create admin_invitations table if it doesn't exist (to prevent errors in previous migrations)
        if (!Schema::hasTable('admin_invitations')) {
            Schema::create('admin_invitations', function (Blueprint $table) {
                $table->id();
                $table->string('email');
                $table->string('name');
                $table->string('token')->unique();
                $table->foreignId('building_id')->constrained()->onDelete('cascade');
                $table->foreignId('invited_by')->constrained('users')->onDelete('cascade');
                $table->enum('role', ['admin'])->default('admin');
                $table->enum('status', ['pending', 'accepted', 'expired', 'cancelled'])->default('pending');
                $table->timestamp('expires_at');
                $table->timestamp('accepted_at')->nullable();
                $table->timestamps();

                $table->index(['email', 'building_id']);
                $table->index(['token']);
                $table->index(['status']);
            });
        }

        // Add admin_level and permissions columns if they don't exist
        if (Schema::hasTable('admin_invitations')) {
            Schema::table('admin_invitations', function (Blueprint $table) {
                if (!Schema::hasColumn('admin_invitations', 'admin_level')) {
                    $table->enum('admin_level', ['primary', 'secondary'])->default('secondary')->after('role');
                }
                if (!Schema::hasColumn('admin_invitations', 'permissions')) {
                    $table->json('permissions')->nullable()->after('admin_level');
                }
            });
        }

        // Now drop the entire table since we don't want the admin invitation system
        Schema::dropIfExists('admin_invitations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is designed to clean up, so we don't recreate the table
    }
};
