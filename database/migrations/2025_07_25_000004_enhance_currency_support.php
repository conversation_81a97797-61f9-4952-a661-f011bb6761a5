<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add currency fields to expenses table (check if columns don't exist)
        if (!Schema::hasColumn('expenses', 'currency')) {
            Schema::table('expenses', function (Blueprint $table) {
                $table->string('currency', 3)->default('USD')->after('amount');
                $table->decimal('original_amount', 10, 4)->nullable()->after('currency');
                $table->string('original_currency', 3)->nullable()->after('original_amount');
                $table->decimal('exchange_rate', 10, 6)->nullable()->after('original_currency');
                $table->timestamp('exchange_rate_date')->nullable()->after('exchange_rate');
            });
        }

        // Add currency fields to incomes table (check if columns don't exist)
        if (!Schema::hasColumn('incomes', 'currency')) {
            Schema::table('incomes', function (Blueprint $table) {
                $table->string('currency', 3)->default('USD')->after('amount');
                $table->decimal('original_amount', 10, 4)->nullable()->after('currency');
                $table->string('original_currency', 3)->nullable()->after('original_amount');
                $table->decimal('exchange_rate', 10, 6)->nullable()->after('original_currency');
                $table->timestamp('exchange_rate_date')->nullable()->after('exchange_rate');
            });
        }

        // Add currency fields to payments table (check if columns don't exist)
        if (!Schema::hasColumn('payments', 'currency')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->string('currency', 3)->default('USD')->after('amount');
                $table->decimal('original_amount', 10, 4)->nullable()->after('currency');
                $table->string('original_currency', 3)->nullable()->after('original_amount');
                $table->decimal('exchange_rate', 10, 6)->nullable()->after('original_currency');
                $table->timestamp('exchange_rate_date')->nullable()->after('exchange_rate');
            });
        }

        // Create currency exchange rates table (check if doesn't exist)
        if (!Schema::hasTable('currency_exchange_rates')) {
            Schema::create('currency_exchange_rates', function (Blueprint $table) {
            $table->id();
            $table->string('from_currency', 3);
            $table->string('to_currency', 3);
            $table->decimal('rate', 15, 6);
            $table->string('provider')->default('manual'); // manual, api, bank
            $table->timestamp('effective_date');
            $table->boolean('is_active')->default(true);
            $table->json('metadata')->nullable(); // Additional rate information
            $table->timestamps();

            // Indexes for efficient querying
            $table->index(['from_currency', 'to_currency', 'effective_date'], 'idx_currency_pair_date');
            $table->index(['is_active', 'effective_date'], 'idx_active_date');
            $table->unique(['from_currency', 'to_currency', 'effective_date', 'provider'], 'unq_rate_date_provider');
        });
        }

        // Create currency settings table for buildings (check if doesn't exist)
        if (!Schema::hasTable('building_currency_settings')) {
            Schema::create('building_currency_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->string('primary_currency', 3)->default('USD');
            $table->json('accepted_currencies')->nullable(); // Additional currencies accepted
            $table->boolean('auto_convert')->default(true); // Auto-convert to primary currency
            $table->string('exchange_rate_provider')->default('manual'); // manual, api
            $table->decimal('conversion_markup', 5, 4)->default(0.0000); // Markup percentage for conversions
            $table->json('regional_settings')->nullable(); // Number formatting, date formats, etc.
            $table->timestamps();

            $table->unique('building_id');
        });
        }

        // Create currency conversion history table (check if doesn't exist)
        if (!Schema::hasTable('currency_conversions')) {
            Schema::create('currency_conversions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->string('convertible_type'); // expense, income, payment
            $table->unsignedBigInteger('convertible_id');
            $table->string('from_currency', 3);
            $table->string('to_currency', 3);
            $table->decimal('from_amount', 10, 4);
            $table->decimal('to_amount', 10, 4);
            $table->decimal('exchange_rate', 10, 6);
            $table->string('rate_provider')->default('manual');
            $table->timestamp('converted_at');
            $table->foreignId('converted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->json('conversion_metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['convertible_type', 'convertible_id'], 'idx_convertible');
            $table->index(['building_id', 'converted_at'], 'idx_building_date');
            $table->index(['from_currency', 'to_currency'], 'idx_currency_pair');
        });
        }

        // Add regional settings to buildings table (check if columns don't exist)
        if (!Schema::hasColumn('buildings', 'country_code')) {
            Schema::table('buildings', function (Blueprint $table) {
                $table->string('country_code', 2)->nullable()->after('country');
                $table->string('timezone')->default('UTC')->after('country_code');
                $table->string('locale')->default('en_US')->after('timezone');
                $table->json('regional_preferences')->nullable()->after('locale');
            });
        }

        // Add currency preferences to users table (check if columns don't exist)
        if (!Schema::hasColumn('users', 'preferred_currency')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('preferred_currency', 3)->nullable()->after('building_id');
                $table->string('locale')->default('en_US')->after('preferred_currency');
                $table->json('display_preferences')->nullable()->after('locale');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove currency fields from expenses table
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropColumn([
                'currency', 'original_amount', 'original_currency', 
                'exchange_rate', 'exchange_rate_date'
            ]);
        });

        // Remove currency fields from incomes table
        Schema::table('incomes', function (Blueprint $table) {
            $table->dropColumn([
                'currency', 'original_amount', 'original_currency', 
                'exchange_rate', 'exchange_rate_date'
            ]);
        });

        // Remove currency fields from payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn([
                'currency', 'original_amount', 'original_currency', 
                'exchange_rate', 'exchange_rate_date'
            ]);
        });

        // Remove regional fields from buildings table
        Schema::table('buildings', function (Blueprint $table) {
            $table->dropColumn([
                'country_code', 'timezone', 'locale', 'regional_preferences'
            ]);
        });

        // Remove currency preferences from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['preferred_currency', 'locale', 'display_preferences']);
        });

        // Drop new tables
        Schema::dropIfExists('currency_conversions');
        Schema::dropIfExists('building_currency_settings');
        Schema::dropIfExists('currency_exchange_rates');
    }
};
