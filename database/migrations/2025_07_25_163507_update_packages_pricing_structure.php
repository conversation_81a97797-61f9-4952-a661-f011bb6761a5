<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Package;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable foreign key checks temporarily
        \DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing packages
        Package::truncate();

        // Re-enable foreign key checks
        \DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Insert new packages with updated pricing structure
        $packages = [
            [
                'name' => 'الباقة الأساسية',
                'slug' => 'basic',
                'description' => 'أداة إدارة SaaS الأساسية للمباني الصغيرة',
                'description_en' => 'Core SaaS admin tool for small buildings',
                'price' => 9.99,
                'annual_price' => 99.90, // 20% discount (9.99 * 12 * 0.8)
                'max_neighbors' => 50,
                'notifications_enabled' => true,
                'email_notifications_enabled' => true,
                'sms_notifications_enabled' => false,
                'priority_support' => false,
                'advanced_reporting' => false,
                'file_attachments_enabled' => true,
                'max_file_size_mb' => 5,
                'max_files_per_record' => 3,
                'storage_limit_gb' => 5,
                'features' => [
                    'core_saas_admin_tool',
                    'expense_tracking',
                    'income_tracking',
                    'user_management',
                    'basic_reports',
                    'email_notifications',
                    'file_attachments'
                ],
                'limitations' => [
                    'no_sms_notifications',
                    'no_phone_support',
                    'no_advanced_reporting',
                    'limited_storage'
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1,
                'billing_cycle' => 'both',
                'trial_days' => 14,
            ],
            [
                'name' => 'الباقة القياسية',
                'slug' => 'standard',
                'description' => 'باقة قياسية مع تصدير التقارير ومتعدد المشرفين',
                'description_en' => 'Standard package with reports export and multi-admin support',
                'price' => 14.99,
                'annual_price' => 149.90, // 20% discount (14.99 * 12 * 0.8)
                'max_neighbors' => null, // unlimited
                'notifications_enabled' => true,
                'email_notifications_enabled' => true,
                'sms_notifications_enabled' => false,
                'priority_support' => false,
                'advanced_reporting' => true,
                'file_attachments_enabled' => true,
                'max_file_size_mb' => 15,
                'max_files_per_record' => 10,
                'storage_limit_gb' => 25,
                'features' => [
                    'core_saas_admin_tool',
                    'reports_export',
                    'multi_admin_support',
                    'unlimited_neighbors',
                    'advanced_reports',
                    'email_notifications',
                    'file_attachments',
                    'expense_tracking',
                    'income_tracking',
                    'user_management',
                    'payment_reminders'
                ],
                'limitations' => [
                    'no_sms_notifications',
                    'no_phone_support',
                    'standard_support'
                ],
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2,
                'billing_cycle' => 'both',
                'trial_days' => 14,
            ],
            [
                'name' => 'الباقة الاحترافية',
                'slug' => 'pro',
                'description' => 'باقة احترافية مع الدعم الهاتفي والأرشفة وحزمة SMS صغيرة',
                'description_en' => 'Pro package with phone support, archive, and small SMS package',
                'price' => 24.99,
                'annual_price' => 249.90, // 20% discount (24.99 * 12 * 0.8)
                'max_neighbors' => null, // unlimited
                'notifications_enabled' => true,
                'email_notifications_enabled' => true,
                'sms_notifications_enabled' => true,
                'priority_support' => true,
                'advanced_reporting' => true,
                'file_attachments_enabled' => true,
                'max_file_size_mb' => 50,
                'max_files_per_record' => 25,
                'storage_limit_gb' => null, // unlimited
                'features' => [
                    'core_saas_admin_tool',
                    'reports_export',
                    'multi_admin_support',
                    'phone_support',
                    'archive_feature',
                    'small_sms_package',
                    'unlimited_neighbors',
                    'unlimited_storage',
                    'advanced_reports',
                    'email_notifications',
                    'sms_notifications',
                    'file_attachments',
                    'large_file_support',
                    'expense_tracking',
                    'income_tracking',
                    'user_management',
                    'payment_reminders',
                    'priority_support',
                    'data_archiving'
                ],
                'limitations' => [],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 3,
                'billing_cycle' => 'both',
                'trial_days' => 30,
            ],
            [
                'name' => 'الخدمات الإضافية',
                'slug' => 'addons',
                'description' => 'خدمات إضافية يتم حجزها بشكل منفصل من لوحة التحكم',
                'description_en' => 'Add-on services booked separately from dashboard',
                'price' => 0.00, // Variable pricing
                'annual_price' => 0.00,
                'max_neighbors' => null,
                'notifications_enabled' => false,
                'email_notifications_enabled' => false,
                'sms_notifications_enabled' => false,
                'priority_support' => false,
                'advanced_reporting' => false,
                'file_attachments_enabled' => false,
                'max_file_size_mb' => 0,
                'max_files_per_record' => 0,
                'storage_limit_gb' => 0,
                'features' => [
                    'custom_integrations',
                    'additional_sms_packages',
                    'premium_support',
                    'custom_reports',
                    'api_access',
                    'white_labeling'
                ],
                'limitations' => [
                    'requires_separate_booking',
                    'variable_pricing'
                ],
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 4,
                'billing_cycle' => 'both',
                'trial_days' => 0,
            ]
        ];

        foreach ($packages as $packageData) {
            Package::create($packageData);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear packages and restore original seeder data if needed
        Package::truncate();

        // You could restore original packages here if needed
        // For now, we'll just clear them
    }
};
