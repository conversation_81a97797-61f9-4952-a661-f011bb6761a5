<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add archive fields to expenses table
        Schema::table('expenses', function (Blueprint $table) {
            $table->boolean('is_archived')->default(false)->after('is_automatic');
            $table->timestamp('archived_at')->nullable()->after('is_archived');
            $table->foreignId('archived_by')->nullable()->constrained('users')->onDelete('set null')->after('archived_at');
            $table->text('archive_reason')->nullable()->after('archived_by');
            
            // Add indexes for archive queries
            $table->index(['is_archived', 'building_id']);
            $table->index(['archived_at']);
        });

        // Add archive fields to incomes table
        Schema::table('incomes', function (Blueprint $table) {
            $table->boolean('is_archived')->default(false)->after('notes');
            $table->timestamp('archived_at')->nullable()->after('is_archived');
            $table->foreignId('archived_by')->nullable()->constrained('users')->onDelete('set null')->after('archived_at');
            $table->text('archive_reason')->nullable()->after('archived_by');
            
            // Add indexes for archive queries
            $table->index(['is_archived', 'building_id']);
            $table->index(['archived_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropIndex(['is_archived', 'building_id']);
            $table->dropIndex(['archived_at']);
            $table->dropForeign(['archived_by']);
            $table->dropColumn(['is_archived', 'archived_at', 'archived_by', 'archive_reason']);
        });

        Schema::table('incomes', function (Blueprint $table) {
            $table->dropIndex(['is_archived', 'building_id']);
            $table->dropIndex(['archived_at']);
            $table->dropForeign(['archived_by']);
            $table->dropColumn(['is_archived', 'archived_at', 'archived_by', 'archive_reason']);
        });
    }
};
