<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop admin invitations table if it exists
        Schema::dropIfExists('admin_invitations');

        // Remove admin invitation related columns from users table if they exist
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                // Check and drop columns that might exist from admin invitation system
                if (Schema::hasColumn('users', 'admin_level')) {
                    $table->dropColumn('admin_level');
                }
                if (Schema::hasColumn('users', 'admin_permissions')) {
                    $table->dropColumn('admin_permissions');
                }
                if (Schema::hasColumn('users', 'last_activity_at')) {
                    $table->dropColumn('last_activity_at');
                }
                if (Schema::hasColumn('users', 'is_active')) {
                    $table->dropColumn('is_active');
                }
            });
        }

        // Drop admin activity logs table if it exists
        Schema::dropIfExists('admin_activity_logs');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't need to recreate these tables as we're removing the admin invitation system
        // This migration is irreversible by design
    }
};
