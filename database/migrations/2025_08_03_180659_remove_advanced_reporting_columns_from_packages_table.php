<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            // Remove outdated advanced reporting columns
            $table->dropColumn([
                'advanced_reporting',
                'custom_reports_enabled',
                'max_custom_reports',
                'report_scheduling_enabled',
                'max_scheduled_reports',
                'advanced_charts_enabled',
                'available_chart_types'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            // Add back the removed columns if migration is rolled back
            $table->boolean('advanced_reporting')->default(false)->after('priority_support');
            $table->boolean('custom_reports_enabled')->default(false)->after('advanced_reporting');
            $table->integer('max_custom_reports')->nullable()->after('custom_reports_enabled');
            $table->boolean('report_scheduling_enabled')->default(false)->after('max_custom_reports');
            $table->integer('max_scheduled_reports')->nullable()->after('report_scheduling_enabled');
            $table->boolean('advanced_charts_enabled')->default(false)->after('max_scheduled_reports');
            $table->json('available_chart_types')->nullable()->after('advanced_charts_enabled');
        });
    }
};
