<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add only essential performance indexes to avoid conflicts

        // Add indexes for users table if they don't exist
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->index(['role', 'building_id'], 'users_role_building_perf');
            });
        } catch (\Exception $e) {
            // Index might already exist
        }

        // Add indexes for payments table if they don't exist
        try {
            Schema::table('payments', function (Blueprint $table) {
                $table->index(['payment_method', 'status'], 'payments_method_status_perf');
            });
        } catch (\Exception $e) {
            // Index might already exist
        }

        // Add indexes for incomes table if they don't exist
        try {
            Schema::table('incomes', function (Blueprint $table) {
                $table->index(['payment_method', 'building_id'], 'incomes_method_building_perf');
            });
        } catch (\Exception $e) {
            // Index might already exist
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the performance indexes we added
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex('users_role_building_perf');
            });
        } catch (\Exception $e) {
            // Index might not exist
        }

        try {
            Schema::table('payments', function (Blueprint $table) {
                $table->dropIndex('payments_method_status_perf');
            });
        } catch (\Exception $e) {
            // Index might not exist
        }

        try {
            Schema::table('incomes', function (Blueprint $table) {
                $table->dropIndex('incomes_method_building_perf');
            });
        } catch (\Exception $e) {
            // Index might not exist
        }
    }
};
