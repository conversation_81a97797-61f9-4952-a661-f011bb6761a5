<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->boolean('setup_fee_paid')->default(false);
            $table->string('setup_fee_payment_id')->nullable();
            $table->timestamp('setup_fee_paid_at')->nullable();
            $table->string('payment_gateway')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->dropColumn(['setup_fee_paid', 'setup_fee_payment_id', 'setup_fee_paid_at', 'payment_gateway']);
        });
    }
};
