<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('expense_type_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->enum('recurrence_type', ['monthly', 'quarterly', 'yearly', 'custom'])->default('monthly');
            $table->integer('recurrence_day')->nullable(); // Day of month (1-31)
            $table->integer('recurrence_month')->nullable(); // Month for yearly (1-12)
            $table->integer('due_days_after')->default(30); // Days after generation when due
            $table->text('notes_template')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('auto_generate')->default(false);
            $table->timestamp('last_generated_at')->nullable();
            $table->date('next_generation_date')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['building_id', 'is_active']);
            $table->index(['building_id', 'auto_generate']);
            $table->index(['next_generation_date', 'auto_generate']);
        });

        // Add template_id to expenses table
        Schema::table('expenses', function (Blueprint $table) {
            $table->foreignId('template_id')->nullable()->after('building_id')->constrained('expense_templates')->onDelete('set null');
            $table->index(['template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropForeign(['template_id']);
            $table->dropColumn('template_id');
        });

        Schema::dropIfExists('expense_templates');
    }
};
