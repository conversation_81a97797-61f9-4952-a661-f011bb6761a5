<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add SMS fields to notifications table (check if columns don't exist)
        if (!Schema::hasColumn('notifications', 'sms_enabled')) {
            Schema::table('notifications', function (Blueprint $table) {
                $table->boolean('sms_enabled')->default(false)->after('email_sent_at');
                $table->boolean('sms_sent')->default(false)->after('sms_enabled');
                $table->timestamp('sms_sent_at')->nullable()->after('sms_sent');
                $table->string('sms_provider')->nullable()->after('sms_sent_at'); // twilio, aws_sns
                $table->string('sms_message_id')->nullable()->after('sms_provider'); // Provider's message ID
                $table->string('sms_status')->nullable()->after('sms_message_id'); // sent, delivered, failed, etc.
                $table->json('sms_metadata')->nullable()->after('sms_status'); // Additional SMS data
            });
        }

        // Add phone number and SMS preferences to users table (check if columns don't exist)
        if (!Schema::hasColumn('users', 'phone_country_code')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'phone_number')) {
                    $table->string('phone_number')->nullable()->after('email');
                }
                $table->string('phone_country_code', 5)->default('+1')->after('phone_number');
                $table->boolean('sms_notifications_enabled')->default(false)->after('phone_country_code');
                $table->json('sms_preferences')->nullable()->after('sms_notifications_enabled');
            });
        }

        // Create SMS notification settings table for buildings (check if doesn't exist)
        if (!Schema::hasTable('building_sms_settings')) {
            Schema::create('building_sms_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->boolean('sms_enabled')->default(false);
            $table->string('sms_provider')->default('twilio'); // twilio, aws_sns
            $table->json('provider_config')->nullable(); // Provider-specific configuration
            $table->integer('monthly_sms_limit')->default(100); // SMS limit per month
            $table->integer('sms_sent_this_month')->default(0);
            $table->decimal('cost_per_sms', 8, 4)->default(0.0075); // Cost per SMS
            $table->json('notification_types')->nullable(); // Which notification types to send via SMS
            $table->boolean('require_user_opt_in')->default(true);
            $table->string('default_country_code', 5)->default('+1');
            $table->timestamps();

            $table->unique('building_id');
        });
        }

        // Create SMS delivery logs table (check if doesn't exist)
        if (!Schema::hasTable('sms_delivery_logs')) {
            Schema::create('sms_delivery_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->string('phone_number');
            $table->string('provider'); // twilio, aws_sns
            $table->string('provider_message_id')->nullable();
            $table->enum('status', ['pending', 'sent', 'delivered', 'failed', 'undelivered'])->default('pending');
            $table->text('message_content');
            $table->decimal('cost', 8, 4)->nullable();
            $table->json('provider_response')->nullable();
            $table->string('error_code')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['building_id', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['provider', 'status']);
            $table->index(['sent_at']);
        });
        }

        // Create SMS templates table (check if doesn't exist)
        if (!Schema::hasTable('sms_templates')) {
            Schema::create('sms_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('notification_type'); // payment_reminder, expense_created, etc.
            $table->text('template'); // SMS template with placeholders
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system_template')->default(false); // System vs custom templates
            $table->json('variables')->nullable(); // Available template variables
            $table->integer('character_count')->default(0);
            $table->timestamps();

            $table->index(['building_id', 'notification_type']);
            $table->index(['is_active', 'notification_type']);
        });
        }

        // Create SMS usage statistics table (check if doesn't exist)
        if (!Schema::hasTable('sms_usage_stats')) {
            Schema::create('sms_usage_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->date('date');
            $table->integer('sms_sent')->default(0);
            $table->integer('sms_delivered')->default(0);
            $table->integer('sms_failed')->default(0);
            $table->decimal('total_cost', 10, 4)->default(0);
            $table->json('breakdown_by_type')->nullable(); // SMS count by notification type
            $table->json('breakdown_by_provider')->nullable(); // SMS count by provider
            $table->timestamps();

            $table->unique(['building_id', 'date']);
            $table->index(['date']);
        });
        }

        // Create SMS opt-in/opt-out logs table (check if doesn't exist)
        if (!Schema::hasTable('sms_opt_logs')) {
            Schema::create('sms_opt_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['opt_in', 'opt_out']);
            $table->string('phone_number');
            $table->string('method')->default('web'); // web, sms, admin
            $table->text('reason')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'action']);
            $table->index(['building_id', 'created_at']);
        });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop new tables
        Schema::dropIfExists('sms_opt_logs');
        Schema::dropIfExists('sms_usage_stats');
        Schema::dropIfExists('sms_templates');
        Schema::dropIfExists('sms_delivery_logs');
        Schema::dropIfExists('building_sms_settings');

        // Remove SMS fields from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone_number',
                'phone_country_code',
                'sms_notifications_enabled',
                'sms_preferences'
            ]);
        });

        // Remove SMS fields from notifications table
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn([
                'sms_enabled',
                'sms_sent',
                'sms_sent_at',
                'sms_provider',
                'sms_message_id',
                'sms_status',
                'sms_metadata'
            ]);
        });
    }
};
