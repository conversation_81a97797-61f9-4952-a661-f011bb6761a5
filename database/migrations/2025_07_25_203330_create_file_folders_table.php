<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_folders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('parent_id')->nullable()->constrained('file_folders')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#6b7280'); // Hex color code
            $table->boolean('is_system')->default(false);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['building_id', 'parent_id']);
            $table->index(['building_id', 'is_system']);
        });

        // Add folder_id to file_attachments table
        Schema::table('file_attachments', function (Blueprint $table) {
            $table->foreignId('folder_id')->nullable()->after('building_id')->constrained('file_folders')->onDelete('set null');
            $table->index(['building_id', 'folder_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('file_attachments', function (Blueprint $table) {
            $table->dropForeign(['folder_id']);
            $table->dropColumn('folder_id');
        });

        Schema::dropIfExists('file_folders');
    }
};
