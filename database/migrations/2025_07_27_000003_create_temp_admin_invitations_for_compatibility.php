<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration creates a temporary admin_invitations table to satisfy
     * the old migration that tries to modify it, then drops it immediately.
     */
    public function up(): void
    {
        // Create admin_invitations table temporarily to satisfy old migration
        if (!Schema::hasTable('admin_invitations')) {
            Schema::create('admin_invitations', function (Blueprint $table) {
                $table->id();
                $table->string('email');
                $table->string('name');
                $table->string('token')->unique();
                $table->foreignId('building_id')->constrained()->onDelete('cascade');
                $table->foreignId('invited_by')->constrained('users')->onDelete('cascade');
                $table->enum('role', ['admin'])->default('admin');
                $table->enum('status', ['pending', 'accepted', 'expired', 'cancelled'])->default('pending');
                $table->timestamp('expires_at');
                $table->timestamp('accepted_at')->nullable();
                $table->timestamps();

                $table->index(['email', 'building_id']);
                $table->index(['token']);
                $table->index(['status']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_invitations');
    }
};
