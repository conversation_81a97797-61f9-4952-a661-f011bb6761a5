<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create report templates table
        Schema::create('report_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('category', ['financial', 'operational', 'analytics', 'custom']);
            $table->json('data_sources'); // Which models/tables to query
            $table->json('fields'); // Available fields for the report
            $table->json('filters'); // Available filter options
            $table->json('grouping_options')->nullable(); // Available grouping options
            $table->json('chart_options')->nullable(); // Chart configuration options
            $table->json('default_config')->nullable(); // Default report configuration
            $table->boolean('is_system_template')->default(false); // System vs custom templates
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['is_system_template', 'is_active']);
        });

        // Create custom reports table
        Schema::create('custom_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('template_id')->nullable()->constrained('report_templates')->onDelete('set null');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('configuration'); // Report configuration (fields, filters, grouping, etc.)
            $table->json('chart_config')->nullable(); // Chart/visualization configuration
            $table->enum('status', ['draft', 'active', 'archived'])->default('draft');
            $table->boolean('is_public')->default(false); // Visible to other admins in building
            $table->timestamp('last_generated_at')->nullable();
            $table->timestamps();

            $table->index(['building_id', 'status']);
            $table->index(['created_by', 'status']);
            $table->index(['template_id']);
        });

        // Create report schedules table
        Schema::create('report_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('report_id')->constrained('custom_reports')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'quarterly']);
            $table->json('frequency_config'); // Day of week, day of month, etc.
            $table->enum('format', ['pdf', 'excel', 'email_summary']);
            $table->json('recipients'); // Email addresses to send to
            $table->boolean('is_active')->default(true);
            $table->timestamp('next_run_at')->nullable();
            $table->timestamp('last_run_at')->nullable();
            $table->json('last_run_result')->nullable(); // Success/failure info
            $table->timestamps();

            $table->index(['building_id', 'is_active']);
            $table->index(['next_run_at', 'is_active']);
        });

        // Create report generations table (history of generated reports)
        Schema::create('report_generations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('report_id')->constrained('custom_reports')->onDelete('cascade');
            $table->foreignId('generated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('schedule_id')->nullable()->constrained('report_schedules')->onDelete('set null');
            $table->string('format');
            $table->json('parameters'); // Parameters used for generation
            $table->enum('status', ['pending', 'processing', 'completed', 'failed']);
            $table->string('file_name')->nullable();
            $table->string('file_path')->nullable();
            $table->integer('file_size')->nullable();
            $table->integer('record_count')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['building_id', 'status']);
            $table->index(['report_id', 'created_at']);
            $table->index(['expires_at']);
        });

        // Create report analytics table
        Schema::create('report_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('building_id')->constrained()->onDelete('cascade');
            $table->foreignId('report_id')->constrained('custom_reports')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('action', ['viewed', 'generated', 'downloaded', 'shared']);
            $table->json('metadata')->nullable(); // Additional action metadata
            $table->timestamp('created_at');

            $table->index(['building_id', 'created_at']);
            $table->index(['report_id', 'action', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });

        // Add advanced reporting fields to packages table
        Schema::table('packages', function (Blueprint $table) {
            $table->boolean('custom_reports_enabled')->default(false)->after('advanced_reporting');
            $table->integer('max_custom_reports')->nullable()->after('custom_reports_enabled');
            $table->boolean('report_scheduling_enabled')->default(false)->after('max_custom_reports');
            $table->integer('max_scheduled_reports')->nullable()->after('report_scheduling_enabled');
            $table->boolean('advanced_charts_enabled')->default(false)->after('max_scheduled_reports');
            $table->json('available_chart_types')->nullable()->after('advanced_charts_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn([
                'custom_reports_enabled',
                'max_custom_reports',
                'report_scheduling_enabled',
                'max_scheduled_reports',
                'advanced_charts_enabled',
                'available_chart_types'
            ]);
        });

        Schema::dropIfExists('report_analytics');
        Schema::dropIfExists('report_generations');
        Schema::dropIfExists('report_schedules');
        Schema::dropIfExists('custom_reports');
        Schema::dropIfExists('report_templates');
    }
};
