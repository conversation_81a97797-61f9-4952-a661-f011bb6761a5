<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update Standard package to enable exports
        DB::table('packages')
            ->where('slug', 'standard')
            ->update([
                'exports_enabled' => true,
                'exports_per_month' => 50,
                'max_records_per_export' => 5000,
                'export_formats' => json_encode(['pdf', 'excel']),
            ]);

        // Update Pro package to enable exports
        DB::table('packages')
            ->where('slug', 'pro')
            ->update([
                'exports_enabled' => true,
                'exports_per_month' => null, // unlimited
                'max_records_per_export' => 10000,
                'export_formats' => json_encode(['pdf', 'excel']),
            ]);

        // Update Basic package to disable exports (if not already set)
        DB::table('packages')
            ->where('slug', 'basic')
            ->update([
                'exports_enabled' => false,
                'exports_per_month' => 0,
                'max_records_per_export' => 0,
                'export_formats' => null,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert all packages to default export settings
        DB::table('packages')->update([
            'exports_enabled' => false,
            'exports_per_month' => 0,
            'max_records_per_export' => 0,
            'export_formats' => null,
        ]);
    }
};
