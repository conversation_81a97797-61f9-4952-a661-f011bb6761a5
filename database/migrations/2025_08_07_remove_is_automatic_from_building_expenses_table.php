<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('building_expenses', function (Blueprint $table) {
            $table->dropColumn('is_automatic');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('building_expenses', function (Blueprint $table) {
            $table->boolean('is_automatic')->default(false)->after('notes');
        });
    }
};
