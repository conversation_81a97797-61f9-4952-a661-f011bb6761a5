#!/bin/bash

# Deployment script for Amaretna.com
# This script prepares the application for production deployment

set -e

echo "🚀 Starting deployment process for Amaretna.com..."

# Check if we're in production mode
if [ "$NODE_ENV" != "production" ]; then
    echo "⚠️  Setting NODE_ENV to production"
    export NODE_ENV=production
fi

# Step 1: Generate APP_KEY if not set
echo "🔑 Checking APP_KEY..."
if grep -q "APP_KEY=base64:GENERATE_NEW_KEY_FOR_PRODUCTION" .env; then
    echo "🔑 Generating new APP_KEY..."
    php artisan key:generate --force
fi

# Step 2: Clear all caches
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Step 3: Install PHP dependencies
echo "📦 Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

# Step 4: Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm ci

# Step 5: Run database migrations
echo "🗄️  Running database migrations..."
php artisan migrate --force

# Step 6: Build frontend assets
echo "🏗️  Building frontend assets..."
npm run build

# Step 7: Optimize Laravel for production
echo "⚡ Optimizing Laravel for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Step 8: Set proper permissions
echo "🔒 Setting proper permissions..."
chmod -R 755 storage bootstrap/cache
chmod -R 775 storage/logs

# Step 9: Create symbolic link for storage (if needed)
if [ ! -L public/storage ]; then
    echo "🔗 Creating storage symbolic link..."
    php artisan storage:link
fi

echo "✅ Deployment completed successfully!"
echo "🌐 Your application is ready for https://amaretna.com"

# Display important reminders
echo ""
echo "📋 Post-deployment checklist:"
echo "   ✓ Verify .env.production settings"
echo "   ✓ Ensure APP_DEBUG=false in production"
echo "   ✓ Configure proper SSL certificates"
echo "   ✓ Set up proper file permissions on server"
echo "   ✓ Configure web server (Apache/Nginx) properly"
echo "   ✓ Set up automated backups"
echo "   ✓ Configure monitoring and logging"
