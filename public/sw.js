// Service Worker for caching static assets and API responses
const CACHE_VERSION = '2.0.0'; // Increment this to force cache refresh
const CACHE_NAME = `lajnet-amara-v${CACHE_VERSION}`;
const STATIC_CACHE_NAME = `lajnet-amara-static-v${CACHE_VERSION}`;
const API_CACHE_NAME = `lajnet-amara-api-v${CACHE_VERSION}`;

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/build/assets/app.css',
    '/build/assets/app.js',
    // Add other critical assets
];

// API endpoints to cache
const CACHEABLE_API_ENDPOINTS = [
    '/api/packages',
    '/api/expense-types',
    '/api/buildings',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static assets
            caches.open(STATIC_CACHE_NAME).then((cache) => {
                return cache.addAll(STATIC_ASSETS);
            }),
            // Skip waiting to activate immediately
            self.skipWaiting()
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== API_CACHE_NAME && 
                            cacheName !== CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            // Take control of all clients
            self.clients.claim()
        ])
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Check for cache bypass in development
    const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
    const hasCacheBypass = url.searchParams.has('sw-bypass') ||
                          request.headers.get('Cache-Control') === 'no-cache';

    if (isDevelopment && hasCacheBypass) {
        // Bypass service worker completely for development debugging
        console.log('Bypassing service worker for:', request.url);
        return;
    }

    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request));
        return;
    }

    // Handle static assets
    if (isStaticAsset(url.pathname)) {
        event.respondWith(handleStaticAsset(request));
        return;
    }

    // Handle navigation requests (SPA routing)
    if (request.mode === 'navigate') {
        event.respondWith(handleNavigation(request));
        return;
    }
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
    const url = new URL(request.url);
    const isCacheable = CACHEABLE_API_ENDPOINTS.some(endpoint => 
        url.pathname.startsWith(endpoint)
    );

    if (!isCacheable) {
        // For non-cacheable API requests, just fetch from network
        return fetch(request);
    }

    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const cache = await caches.open(API_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        // Network failed, try cache
        console.log('Network failed, trying cache for:', request.url);
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // If no cache, return error response
        return new Response(
            JSON.stringify({ error: 'Network unavailable and no cached data' }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle static assets with stale-while-revalidate strategy
async function handleStaticAsset(request) {
    // Skip unsupported schemes (chrome-extension, etc.)
    const url = new URL(request.url);
    if (url.protocol !== 'http:' && url.protocol !== 'https:') {
        console.warn('Skipping unsupported scheme:', url.protocol, request.url);
        return fetch(request);
    }

    const cache = await caches.open(STATIC_CACHE_NAME);

    // For development or critical assets, use network-first strategy
    const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';
    const isCriticalAsset = url.pathname.includes('/build/assets/app.') ||
                           url.pathname.includes('/build/assets/vendor.') ||
                           url.pathname.includes('manifest.json');

    if (isDevelopment || isCriticalAsset) {
        // Network-first for development and critical assets
        try {
            const networkResponse = await fetch(request);
            if (networkResponse.ok) {
                // Update cache with fresh content
                cache.put(request, networkResponse.clone());
                return networkResponse;
            }
        } catch (error) {
            console.warn('Network failed for critical asset, trying cache:', request.url);
        }

        // Fallback to cache if network fails
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        throw new Error(`Failed to load critical asset: ${request.url}`);
    }

    // Stale-while-revalidate for other static assets
    const cachedResponse = await cache.match(request);

    // Start background fetch to update cache
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.warn('Background fetch failed:', request.url, error);
    });

    if (cachedResponse) {
        // Return cached version immediately, update in background
        fetchPromise; // Don't await, let it run in background
        return cachedResponse;
    }

    // No cached version, wait for network
    try {
        return await fetchPromise;
    } catch (error) {
        console.error('Failed to fetch static asset:', request.url, error);
        throw error;
    }
}

// Handle navigation requests (SPA routing)
async function handleNavigation(request) {
    const url = new URL(request.url);
    const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';

    try {
        // Always try network first for navigation to get fresh content
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            // Cache successful navigation responses for offline use (but not in development)
            if (!isDevelopment) {
                const cache = await caches.open(STATIC_CACHE_NAME);
                cache.put(request, networkResponse.clone());
            }
            return networkResponse;
        }

        throw new Error(`Navigation failed with status: ${networkResponse.status}`);
    } catch (error) {
        console.warn('Navigation network request failed:', request.url, error);

        // If network fails, return cached version for offline support
        if (!isDevelopment) {
            const cache = await caches.open(STATIC_CACHE_NAME);

            // Try exact match first
            let cachedResponse = await cache.match(request);

            // Fallback to root for SPA routing
            if (!cachedResponse) {
                cachedResponse = await cache.match('/');
            }

            if (cachedResponse) {
                console.log('Serving cached navigation response for:', request.url);
                return cachedResponse;
            }
        }

        // Fallback error response
        return new Response(
            '<html><body><h1>Offline</h1><p>Please check your internet connection.</p></body></html>',
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'text/html' }
            }
        );
    }
}

// Helper function to check if a path is a static asset
function isStaticAsset(pathname) {
    return pathname.startsWith('/build/') ||
           pathname.startsWith('/assets/') ||
           pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

// Handle background sync for offline actions (if needed)
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('Background sync triggered');
        // Handle offline actions when connection is restored
    }
});

// Handle push notifications (if implemented)
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body,
            icon: '/build/assets/icon-192x192.png',
            badge: '/build/assets/badge-72x72.png',
            tag: data.tag || 'default',
            requireInteraction: data.requireInteraction || false,
            actions: data.actions || []
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    // Handle notification action
    if (event.action) {
        console.log('Notification action clicked:', event.action);
    }
    
    // Open the app
    event.waitUntil(
        clients.openWindow('/')
    );
});

// Handle messages from the main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker received message:', event.data);

    const { type, payload } = event.data || {};

    switch (type) {
        case 'SKIP_WAITING':
            // Skip waiting and activate immediately
            console.log('Service Worker skipping waiting...');
            self.skipWaiting();
            break;

        case 'GET_VERSION':
            // Send back the current cache version
            if (event.ports && event.ports[0]) {
                event.ports[0].postMessage({
                    type: 'VERSION_RESPONSE',
                    payload: { version: CACHE_NAME }
                });
            }
            break;

        case 'CLEAR_CACHE':
            // Clear all caches
            event.waitUntil(
                caches.keys().then(cacheNames => {
                    return Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }).then(() => {
                    console.log('All caches cleared');
                    if (event.ports && event.ports[0]) {
                        event.ports[0].postMessage({
                            type: 'CACHE_CLEARED',
                            payload: { success: true }
                        });
                    }
                }).catch(error => {
                    console.error('Failed to clear caches:', error);
                    if (event.ports && event.ports[0]) {
                        event.ports[0].postMessage({
                            type: 'CACHE_CLEARED',
                            payload: { success: false, error: error.message }
                        });
                    }
                })
            );
            break;

        default:
            console.log('Unknown message type:', type);
            // Send acknowledgment for unknown messages to prevent port closure
            if (event.ports && event.ports[0]) {
                event.ports[0].postMessage({
                    type: 'MESSAGE_RECEIVED',
                    payload: { originalType: type, acknowledged: true }
                });
            }
    }
});

console.log('Service Worker loaded successfully');
