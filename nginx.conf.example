server {
    listen 80;
    server_name amaretna.com www.amaretna.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name amaretna.com www.amaretna.com;

    # SSL Configuration
    ssl_certificate /path/to/fullchain.pem;
    ssl_certificate_key /path/to/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # HSTS (optional but recommended)
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # Other security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # MIME types for Safari compatibility
    location ~* \.css$ {
        add_header Content-Type "text/css; charset=utf-8" always;
    }
    location ~* \.js$ {
        add_header Content-Type "application/javascript; charset=utf-8" always;
    }
    
    # Root directory
    root /path/to/lajnet-amara/public;
    index index.php;
    
    # Logs
    access_log /var/log/nginx/amaretna.access.log;
    error_log /var/log/nginx/amaretna.error.log;
    
    # Handle CSS files with explicit MIME type (Safari fix)
    location ~* \.css$ {
        add_header Content-Type "text/css; charset=utf-8" always;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle JavaScript files with explicit MIME type
    location ~* \.js$ {
        add_header Content-Type "application/javascript; charset=utf-8" always;
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle other static assets
    location ~* \.(jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Fix for Vite asset paths - redirect /assets/ to /build/assets/
    location ^~ /assets/ {
        rewrite ^/assets/(.*)$ /build/assets/$1 permanent;
    }

    # Handle PHP files
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock; # Adjust to your PHP-FPM socket
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_param PHP_VALUE "upload_max_filesize=64M \n post_max_size=64M";
        fastcgi_read_timeout 300;
    }
    
    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }
    
    # Deny access to .env files
    location ~ \.env {
        deny all;
    }
    
    # Deny access to composer files
    location ~ composer\.(json|lock) {
        deny all;
    }
}
