APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=null
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Amara Building Management"

# Alternative email providers (uncomment to use)
# For SendGrid:
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.sendgrid.net
# MAIL_PORT=587
# MAIL_USERNAME=apikey
# MAIL_PASSWORD=your-sendgrid-api-key

# For Mailgun:
# MAIL_MAILER=mailgun
# MAILGUN_DOMAIN=your-domain.com
# MAILGUN_SECRET=your-mailgun-secret

# For development/testing (logs emails to storage/logs/laravel.log):
# MAIL_MAILER=log

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# File Attachment Configuration
FILE_MAX_SIZE_MB=10
FILE_MAX_FILES_PER_RECORD=5
FILE_MAX_STORAGE_GB=
FILE_STORAGE_DISK=local
FILE_STORAGE_PATH=attachments
FILE_ORGANIZE_BY_DATE=true
FILE_CLEANUP_ORPHANED=true
FILE_CLEANUP_AFTER_DAYS=30
FILE_VIRUS_SCAN=false
FILE_VALIDATE_CONTENTS=true
FILE_MAX_FILENAME_LENGTH=255
FILE_USE_UNIQUE_FILENAMES=true
FILE_GENERATE_THUMBNAILS=true
FILE_THUMBNAIL_QUALITY=80
FILE_COMPRESS_IMAGES=true
FILE_IMAGE_QUALITY=85
