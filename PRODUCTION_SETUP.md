# Production Server Setup Guide

## 🚀 Laravel Application Configuration

### 1. Environment Configuration (.env)

```bash
# Application
APP_NAME="Amaretna Management System"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
APP_TIMEZONE=Asia/Riyadh

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=amaretna_production
DB_USERNAME=amaretna_user
DB_PASSWORD=secure_password_here

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# File Storage
FILESYSTEM_DISK=local
# For production, consider using S3 or other cloud storage
# FILESYSTEM_DISK=s3

# Export System Configuration
EXPORT_STORAGE_DISK=local
EXPORT_STORAGE_PATH=exports
EXPORT_STORAGE_CLEANUP_AFTER_HOURS=24
EXPORT_QUEUE_ENABLED=true
EXPORT_QUEUE_NAME=exports
EXPORT_QUEUE_MAX_TRIES=3
EXPORT_LIMITS_MAX_CONCURRENT_EXPORTS=5
EXPORT_LIMITS_EXPORT_TIMEOUT_MINUTES=30

# PDF Configuration
EXPORT_PDF_DOMPDF_ENABLE_REMOTE=false
EXPORT_PDF_DOMPDF_ENABLE_PHP=false
EXPORT_PDF_DOMPDF_ENABLE_JAVASCRIPT=false
EXPORT_PDF_DOMPDF_ENABLE_HTML5_PARSER=true
EXPORT_PDF_DOMPDF_FONT_CACHE=/tmp/dompdf-fonts
EXPORT_PDF_DOMPDF_TEMP_DIR=/tmp/dompdf-temp

# Notification System
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=true
NOTIFICATION_PUSH_ENABLED=false

# Payment Gateway Configuration
PAYMENT_STRIPE_ENABLED=true
PAYMENT_STRIPE_PUBLIC_KEY=pk_test_...
PAYMENT_STRIPE_SECRET_KEY=sk_test_...
PAYMENT_STRIPE_WEBHOOK_SECRET=whsec_...

PAYMENT_PAYPAL_ENABLED=true
PAYMENT_PAYPAL_CLIENT_ID=your-paypal-client-id
PAYMENT_PAYPAL_CLIENT_SECRET=your-paypal-secret
PAYMENT_PAYPAL_MODE=live

# SMS Configuration
SMS_DRIVER=twilio
SMS_TWILIO_ACCOUNT_SID=your-twilio-sid
SMS_TWILIO_AUTH_TOKEN=your-twilio-token
SMS_TWILIO_FROM_NUMBER=+**********

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=error
LOG_DEPRECATIONS_CHANNEL=null
LOG_DAYS=30

# Security
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### 2. Server Requirements

#### System Requirements:
```bash
# PHP 8.1+ with extensions
php -m | grep -E "(bcmath|ctype|fileinfo|json|mbstring|openssl|pdo|tokenizer|xml|curl|gd|zip|redis)"

# Required PHP Extensions:
# - bcmath
# - ctype
# - fileinfo
# - json
# - mbstring
# - openssl
# - pdo
# - tokenizer
# - xml
# - curl
# - gd (for image processing)
# - zip (for exports)
# - redis (for caching and queues)
```

#### Web Server Configuration:

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/amaretna/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Handle Laravel routes
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to sensitive files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Export file access (if needed)
    location /storage/exports/ {
        alias /var/www/amaretna/storage/app/exports/;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}
```

**Apache Configuration:**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/amaretna/public
    
    <Directory /var/www/amaretna/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Security headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    
    # Export file access
    Alias /exports /var/www/amaretna/storage/app/exports
    <Directory /var/www/amaretna/storage/app/exports>
        Require all granted
        ExpiresActive On
        ExpiresDefault "access plus 1 hour"
    </Directory>
</VirtualHost>
```

### 3. Cron Jobs Setup

#### Add to crontab:
```bash
# Laravel Scheduler (runs every minute)
* * * * * cd /var/www/amaretna && php artisan schedule:run >> /dev/null 2>&1

# Queue Processing (alternative to scheduler)
* * * * * cd /var/www/amaretna && php artisan queue:work --queue=exports --stop-when-empty --max-jobs=10 --max-time=60 >> /dev/null 2>&1
```

### 4. Queue Worker Setup

#### Create systemd service for queue workers:
```bash
# Create service file
sudo nano /etc/systemd/system/amaretna-queue.service
```

**Service Configuration:**
```ini
[Unit]
Description=Amaretna Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/amaretna
ExecStart=/usr/bin/php artisan queue:work --queue=exports,default --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Enable and start the service:**
```bash
sudo systemctl enable amaretna-queue
sudo systemctl start amaretna-queue
sudo systemctl status amaretna-queue
```

### 5. File Permissions

```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/amaretna

# Set proper permissions
sudo find /var/www/amaretna -type f -exec chmod 644 {} \;
sudo find /var/www/amaretna -type d -exec chmod 755 {} \;

# Make storage and bootstrap/cache writable
sudo chmod -R 775 /var/www/amaretna/storage
sudo chmod -R 775 /var/www/amaretna/bootstrap/cache

# Create export directory
sudo mkdir -p /var/www/amaretna/storage/app/exports
sudo chown -R www-data:www-data /var/www/amaretna/storage/app/exports
sudo chmod -R 775 /var/www/amaretna/storage/app/exports
```

### 6. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. Database Setup

```bash
# Create database and user
mysql -u root -p
```

```sql
CREATE DATABASE amaretna_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'amaretna_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON amaretna_production.* TO 'amaretna_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 8. Application Deployment

```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/your-repo/amaretna.git
sudo chown -R www-data:www-data amaretna

# Install dependencies
cd amaretna
composer install --no-dev --optimize-autoloader

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Seed database (if needed)
php artisan db:seed --force

# Clear and cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage links
php artisan storage:link
```

### 9. Monitoring and Logging

#### Log Rotation:
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/amaretna
```

```conf
/var/www/amaretna/storage/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

#### Monitoring Script:
```bash
# Create monitoring script
sudo nano /usr/local/bin/amaretna-monitor.sh
```

```bash
#!/bin/bash

# Check if queue worker is running
if ! systemctl is-active --quiet amaretna-queue; then
    echo "Queue worker is down, restarting..."
    systemctl restart amaretna-queue
    echo "amaretna-queue restarted at $(date)" >> /var/log/amaretna-monitor.log
fi

# Check disk space
DISK_USAGE=$(df /var/www/amaretna/storage | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "High disk usage detected: ${DISK_USAGE}%" >> /var/log/amaretna-monitor.log
fi

# Clean old export files
find /var/www/amaretna/storage/app/exports -name "*.pdf" -mtime +1 -delete
find /var/www/amaretna/storage/app/exports -name "*.xlsx" -mtime +1 -delete
```

```bash
# Make executable and add to crontab
sudo chmod +x /usr/local/bin/amaretna-monitor.sh
# Add to crontab: */5 * * * * /usr/local/bin/amaretna-monitor.sh
```

### 10. Backup Strategy

#### Database Backup:
```bash
# Create backup script
sudo nano /usr/local/bin/amaretna-backup.sh
```

```bash
#!/bin/bash

BACKUP_DIR="/var/backups/amaretna"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u amaretna_user -p'secure_password_here' amaretna_production > $BACKUP_DIR/db_backup_$DATE.sql

# Export files backup
tar -czf $BACKUP_DIR/exports_backup_$DATE.tar.gz /var/www/amaretna/storage/app/exports

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# Make executable and add to crontab
sudo chmod +x /usr/local/bin/amaretna-backup.sh
# Add to crontab: 0 2 * * * /usr/local/bin/amaretna-backup.sh
```

### 11. Security Checklist

- [ ] SSL certificate installed
- [ ] Firewall configured (UFW)
- [ ] Database user has limited privileges
- [ ] File permissions set correctly
- [ ] Sensitive files protected (.env, storage)
- [ ] Regular backups configured
- [ ] Monitoring scripts in place
- [ ] Log rotation configured
- [ ] Queue workers running
- [ ] Cron jobs configured

### 12. Performance Optimization

#### PHP Configuration:
```ini
# /etc/php/8.1/fpm/php.ini
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
```

#### Redis Configuration:
```ini
# /etc/redis/redis.conf
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 13. Troubleshooting Commands

```bash
# Check queue status
php artisan queue:work --queue=exports --once

# Check export status
php artisan exports:status

# View logs
tail -f storage/logs/laravel.log

# Check cron jobs
crontab -l

# Check systemd services
systemctl status amaretna-queue

# Check disk space
df -h

# Check memory usage
free -h

# Check PHP processes
ps aux | grep php
```

This comprehensive setup ensures your Amaretna application runs smoothly in production with proper queue processing, monitoring, and security measures! 🚀 