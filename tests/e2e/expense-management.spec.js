const { test, expect } = require('@playwright/test');

test.describe('Expense Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('navigate to expense management', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);
    
    // Should see expense management page
    await expect(page.locator('h1, h2').filter({ hasText: /expense/i })).toBeVisible();
  });

  test('create new expense', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Click create expense button
    await page.click('text=Create Expense');

    // Fill expense form
    await page.selectOption('select[name="expense_type_id"]', { index: 1 }); // Select first expense type
    await page.selectOption('select[name="user_id"]', { index: 1 }); // Select first user
    await page.selectOption('select[name="month"]', '01'); // January
    await page.fill('input[name="year"]', '2024');
    await page.fill('input[name="amount"]', '150.50');
    await page.fill('textarea[name="notes"]', 'Test expense created via E2E test');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show success message or redirect
    await expect(page.locator('text=success', { timeout: 5000 })).toBeVisible().catch(() => {
      // Alternative: check if we're back on the expenses list
      return expect(page).toHaveURL(/.*expenses/);
    });
  });

  test('view expense list', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Should see expense table or list
    await expect(page.locator('table, .expense-list, [data-testid="expense-list"]')).toBeVisible();

    // Should see some expense data (from seeders)
    await expect(page.locator('text=Maintenance, text=Utilities, text=Cleaning')).toBeVisible();
  });

  test('edit existing expense', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Find and click edit button for first expense
    const editButton = page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
    await editButton.click();

    // Should be on edit form
    await expect(page.locator('h1, h2').filter({ hasText: /edit.*expense/i })).toBeVisible();

    // Modify amount
    await page.fill('input[name="amount"]', '200.75');
    await page.fill('textarea[name="notes"]', 'Updated via E2E test');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show success message or redirect
    await expect(page.locator('text=success', { timeout: 5000 })).toBeVisible().catch(() => {
      return expect(page).toHaveURL(/.*expenses/);
    });
  });

  test('delete expense', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Count initial expenses
    const initialCount = await page.locator('tr, .expense-item').count();

    // Find and click delete button for first expense
    const deleteButton = page.locator('button:has-text("Delete"), a:has-text("Delete")').first();
    await deleteButton.click();

    // Handle confirmation dialog if present
    page.on('dialog', dialog => dialog.accept());

    // Should have one less expense
    await expect(page.locator('tr, .expense-item')).toHaveCount(initialCount - 1, { timeout: 5000 });
  });

  test('filter expenses by month', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Look for month filter
    const monthFilter = page.locator('select[name="month"], input[name="month"]');
    if (await monthFilter.isVisible()) {
      await monthFilter.selectOption('01'); // January
      
      // Should filter results
      await page.waitForTimeout(1000); // Wait for filter to apply
      
      // Verify filtered results
      const expenses = page.locator('tr, .expense-item');
      const count = await expenses.count();
      expect(count).toBeGreaterThan(0);
    }
  });

  test('search expenses', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Look for search input
    const searchInput = page.locator('input[name="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('Maintenance');
      
      // Should filter results
      await page.waitForTimeout(1000); // Wait for search to apply
      
      // Verify search results contain the search term
      await expect(page.locator('text=Maintenance')).toBeVisible();
    }
  });

  test('expense form validation', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Click create expense button
    await page.click('text=Create Expense');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors (HTML5 validation)
    const requiredFields = [
      'select[name="expense_type_id"]',
      'select[name="user_id"]',
      'select[name="month"]',
      'input[name="year"]',
      'input[name="amount"]'
    ];

    for (const field of requiredFields) {
      const element = page.locator(field);
      if (await element.isVisible()) {
        await expect(element).toHaveAttribute('required');
      }
    }
  });

  test('expense amount validation', async ({ page }) => {
    // Navigate to expense management
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Click create expense button
    await page.click('text=Create Expense');

    // Fill form with invalid amount
    await page.selectOption('select[name="expense_type_id"]', { index: 1 });
    await page.selectOption('select[name="user_id"]', { index: 1 });
    await page.selectOption('select[name="month"]', '01');
    await page.fill('input[name="year"]', '2024');
    await page.fill('input[name="amount"]', '-50'); // Negative amount

    // Submit form
    await page.click('button[type="submit"]');

    // Should show validation error or prevent submission
    const amountInput = page.locator('input[name="amount"]');
    await expect(amountInput).toHaveAttribute('min', '0');
  });
});
