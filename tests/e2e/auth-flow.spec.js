const { test, expect } = require('@playwright/test');

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start fresh for each test
    await page.goto('/');
  });

  test('complete signup and login flow', async ({ page }) => {
    // Generate unique test data
    const timestamp = Date.now();
    const testEmail = `admin${timestamp}@test.com`;
    const testPassword = 'password123';
    const buildingName = `Test Building ${timestamp}`;

    // Navigate to signup page
    await page.click('text=Sign Up');
    await expect(page).toHaveURL(/.*signup/);

    // Fill signup form
    await page.fill('input[name="name"]', 'Test Admin');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.fill('input[name="password_confirmation"]', testPassword);
    await page.fill('input[name="building_name"]', buildingName);
    await page.fill('input[name="building_address"]', '123 Test Street');
    await page.fill('input[name="building_city"]', 'Test City');
    await page.fill('input[name="building_country"]', 'Test Country');
    await page.fill('input[name="building_postal_code"]', '12345');

    // Submit signup form
    await page.click('button[type="submit"]');

    // Should redirect to success page or dashboard
    await expect(page).toHaveURL(/.*success|.*dashboard/);

    // If redirected to success page, navigate to login
    if (page.url().includes('success')) {
      await page.click('text=Login');
    }

    // If not already logged in, perform login
    if (page.url().includes('login')) {
      await page.fill('input[name="email"]', testEmail);
      await page.fill('input[name="password"]', testPassword);
      await page.click('button[type="submit"]');
    }

    // Should be on dashboard
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test('login with existing user', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Login');
    await expect(page).toHaveURL(/.*login/);

    // Try to login with seeded admin user
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test('login validation errors', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Login');
    await expect(page).toHaveURL(/.*login/);

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors (HTML5 validation)
    const emailInput = page.locator('input[name="email"]');
    await expect(emailInput).toHaveAttribute('required');

    // Try with invalid credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');

    // Should show error message (wait for it to appear)
    await page.waitForSelector('text=Invalid credentials', { timeout: 5000 }).catch(() => {
      // If exact text not found, look for any error message
      return page.waitForSelector('[class*="error"], [class*="alert"]', { timeout: 5000 });
    });
  });

  test('logout functionality', async ({ page }) => {
    // Login first
    await page.click('text=Login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // Should be on dashboard
    await expect(page).toHaveURL(/.*dashboard/);

    // Find and click logout button/link
    await page.click('text=Logout');

    // Should redirect to home page
    await expect(page).toHaveURL('/');
    await expect(page.locator('text=Login')).toBeVisible();
  });

  test('protected route access', async ({ page }) => {
    // Try to access dashboard without login
    await page.goto('/dashboard');

    // Should redirect to login page
    await expect(page).toHaveURL(/.*login/);
  });
});
