const { test, expect } = require('@playwright/test');

test.describe('Complete Application Flow', () => {
  test('complete user journey: signup → login → manage expenses → manage incomes', async ({ page }) => {
    // Generate unique test data
    const timestamp = Date.now();
    const testEmail = `admin${timestamp}@test.com`;
    const testPassword = 'password123';
    const buildingName = `Test Building ${timestamp}`;

    // Step 1: Signup
    await page.goto('/');
    await page.click('text=Sign Up');
    await expect(page).toHaveURL(/.*signup/);

    // Fill signup form
    await page.fill('input[name="name"]', 'Test Admin');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.fill('input[name="password_confirmation"]', testPassword);
    await page.fill('input[name="building_name"]', buildingName);
    await page.fill('input[name="building_address"]', '123 Test Street');
    await page.fill('input[name="building_city"]', 'Test City');
    await page.fill('input[name="building_country"]', 'Test Country');
    await page.fill('input[name="building_postal_code"]', '12345');

    // Submit signup
    await page.click('button[type="submit"]');

    // Handle success page or direct login
    if (page.url().includes('success')) {
      await page.click('text=Login');
    }

    // Step 2: Login (if not already logged in)
    if (page.url().includes('login')) {
      await page.fill('input[name="email"]', testEmail);
      await page.fill('input[name="password"]', testPassword);
      await page.click('button[type="submit"]');
    }

    // Should be on dashboard
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('text=Dashboard')).toBeVisible();

    // Step 3: Create a neighbor user first (needed for expenses/incomes)
    await page.click('text=Users');
    await page.click('text=Create User');

    // Fill neighbor form
    await page.fill('input[name="name"]', 'Test Neighbor');
    await page.fill('input[name="email"]', `neighbor${timestamp}@test.com`);
    await page.fill('input[name="password"]', 'password123');
    await page.fill('input[name="password_confirmation"]', 'password123');
    await page.selectOption('select[name="role"]', 'neighbor');
    await page.fill('input[name="apartment_number"]', 'A101');
    await page.fill('input[name="phone_number"]', '+1234567890');

    // Submit neighbor creation
    await page.click('button[type="submit"]');

    // Step 4: Create expense type
    await page.click('text=Expense Types');
    await page.click('text=Create Expense Type');

    await page.fill('input[name="name"]', 'Test Maintenance');
    await page.fill('textarea[name="description"]', 'Test maintenance expenses');
    await page.click('button[type="submit"]');

    // Step 5: Manage Expenses
    await page.click('text=Expenses');
    await expect(page).toHaveURL(/.*expenses/);

    // Create new expense
    await page.click('text=Create Expense');

    // Fill expense form
    await page.selectOption('select[name="expense_type_id"]', { index: 1 }); // Select first expense type
    await page.selectOption('select[name="user_id"]', { index: 1 }); // Select first user
    await page.selectOption('select[name="month"]', '01'); // January
    await page.fill('input[name="year"]', '2024');
    await page.fill('input[name="amount"]', '150.50');
    await page.fill('textarea[name="notes"]', 'Test expense from complete flow');

    // Submit expense
    await page.click('button[type="submit"]');

    // Should return to expenses list
    await expect(page).toHaveURL(/.*expenses/);

    // Verify expense was created
    await expect(page.locator('text=150.50')).toBeVisible();
    await expect(page.locator('text=Test expense from complete flow')).toBeVisible();

    // Step 6: Manage Incomes
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Create new income
    await page.click('text=Create Income');

    // Fill income form
    await page.selectOption('select[name="user_id"]', { index: 1 }); // Select first user
    await page.fill('input[name="amount"]', '250.75');
    await page.fill('input[name="payment_date"]', '2024-01-15');
    await page.selectOption('select[name="payment_method"]', 'bank_transfer');
    await page.fill('textarea[name="notes"]', 'Test income from complete flow');

    // Submit income
    await page.click('button[type="submit"]');

    // Should return to incomes list
    await expect(page).toHaveURL(/.*incomes/);

    // Verify income was created
    await expect(page.locator('text=250.75')).toBeVisible();
    await expect(page.locator('text=Test income from complete flow')).toBeVisible();

    // Step 7: View Dashboard Summary
    await page.click('text=Dashboard');
    await expect(page).toHaveURL(/.*dashboard/);

    // Should see summary information
    await expect(page.locator('text=Total Expenses, text=Total Income')).toBeVisible();

    // Step 8: Edit the created expense
    await page.click('text=Expenses');
    const editExpenseButton = page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
    await editExpenseButton.click();

    // Modify expense
    await page.fill('input[name="amount"]', '175.25');
    await page.fill('textarea[name="notes"]', 'Updated test expense from complete flow');
    await page.click('button[type="submit"]');

    // Verify update
    await expect(page.locator('text=175.25')).toBeVisible();
    await expect(page.locator('text=Updated test expense from complete flow')).toBeVisible();

    // Step 9: Edit the created income
    await page.click('text=Incomes');
    const editIncomeButton = page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
    await editIncomeButton.click();

    // Modify income
    await page.fill('input[name="amount"]', '275.00');
    await page.fill('textarea[name="notes"]', 'Updated test income from complete flow');
    await page.click('button[type="submit"]');

    // Verify update
    await expect(page.locator('text=275.00')).toBeVisible();
    await expect(page.locator('text=Updated test income from complete flow')).toBeVisible();

    // Step 10: Test search functionality
    await page.click('text=Expenses');
    const searchInput = page.locator('input[name="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('Updated test expense');
      await page.waitForTimeout(1000);
      await expect(page.locator('text=Updated test expense from complete flow')).toBeVisible();
    }

    // Step 11: Test filtering
    await page.click('text=Incomes');
    const methodFilter = page.locator('select[name="payment_method"]');
    if (await methodFilter.isVisible()) {
      await methodFilter.selectOption('bank_transfer');
      await page.waitForTimeout(1000);
      await expect(page.locator('text=bank_transfer')).toBeVisible();
    }

    // Step 12: Logout
    await page.click('text=Logout');
    await expect(page).toHaveURL('/');
    await expect(page.locator('text=Login')).toBeVisible();

    // Step 13: Login again to verify persistence
    await page.click('text=Login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');

    // Should be back on dashboard
    await expect(page).toHaveURL(/.*dashboard/);

    // Verify data persistence
    await page.click('text=Expenses');
    await expect(page.locator('text=175.25')).toBeVisible();

    await page.click('text=Incomes');
    await expect(page.locator('text=275.00')).toBeVisible();
  });

  test('admin workflow with multiple users and transactions', async ({ page }) => {
    // Login as existing admin
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    await expect(page).toHaveURL(/.*dashboard/);

    // Create multiple expenses for different users
    await page.click('text=Expenses');

    for (let i = 1; i <= 3; i++) {
      await page.click('text=Create Expense');
      
      await page.selectOption('select[name="expense_type_id"]', { index: 1 });
      await page.selectOption('select[name="user_id"]', { index: i });
      await page.selectOption('select[name="month"]', '01');
      await page.fill('input[name="year"]', '2024');
      await page.fill('input[name="amount"]', `${100 + i * 25}.00`);
      await page.fill('textarea[name="notes"]', `Bulk expense ${i}`);
      
      await page.click('button[type="submit"]');
      await expect(page).toHaveURL(/.*expenses/);
    }

    // Create multiple incomes
    await page.click('text=Incomes');

    for (let i = 1; i <= 3; i++) {
      await page.click('text=Create Income');
      
      await page.selectOption('select[name="user_id"]', { index: i });
      await page.fill('input[name="amount"]', `${200 + i * 50}.00`);
      await page.fill('input[name="payment_date"]', '2024-01-15');
      await page.selectOption('select[name="payment_method"]', 'bank_transfer');
      await page.fill('textarea[name="notes"]', `Bulk income ${i}`);
      
      await page.click('button[type="submit"]');
      await expect(page).toHaveURL(/.*incomes/);
    }

    // Verify all transactions were created
    await page.click('text=Expenses');
    await expect(page.locator('text=Bulk expense 1')).toBeVisible();
    await expect(page.locator('text=Bulk expense 2')).toBeVisible();
    await expect(page.locator('text=Bulk expense 3')).toBeVisible();

    await page.click('text=Incomes');
    await expect(page.locator('text=Bulk income 1')).toBeVisible();
    await expect(page.locator('text=Bulk income 2')).toBeVisible();
    await expect(page.locator('text=Bulk income 3')).toBeVisible();

    // Check dashboard reflects the changes
    await page.click('text=Dashboard');
    await expect(page.locator('text=Total Expenses, text=Total Income')).toBeVisible();
  });
});
