const { test, expect } = require('@playwright/test');

test.describe('Income Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('navigate to income management', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);
    
    // Should see income management page
    await expect(page.locator('h1, h2').filter({ hasText: /income/i })).toBeVisible();
  });

  test('create new income', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Click create income button
    await page.click('text=Create Income');

    // Fill income form
    await page.selectOption('select[name="user_id"]', { index: 1 }); // Select first user
    await page.fill('input[name="amount"]', '250.75');
    await page.fill('input[name="payment_date"]', '2024-01-15');
    await page.selectOption('select[name="payment_method"]', 'bank_transfer');
    await page.fill('textarea[name="notes"]', 'Test income created via E2E test');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show success message or redirect
    await expect(page.locator('text=success', { timeout: 5000 })).toBeVisible().catch(() => {
      // Alternative: check if we're back on the incomes list
      return expect(page).toHaveURL(/.*incomes/);
    });
  });

  test('view income list', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Should see income table or list
    await expect(page.locator('table, .income-list, [data-testid="income-list"]')).toBeVisible();

    // Should see some income data (from seeders)
    await expect(page.locator('text=bank_transfer, text=cash, text=check')).toBeVisible();
  });

  test('edit existing income', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Find and click edit button for first income
    const editButton = page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
    await editButton.click();

    // Should be on edit form
    await expect(page.locator('h1, h2').filter({ hasText: /edit.*income/i })).toBeVisible();

    // Modify amount
    await page.fill('input[name="amount"]', '300.00');
    await page.fill('textarea[name="notes"]', 'Updated via E2E test');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show success message or redirect
    await expect(page.locator('text=success', { timeout: 5000 })).toBeVisible().catch(() => {
      return expect(page).toHaveURL(/.*incomes/);
    });
  });

  test('delete income', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Count initial incomes
    const initialCount = await page.locator('tr, .income-item').count();

    // Find and click delete button for first income
    const deleteButton = page.locator('button:has-text("Delete"), a:has-text("Delete")').first();
    await deleteButton.click();

    // Handle confirmation dialog if present
    page.on('dialog', dialog => dialog.accept());

    // Should have one less income
    await expect(page.locator('tr, .income-item')).toHaveCount(initialCount - 1, { timeout: 5000 });
  });

  test('filter incomes by payment method', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Look for payment method filter
    const methodFilter = page.locator('select[name="payment_method"]');
    if (await methodFilter.isVisible()) {
      await methodFilter.selectOption('bank_transfer');
      
      // Should filter results
      await page.waitForTimeout(1000); // Wait for filter to apply
      
      // Verify filtered results
      const incomes = page.locator('tr, .income-item');
      const count = await incomes.count();
      expect(count).toBeGreaterThan(0);
    }
  });

  test('filter incomes by date range', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Look for date filters
    const startDateFilter = page.locator('input[name="start_date"], input[name="date_from"]');
    const endDateFilter = page.locator('input[name="end_date"], input[name="date_to"]');
    
    if (await startDateFilter.isVisible() && await endDateFilter.isVisible()) {
      await startDateFilter.fill('2024-01-01');
      await endDateFilter.fill('2024-01-31');
      
      // Should filter results
      await page.waitForTimeout(1000); // Wait for filter to apply
      
      // Verify filtered results
      const incomes = page.locator('tr, .income-item');
      const count = await incomes.count();
      expect(count).toBeGreaterThanOrEqual(0);
    }
  });

  test('search incomes', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Look for search input
    const searchInput = page.locator('input[name="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('Monthly payment');
      
      // Should filter results
      await page.waitForTimeout(1000); // Wait for search to apply
      
      // Verify search results contain the search term
      await expect(page.locator('text=Monthly payment')).toBeVisible();
    }
  });

  test('income form validation', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Click create income button
    await page.click('text=Create Income');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors (HTML5 validation)
    const requiredFields = [
      'select[name="user_id"]',
      'input[name="amount"]',
      'input[name="payment_date"]',
      'select[name="payment_method"]'
    ];

    for (const field of requiredFields) {
      const element = page.locator(field);
      if (await element.isVisible()) {
        await expect(element).toHaveAttribute('required');
      }
    }
  });

  test('income amount validation', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Click create income button
    await page.click('text=Create Income');

    // Fill form with invalid amount
    await page.selectOption('select[name="user_id"]', { index: 1 });
    await page.fill('input[name="amount"]', '-100'); // Negative amount
    await page.fill('input[name="payment_date"]', '2024-01-15');
    await page.selectOption('select[name="payment_method"]', 'cash');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show validation error or prevent submission
    const amountInput = page.locator('input[name="amount"]');
    await expect(amountInput).toHaveAttribute('min', '0');
  });

  test('payment method options', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Click create income button
    await page.click('text=Create Income');

    // Check payment method options
    const paymentMethodSelect = page.locator('select[name="payment_method"]');
    await expect(paymentMethodSelect).toBeVisible();

    // Should have the expected payment methods
    const options = await paymentMethodSelect.locator('option').allTextContents();
    expect(options).toContain('Cash');
    expect(options).toContain('Bank Transfer');
    expect(options).toContain('Check');
  });

  test('income date validation', async ({ page }) => {
    // Navigate to income management
    await page.click('text=Incomes');
    await expect(page).toHaveURL(/.*incomes/);

    // Click create income button
    await page.click('text=Create Income');

    // Check date input type
    const dateInput = page.locator('input[name="payment_date"]');
    await expect(dateInput).toHaveAttribute('type', 'date');

    // Fill with valid date
    await dateInput.fill('2024-01-15');
    const value = await dateInput.inputValue();
    expect(value).toBe('2024-01-15');
  });
});
