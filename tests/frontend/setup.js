import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock axios
const mockAxios = {
  get: vi.fn(() => Promise.resolve({ data: {} })),
  post: vi.fn(() => Promise.resolve({ data: {} })),
  put: vi.fn(() => Promise.resolve({ data: {} })),
  delete: vi.fn(() => Promise.resolve({ data: {} })),
  patch: vi.fn(() => Promise.resolve({ data: {} })),
}

// Global mocks
global.$axios = mockAxios
global.$t = vi.fn((key) => key) // Mock translation function
global.$isRTL = vi.fn(() => false) // Mock RTL function

// Configure Vue Test Utils globally
config.global.mocks = {
  $axios: mockAxios,
  $t: (key) => key,
  $isRTL: () => false,
}

config.global.stubs = {
  'router-link': true,
  'router-view': true,
}

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock window.location
delete window.location
window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  pathname: '/',
  search: '',
  hash: '',
  reload: vi.fn(),
  assign: vi.fn(),
  replace: vi.fn(),
}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Mock Date.now for consistent testing
vi.setSystemTime(new Date('2024-01-01T00:00:00.000Z'))
