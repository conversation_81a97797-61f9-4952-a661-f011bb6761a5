import { mount } from '@vue/test-utils'
import { vi } from 'vitest'

/**
 * Create a mock Vue component with common properties and methods
 */
export function createMockComponent(options = {}) {
  return {
    template: '<div>Mock Component</div>',
    ...options
  }
}

/**
 * Create mock axios instance for testing
 */
export function createMockAxios() {
  return {
    get: vi.fn(() => Promise.resolve({ data: {} })),
    post: vi.fn(() => Promise.resolve({ data: {} })),
    put: vi.fn(() => Promise.resolve({ data: {} })),
    delete: vi.fn(() => Promise.resolve({ data: {} })),
    patch: vi.fn(() => Promise.resolve({ data: {} })),
  }
}

/**
 * Create a wrapper with common global properties
 */
export function mountWithGlobals(component, options = {}) {
  const mockAxios = createMockAxios()

  const defaultGlobal = {
    mocks: {
      $axios: mockAxios,
      $t: (key) => key,
      $isRTL: () => false,
      $emit: vi.fn(),
      $router: {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
      },
      $route: {
        params: {},
        query: {},
        path: '/',
        name: 'test',
      },
    },
    stubs: {
      'router-link': true,
      'router-view': true,
    },
  }

  // Merge global options properly
  const mergedGlobal = {
    ...defaultGlobal,
    ...options.global,
    mocks: {
      ...defaultGlobal.mocks,
      ...options.global?.mocks,
    },
    stubs: {
      ...defaultGlobal.stubs,
      ...options.global?.stubs,
    },
  }

  return mount(component, {
    global: mergedGlobal,
    ...options,
  })
}

/**
 * Mock user data for testing
 */
export function createMockUser(overrides = {}) {
  return {
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin',
    building_id: 1,
    apartment_number: 'A101',
    phone_number: '+1234567890',
    ...overrides,
  }
}

/**
 * Mock building data for testing
 */
export function createMockBuilding(overrides = {}) {
  return {
    id: 1,
    name: 'Test Building',
    address: '123 Test Street',
    city: 'Test City',
    country: 'Test Country',
    postal_code: '12345',
    monthly_fee: 150.00,
    currency: 'USD',
    ...overrides,
  }
}

/**
 * Mock expense data for testing
 */
export function createMockExpense(overrides = {}) {
  return {
    id: 1,
    expense_type_id: 1,
    user_id: 1,
    building_id: 1,
    amount: 100.50,
    due_date: '2024-01-15',
    month: '01',
    year: 2024,
    notes: 'Test expense',
    is_automatic: false,
    is_archived: false,
    expense_type: {
      id: 1,
      name: 'Maintenance',
      description: 'Building maintenance',
    },
    user: {
      id: 1,
      name: 'Test User',
      apartment_number: 'A101',
    },
    ...overrides,
  }
}

/**
 * Mock income data for testing
 */
export function createMockIncome(overrides = {}) {
  return {
    id: 1,
    user_id: 1,
    building_id: 1,
    amount: 150.00,
    payment_date: '2024-01-15',
    payment_method: 'bank_transfer',
    notes: 'Test income',
    is_archived: false,
    user: {
      id: 1,
      name: 'Test User',
      apartment_number: 'A101',
    },
    ...overrides,
  }
}

/**
 * Mock expense type data for testing
 */
export function createMockExpenseType(overrides = {}) {
  return {
    id: 1,
    name: 'Maintenance',
    description: 'Building maintenance expenses',
    ...overrides,
  }
}

/**
 * Wait for next tick and DOM updates
 */
export async function flushPromises() {
  return new Promise((resolve) => setTimeout(resolve, 0))
}

/**
 * Simulate user input in a form field
 */
export async function setInputValue(wrapper, selector, value) {
  const input = wrapper.find(selector)
  await input.setValue(value)
  await input.trigger('input')
  await flushPromises()
}

/**
 * Simulate form submission
 */
export async function submitForm(wrapper, selector = 'form') {
  const form = wrapper.find(selector)
  await form.trigger('submit.prevent')
  await flushPromises()
}
