import { describe, it, expect } from 'vitest'

describe('Frontend Test Setup', () => {
  it('should have global mocks available', () => {
    expect(global.$axios).toBeDefined()
    expect(global.$t).toBeDefined()
    expect(global.$isRTL).toBeDefined()
    expect(global.localStorage).toBeDefined()
  })

  it('should mock translation function', () => {
    expect(global.$t('test_key')).toBe('test_key')
  })

  it('should mock RTL function', () => {
    expect(global.$isRTL()).toBe(false)
  })

  it('should mock localStorage', () => {
    global.localStorage.setItem('test', 'value')
    expect(global.localStorage.setItem).toHaveBeenCalledWith('test', 'value')
  })

  it('should mock axios methods', () => {
    expect(global.$axios.get).toBeDefined()
    expect(global.$axios.post).toBeDefined()
    expect(global.$axios.put).toBeDefined()
    expect(global.$axios.delete).toBeDefined()
  })
})
