import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mountWithGlobals, setInputValue, submitForm, flushPromises } from '../helpers/testUtils.js'
import Login from '@/views/Login.vue'

describe('Login Component', () => {
  let wrapper
  let mockAxios

  beforeEach(() => {
    mockAxios = {
      post: vi.fn(() => Promise.resolve({
        data: {
          user: { id: 1, name: 'Test User', email: '<EMAIL>' },
          token: 'test-token'
        }
      }))
    }

    wrapper = mountWithGlobals(Login, {
      global: {
        mocks: {
          $axios: mockAxios,
          $router: {
            push: vi.fn()
          }
        }
      }
    })
  })

  it('renders login form correctly', () => {
    expect(wrapper.find('form').exists()).toBe(true)
    expect(wrapper.find('#email').exists()).toBe(true)
    expect(wrapper.find('#password').exists()).toBe(true)
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('displays welcome message', () => {
    expect(wrapper.text()).toContain('welcome_back')
    expect(wrapper.text()).toContain('sign_in_to_your_account')
  })

  it('shows password toggle functionality', async () => {
    const passwordInput = wrapper.find('#password')
    const toggleButton = wrapper.find('button[type="button"]')

    // Initially password should be hidden
    expect(passwordInput.attributes('type')).toBe('password')

    // Click toggle button
    await toggleButton.trigger('click')
    expect(passwordInput.attributes('type')).toBe('text')

    // Click again to hide
    await toggleButton.trigger('click')
    expect(passwordInput.attributes('type')).toBe('password')
  })

  it('updates form data when user types', async () => {
    await setInputValue(wrapper, '#email', '<EMAIL>')
    await setInputValue(wrapper, '#password', 'password123')

    expect(wrapper.vm.email).toBe('<EMAIL>')
    expect(wrapper.vm.password).toBe('password123')
  })

  it('handles remember me checkbox', async () => {
    const checkbox = wrapper.find('#remember-me')
    
    expect(wrapper.vm.rememberMe).toBe(false)
    
    await checkbox.setChecked(true)
    expect(wrapper.vm.rememberMe).toBe(true)
  })

  it('submits form with correct data', async () => {
    await setInputValue(wrapper, '#email', '<EMAIL>')
    await setInputValue(wrapper, '#password', 'password123')
    
    const checkbox = wrapper.find('#remember-me')
    await checkbox.setChecked(true)

    await submitForm(wrapper)

    expect(mockAxios.post).toHaveBeenCalledWith('/login', {
      email: '<EMAIL>',
      password: 'password123',
      remember: true
    })
  })

  it('handles successful login', async () => {
    const mockRouter = wrapper.vm.$router
    
    await setInputValue(wrapper, '#email', '<EMAIL>')
    await setInputValue(wrapper, '#password', 'password123')
    
    await submitForm(wrapper)
    await flushPromises()

    // Should store user data and token
    expect(localStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify({
      id: 1, name: 'Test User', email: '<EMAIL>'
    }))
    expect(localStorage.setItem).toHaveBeenCalledWith('token', 'test-token')
    
    // Should redirect to dashboard
    expect(mockRouter.push).toHaveBeenCalledWith('/dashboard')
  })

  it('handles login error', async () => {
    mockAxios.post.mockRejectedValueOnce({
      response: {
        data: {
          message: 'Invalid credentials'
        }
      }
    })

    await setInputValue(wrapper, '#email', '<EMAIL>')
    await setInputValue(wrapper, '#password', 'wrongpassword')
    
    await submitForm(wrapper)
    await flushPromises()

    expect(wrapper.vm.error).toBe('Invalid credentials')
    expect(wrapper.text()).toContain('Invalid credentials')
  })

  it('shows loading state during login', async () => {
    // Mock a delayed response
    mockAxios.post.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        data: { user: {}, token: 'token' }
      }), 100))
    )

    await setInputValue(wrapper, '#email', '<EMAIL>')
    await setInputValue(wrapper, '#password', 'password123')
    
    const submitButton = wrapper.find('button[type="submit"]')
    await submitForm(wrapper)

    expect(wrapper.vm.loading).toBe(true)
    expect(submitButton.attributes('disabled')).toBeDefined()
  })

  it('validates required fields', async () => {
    const form = wrapper.find('form')
    const emailInput = wrapper.find('#email')
    const passwordInput = wrapper.find('#password')

    expect(emailInput.attributes('required')).toBeDefined()
    expect(passwordInput.attributes('required')).toBeDefined()
    expect(emailInput.attributes('type')).toBe('email')
  })

  it('has proper accessibility attributes', () => {
    const emailInput = wrapper.find('#email')
    const passwordInput = wrapper.find('#password')
    const emailLabel = wrapper.find('label[for="email"]')
    const passwordLabel = wrapper.find('label[for="password"]')

    expect(emailLabel.exists()).toBe(true)
    expect(passwordLabel.exists()).toBe(true)
    expect(emailInput.attributes('autocomplete')).toBe('email')
    expect(passwordInput.attributes('autocomplete')).toBe('current-password')
  })
})
