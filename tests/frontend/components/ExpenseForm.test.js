import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mountWithGlobals, setInputValue, submitForm, flushPromises, createMockExpense, createMockExpenseType, createMockUser } from '../helpers/testUtils.js'
import ExpenseForm from '@/components/ExpenseForm.vue'

describe('ExpenseForm Component', () => {
  let wrapper
  let mockAxios

  const mockExpenseTypes = [
    createMockExpenseType({ id: 1, name: 'Maintenance' }),
    createMockExpenseType({ id: 2, name: 'Utilities' }),
  ]

  const mockNeighbors = [
    createMockUser({ id: 1, name: '<PERSON>', apartment_number: 'A101' }),
    createMockUser({ id: 2, name: '<PERSON>', apartment_number: 'A102' }),
  ]

  beforeEach(() => {
    mockAxios = {
      get: vi.fn((url) => {
        if (url === '/expense-types') {
          return Promise.resolve({ data: mockExpenseTypes })
        }
        if (url === '/admin/users') {
          return Promise.resolve({ data: mockNeighbors })
        }
        return Promise.resolve({ data: {} })
      }),
      post: vi.fn(() => Promise.resolve({ data: createMockExpense() })),
      put: vi.fn(() => Promise.resolve({ data: createMockExpense() })),
    }

    wrapper = mountWithGlobals(ExpenseForm, {
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })
  })

  it('renders form fields correctly', () => {
    expect(wrapper.find('#expense_type').exists()).toBe(true)
    expect(wrapper.find('#user').exists()).toBe(true)
    expect(wrapper.find('#month').exists()).toBe(true)
    expect(wrapper.find('#year').exists()).toBe(true)
    expect(wrapper.find('#amount').exists()).toBe(true)
    expect(wrapper.find('#notes').exists()).toBe(true)
  })

  it('loads expense types and neighbors on mount', async () => {
    await flushPromises()

    expect(mockAxios.get).toHaveBeenCalledWith('/expense-types')
    expect(mockAxios.get).toHaveBeenCalledWith('/admin/users')
    expect(wrapper.vm.expenseTypes).toEqual(mockExpenseTypes)
    expect(wrapper.vm.neighbors).toEqual(mockNeighbors)
  })

  it('populates expense type options', async () => {
    await flushPromises()

    const expenseTypeSelect = wrapper.find('#expense_type')
    const options = expenseTypeSelect.findAll('option')
    
    expect(options).toHaveLength(3) // Including default option
    expect(options[1].text()).toBe('Maintenance')
    expect(options[2].text()).toBe('Utilities')
  })

  it('populates neighbor options', async () => {
    await flushPromises()

    const userSelect = wrapper.find('#user')
    const options = userSelect.findAll('option')
    
    expect(options).toHaveLength(3) // Including default option
    expect(options[1].text()).toContain('John Doe')
    expect(options[1].text()).toContain('A101')
    expect(options[2].text()).toContain('Jane Smith')
    expect(options[2].text()).toContain('A102')
  })

  it('populates month options', () => {
    const monthSelect = wrapper.find('#month')
    const options = monthSelect.findAll('option')
    
    expect(options).toHaveLength(13) // Including default option
    expect(options[1].attributes('value')).toBe('01')
    expect(options[12].attributes('value')).toBe('12')
  })

  it('sets default year to current year', () => {
    const yearInput = wrapper.find('#year')
    expect(yearInput.element.value).toBe('2024') // Based on mocked date
  })

  it('updates form data when user inputs', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '01')
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')
    await setInputValue(wrapper, '#notes', 'Test expense')

    expect(wrapper.vm.formData.expense_type_id).toBe('1')
    expect(wrapper.vm.formData.user_id).toBe('1')
    expect(wrapper.vm.formData.month).toBe('01')
    expect(wrapper.vm.formData.year).toBe('2024')
    expect(wrapper.vm.formData.amount).toBe('150.50')
    expect(wrapper.vm.formData.notes).toBe('Test expense')
  })

  it('submits form with correct data for new expense', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '01')
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')
    await setInputValue(wrapper, '#notes', 'Test expense')

    await submitForm(wrapper)

    expect(mockAxios.post).toHaveBeenCalledWith('/expenses', {
      expense_type_id: '1',
      user_id: '1',
      month: '01',
      year: 2024,
      amount: '150.50',
      notes: 'Test expense',
      is_automatic: false
    })
  })

  it('emits success event on successful submission', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '01')
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')

    await submitForm(wrapper)
    await flushPromises()

    expect(wrapper.emitted('success')).toBeTruthy()
    expect(wrapper.emitted('success')[0][0]).toEqual(createMockExpense())
  })

  it('handles form submission error', async () => {
    mockAxios.post.mockRejectedValueOnce({
      response: {
        data: {
          message: 'Validation failed'
        }
      }
    })

    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '01')
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')

    await submitForm(wrapper)
    await flushPromises()

    expect(wrapper.emitted('error')).toBeTruthy()
    expect(wrapper.emitted('error')[0][0]).toBe('Validation failed')
  })

  it('shows loading state during submission', async () => {
    mockAxios.post.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: {} }), 100))
    )

    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '01')
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')

    const submitButton = wrapper.find('button[type="submit"]')
    await submitForm(wrapper)

    expect(wrapper.vm.processing).toBe(true)
    expect(submitButton.attributes('disabled')).toBeDefined()
  })

  it('validates required fields', () => {
    const requiredFields = ['#expense_type', '#user', '#month', '#year', '#amount']
    
    requiredFields.forEach(selector => {
      const field = wrapper.find(selector)
      expect(field.attributes('required')).toBeDefined()
    })
  })

  it('handles edit mode correctly', async () => {
    const mockExpense = createMockExpense({
      id: 1,
      expense_type_id: 1,
      user_id: 1,
      month: '01',
      year: 2024,
      amount: 150.50,
      notes: 'Test expense'
    })

    wrapper = mountWithGlobals(ExpenseForm, {
      props: {
        expense: mockExpense,
        isEdit: true
      },
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })

    await flushPromises()

    expect(wrapper.vm.formData.expense_type_id).toBe(1)
    expect(wrapper.vm.formData.user_id).toBe(1)
    expect(wrapper.vm.formData.month).toBe('01')
    expect(wrapper.vm.formData.year).toBe(2024)
    expect(wrapper.vm.formData.amount).toBe(150.50)
    expect(wrapper.vm.formData.notes).toBe('Test expense')
  })

  it('emits cancel event when cancel button is clicked', async () => {
    const cancelButton = wrapper.find('button[type="button"]')
    await cancelButton.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('formats month as 2-digit string on submission', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#expense_type', '1')
    await setInputValue(wrapper, '#user', '1')
    await setInputValue(wrapper, '#month', '1') // Single digit
    await setInputValue(wrapper, '#year', '2024')
    await setInputValue(wrapper, '#amount', '150.50')

    await submitForm(wrapper)

    expect(mockAxios.post).toHaveBeenCalledWith('/expenses', expect.objectContaining({
      month: '01' // Should be padded to 2 digits
    }))
  })
})
