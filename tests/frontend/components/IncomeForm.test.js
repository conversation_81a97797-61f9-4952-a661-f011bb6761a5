import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mountWithGlobals, setInputValue, submitForm, flushPromises, createMockIncome, createMockUser } from '../helpers/testUtils.js'
import IncomeForm from '@/components/IncomeForm.vue'

describe('IncomeForm Component', () => {
  let wrapper
  let mockAxios

  const mockNeighbors = [
    createMockUser({ id: 1, name: '<PERSON>', apartment_number: 'A101' }),
    createMockUser({ id: 2, name: '<PERSON>', apartment_number: 'A102' }),
  ]

  beforeEach(() => {
    mockAxios = {
      get: vi.fn((url) => {
        if (url === '/admin/users') {
          return Promise.resolve({ data: mockNeighbors })
        }
        return Promise.resolve({ data: {} })
      }),
      post: vi.fn(() => Promise.resolve({ data: createMockIncome() })),
      put: vi.fn(() => Promise.resolve({ data: createMockIncome() })),
    }

    wrapper = mountWithGlobals(IncomeForm, {
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })
  })

  it('renders form fields correctly', () => {
    expect(wrapper.find('#user_id').exists()).toBe(true)
    expect(wrapper.find('#amount').exists()).toBe(true)
    expect(wrapper.find('#payment_date').exists()).toBe(true)
    expect(wrapper.find('#payment_method').exists()).toBe(true)
    expect(wrapper.find('#notes').exists()).toBe(true)
  })

  it('loads neighbors on mount', async () => {
    await flushPromises()

    expect(mockAxios.get).toHaveBeenCalledWith('/admin/users')
    expect(wrapper.vm.neighbors).toEqual(mockNeighbors)
  })

  it('populates neighbor options', async () => {
    await flushPromises()

    const userSelect = wrapper.find('#user_id')
    const options = userSelect.findAll('option')
    
    expect(options).toHaveLength(3) // Including default option
    expect(options[1].text()).toContain('John Doe')
    expect(options[1].text()).toContain('A101')
    expect(options[2].text()).toContain('Jane Smith')
    expect(options[2].text()).toContain('A102')
  })

  it('has payment method options', () => {
    const paymentMethodSelect = wrapper.find('#payment_method')
    const options = paymentMethodSelect.findAll('option')
    
    expect(options).toHaveLength(3)
    expect(options[0].attributes('value')).toBe('cash')
    expect(options[1].attributes('value')).toBe('bank_transfer')
    expect(options[2].attributes('value')).toBe('check')
  })

  it('sets default payment date to today', () => {
    const paymentDateInput = wrapper.find('#payment_date')
    expect(paymentDateInput.element.value).toBe('2024-01-01') // Based on mocked date
  })

  it('sets default payment method to cash', () => {
    expect(wrapper.vm.formData.payment_method).toBe('cash')
  })

  it('updates form data when user inputs', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '250.75')
    await setInputValue(wrapper, '#payment_date', '2024-01-15')
    await setInputValue(wrapper, '#payment_method', 'bank_transfer')
    await setInputValue(wrapper, '#notes', 'Monthly payment')

    expect(wrapper.vm.formData.user_id).toBe('1')
    expect(wrapper.vm.formData.amount).toBe('250.75')
    expect(wrapper.vm.formData.payment_date).toBe('2024-01-15')
    expect(wrapper.vm.formData.payment_method).toBe('bank_transfer')
    expect(wrapper.vm.formData.notes).toBe('Monthly payment')
  })

  it('submits form with correct data for new income', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '250.75')
    await setInputValue(wrapper, '#payment_date', '2024-01-15')
    await setInputValue(wrapper, '#payment_method', 'bank_transfer')
    await setInputValue(wrapper, '#notes', 'Monthly payment')

    await submitForm(wrapper)

    expect(mockAxios.post).toHaveBeenCalledWith('/incomes', {
      user_id: '1',
      amount: '250.75',
      payment_date: '2024-01-15',
      payment_method: 'bank_transfer',
      notes: 'Monthly payment'
    })
  })

  it('emits success event on successful submission', async () => {
    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '250.75')
    await setInputValue(wrapper, '#payment_date', '2024-01-15')
    await setInputValue(wrapper, '#payment_method', 'bank_transfer')

    await submitForm(wrapper)
    await flushPromises()

    expect(wrapper.emitted('success')).toBeTruthy()
    expect(wrapper.emitted('success')[0][0]).toEqual(createMockIncome())
  })

  it('handles form submission error', async () => {
    mockAxios.post.mockRejectedValueOnce({
      response: {
        data: {
          message: 'Validation failed'
        }
      }
    })

    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '250.75')
    await setInputValue(wrapper, '#payment_date', '2024-01-15')
    await setInputValue(wrapper, '#payment_method', 'bank_transfer')

    await submitForm(wrapper)
    await flushPromises()

    expect(wrapper.emitted('error')).toBeTruthy()
    expect(wrapper.emitted('error')[0][0]).toBe('Validation failed')
  })

  it('shows loading state during submission', async () => {
    mockAxios.post.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: {} }), 100))
    )

    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '250.75')
    await setInputValue(wrapper, '#payment_date', '2024-01-15')
    await setInputValue(wrapper, '#payment_method', 'bank_transfer')

    const submitButton = wrapper.find('button[type="submit"]')
    await submitForm(wrapper)

    expect(wrapper.vm.processing).toBe(true)
    expect(submitButton.attributes('disabled')).toBeDefined()
  })

  it('validates required fields', () => {
    const requiredFields = ['#user_id', '#amount', '#payment_date', '#payment_method']
    
    requiredFields.forEach(selector => {
      const field = wrapper.find(selector)
      expect(field.attributes('required')).toBeDefined()
    })
  })

  it('validates amount field type and constraints', () => {
    const amountInput = wrapper.find('#amount')
    
    expect(amountInput.attributes('type')).toBe('number')
    expect(amountInput.attributes('step')).toBe('0.01')
    expect(amountInput.attributes('min')).toBe('0')
  })

  it('handles edit mode correctly', async () => {
    const mockIncome = createMockIncome({
      id: 1,
      user_id: 1,
      amount: 250.75,
      payment_date: '2024-01-15',
      payment_method: 'bank_transfer',
      notes: 'Monthly payment'
    })

    wrapper = mountWithGlobals(IncomeForm, {
      props: {
        income: mockIncome,
        isEdit: true
      },
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })

    await flushPromises()

    expect(wrapper.vm.formData.user_id).toBe(1)
    expect(wrapper.vm.formData.amount).toBe(250.75)
    expect(wrapper.vm.formData.payment_date).toBe('2024-01-15')
    expect(wrapper.vm.formData.payment_method).toBe('bank_transfer')
    expect(wrapper.vm.formData.notes).toBe('Monthly payment')
  })

  it('submits PUT request for edit mode', async () => {
    const mockIncome = createMockIncome({ id: 1 })

    wrapper = mountWithGlobals(IncomeForm, {
      props: {
        income: mockIncome,
        isEdit: true
      },
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })

    await flushPromises()

    await setInputValue(wrapper, '#user_id', '1')
    await setInputValue(wrapper, '#amount', '300.00')
    await setInputValue(wrapper, '#payment_date', '2024-01-20')
    await setInputValue(wrapper, '#payment_method', 'cash')

    await submitForm(wrapper)

    expect(mockAxios.put).toHaveBeenCalledWith('/incomes/1', expect.any(Object))
  })

  it('emits cancel event when cancel button is clicked', async () => {
    const cancelButton = wrapper.find('button[type="button"]')
    await cancelButton.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('displays correct button text for create vs edit mode', () => {
    // Create mode
    let submitButton = wrapper.find('button[type="submit"]')
    expect(submitButton.text()).toContain('create_income')

    // Edit mode
    wrapper = mountWithGlobals(IncomeForm, {
      props: {
        income: createMockIncome(),
        isEdit: true
      },
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })

    submitButton = wrapper.find('button[type="submit"]')
    expect(submitButton.text()).toContain('edit_income')
  })

  it('shows create income header for new income', () => {
    expect(wrapper.find('h2').text()).toBe('create_income')
  })

  it('hides header in edit mode', () => {
    wrapper = mountWithGlobals(IncomeForm, {
      props: {
        income: createMockIncome(),
        isEdit: true
      },
      global: {
        mocks: {
          $axios: mockAxios,
        },
        stubs: {
          'file-manager': true,
        }
      }
    })

    expect(wrapper.find('h2').exists()).toBe(false)
  })
})
