<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Building;
use App\Models\Income;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class IncomeManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $building;
    protected $admin;
    protected $neighbor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->building = Building::factory()->create();
        $this->admin = User::factory()->admin()->create(['building_id' => $this->building->id]);
        $this->neighbor = User::factory()->neighbor()->create(['building_id' => $this->building->id]);
    }

    /** @test */
    public function authenticated_user_can_view_incomes()
    {
        Sanctum::actingAs($this->neighbor);

        $income = Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        $response = $this->getJson('/api/incomes');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'amount', 'payment_date', 'payment_method', 'notes']
                ]
            ]);
    }

    /** @test */
    public function admin_can_create_income()
    {
        Sanctum::actingAs($this->admin);

        $incomeData = [
            'user_id' => $this->neighbor->id,
            'amount' => 250.75,
            'payment_date' => '2024-12-15',
            'payment_method' => 'bank_transfer',
            'notes' => 'Monthly payment received',
        ];

        $response = $this->postJson('/api/incomes', $incomeData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'id', 'amount', 'payment_date', 'payment_method', 'notes'
            ]);

        $this->assertDatabaseHas('incomes', [
            'user_id' => $this->neighbor->id,
            'amount' => 250.75,
            'payment_method' => 'bank_transfer',
            'notes' => 'Monthly payment received',
        ]);
    }

    /** @test */
    public function neighbor_cannot_create_income()
    {
        Sanctum::actingAs($this->neighbor);

        $incomeData = [
            'user_id' => $this->neighbor->id,
            'amount' => 250.75,
            'payment_date' => '2024-12-15',
            'payment_method' => 'bank_transfer',
        ];

        $response = $this->postJson('/api/incomes', $incomeData);

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_update_income()
    {
        Sanctum::actingAs($this->admin);

        $income = Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        $updateData = [
            'user_id' => $this->neighbor->id,
            'amount' => 300.00,
            'payment_date' => '2024-12-20',
            'payment_method' => 'cash',
            'notes' => 'Updated payment record',
        ];

        $response = $this->putJson("/api/incomes/{$income->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('incomes', [
            'id' => $income->id,
            'amount' => 300.00,
            'payment_method' => 'cash',
            'notes' => 'Updated payment record',
        ]);
    }

    /** @test */
    public function admin_can_delete_income()
    {
        Sanctum::actingAs($this->admin);

        $income = Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        $response = $this->deleteJson("/api/incomes/{$income->id}");

        $response->assertStatus(204);

        $this->assertDatabaseMissing('incomes', [
            'id' => $income->id,
        ]);
    }

    /** @test */
    public function user_can_view_single_income()
    {
        Sanctum::actingAs($this->neighbor);

        $income = Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        $response = $this->getJson("/api/incomes/{$income->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id', 'amount', 'payment_date', 'payment_method', 'notes',
                'user' => ['id', 'name']
            ]);
    }

    /** @test */
    public function user_can_get_income_summary()
    {
        Sanctum::actingAs($this->neighbor);

        Income::factory()->count(3)->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        $response = $this->getJson('/api/incomes/summary');

        $response->assertStatus(200);
    }

    /** @test */
    public function income_creation_requires_valid_data()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/incomes', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'user_id',
                'amount',
                'payment_date',
                'payment_method'
            ]);
    }

    /** @test */
    public function income_amount_must_be_positive()
    {
        Sanctum::actingAs($this->admin);

        $incomeData = [
            'user_id' => $this->neighbor->id,
            'amount' => -100.00, // Negative amount
            'payment_date' => '2024-12-15',
            'payment_method' => 'cash',
        ];

        $response = $this->postJson('/api/incomes', $incomeData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount']);
    }

    /** @test */
    public function income_payment_method_must_be_valid()
    {
        Sanctum::actingAs($this->admin);

        $incomeData = [
            'user_id' => $this->neighbor->id,
            'amount' => 100.00,
            'payment_date' => '2024-12-15',
            'payment_method' => 'invalid_method', // Invalid payment method
        ];

        $response = $this->postJson('/api/incomes', $incomeData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['payment_method']);
    }

    /** @test */
    public function user_can_filter_incomes_by_date_range()
    {
        Sanctum::actingAs($this->neighbor);

        // Create incomes with different dates
        Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'payment_date' => '2024-01-15',
        ]);

        Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'payment_date' => '2024-06-15',
        ]);

        Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'payment_date' => '2024-12-15',
        ]);

        $response = $this->getJson('/api/incomes?date_from=2024-06-01&date_to=2024-12-31');

        $response->assertStatus(200);
        
        $incomes = $response->json('data');
        $this->assertCount(2, $incomes); // Should return 2 incomes within the date range
    }

    /** @test */
    public function user_can_filter_incomes_by_user()
    {
        Sanctum::actingAs($this->admin);

        $anotherNeighbor = User::factory()->neighbor()->create(['building_id' => $this->building->id]);

        Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
        ]);

        Income::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $anotherNeighbor->id,
        ]);

        $response = $this->getJson("/api/incomes?user_id={$this->neighbor->id}");

        $response->assertStatus(200);
        
        $incomes = $response->json('data');
        $this->assertCount(1, $incomes); // Should return only 1 income for the specific user
    }
}
