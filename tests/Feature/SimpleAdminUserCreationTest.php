<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Models\Package;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SimpleAdminUserCreationTest extends TestCase
{
    use DatabaseTransactions;

    public function test_admin_can_create_neighbor_user()
    {
        // Create a standard package
        $package = Package::create([
            'name' => 'Standard Package',
            'slug' => 'standard',
            'description' => 'Standard package',
            'price' => 14.99,
            'max_neighbors' => null,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => true,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 25,
            'features' => ['core_saas_admin_tool', 'multi_admin_support'],
            'is_active' => true,
        ]);

        // Create building
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $package->id,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating neighbor user
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Neighbor',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => '101',
            'role' => 'neighbor',
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'neighbor',
            'building_id' => $building->id,
        ]);
    }

    public function test_admin_can_create_admin_user_with_standard_package()
    {
        // Create a standard package
        $package = Package::create([
            'name' => 'Standard Package',
            'slug' => 'standard',
            'description' => 'Standard package',
            'price' => 14.99,
            'max_neighbors' => null,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => true,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 25,
            'features' => ['core_saas_admin_tool', 'multi_admin_support'],
            'is_active' => true,
        ]);

        // Create building
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $package->id,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating admin user
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Admin 2',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'ADMIN2',
            'role' => 'admin',
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'admin',
            'building_id' => $building->id,
        ]);
    }

    public function test_admin_cannot_create_admin_user_with_basic_package()
    {
        // Create a basic package
        $package = Package::create([
            'name' => 'Basic Package',
            'slug' => 'basic',
            'description' => 'Basic package',
            'price' => 9.99,
            'max_neighbors' => 50,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => false,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 5,
            'features' => ['core_saas_admin_tool'],
            'is_active' => true,
        ]);

        // Create building
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $package->id,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating admin user should fail
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Admin 2',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'ADMIN2',
            'role' => 'admin',
        ]);

        $response->assertStatus(403);
        $response->assertJson([
            'message' => 'Your package does not support multiple admins.'
        ]);
        
        // Verify the admin user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }
}
