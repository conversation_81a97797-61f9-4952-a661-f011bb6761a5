<?php

namespace Tests\Feature;

use App\Http\Middleware\CheckPackageLimits;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;
use Mockery;

class SuperAdminPackageLimitsTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_super_admin_bypasses_all_package_limits()
    {
        // Mock a super admin user
        $superAdmin = Mockery::mock(User::class);
        $superAdmin->shouldReceive('isSuperAdmin')->andReturn(true);

        // Create a request with the super admin
        $request = Request::create('/test', 'GET');
        $request->setUserResolver(function () use ($superAdmin) {
            return $superAdmin;
        });

        $middleware = new CheckPackageLimits();

        // Test that super admin bypasses all limits
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        }, 'neighbors');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_super_admin_bypasses_different_features()
    {
        // Mock a super admin user
        $superAdmin = Mockery::mock(User::class);
        $superAdmin->shouldReceive('isSuperAdmin')->andReturn(true);

        $request = Request::create('/test', 'GET');
        $request->setUserResolver(function () use ($superAdmin) {
            return $superAdmin;
        });

        $middleware = new CheckPackageLimits();

        $features = ['neighbors', 'notifications', 'file_attachments', 'storage'];

        foreach ($features as $feature) {
            $response = $middleware->handle($request, function ($req) {
                return new Response('Success', 200);
            }, $feature);

            $this->assertEquals(200, $response->getStatusCode(),
                "Super admin should bypass {$feature} limits");
        }
    }

    public function test_non_super_admin_user_continues_to_middleware()
    {
        // Mock a regular admin user (not super admin)
        $regularUser = Mockery::mock(User::class);
        $regularUser->shouldReceive('isSuperAdmin')->andReturn(false);
        $regularUser->shouldReceive('getAttribute')->with('building')->andReturn(null);
        $regularUser->shouldReceive('setAttribute')->andReturn(null);

        $request = Request::create('/test', 'GET');
        $request->setUserResolver(function () use ($regularUser) {
            return $regularUser;
        });

        $middleware = new CheckPackageLimits();

        // For non-super admin without building, should get 403 error
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        }, 'neighbors');

        $this->assertEquals(403, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertStringContainsString('No building associated', $responseData['message']);
    }
}
