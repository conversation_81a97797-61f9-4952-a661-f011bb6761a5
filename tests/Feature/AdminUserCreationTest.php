<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Models\Package;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AdminUserCreationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create packages
        $this->basicPackage = Package::create([
            'name' => 'Basic Package',
            'slug' => 'basic',
            'description' => 'Basic package',
            'price' => 9.99,
            'max_neighbors' => 50,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => false,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 5,
            'features' => ['core_saas_admin_tool'],
            'is_active' => true,
        ]);

        $this->standardPackage = Package::create([
            'name' => 'Standard Package',
            'slug' => 'standard',
            'description' => 'Standard package with multi-admin support',
            'price' => 14.99,
            'max_neighbors' => null,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => true,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 25,
            'features' => ['core_saas_admin_tool', 'multi_admin_support'],
            'is_active' => true,
        ]);
    }

    public function test_regular_admin_can_create_neighbor_user()
    {
        // Create building with standard package (supports multi-admin)
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $this->standardPackage->id,
        ]);

        // Create regular admin
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating neighbor user
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Neighbor',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => '101',
            'role' => 'neighbor',
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'neighbor',
            'building_id' => $building->id,
        ]);
    }

    public function test_regular_admin_can_create_admin_user_with_standard_package()
    {
        // Create building with standard package (supports multi-admin)
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $this->standardPackage->id,
        ]);

        // Create regular admin
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating admin user
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Admin 2',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'ADMIN2',
            'role' => 'admin',
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'admin',
            'building_id' => $building->id,
        ]);
    }

    public function test_regular_admin_cannot_create_admin_user_with_basic_package()
    {
        // Create building with basic package (no multi-admin support)
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $this->basicPackage->id,
        ]);

        // Create regular admin
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $building->id,
            'apartment_number' => 'ADMIN',
        ]);

        // Test creating admin user should fail
        $response = $this->actingAs($admin)->postJson('/admin/users', [
            'name' => 'Test Admin 2',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'ADMIN2',
            'role' => 'admin',
        ]);

        $response->assertStatus(403);
        $response->assertJson([
            'message' => 'Your package does not support multiple admins.'
        ]);

        // Verify the admin user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }

    public function test_super_admin_can_create_any_user_regardless_of_package()
    {
        // Create building with basic package
        $building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $this->basicPackage->id,
        ]);

        // Create super admin
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'super_admin',
            'building_id' => null,
            'apartment_number' => null,
        ]);

        // Test creating admin user should succeed even with basic package
        $response = $this->actingAs($superAdmin)->postJson('/admin/users', [
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'ADMIN',
            'role' => 'admin',
            'building_id' => $building->id,
        ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'admin',
            'building_id' => $building->id,
        ]);
    }
}
