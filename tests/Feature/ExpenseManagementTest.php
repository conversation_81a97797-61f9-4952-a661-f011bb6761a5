<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Building;
use App\Models\Expense;
use App\Models\ExpenseType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class ExpenseManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $building;
    protected $admin;
    protected $neighbor;
    protected $expenseType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->building = Building::factory()->create();
        $this->admin = User::factory()->admin()->create(['building_id' => $this->building->id]);
        $this->neighbor = User::factory()->neighbor()->create(['building_id' => $this->building->id]);
        $this->expenseType = ExpenseType::factory()->create();
    }

    /** @test */
    public function authenticated_user_can_view_expenses()
    {
        Sanctum::actingAs($this->neighbor);

        $expense = Expense::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
        ]);

        $response = $this->getJson('/api/expenses');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'amount', 'due_date', 'month', 'year', 'notes']
                ]
            ]);
    }

    /** @test */
    public function admin_can_create_expense()
    {
        Sanctum::actingAs($this->admin);

        $expenseData = [
            'expense_type_id' => $this->expenseType->id,
            'user_id' => $this->neighbor->id,
            'amount' => 150.50,
            'month' => '12',
            'year' => 2024,
            'notes' => 'Monthly maintenance fee',
            'is_automatic' => false,
        ];

        $response = $this->postJson('/api/expenses', $expenseData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'id', 'amount', 'due_date', 'month', 'year', 'notes'
            ]);

        $this->assertDatabaseHas('expenses', [
            'expense_type_id' => $this->expenseType->id,
            'user_id' => $this->neighbor->id,
            'amount' => 150.50,
            'month' => '12',
            'year' => 2024,
        ]);
    }

    /** @test */
    public function neighbor_cannot_create_expense()
    {
        Sanctum::actingAs($this->neighbor);

        $expenseData = [
            'expense_type_id' => $this->expenseType->id,
            'user_id' => $this->neighbor->id,
            'amount' => 150.50,
            'month' => '12',
            'year' => 2024,
        ];

        $response = $this->postJson('/api/expenses', $expenseData);

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_can_update_expense()
    {
        Sanctum::actingAs($this->admin);

        $expense = Expense::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
        ]);

        $updateData = [
            'expense_type_id' => $this->expenseType->id,
            'user_id' => $this->neighbor->id,
            'amount' => 200.00,
            'month' => '01',
            'year' => 2025,
            'notes' => 'Updated expense',
            'is_automatic' => true,
        ];

        $response = $this->putJson("/api/expenses/{$expense->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('expenses', [
            'id' => $expense->id,
            'amount' => 200.00,
            'notes' => 'Updated expense',
            'is_automatic' => true,
        ]);
    }

    /** @test */
    public function admin_can_delete_expense()
    {
        Sanctum::actingAs($this->admin);

        $expense = Expense::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
        ]);

        $response = $this->deleteJson("/api/expenses/{$expense->id}");

        $response->assertStatus(204);

        $this->assertDatabaseMissing('expenses', [
            'id' => $expense->id,
        ]);
    }

    /** @test */
    public function user_can_view_single_expense()
    {
        Sanctum::actingAs($this->neighbor);

        $expense = Expense::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
        ]);

        $response = $this->getJson("/api/expenses/{$expense->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id', 'amount', 'due_date', 'month', 'year', 'notes',
                'expense_type' => ['id', 'name'],
                'user' => ['id', 'name']
            ]);
    }

    /** @test */
    public function admin_can_generate_monthly_expenses()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/expenses/generate-monthly', [
            'month' => '01',
            'year' => '2025',
        ]);

        $response->assertStatus(200);
    }

    /** @test */
    public function user_can_get_monthly_expenses()
    {
        Sanctum::actingAs($this->neighbor);

        Expense::factory()->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
            'month' => '12',
            'year' => 2024,
        ]);

        $response = $this->getJson('/api/expenses/monthly?month=12&year=2025');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'amount', 'month', 'year']
                ]
            ]);
    }

    /** @test */
    public function expense_creation_requires_valid_data()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->postJson('/api/expenses', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'expense_type_id',
                'user_id',
                'amount',
                'month',
                'year'
            ]);
    }

    /** @test */
    public function expense_amount_must_be_positive()
    {
        Sanctum::actingAs($this->admin);

        $expenseData = [
            'expense_type_id' => $this->expenseType->id,
            'user_id' => $this->neighbor->id,
            'amount' => -50.00, // Negative amount
            'month' => '12',
            'year' => 2025,
        ];

        $response = $this->postJson('/api/expenses', $expenseData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount']);
    }

    /** @test */
    public function user_can_get_expense_summary()
    {
        Sanctum::actingAs($this->neighbor);

        Expense::factory()->count(3)->create([
            'building_id' => $this->building->id,
            'user_id' => $this->neighbor->id,
            'expense_type_id' => $this->expenseType->id,
        ]);

        $response = $this->getJson('/api/expenses/summary');

        $response->assertStatus(200);
    }
}
