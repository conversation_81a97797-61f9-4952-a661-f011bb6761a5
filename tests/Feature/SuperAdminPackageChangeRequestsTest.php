<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Building;
use App\Models\Package;
use App\Models\PackageChangeRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SuperAdminPackageChangeRequestsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test packages manually to avoid factory issues
        $this->freePackage = Package::create([
            'name' => 'Free Package',
            'slug' => 'free',
            'description' => 'Free package for basic use',
            'price' => 0,
            'annual_price' => 0,
            'max_neighbors' => 10,
            'max_admins' => 1,
            'storage_limit_gb' => 1,
            'is_active' => true,
            'billing_cycle' => 'monthly',
            'trial_days' => 0,
        ]);
        
        $this->premiumPackage = Package::create([
            'name' => 'Premium Package',
            'slug' => 'premium',
            'description' => 'Premium package with advanced features',
            'price' => 99.99,
            'annual_price' => 999.99,
            'max_neighbors' => 100,
            'max_admins' => 5,
            'storage_limit_gb' => 100,
            'is_active' => true,
            'billing_cycle' => 'monthly',
            'trial_days' => 0,
        ]);
        
        // Create super admin user
        $this->superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'super_admin',
        ]);
        
        // Create building with admin
        $this->building = Building::create([
            'name' => 'Test Building',
            'address' => 'Test Address',
            'current_package_id' => $this->freePackage->id,
        ]);
        
        $this->buildingAdmin = User::create([
            'name' => 'Building Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $this->building->id,
        ]);
    }

    public function test_super_admin_can_access_package_change_requests_page()
    {
        $response = $this->actingAs($this->superAdmin)
            ->get('/api/super-admin/package-approvals');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'current_page',
            'last_page',
            'per_page',
            'total',
        ]);
    }

    public function test_super_admin_can_view_package_change_request_statistics()
    {
        $response = $this->actingAs($this->superAdmin)
            ->get('/api/super-admin/package-approvals/statistics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_requests',
            'pending_requests',
            'approved_requests',
            'rejected_requests',
        ]);
    }

    public function test_super_admin_can_approve_package_change_request()
    {
        // Create a package change request
        $packageChangeRequest = PackageChangeRequest::create([
            'building_id' => $this->building->id,
            'requested_by_user_id' => $this->buildingAdmin->id,
            'current_package_id' => $this->freePackage->id,
            'requested_package_id' => $this->premiumPackage->id,
            'status' => 'pending',
            'billing_cycle' => 'monthly',
            'payment_method' => 'credit_card',
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->post("/api/super-admin/package-approvals/{$packageChangeRequest->id}/approve");

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Package change request approved successfully.',
        ]);

        // Check that the request was approved
        $this->assertDatabaseHas('package_change_requests', [
            'id' => $packageChangeRequest->id,
            'status' => 'approved',
        ]);

        // Check that the building's package was updated
        $this->assertDatabaseHas('buildings', [
            'id' => $this->building->id,
            'current_package_id' => $this->premiumPackage->id,
        ]);
    }

    public function test_super_admin_can_reject_package_change_request()
    {
        // Create a package change request
        $packageChangeRequest = PackageChangeRequest::create([
            'building_id' => $this->building->id,
            'requested_by_user_id' => $this->buildingAdmin->id,
            'current_package_id' => $this->freePackage->id,
            'requested_package_id' => $this->premiumPackage->id,
            'status' => 'pending',
            'billing_cycle' => 'monthly',
            'payment_method' => 'credit_card',
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->post("/api/super-admin/package-approvals/{$packageChangeRequest->id}/reject", [
                'admin_notes' => 'Request rejected due to insufficient information.',
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Package change request rejected successfully.',
        ]);

        // Check that the request was rejected
        $this->assertDatabaseHas('package_change_requests', [
            'id' => $packageChangeRequest->id,
            'status' => 'rejected',
            'admin_notes' => 'Request rejected due to insufficient information.',
        ]);
    }

    public function test_super_admin_cannot_approve_already_processed_request()
    {
        // Create an already approved package change request
        $packageChangeRequest = PackageChangeRequest::create([
            'building_id' => $this->building->id,
            'requested_by_user_id' => $this->buildingAdmin->id,
            'current_package_id' => $this->freePackage->id,
            'requested_package_id' => $this->premiumPackage->id,
            'status' => 'approved',
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->post("/api/super-admin/package-approvals/{$packageChangeRequest->id}/approve");

        $response->assertStatus(400);
        $response->assertJson([
            'message' => 'Request is not pending.',
        ]);
    }

    public function test_super_admin_cannot_reject_already_processed_request()
    {
        // Create an already rejected package change request
        $packageChangeRequest = PackageChangeRequest::create([
            'building_id' => $this->building->id,
            'requested_by_user_id' => $this->buildingAdmin->id,
            'current_package_id' => $this->freePackage->id,
            'requested_package_id' => $this->premiumPackage->id,
            'status' => 'rejected',
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->post("/api/super-admin/package-approvals/{$packageChangeRequest->id}/reject", [
                'admin_notes' => 'Additional rejection note.',
            ]);

        $response->assertStatus(400);
        $response->assertJson([
            'message' => 'Request is not pending.',
        ]);
    }

    public function test_non_super_admin_cannot_access_package_change_requests()
    {
        $response = $this->actingAs($this->buildingAdmin)
            ->get('/api/super-admin/package-approvals');

        $response->assertStatus(403);
    }

    public function test_package_change_request_with_relationships()
    {
        // Create a package change request with all relationships
        $packageChangeRequest = PackageChangeRequest::create([
            'building_id' => $this->building->id,
            'requested_by_user_id' => $this->buildingAdmin->id,
            'current_package_id' => $this->freePackage->id,
            'requested_package_id' => $this->premiumPackage->id,
            'status' => 'pending',
            'billing_cycle' => 'annual',
            'payment_method' => 'bank_transfer',
            'reason' => 'Need more features for our building management.',
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->get("/api/super-admin/package-approvals/{$packageChangeRequest->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'building' => [
                'id',
                'name',
                'address',
            ],
            'requested_by' => [
                'id',
                'name',
                'email',
            ],
            'current_package' => [
                'id',
                'name',
                'price',
            ],
            'requested_package' => [
                'id',
                'name',
                'price',
            ],
            'status',
            'billing_cycle',
            'payment_method',
            'reason',
            'created_at',
        ]);
    }
} 