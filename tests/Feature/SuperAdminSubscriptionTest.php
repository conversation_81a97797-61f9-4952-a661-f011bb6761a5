<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Models\Package;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SuperAdminSubscriptionTest extends TestCase
{
    use RefreshDatabase;

    protected $superAdmin;
    protected $package;
    protected $building;
    protected $subscription;
    protected $expiredSubscription;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a package
        $this->package = Package::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 29.99,
            'max_neighbors' => 100,
            'notifications_enabled' => true,
            'email_notifications_enabled' => true,
            'sms_notifications_enabled' => false,
            'priority_support' => false,
            'advanced_reporting' => true,
            'file_attachments_enabled' => true,
            'storage_limit_gb' => 10,
            'features' => ['core_saas_admin_tool'],
            'is_active' => true,
        ]);

        // Create a building
        $this->building = Building::create([
            'name' => 'Test Building',
            'address' => '123 Test St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'current_package_id' => $this->package->id,
        ]);

        // Create super admin user
        $this->superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'super_admin',
        ]);

        // Create a subscription
        $this->subscription = Subscription::create([
            'building_id' => $this->building->id,
            'package_id' => $this->package->id,
            'status' => 'active',
            'billing_cycle' => 'monthly',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
            'auto_renew' => true,
        ]);

        // Create an expired subscription for testing renew button logic
        $this->expiredSubscription = Subscription::create([
            'building_id' => $this->building->id,
            'package_id' => $this->package->id,
            'status' => 'active',
            'billing_cycle' => 'monthly',
            'starts_at' => now()->subMonths(2),
            'ends_at' => now()->subDays(1), // Expired yesterday
            'auto_renew' => false,
        ]);
    }

    /** @test */
    public function super_admin_can_view_subscriptions()
    {
        Sanctum::actingAs($this->superAdmin);

        $response = $this->getJson('/api/super-admin/subscriptions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'status',
                        'billing_cycle',
                        'building' => ['id', 'name'],
                        'package' => ['id', 'name'],
                    ]
                ],
                'current_page',
                'total'
            ]);
    }

    /** @test */
    public function super_admin_can_update_subscription()
    {
        Sanctum::actingAs($this->superAdmin);

        $updateData = [
            'package_id' => $this->package->id,
            'billing_cycle' => 'annual',
            'status' => 'inactive',
            'auto_renew' => false,
            'ends_at' => now()->addMonths(6)->format('Y-m-d'),
            'notes' => 'Updated subscription notes'
        ];

        $response = $this->putJson("/api/super-admin/subscriptions/{$this->subscription->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Subscription updated successfully'
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $this->subscription->id,
            'billing_cycle' => 'annual',
            'status' => 'inactive',
            'auto_renew' => false,
            'notes' => 'Updated subscription notes'
        ]);

        // Check that ends_at was updated (allowing for slight time differences)
        $updatedSubscription = $this->subscription->fresh();
        $this->assertEquals(
            now()->addMonths(6)->format('Y-m-d'),
            $updatedSubscription->ends_at->format('Y-m-d')
        );
    }

    /** @test */
    public function super_admin_can_update_subscription_expiration_date()
    {
        Sanctum::actingAs($this->superAdmin);

        $newExpirationDate = now()->addYear()->format('Y-m-d');

        $updateData = [
            'ends_at' => $newExpirationDate
        ];

        $response = $this->putJson("/api/super-admin/subscriptions/{$this->subscription->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Subscription updated successfully'
            ]);

        $updatedSubscription = $this->subscription->fresh();
        $this->assertEquals(
            $newExpirationDate,
            $updatedSubscription->ends_at->format('Y-m-d')
        );
    }

    /** @test */
    public function super_admin_can_create_subscription()
    {
        Sanctum::actingAs($this->superAdmin);

        $subscriptionData = [
            'building_id' => $this->building->id,
            'package_id' => $this->package->id,
            'billing_cycle' => 'monthly',
            'trial_days' => 14,
            'notes' => 'New subscription for testing'
        ];

        $response = $this->postJson('/api/super-admin/subscriptions', $subscriptionData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'subscription' => [
                    'id',
                    'status',
                    'billing_cycle',
                    'building',
                    'package'
                ]
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'building_id' => $this->building->id,
            'package_id' => $this->package->id,
            'billing_cycle' => 'monthly',
            'notes' => 'New subscription for testing'
        ]);
    }

    /** @test */
    public function super_admin_cannot_set_expiration_date_in_past()
    {
        Sanctum::actingAs($this->superAdmin);

        $pastDate = now()->subDays(1)->format('Y-m-d');

        $updateData = [
            'ends_at' => $pastDate
        ];

        $response = $this->putJson("/api/super-admin/subscriptions/{$this->subscription->id}", $updateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ends_at']);
    }

    /** @test */
    public function subscription_data_includes_expiration_dates_for_frontend_logic()
    {
        Sanctum::actingAs($this->superAdmin);

        $response = $this->getJson('/api/super-admin/subscriptions');

        $response->assertStatus(200);

        $subscriptions = $response->json('data');

        // Verify that subscriptions include ends_at field for frontend renew button logic
        foreach ($subscriptions as $subscription) {
            $this->assertArrayHasKey('ends_at', $subscription);
        }

        // Note: The frontend shouldShowRenewButton() method will use this data to:
        // - Show renew button for expired subscriptions (ends_at <= today)
        // - Hide renew button for future subscriptions (ends_at > today)
        // - Show renew button if ends_at is null
    }

    /** @test */
    public function regular_user_cannot_access_super_admin_subscriptions()
    {
        $regularUser = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'building_id' => $this->building->id,
        ]);

        Sanctum::actingAs($regularUser);

        $response = $this->getJson('/api/super-admin/subscriptions');

        $response->assertStatus(403);
    }
}
