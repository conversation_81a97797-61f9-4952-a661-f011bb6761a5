<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Building;
use App\Models\BuildingExpense;
use App\Models\BuildingExpenseType;
use Laravel\Sanctum\Sanctum;

class BuildingExpenseManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $building;
    protected $buildingExpenseType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->building = Building::factory()->create();
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'building_id' => $this->building->id,
        ]);
        $this->buildingExpenseType = BuildingExpenseType::factory()->create();
    }

    /** @test */
    public function admin_can_view_building_expenses_list()
    {
        Sanctum::actingAs($this->admin);

        $buildingExpense = BuildingExpense::factory()->create([
            'building_id' => $this->building->id,
            'building_expense_type_id' => $this->buildingExpenseType->id,
        ]);

        $response = $this->getJson('/api/building-expenses');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id', 'amount', 'month', 'year', 'notes',
                        'building_expense_type' => ['id', 'name'],
                        'building' => ['id', 'name']
                    ]
                ]
            ]);
    }

    /** @test */
    public function admin_can_create_building_expense()
    {
        Sanctum::actingAs($this->admin);

        $buildingExpenseData = [
            'building_expense_type_id' => $this->buildingExpenseType->id,
            'amount' => 150.00,
            'month' => '01',
            'year' => 2025,
            'notes' => 'Test building expense',
            'is_automatic' => false,
        ];

        $response = $this->postJson('/api/building-expenses', $buildingExpenseData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'id', 'amount', 'month', 'year', 'notes',
                'building_expense_type' => ['id', 'name'],
                'building' => ['id', 'name']
            ]);

        $this->assertDatabaseHas('building_expenses', [
            'building_expense_type_id' => $this->buildingExpenseType->id,
            'building_id' => $this->building->id,
            'amount' => 150.00,
            'month' => '01',
            'year' => '2025',
            'notes' => 'Test building expense',
        ]);
    }

    /** @test */
    public function admin_can_update_building_expense()
    {
        Sanctum::actingAs($this->admin);

        $buildingExpense = BuildingExpense::factory()->create([
            'building_id' => $this->building->id,
            'building_expense_type_id' => $this->buildingExpenseType->id,
        ]);

        $updateData = [
            'building_expense_type_id' => $this->buildingExpenseType->id,
            'amount' => 200.00,
            'month' => '02',
            'year' => 2025,
            'notes' => 'Updated building expense',
            'is_automatic' => true,
        ];

        $response = $this->putJson("/api/building-expenses/{$buildingExpense->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('building_expenses', [
            'id' => $buildingExpense->id,
            'amount' => 200.00,
            'month' => '02',
            'year' => '2025',
            'notes' => 'Updated building expense',
            'is_automatic' => true,
        ]);
    }

    /** @test */
    public function admin_can_delete_building_expense()
    {
        Sanctum::actingAs($this->admin);

        $buildingExpense = BuildingExpense::factory()->create([
            'building_id' => $this->building->id,
            'building_expense_type_id' => $this->buildingExpenseType->id,
        ]);

        $response = $this->deleteJson("/api/building-expenses/{$buildingExpense->id}");

        $response->assertStatus(204);

        $this->assertDatabaseMissing('building_expenses', [
            'id' => $buildingExpense->id,
        ]);
    }

    /** @test */
    public function admin_can_view_single_building_expense()
    {
        Sanctum::actingAs($this->admin);

        $buildingExpense = BuildingExpense::factory()->create([
            'building_id' => $this->building->id,
            'building_expense_type_id' => $this->buildingExpenseType->id,
        ]);

        $response = $this->getJson("/api/building-expenses/{$buildingExpense->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id', 'amount', 'month', 'year', 'notes',
                'building_expense_type' => ['id', 'name'],
                'building' => ['id', 'name']
            ]);
    }

    /** @test */
    public function admin_cannot_access_building_expenses_from_other_buildings()
    {
        $otherBuilding = Building::factory()->create();
        $otherAdmin = User::factory()->create([
            'role' => 'admin',
            'building_id' => $otherBuilding->id,
        ]);

        Sanctum::actingAs($otherAdmin);

        $buildingExpense = BuildingExpense::factory()->create([
            'building_id' => $this->building->id,
            'building_expense_type_id' => $this->buildingExpenseType->id,
        ]);

        $response = $this->getJson("/api/building-expenses/{$buildingExpense->id}");

        $response->assertStatus(403);
    }
}
