<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Models\CustomReport;
use App\Models\Expense;
use App\Models\ExpenseType;
use App\Models\Package;
use App\Models\ReportTemplate;
use App\Models\User;
use App\Services\AdvancedReportingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdvancedReportingTest extends TestCase
{
    use RefreshDatabase;

    protected $building;
    protected $user;
    protected $package;
    protected $template;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a Pro package with advanced reporting enabled
        $this->package = Package::factory()->create([
            'name' => 'Pro Package',
            'slug' => 'pro',
            'advanced_reporting' => true,
            'custom_reports_enabled' => true,
            'max_custom_reports' => null,
            'report_scheduling_enabled' => true,
            'advanced_charts_enabled' => true,
            'available_chart_types' => ['bar', 'line', 'pie', 'doughnut', 'area', 'scatter', 'radar', 'table'],
        ]);

        // Create a building with the Pro package
        $this->building = Building::factory()->create([
            'current_package_id' => $this->package->id,
        ]);

        // Create an admin user
        $this->user = User::factory()->create([
            'building_id' => $this->building->id,
            'role' => 'admin',
        ]);

        // Create a report template
        $this->template = ReportTemplate::factory()->create([
            'name' => 'Test Financial Report',
            'category' => 'financial',
            'data_sources' => ['expenses'],
            'fields' => [
                ['key' => 'amount', 'type' => 'currency', 'label' => 'Amount'],
                ['key' => 'created_at', 'type' => 'date', 'label' => 'Date'],
                ['key' => 'expense_type_name', 'type' => 'string', 'label' => 'Expense Type'],
            ],
            'filters' => [
                ['key' => 'expense_type_id', 'type' => 'select', 'label' => 'Expense Type'],
                ['key' => 'amount_min', 'type' => 'number', 'label' => 'Minimum Amount'],
            ],
        ]);

        $this->service = new AdvancedReportingService();
    }

    public function test_package_integration_works_correctly()
    {
        // Test Pro package
        $this->assertTrue($this->service->hasAdvancedReporting($this->building));
        $this->assertTrue($this->service->hasCustomReports($this->building));
        $this->assertTrue($this->service->hasAdvancedCharts($this->building));
        $this->assertTrue($this->service->canCreateMoreReports($this->building));

        $chartTypes = $this->service->getAvailableChartTypes($this->building);
        $this->assertContains('bar', $chartTypes);
        $this->assertContains('pie', $chartTypes);
        $this->assertContains('radar', $chartTypes);

        // Test Basic package
        $basicPackage = Package::factory()->create([
            'slug' => 'basic',
            'advanced_reporting' => false,
            'custom_reports_enabled' => false,
            'advanced_charts_enabled' => false,
        ]);

        $this->building->update(['current_package_id' => $basicPackage->id]);
        $this->building->refresh();

        $this->assertFalse($this->service->hasAdvancedReporting($this->building));
        $this->assertFalse($this->service->hasCustomReports($this->building));
        $this->assertFalse($this->service->hasAdvancedCharts($this->building));
        $this->assertFalse($this->service->canCreateMoreReports($this->building));

        $chartTypes = $this->service->getAvailableChartTypes($this->building);
        $this->assertEquals(['bar', 'line'], $chartTypes);
    }

    public function test_reporting_stats_api_endpoint()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/advanced-reporting/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'has_advanced_reporting',
                'has_custom_reports',
                'has_scheduling',
                'has_advanced_charts',
                'custom_reports_count',
                'max_custom_reports',
                'can_create_more',
                'total_generations',
                'recent_generations',
                'available_chart_types',
            ]);

        $data = $response->json();
        $this->assertTrue($data['has_advanced_reporting']);
        $this->assertTrue($data['has_custom_reports']);
        $this->assertTrue($data['can_create_more']);
        $this->assertIsArray($data['available_chart_types']);
    }

    public function test_templates_api_endpoint()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/advanced-reporting/templates');

        $response->assertStatus(200);
        $templates = $response->json();
        $this->assertIsArray($templates);
        $this->assertGreaterThan(0, count($templates));
    }

    public function test_custom_report_creation()
    {
        $config = [
            'name' => 'Test Financial Report',
            'description' => 'A test report for financial data',
            'fields' => ['amount', 'created_at', 'expense_type_name'],
            'filters' => [],
            'grouping' => [],
            'chart_config' => [
                'type' => 'bar',
                'title' => 'Test Financial Report'
            ]
        ];

        $report = $this->service->createCustomReport(
            $this->building,
            $this->user,
            $this->template,
            $config
        );

        $this->assertInstanceOf(CustomReport::class, $report);
        $this->assertEquals('Test Financial Report', $report->name);
        $this->assertEquals('active', $report->status);
        $this->assertEquals($this->building->id, $report->building_id);
        $this->assertEquals($this->user->id, $report->created_by);
        $this->assertEquals($this->template->id, $report->template_id);
    }

    public function test_report_data_generation()
    {
        // Create test data
        $expenseType = ExpenseType::factory()->create(['name' => 'Test Expense']);
        
        Expense::factory()->count(3)->create([
            'building_id' => $this->building->id,
            'user_id' => $this->user->id,
            'expense_type_id' => $expenseType->id,
        ]);

        // Create a custom report
        $config = [
            'name' => 'Test Report',
            'fields' => ['amount', 'created_at', 'expense_type_name'],
            'filters' => [],
            'grouping' => [],
        ];

        $report = $this->service->createCustomReport(
            $this->building,
            $this->user,
            $this->template,
            $config
        );

        // Generate report data
        $data = $this->service->generateReportData($report, []);

        $this->assertIsArray($data);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('record_count', $data);
        $this->assertArrayHasKey('generated_at', $data);
        $this->assertEquals(3, $data['record_count']);
        $this->assertCount(3, $data['data']);
    }

    public function test_custom_reports_api_crud()
    {
        // Test creating a report via API
        $reportData = [
            'template_id' => $this->template->id,
            'name' => 'API Test Report',
            'description' => 'Created via API',
            'configuration' => [
                'fields' => ['amount', 'created_at'],
                'filters' => [],
                'grouping' => [],
            ],
            'chart_config' => [
                'type' => 'bar',
                'title' => 'API Test Report'
            ],
            'is_public' => false,
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/advanced-reporting/reports', $reportData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'report' => [
                    'id',
                    'name',
                    'status',
                    'template',
                    'creator',
                ]
            ]);

        $reportId = $response->json('report.id');

        // Test getting the report
        $response = $this->actingAs($this->user)
            ->getJson("/api/advanced-reporting/reports/{$reportId}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'report',
                'analytics',
                'recent_generations',
            ]);

        // Test updating the report
        $updateData = [
            'name' => 'Updated API Test Report',
            'description' => 'Updated via API',
        ];

        $response = $this->actingAs($this->user)
            ->putJson("/api/advanced-reporting/reports/{$reportId}", $updateData);

        $response->assertStatus(200);

        // Test generating report data
        $response = $this->actingAs($this->user)
            ->postJson("/api/advanced-reporting/reports/{$reportId}/generate");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'record_count',
                'generated_at',
            ]);

        // Test deleting the report
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/advanced-reporting/reports/{$reportId}");

        $response->assertStatus(200);

        // Verify the report is deleted
        $this->assertDatabaseMissing('custom_reports', ['id' => $reportId]);
    }

    public function test_unauthorized_access_is_blocked()
    {
        // Create a user with basic package (no advanced reporting)
        $basicPackage = Package::factory()->create([
            'slug' => 'basic',
            'advanced_reporting' => false,
        ]);

        $basicBuilding = Building::factory()->create([
            'current_package_id' => $basicPackage->id,
        ]);

        $basicUser = User::factory()->create([
            'building_id' => $basicBuilding->id,
            'role' => 'admin',
        ]);

        // Test that templates endpoint blocks access
        $response = $this->actingAs($basicUser)
            ->getJson('/api/advanced-reporting/templates');

        $response->assertStatus(403);

        // Test that custom reports endpoint blocks access
        $response = $this->actingAs($basicUser)
            ->getJson('/api/advanced-reporting/reports');

        $response->assertStatus(403);
    }
}
