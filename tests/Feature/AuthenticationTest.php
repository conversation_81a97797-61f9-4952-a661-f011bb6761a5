<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Building;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\Sanctum;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_register_with_existing_building()
    {
        $building = Building::factory()->create();

        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone_number' => '+1234567890',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'A101',
            'building_id' => $building->id,
        ];

        $response = $this->postJson('/api/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'user' => ['id', 'name', 'email', 'apartment_number', 'building_id'],
                'token',
                'message'
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => '<PERSON>',
            'apartment_number' => 'A101',
            'building_id' => $building->id,
        ]);
    }

    /** @test */
    public function user_can_signup_with_new_building()
    {
        $userData = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'phone_number' => '+1234567891',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'building' => [
                'name' => 'Sunset Apartments',
                'address' => '123 Main St',
                'city' => 'New York',
                'postal_code' => '10001',
                'monthly_fee' => 150.00,
                'currency' => 'USD',
            ],
        ];

        $response = $this->postJson('/api/signup', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'user' => ['id', 'name', 'email', 'building_id'],
                'building' => ['id', 'name', 'address', 'monthly_fee'],
                'token'
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Jane Doe',
            'role' => 'admin', // First user becomes admin
        ]);

        $this->assertDatabaseHas('buildings', [
            'name' => 'Sunset Apartments',
            'address' => '123 Main St',
            'monthly_fee' => 150.00,
        ]);
    }

    /** @test */
    public function user_can_login_with_valid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'user' => ['id', 'name', 'email'],
                'token'
            ]);
    }

    /** @test */
    public function user_cannot_login_with_invalid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function authenticated_user_can_logout()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/logout');

        $response->assertStatus(200)
            ->assertJson(['message' => 'Logged out successfully']);
    }

    /** @test */
    public function authenticated_user_can_get_profile()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id', 'name', 'email', 'apartment_number', 'role'
            ]);
    }

    /** @test */
    public function registration_requires_valid_data()
    {
        $response = $this->postJson('/api/register', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'email', 'password', 'apartment_number']);
    }

    /** @test */
    public function signup_requires_valid_building_data()
    {
        $response = $this->postJson('/api/signup', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'building' => [], // Empty building data
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'building.name',
                'building.monthly_fee',
                'building.currency'
            ]);
    }

    /** @test */
    public function email_must_be_unique_for_registration()
    {
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'apartment_number' => 'A101',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }
}
