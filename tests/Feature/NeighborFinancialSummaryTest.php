<?php

namespace Tests\Feature;

use App\Models\Building;
use App\Models\User;
use App\Models\Expense;
use App\Models\Income;
use App\Models\ExpenseType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class NeighborFinancialSummaryTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_get_neighbor_financial_summary()
    {
        // Create a building
        $building = Building::factory()->create();

        // Create an admin user
        $admin = User::factory()->create([
            'role' => 'admin',
            'building_id' => $building->id,
        ]);

        // Create some neighbors
        $neighbor1 = User::factory()->create([
            'role' => 'neighbor',
            'building_id' => $building->id,
            'name' => 'John Do<PERSON>',
            'apartment_number' => '101',
        ]);

        $neighbor2 = User::factory()->create([
            'role' => 'neighbor',
            'building_id' => $building->id,
            'name' => '<PERSON>',
            'apartment_number' => '102',
        ]);

        // Create expense type
        $expenseType = ExpenseType::factory()->create();

        // Create some expenses for neighbors
        Expense::factory()->create([
            'user_id' => $neighbor1->id,
            'building_id' => $building->id,
            'expense_type_id' => $expenseType->id,
            'amount' => 100.00,
            'is_archived' => false,
        ]);

        Expense::factory()->create([
            'user_id' => $neighbor2->id,
            'building_id' => $building->id,
            'expense_type_id' => $expenseType->id,
            'amount' => 150.00,
            'is_archived' => false,
        ]);

        // Create some incomes for neighbors
        Income::factory()->create([
            'user_id' => $neighbor1->id,
            'building_id' => $building->id,
            'amount' => 50.00,
            'is_archived' => false,
        ]);

        Income::factory()->create([
            'user_id' => $neighbor2->id,
            'building_id' => $building->id,
            'amount' => 150.00,
            'is_archived' => false,
        ]);

        // Act as admin and call the API
        $response = $this->actingAs($admin, 'sanctum')
            ->getJson('/api/admin/neighbor-financial-summary');

        // Assert the response
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'neighbor_name',
                'apartment_number',
                'expenses',
                'incomes',
                'outstanding_balance'
            ]
        ]);

        $data = $response->json();
        
        // Verify the data structure and calculations
        $this->assertCount(2, $data);
        
        // Find neighbor1 data
        $neighbor1Data = collect($data)->firstWhere('neighbor_name', 'John Doe');
        $this->assertEquals('101', $neighbor1Data['apartment_number']);
        $this->assertEquals(100.0, $neighbor1Data['expenses']);
        $this->assertEquals(50.0, $neighbor1Data['incomes']);
        $this->assertEquals(50.0, $neighbor1Data['outstanding_balance']);

        // Find neighbor2 data
        $neighbor2Data = collect($data)->firstWhere('neighbor_name', 'Jane Smith');
        $this->assertEquals('102', $neighbor2Data['apartment_number']);
        $this->assertEquals(150.0, $neighbor2Data['expenses']);
        $this->assertEquals(150.0, $neighbor2Data['incomes']);
        $this->assertEquals(0.0, $neighbor2Data['outstanding_balance']);
    }

    public function test_neighbor_cannot_access_financial_summary()
    {
        // Create a building
        $building = Building::factory()->create();

        // Create a neighbor user
        $neighbor = User::factory()->create([
            'role' => 'neighbor',
            'building_id' => $building->id,
        ]);

        // Act as neighbor and try to call the API
        $response = $this->actingAs($neighbor, 'sanctum')
            ->getJson('/api/admin/neighbor-financial-summary');

        // Assert access is denied
        $response->assertStatus(403);
    }

    public function test_admin_without_building_gets_error()
    {
        // Create an admin user without building
        $admin = User::factory()->create([
            'role' => 'admin',
            'building_id' => null,
        ]);

        // Act as admin and call the API
        $response = $this->actingAs($admin, 'sanctum')
            ->getJson('/api/admin/neighbor-financial-summary');

        // Assert error response
        $response->assertStatus(400);
        $response->assertJson(['message' => 'No building assigned']);
    }
}
