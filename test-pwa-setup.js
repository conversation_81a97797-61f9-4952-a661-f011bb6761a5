#!/usr/bin/env node

/**
 * Test PWA setup to ensure all required files exist and are valid
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test results
const results = {
    passed: 0,
    failed: 0,
    tests: []
};

function test(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
        results.passed++;
        console.log(`✅ ${name}`);
    } else {
        results.failed++;
        console.log(`❌ ${name}: ${message}`);
    }
}

function testFileExists(filePath, description) {
    const exists = fs.existsSync(filePath);
    test(
        `${description} exists`,
        exists,
        `File not found: ${filePath}`
    );
    return exists;
}

function testManifestValid() {
    const manifestPath = path.join(__dirname, 'public', 'manifest.json');
    if (!testFileExists(manifestPath, 'PWA Manifest')) {
        return false;
    }

    try {
        const manifestContent = fs.readFileSync(manifestPath, 'utf8');
        const manifest = JSON.parse(manifestContent);
        
        test(
            'Manifest has required fields',
            manifest.name && manifest.short_name && manifest.start_url && manifest.display,
            'Missing required manifest fields'
        );

        test(
            'Manifest has icons',
            manifest.icons && manifest.icons.length > 0,
            'No icons defined in manifest'
        );

        // Test that all referenced icons exist
        let allIconsExist = true;
        if (manifest.icons) {
            manifest.icons.forEach(icon => {
                const iconPath = path.join(__dirname, 'public', icon.src);
                if (!fs.existsSync(iconPath)) {
                    allIconsExist = false;
                    console.log(`   ⚠️  Icon missing: ${icon.src}`);
                }
            });
        }

        test(
            'All manifest icons exist',
            allIconsExist,
            'Some icons referenced in manifest are missing'
        );

        return true;
    } catch (error) {
        test(
            'Manifest is valid JSON',
            false,
            `Invalid JSON: ${error.message}`
        );
        return false;
    }
}

function testServiceWorkerValid() {
    const swPath = path.join(__dirname, 'public', 'sw.js');
    if (!testFileExists(swPath, 'Service Worker')) {
        return false;
    }

    try {
        const swContent = fs.readFileSync(swPath, 'utf8');
        
        test(
            'Service Worker has install event',
            swContent.includes('install') && swContent.includes('addEventListener'),
            'Service worker missing install event handler'
        );

        test(
            'Service Worker has fetch event',
            swContent.includes('fetch') && swContent.includes('addEventListener'),
            'Service worker missing fetch event handler'
        );

        test(
            'Service Worker handles unsupported schemes',
            swContent.includes('protocol') && swContent.includes('http'),
            'Service worker does not filter unsupported URL schemes'
        );

        test(
            'Service Worker has message handler',
            swContent.includes('addEventListener(\'message\'') && swContent.includes('SKIP_WAITING'),
            'Service worker missing message event handler for client communication'
        );

        test(
            'Service Worker has development optimizations',
            swContent.includes('isDevelopment') && swContent.includes('localhost'),
            'Service worker missing development mode optimizations'
        );

        test(
            'Service Worker has cache bypass mechanism',
            swContent.includes('sw-bypass') || swContent.includes('Cache-Control'),
            'Service worker missing cache bypass for development debugging'
        );

        return true;
    } catch (error) {
        test(
            'Service Worker is readable',
            false,
            `Cannot read service worker: ${error.message}`
        );
        return false;
    }
}

function testIconGeneration() {
    const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
    let allIconsExist = true;

    iconSizes.forEach(size => {
        const iconPath = path.join(__dirname, 'public', 'build', 'assets', `icon-${size}x${size}.png`);
        if (!fs.existsSync(iconPath)) {
            allIconsExist = false;
            console.log(`   ⚠️  Missing icon: icon-${size}x${size}.png`);
        }
    });

    test(
        'All PWA icons generated',
        allIconsExist,
        'Some PWA icons are missing'
    );

    return allIconsExist;
}

function main() {
    console.log('🧪 Testing PWA Setup...\n');

    // Test manifest
    testManifestValid();

    // Test service worker
    testServiceWorkerValid();

    // Test icon generation
    testIconGeneration();

    // Test other required files
    testFileExists(path.join(__dirname, 'public', 'favicon.ico'), 'Favicon');
    testFileExists(path.join(__dirname, 'generate-pwa-icons.js'), 'Icon generation script');

    // Summary
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);

    if (results.failed === 0) {
        console.log('\n🎉 All PWA tests passed! Your PWA setup is ready.');
    } else {
        console.log('\n⚠️  Some tests failed. Please fix the issues above.');
        process.exit(1);
    }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
