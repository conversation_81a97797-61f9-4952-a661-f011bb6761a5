import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    base: '/',
    plugins: [
        tailwindcss(),
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
            buildDirectory: 'build',
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
                compilerOptions: {
                    // Enable production optimizations
                    hoistStatic: true,
                    cacheHandlers: true,
                },
            },
            // Enable script setup and other performance features
            script: {
                defineModel: true,
                propsDestructure: true,
            },
        }),
    ],
    build: {
        manifest: true,
        outDir: 'public/build',
        assetsDir: 'assets',
        // Performance optimizations
        rollupOptions: {
            output: {
                // Manual chunk splitting for better caching
                manualChunks: {
                    // Vendor chunk for third-party libraries
                    vendor: ['vue', 'vue-router', 'axios'],
                    // UI components chunk
                    ui: ['@headlessui/vue', '@heroicons/vue'],
                    // Charts and visualization libraries (if used)
                    charts: ['chart.js', 'vue-chartjs'].filter(pkg => {
                        try {
                            require.resolve(pkg);
                            return true;
                        } catch {
                            return false;
                        }
                    }),
                },
                // Optimize chunk file names
                chunkFileNames: 'assets/js/[name]-[hash].js',
                entryFileNames: 'assets/js/[name]-[hash].js',
                assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
            },
        },
        // Enable source maps for production debugging (optional)
        sourcemap: process.env.NODE_ENV === 'development',
        // Optimize bundle size
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: process.env.NODE_ENV === 'production',
                drop_debugger: true,
            },
        },
        // Set chunk size warning limit
        chunkSizeWarningLimit: 1000,
    },
    resolve: {
        alias: {
            '@': '/resources/js',
        },
    },
    server: {
        host: '0.0.0.0',
        port: 5173,
        https: false,
        proxy: {
            '/api': {
                target: process.env.NODE_ENV === 'production'
                    ? 'https://amaretna.com'
                    : 'http://localhost:8000',
                changeOrigin: true,
                secure: process.env.NODE_ENV === 'production',
                rewrite: (path) => path.replace(/^\/api/, '/api'),
            },
        },
    },
});
