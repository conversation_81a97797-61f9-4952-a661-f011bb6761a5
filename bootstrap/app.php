<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'super_admin' => \App\Http\Middleware\SuperAdminMiddleware::class,
            'package.limits' => \App\Http\Middleware\CheckPackageLimits::class,
            'verified' => \App\Http\Middleware\EnsureEmailIsVerified::class,
            'building.access' => \App\Http\Middleware\CheckBuildingAccess::class,
            'export.permissions' => \App\Http\Middleware\CheckExportPermissions::class,
            'track.admin.activity' => \App\Http\Middleware\TrackAdminActivity::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
