<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Send scheduled notifications every hour
        $schedule->command('notifications:send --type=scheduled')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send payment reminders daily at 9 AM
        $schedule->command('notifications:send --type=reminders')
                 ->dailyAt('09:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Process all notifications (scheduled + reminders) twice daily
        $schedule->command('notifications:send --type=all')
                 ->twiceDaily(9, 18) // 9 AM and 6 PM
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up old notifications (older than 30 days) weekly
        $schedule->command('notifications:cleanup')
                 ->weekly()
                 ->sundays()
                 ->at('02:00');

        // Export Queue Processing - Process exports every minute
        $schedule->command('queue:work --queue=exports --stop-when-empty --max-jobs=10 --max-time=60')
                 ->everyMinute()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Export Queue Processing - Process all queues every 5 minutes
        $schedule->command('queue:work --stop-when-empty --max-jobs=20 --max-time=300')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up expired exports daily at 2 AM
        $schedule->command('exports:cleanup')
                 ->daily()
                 ->at('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Retry failed exports every 30 minutes
        $schedule->command('exports:retry-failed')
                 ->everyThirtyMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Handle expired subscriptions daily at 1 AM
        $schedule->command('subscriptions:handle-expired')
                 ->daily()
                 ->at('01:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send subscription expiration warnings daily at 9 AM
        $schedule->command('subscriptions:send-warnings')
                 ->dailyAt('09:00')
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 