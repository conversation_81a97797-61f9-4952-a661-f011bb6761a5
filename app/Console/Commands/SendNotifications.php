<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:send {--type=all : Type of notifications to send (all, scheduled, reminders)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send scheduled notifications and payment reminders';

    protected NotificationService $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info("Starting notification sending process...");

        $totalSent = 0;

        if ($type === 'all' || $type === 'scheduled') {
            $this->info("Processing scheduled notifications...");
            $scheduledSent = $this->notificationService->processScheduledNotifications();
            $this->info("Sent {$scheduledSent} scheduled notifications.");
            $totalSent += $scheduledSent;
        }

        if ($type === 'all' || $type === 'reminders') {
            $this->info("Processing payment reminders...");
            $remindersSent = $this->notificationService->sendPaymentReminders();
            $this->info("Sent {$remindersSent} payment reminders.");
            $totalSent += $remindersSent;
        }

        $this->info("Notification sending process completed. Total sent: {$totalSent}");

        return Command::SUCCESS;
    }
}
