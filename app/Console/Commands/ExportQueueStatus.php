<?php

namespace App\Console\Commands;

use App\Models\ExportRequest;
use App\Services\ExportService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ExportQueueStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exports:status 
                            {--building= : Show status for specific building ID}
                            {--detailed : Show detailed information for each export}
                            {--refresh=0 : Auto-refresh every N seconds (0 to disable)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show export queue status and statistics';

    /**
     * Execute the console command.
     */
    public function handle(ExportService $exportService): int
    {
        $buildingId = $this->option('building');
        $detailed = $this->option('detailed');
        $refresh = (int) $this->option('refresh');

        do {
            if ($refresh > 0) {
                $this->output->write("\033[2J\033[H"); // Clear screen
            }

            $this->displayExportStatus($exportService, $buildingId, $detailed);

            if ($refresh > 0) {
                $this->info("\nRefreshing in {$refresh} seconds... (Press Ctrl+C to stop)");
                sleep($refresh);
            }
        } while ($refresh > 0);

        return Command::SUCCESS;
    }

    /**
     * Display export status information.
     */
    private function displayExportStatus(ExportService $exportService, ?string $buildingId, bool $detailed): void
    {
        $this->info('Export Queue Status - ' . now()->format('Y-m-d H:i:s'));
        $this->line(str_repeat('=', 60));

        // Overall statistics
        $query = ExportRequest::query();
        if ($buildingId) {
            $query->where('building_id', $buildingId);
        }

        $totalExports = $query->count();
        $pendingExports = $query->where('status', 'pending')->count();
        $processingExports = $query->where('status', 'processing')->count();
        $completedExports = $query->where('status', 'completed')->count();
        $failedExports = $query->where('status', 'failed')->count();

        $this->table(
            ['Status', 'Count', 'Percentage'],
            [
                ['Pending', $pendingExports, $totalExports > 0 ? round(($pendingExports / $totalExports) * 100, 1) . '%' : '0%'],
                ['Processing', $processingExports, $totalExports > 0 ? round(($processingExports / $totalExports) * 100, 1) . '%' : '0%'],
                ['Completed', $completedExports, $totalExports > 0 ? round(($completedExports / $totalExports) * 100, 1) . '%' : '0%'],
                ['Failed', $failedExports, $totalExports > 0 ? round(($failedExports / $totalExports) * 100, 1) . '%' : '0%'],
                ['Total', $totalExports, '100%'],
            ]
        );

        // Recent activity (last 24 hours)
        $recentQuery = ExportRequest::query()
            ->where('created_at', '>=', Carbon::now()->subDay());
        
        if ($buildingId) {
            $recentQuery->where('building_id', $buildingId);
        }

        $recentExports = $recentQuery->count();
        $recentCompleted = $recentQuery->where('status', 'completed')->count();
        $recentFailed = $recentQuery->where('status', 'failed')->count();

        $this->info("\nRecent Activity (Last 24 Hours):");
        $this->line("• New exports: {$recentExports}");
        $this->line("• Completed: {$recentCompleted}");
        $this->line("• Failed: {$recentFailed}");

        // Storage information
        $storageStats = $exportService->getStorageStats();
        $this->info("\nStorage Information:");
        $this->line("• Total files: {$storageStats['total_files']}");
        $this->line("• Storage used: {$storageStats['total_size_mb']} MB / {$storageStats['max_storage_mb']} MB");
        $this->line("• Usage: {$storageStats['usage_percentage']}%");

        // Show detailed information if requested
        if ($detailed) {
            $this->showDetailedExports($buildingId);
        }

        // Show active/stuck exports
        $this->showActiveExports($buildingId);
    }

    /**
     * Show detailed export information.
     */
    private function showDetailedExports(?string $buildingId): void
    {
        $this->info("\nDetailed Export Information:");

        $query = ExportRequest::with(['building:id,name', 'user:id,name'])
            ->orderBy('created_at', 'desc')
            ->limit(20);

        if ($buildingId) {
            $query->where('building_id', $buildingId);
        }

        $exports = $query->get();

        if ($exports->isEmpty()) {
            $this->line('No exports found.');
            return;
        }

        $this->table(
            ['ID', 'Building', 'User', 'Type', 'Format', 'Status', 'Created', 'Size'],
            $exports->map(function ($export) {
                return [
                    $export->id,
                    $export->building->name ?? 'N/A',
                    $export->user->name ?? 'N/A',
                    $export->type,
                    $export->format,
                    $export->status,
                    $export->created_at->format('m-d H:i'),
                    $export->file_size_human ?? 'N/A',
                ];
            })->toArray()
        );
    }

    /**
     * Show active/stuck exports that might need attention.
     */
    private function showActiveExports(?string $buildingId): void
    {
        // Find exports that have been processing for too long
        $stuckThreshold = Carbon::now()->subMinutes(config('export.limits.export_timeout_minutes', 10));
        
        $query = ExportRequest::with(['building:id,name', 'user:id,name'])
            ->where('status', 'processing')
            ->where('started_at', '<', $stuckThreshold);

        if ($buildingId) {
            $query->where('building_id', $buildingId);
        }

        $stuckExports = $query->get();

        if ($stuckExports->isNotEmpty()) {
            $this->warn("\nPotentially Stuck Exports (Processing > 10 minutes):");
            $this->table(
                ['ID', 'Building', 'Type', 'Started At', 'Duration'],
                $stuckExports->map(function ($export) {
                    $duration = $export->started_at->diffForHumans(null, true);
                    return [
                        $export->id,
                        $export->building->name ?? 'N/A',
                        $export->type,
                        $export->started_at->format('Y-m-d H:i:s'),
                        $duration,
                    ];
                })->toArray()
            );
        }

        // Show pending exports waiting in queue
        $pendingQuery = ExportRequest::with(['building:id,name', 'user:id,name'])
            ->where('status', 'pending')
            ->orderBy('created_at', 'asc')
            ->limit(10);

        if ($buildingId) {
            $pendingQuery->where('building_id', $buildingId);
        }

        $pendingExports = $pendingQuery->get();

        if ($pendingExports->isNotEmpty()) {
            $this->info("\nNext Pending Exports in Queue:");
            $this->table(
                ['ID', 'Building', 'Type', 'Format', 'Queued At', 'Wait Time'],
                $pendingExports->map(function ($export) {
                    $waitTime = $export->created_at->diffForHumans(null, true);
                    return [
                        $export->id,
                        $export->building->name ?? 'N/A',
                        $export->type,
                        $export->format,
                        $export->created_at->format('m-d H:i'),
                        $waitTime,
                    ];
                })->toArray()
            );
        }
    }
}
