<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SendSubscriptionWarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:send-warnings
                            {--dry-run : Show what notifications would be sent without sending them}
                            {--days= : Specific warning days to process (comma-separated)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send expiration warning notifications to subscription holders';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if (!config('subscription.notifications.enabled', true)) {
            $this->info('Subscription notifications are disabled.');
            return Command::SUCCESS;
        }

        $dryRun = $this->option('dry-run');
        $specificDays = $this->option('days');

        $warningDays = $specificDays
            ? array_map('intval', explode(',', $specificDays))
            : config('subscription.warning_days', [30, 14, 7, 3, 1]);

        $this->info('Processing subscription expiration warnings...');

        $totalSent = 0;

        foreach ($warningDays as $days) {
            $sent = $this->processWarningsForDays($days, $dryRun);
            $totalSent += $sent;
        }

        $this->info("Total notifications " . ($dryRun ? 'would be sent' : 'sent') . ": {$totalSent}");

        return Command::SUCCESS;
    }

    /**
     * Process warnings for specific number of days.
     */
    private function processWarningsForDays(int $days, bool $dryRun): int
    {
        $subscriptions = Subscription::approachingExpiration($days)
            ->with(['building', 'package'])
            ->get();

        if ($subscriptions->isEmpty()) {
            $this->info("No subscriptions expiring in {$days} days.");
            return 0;
        }

        $this->info("Found {$subscriptions->count()} subscriptions expiring in {$days} days:");

        $tableData = $subscriptions->map(function ($subscription) use ($days) {
            return [
                $subscription->id,
                $subscription->building->name ?? 'N/A',
                $subscription->package->name ?? 'N/A',
                $subscription->ends_at->format('Y-m-d'),
                $days,
            ];
        })->toArray();

        $this->table(
            ['ID', 'Building', 'Package', 'Expires', 'Days Warning'],
            $tableData
        );

        if ($dryRun) {
            $this->info("Dry run: Would send {$subscriptions->count()} notifications for {$days}-day warning.");
            return $subscriptions->count();
        }

        $sent = 0;

        foreach ($subscriptions as $subscription) {
            try {
                // Get building admins
                $admins = User::where('building_id', $subscription->building_id)
                    ->where('role', 'admin')
                    ->where('is_active', true)
                    ->get();

                if ($admins->isEmpty()) {
                    $this->warn("No active admins found for building: {$subscription->building->name}");
                    continue;
                }

                // Send notification to each admin
                foreach ($admins as $admin) {
                    $this->sendExpirationWarning($admin, $subscription, $days);
                    $sent++;
                }

                Log::info('Subscription expiration warning sent', [
                    'subscription_id' => $subscription->id,
                    'building_id' => $subscription->building_id,
                    'days_warning' => $days,
                    'admins_notified' => $admins->count(),
                ]);

            } catch (\Exception $e) {
                $this->error("Failed to send warning for subscription {$subscription->id}: " . $e->getMessage());
                Log::error('Failed to send subscription warning', [
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->info("Sent {$sent} notifications for {$days}-day warning.");
        return $sent;
    }

    /**
     * Send expiration warning to a user.
     */
    private function sendExpirationWarning(User $user, Subscription $subscription, int $days): void
    {
        // For now, we'll create a simple database notification
        // This can be extended to include email notifications later

        $notificationData = [
            'type' => 'subscription_expiration_warning',
            'title' => "Subscription Expiring in {$days} Days",
            'message' => "Your {$subscription->package->name} subscription for {$subscription->building->name} will expire on {$subscription->ends_at->format('M j, Y')}.",
            'subscription_id' => $subscription->id,
            'days_remaining' => $days,
            'action_url' => '/admin/package-management',
            'priority' => $days <= 3 ? 'high' : 'medium',
        ];

        // Create database notification
        $user->notifications()->create([
            'id' => Str::uuid(),
            'type' => 'App\\Notifications\\SubscriptionExpirationWarning',
            'data' => $notificationData,
            'created_at' => now(),
        ]);
    }
}