<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Package;

class ShowPackages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'packages:show';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display all available packages with pricing information';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $packages = Package::active()->ordered()->get();

        if ($packages->isEmpty()) {
            $this->error('No packages found.');
            return;
        }

        $this->info('Available Subscription Packages:');
        $this->line('');

        foreach ($packages as $package) {
            $this->line("📦 <fg=cyan>{$package->name}</fg=cyan> ({$package->slug})");
            $this->line("   Description: {$package->description_en}");
            $this->line("   Monthly Price: $<fg=green>{$package->price}</fg=green>");

            if ($package->annual_price) {
                $monthlyEquivalent = round($package->annual_price / 12, 2);
                $savings = round($package->getAnnualSavings(), 2);
                $savingsPercentage = round($package->getAnnualSavingsPercentage(), 1);

                $this->line("   Annual Price: $<fg=green>{$package->annual_price}</fg=green> (${monthlyEquivalent}/month)");
                $this->line("   Annual Savings: $<fg=yellow>{$savings}</fg=yellow> ({$savingsPercentage}% discount)");
            }

            $this->line("   Max Neighbors: " . ($package->max_neighbors ? $package->max_neighbors : 'Unlimited'));
            $this->line("   Storage Limit: " . ($package->storage_limit_gb ? $package->storage_limit_gb . 'GB' : 'Unlimited'));
            $this->line("   Trial Days: {$package->trial_days}");

            if ($package->features) {
                $this->line("   Features: " . implode(', ', $package->features));
            }

            $this->line('');
        }

        $this->info('All packages include 20% discount for annual billing!');
    }
}
