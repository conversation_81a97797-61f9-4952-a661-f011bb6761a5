<?php

namespace App\Console\Commands;

use App\Jobs\GenerateAutomaticExpenses;
use App\Models\ExpenseTemplate;
use Illuminate\Console\Command;

class GenerateAutomaticExpensesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expenses:generate-automatic {--building= : Generate for specific building ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate automatic expenses from active templates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $buildingId = $this->option('building');

        if ($buildingId) {
            $this->info("Generating automatic expenses for building ID: {$buildingId}");
        } else {
            $this->info('Generating automatic expenses for all buildings');
        }

        // Get count of templates that will be processed
        $query = ExpenseTemplate::active()
            ->autoGenerate()
            ->dueForGeneration();

        if ($buildingId) {
            $query->forBuilding($buildingId);
        }

        $templateCount = $query->count();

        if ($templateCount === 0) {
            $this->info('No templates are due for automatic generation.');
            return 0;
        }

        $this->info("Found {$templateCount} templates due for generation.");

        // Dispatch the job
        GenerateAutomaticExpenses::dispatch($buildingId ? (int)$buildingId : null);

        $this->info('Automatic expense generation job has been dispatched.');

        return 0;
    }
}
