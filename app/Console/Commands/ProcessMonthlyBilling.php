<?php

namespace App\Console\Commands;

use App\Services\SubscriptionBillingService;
use Illuminate\Console\Command;

class ProcessMonthlyBilling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:process-monthly {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process monthly billing for all active subscriptions';

    private $subscriptionBillingService;

    /**
     * Create a new command instance.
     */
    public function __construct(SubscriptionBillingService $subscriptionBillingService)
    {
        parent::__construct();
        $this->subscriptionBillingService = $subscriptionBillingService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in dry-run mode - no changes will be made');
        }

        $this->info('Starting monthly billing process...');

        try {
            if ($isDryRun) {
                // In dry-run mode, just show statistics
                $stats = $this->subscriptionBillingService->getSubscriptionStatistics();
                $this->displayStatistics($stats);
                return 0;
            }

            $results = $this->subscriptionBillingService->processMonthlyBilling();

            $this->displayResults($results);

            $successCount = collect($results)->where('status', 'success')->count();
            $errorCount = collect($results)->where('status', 'error')->count();

            $this->info("Monthly billing process completed:");
            $this->info("- Successful: {$successCount}");
            if ($errorCount > 0) {
                $this->error("- Errors: {$errorCount}");
            }

            return $errorCount > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $this->error('Monthly billing process failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Display billing results
     */
    private function displayResults(array $results): void
    {
        if (empty($results)) {
            $this->info('No subscriptions due for billing.');
            return;
        }

        $this->table(
            ['Subscription ID', 'Building', 'Status', 'Details'],
            collect($results)->map(function ($result) {
                return [
                    $result['subscription_id'],
                    $result['building_name'],
                    $result['status'],
                    $result['status'] === 'success'
                        ? ($result['result']['message'] ?? 'Processed successfully')
                        : $result['error']
                ];
            })->toArray()
        );
    }

    /**
     * Display subscription statistics
     */
    private function displayStatistics(array $stats): void
    {
        $this->info('Subscription Statistics:');
        $this->line("Total Subscriptions: {$stats['total_subscriptions']}");
        $this->line("Active Subscriptions: {$stats['active_subscriptions']}");
        $this->line("Cancelled Subscriptions: {$stats['cancelled_subscriptions']}");
        $this->line("Monthly Recurring Revenue: $" . number_format($stats['monthly_recurring_revenue'], 2));
        $this->line("Cancellation Rate: " . number_format($stats['cancellation_rate'], 2) . "%");
    }
}
