<?php

namespace App\Console\Commands;

use App\Models\Building;
use App\Models\Expense;
use App\Models\ExpenseType;
use Illuminate\Console\Command;
use Carbon\Carbon;

class GenerateMonthlySubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:generate-monthly {--month=} {--year=} {--building=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly subscription expenses for buildings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $month = $this->option('month') ?: Carbon::now()->format('m');
        $year = $this->option('year') ?: Carbon::now()->format('Y');
        $buildingId = $this->option('building');

        // Get or create the monthly subscription expense type
        $expenseType = ExpenseType::firstOrCreate(
            ['name' => 'Monthly Subscription'],
            ['description' => 'Monthly subscription fee per building']
        );

        // Get buildings based on filter
        $buildingsQuery = Building::query();

        if ($buildingId) {
            $buildingsQuery->where('id', $buildingId);
        }

        $buildings = $buildingsQuery->with(['users' => function ($query) {
            $query->where('role', 'admin');
        }])->get();

        $created = 0;
        $skipped = 0;
        $buildingStats = [];

        foreach ($buildings as $building) {
            // Skip if building has no admin assigned
            $admin = $building->users->where('role', 'admin')->first();
            if (!$admin) {
                $this->warn("Skipping building {$building->name} - no admin assigned");
                continue;
            }

            // Skip if this is the building's first month (subscription starts next month)
            $buildingCreatedMonth = Carbon::parse($building->created_at)->format('m');
            $buildingCreatedYear = Carbon::parse($building->created_at)->format('Y');

            if ($buildingCreatedMonth == $month && $buildingCreatedYear == $year) {
                $this->info("Skipping building {$building->name} - first month, subscription starts next month");
                $skipped++;
                continue;
            }

            // Check if subscription expense already exists for this building, month, and year
            $existingExpense = Expense::where('building_id', $building->id)
                ->where('expense_type_id', $expenseType->id)
                ->where('month', $month)
                ->where('year', $year)
                ->where('is_automatic', true)
                ->first();

            if ($existingExpense) {
                $skipped++;
                continue;
            }

            // Create the monthly subscription expense
            Expense::create([
                'expense_type_id' => $expenseType->id,
                'user_id' => $admin->id,
                'building_id' => $building->id,
                'amount' => $subscriptionFee,
                'due_date' => Carbon::createFromDate($year, $month, 1)->endOfMonth(),
                'month' => $month,
                'year' => $year,
                'notes' => 'Monthly subscription fee for building',
                'is_automatic' => true,
            ]);

            $created++;

            // Track stats per building
            $buildingStats[$building->name] = ['fee' => $subscriptionFee];
        }

        $this->info("Monthly subscription expenses generated for {$month}/{$year}:");
        $this->info("Created: {$created} expenses");
        $this->info("Skipped: {$skipped} expenses (already exist)");

        if (!empty($buildingStats)) {
            $this->info("\nBreakdown by building:");
            foreach ($buildingStats as $buildingName => $stats) {
                $this->info("- {$buildingName}: {$stats['fee']}");
            }
        }

        return Command::SUCCESS;
    }
}
