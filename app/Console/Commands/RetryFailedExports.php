<?php

namespace App\Console\Commands;

use App\Models\ExportRequest;
use App\Jobs\ProcessExportJob;
use Illuminate\Console\Command;
use Carbon\Carbon;

class RetryFailedExports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exports:retry-failed 
                            {--hours=24 : Retry exports failed within the last X hours}
                            {--limit=10 : Maximum number of exports to retry}
                            {--building= : Retry exports for specific building ID only}
                            {--type= : Retry exports of specific type only}
                            {--dry-run : Show what would be retried without actually retrying}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed export requests';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $hours = (int) $this->option('hours');
        $limit = (int) $this->option('limit');
        $buildingId = $this->option('building');
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');

        $this->info("Searching for failed exports from the last {$hours} hours...");

        // Build query for failed exports
        $query = ExportRequest::with(['building:id,name', 'user:id,name'])
            ->where('status', 'failed')
            ->where('created_at', '>=', Carbon::now()->subHours($hours))
            ->orderBy('created_at', 'desc');

        if ($buildingId) {
            $query->where('building_id', $buildingId);
        }

        if ($type) {
            $query->where('type', $type);
        }

        $failedExports = $query->limit($limit)->get();

        if ($failedExports->isEmpty()) {
            $this->info('No failed exports found to retry.');
            return Command::SUCCESS;
        }

        $this->info("Found {$failedExports->count()} failed exports to retry.");

        if ($dryRun) {
            $this->table(
                ['ID', 'Building', 'Type', 'Format', 'Failed At', 'Error'],
                $failedExports->map(function ($export) {
                    return [
                        $export->id,
                        $export->building->name ?? 'N/A',
                        $export->type,
                        $export->format,
                        $export->completed_at?->format('Y-m-d H:i:s') ?? 'N/A',
                        substr($export->error_message ?? 'Unknown error', 0, 50) . '...'
                    ];
                })->toArray()
            );
            
            $this->info('Dry run completed. Use without --dry-run to actually retry these exports.');
            return Command::SUCCESS;
        }

        $retried = 0;
        $skipped = 0;

        foreach ($failedExports as $export) {
            try {
                // Reset the export status
                $export->update([
                    'status' => 'pending',
                    'error_message' => null,
                    'started_at' => null,
                    'completed_at' => null,
                ]);

                // Dispatch the job again
                ProcessExportJob::dispatch($export)
                    ->onQueue(config('export.queue.queue_name', 'exports'));
                
                $this->info("Retrying export ID {$export->id} ({$export->type}) for {$export->building->name}");
                $retried++;
                
            } catch (\Exception $e) {
                $this->error("Failed to retry export ID {$export->id}: " . $e->getMessage());
                $skipped++;
            }
        }

        $this->info("Retry completed: {$retried} exports queued for retry, {$skipped} skipped.");
        
        return Command::SUCCESS;
    }
}
