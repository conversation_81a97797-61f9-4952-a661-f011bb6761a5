<?php

namespace App\Console\Commands;

use App\Services\ExportService;
use Illuminate\Console\Command;

class CleanupExpiredExports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exports:cleanup 
                            {--dry-run : Show what would be cleaned up without actually deleting files}
                            {--force : Force cleanup even if files are recently accessed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired export files to free up storage space';

    /**
     * Execute the console command.
     */
    public function handle(ExportService $exportService): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Starting export cleanup process...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will actually be deleted');
        }

        // Get storage stats before cleanup
        $statsBefore = $exportService->getStorageStats();
        $this->info("Storage before cleanup: {$statsBefore['total_files']} files, {$statsBefore['total_size_mb']} MB");

        if (!$dryRun) {
            $cleaned = $exportService->cleanupExpiredExports();
            $this->info("Cleaned up {$cleaned} expired export files.");
        } else {
            // Show what would be cleaned up
            $expiredExports = \App\Models\ExportRequest::getExportsForCleanup();
            
            if ($expiredExports->isEmpty()) {
                $this->info('No expired exports found for cleanup.');
                return Command::SUCCESS;
            }

            $this->info("Found {$expiredExports->count()} expired exports for cleanup:");
            
            $this->table(
                ['ID', 'Building', 'Type', 'Format', 'File Size', 'Expired At'],
                $expiredExports->map(function ($export) {
                    return [
                        $export->id,
                        $export->building->name,
                        $export->type,
                        $export->format,
                        $export->file_size_human,
                        $export->expires_at?->format('Y-m-d H:i:s') ?? 'N/A',
                    ];
                })->toArray()
            );
        }

        // Get storage stats after cleanup
        if (!$dryRun) {
            $statsAfter = $exportService->getStorageStats();
            $this->info("Storage after cleanup: {$statsAfter['total_files']} files, {$statsAfter['total_size_mb']} MB");
            
            $savedMB = $statsBefore['total_size_mb'] - $statsAfter['total_size_mb'];
            if ($savedMB > 0) {
                $this->info("Freed up {$savedMB} MB of storage space.");
            }
        }

        $this->info('Export cleanup completed successfully.');
        
        return Command::SUCCESS;
    }
}
