<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\Package;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleExpiredSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:handle-expired
                            {--dry-run : Show what would be processed without making changes}
                            {--force : Force processing even if not in grace period}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle expired subscriptions by moving them to grace period or downgrading to free package';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Processing expired subscriptions...');

        // Step 1: Move expired subscriptions to grace period
        $this->processExpiredToGracePeriod($dryRun);

        // Step 2: Process grace period subscriptions that have exceeded grace period
        $this->processGracePeriodExpired($dryRun, $force);

        $this->info('Expired subscription processing completed.');

        return Command::SUCCESS;
    }

    /**
     * Move expired subscriptions to grace period.
     */
    private function processExpiredToGracePeriod(bool $dryRun): void
    {
        $expiredSubscriptions = Subscription::needingGracePeriod()->get();

        if ($expiredSubscriptions->isEmpty()) {
            $this->info('No subscriptions need to enter grace period.');
            return;
        }

        $this->info("Found {$expiredSubscriptions->count()} subscriptions to move to grace period:");

        $this->table(
            ['ID', 'Building', 'Package', 'Expired Date', 'Status'],
            $expiredSubscriptions->map(function ($subscription) {
                return [
                    $subscription->id,
                    $subscription->building->name ?? 'N/A',
                    $subscription->package->name ?? 'N/A',
                    $subscription->ends_at->format('Y-m-d H:i:s'),
                    $subscription->status,
                ];
            })->toArray()
        );

        if (!$dryRun) {
            $processed = 0;
            DB::beginTransaction();

            try {
                foreach ($expiredSubscriptions as $subscription) {
                    if ($subscription->enterGracePeriod()) {
                        $processed++;

                        Log::info('Subscription entered grace period', [
                            'subscription_id' => $subscription->id,
                            'building_id' => $subscription->building_id,
                            'expired_at' => $subscription->ends_at,
                        ]);
                    }
                }

                DB::commit();
                $this->info("Successfully moved {$processed} subscriptions to grace period.");

            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Error processing expired subscriptions: " . $e->getMessage());
                Log::error('Failed to process expired subscriptions', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        } else {
            $this->info('Dry run: No changes made.');
        }
    }

    /**
     * Process grace period subscriptions that have exceeded grace period.
     */
    private function processGracePeriodExpired(bool $dryRun, bool $force): void
    {
        $gracePeriodDays = config('subscription.grace_period_days', 7);
        $cutoffDate = now()->subDays($gracePeriodDays);

        $query = Subscription::inGracePeriod();

        if (!$force) {
            $query->where('ends_at', '<', $cutoffDate);
        }

        $expiredGraceSubscriptions = $query->get();

        if ($expiredGraceSubscriptions->isEmpty()) {
            $this->info('No grace period subscriptions need processing.');
            return;
        }

        $this->info("Found {$expiredGraceSubscriptions->count()} grace period subscriptions to downgrade:");

        $this->table(
            ['ID', 'Building', 'Package', 'Grace End Date', 'Days Over'],
            $expiredGraceSubscriptions->map(function ($subscription) use ($gracePeriodDays) {
                $graceEndDate = $subscription->ends_at->addDays($gracePeriodDays);
                $daysOver = max(0, now()->diffInDays($graceEndDate, false));

                return [
                    $subscription->id,
                    $subscription->building->name ?? 'N/A',
                    $subscription->package->name ?? 'N/A',
                    $graceEndDate->format('Y-m-d H:i:s'),
                    $daysOver,
                ];
            })->toArray()
        );

        if (!$dryRun) {
            $processed = 0;
            DB::beginTransaction();

            try {
                foreach ($expiredGraceSubscriptions as $subscription) {
                    if ($subscription->exitGracePeriod()) {
                        $processed++;

                        Log::info('Subscription downgraded to free package', [
                            'subscription_id' => $subscription->id,
                            'building_id' => $subscription->building_id,
                            'original_package' => $subscription->package->name,
                        ]);
                    }
                }

                DB::commit();
                $this->info("Successfully downgraded {$processed} subscriptions to free package.");

            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Error processing grace period subscriptions: " . $e->getMessage());
                Log::error('Failed to process grace period subscriptions', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        } else {
            $this->info('Dry run: No changes made.');
        }
    }
}