<?php

namespace App\Console\Commands;

use App\Models\EmailUsage;
use App\Jobs\SendTrackedEmailJob;
use App\Mail\NotificationMail;
use App\Models\Notification;
use Illuminate\Console\Command;
use Carbon\Carbon;

class RetryFailedEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:retry-failed 
                            {--hours=24 : Retry emails failed within the last X hours}
                            {--limit=50 : Maximum number of emails to retry}
                            {--type= : Specific email type to retry}
                            {--dry-run : Show what would be retried without actually retrying}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed email deliveries';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $hours = (int) $this->option('hours');
        $limit = (int) $this->option('limit');
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');

        $this->info("Searching for failed emails from the last {$hours} hours...");

        // Build query for failed emails
        $query = EmailUsage::where('status', 'failed')
            ->where('created_at', '>=', Carbon::now()->subHours($hours))
            ->orderBy('created_at', 'desc');

        if ($type) {
            $query->where('email_type', $type);
        }

        $failedEmails = $query->limit($limit)->get();

        if ($failedEmails->isEmpty()) {
            $this->info('No failed emails found to retry.');
            return Command::SUCCESS;
        }

        $this->info("Found {$failedEmails->count()} failed emails to retry.");

        if ($dryRun) {
            $this->table(
                ['ID', 'Type', 'Recipient', 'Subject', 'Failed At', 'Error'],
                $failedEmails->map(function ($email) {
                    return [
                        $email->id,
                        $email->email_type,
                        $email->recipient_email,
                        $email->subject,
                        $email->updated_at->format('Y-m-d H:i:s'),
                        substr($email->error_message ?? 'Unknown error', 0, 50) . '...'
                    ];
                })->toArray()
            );
            
            $this->info('Dry run completed. Use without --dry-run to actually retry these emails.');
            return Command::SUCCESS;
        }

        $retried = 0;
        $skipped = 0;

        foreach ($failedEmails as $emailUsage) {
            try {
                // Create appropriate mailable based on email type
                $mailable = $this->createMailableForEmailUsage($emailUsage);
                
                if (!$mailable) {
                    $this->warn("Skipping email ID {$emailUsage->id}: Cannot create mailable for type '{$emailUsage->email_type}'");
                    $skipped++;
                    continue;
                }

                // Reset status to queued for retry
                $emailUsage->update([
                    'status' => 'queued',
                    'error_message' => null,
                ]);

                // Dispatch the job
                SendTrackedEmailJob::dispatch($emailUsage, $mailable);
                
                $this->info("Retrying email ID {$emailUsage->id} ({$emailUsage->email_type}) to {$emailUsage->recipient_email}");
                $retried++;
                
            } catch (\Exception $e) {
                $this->error("Failed to retry email ID {$emailUsage->id}: " . $e->getMessage());
                $skipped++;
            }
        }

        $this->info("Retry completed: {$retried} emails queued for retry, {$skipped} skipped.");
        
        return Command::SUCCESS;
    }

    /**
     * Create appropriate mailable for the email usage record.
     */
    private function createMailableForEmailUsage(EmailUsage $emailUsage): ?\Illuminate\Mail\Mailable
    {
        $metadata = $emailUsage->metadata ?? [];

        switch ($emailUsage->email_type) {
            case 'notification':
                if (isset($metadata['notification_id'])) {
                    $notification = Notification::find($metadata['notification_id']);
                    if ($notification) {
                        return new NotificationMail($notification);
                    }
                }
                break;

            case 'payment_confirmation':
                // You would implement this based on your payment confirmation email structure
                // For now, we'll skip these
                break;

            case 'monthly_summary':
                // You would implement this based on your monthly summary email structure
                // For now, we'll skip these
                break;

            case 'package_upgrade':
                // You would implement this based on your package upgrade email structure
                // For now, we'll skip these
                break;

            case 'system_announcement':
                // You would implement this based on your system announcement email structure
                // For now, we'll skip these
                break;

            default:
                $this->warn("Unknown email type: {$emailUsage->email_type}");
                break;
        }

        return null;
    }
}
