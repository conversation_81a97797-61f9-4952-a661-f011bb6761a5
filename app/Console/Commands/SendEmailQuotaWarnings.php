<?php

namespace App\Console\Commands;

use App\Services\EmailUsageService;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class SendEmailQuotaWarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:send-quota-warnings 
                            {--dry-run : Show what warnings would be sent without actually sending them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email quota warning notifications to building admins';

    /**
     * Execute the console command.
     */
    public function handle(EmailUsageService $emailUsageService, NotificationService $notificationService): int
    {
        $dryRun = $this->option('dry-run');

        $this->info('Checking buildings for email quota warnings...');

        $buildingsNeedingWarnings = $emailUsageService->getBuildingsNeedingQuotaWarnings();

        if (empty($buildingsNeedingWarnings)) {
            $this->info('No buildings need quota warnings at this time.');
            return Command::SUCCESS;
        }

        $this->info("Found " . count($buildingsNeedingWarnings) . " buildings needing quota warnings.");

        $warningsSent = 0;

        foreach ($buildingsNeedingWarnings as $buildingData) {
            $building = $buildingData['building'];
            $warnings = $buildingData['warnings'];

            $this->info("Building: {$building->name} (ID: {$building->id})");

            foreach ($warnings as $warning) {
                $this->warn("  - {$warning['level']}: {$warning['message']}");

                if (!$dryRun) {
                    // Get building admins
                    $admins = $building->users()->where('role', 'admin')->get();

                    foreach ($admins as $admin) {
                        $title = match($warning['level']) {
                            'critical' => 'تحذير حرج: حصة البريد الإلكتروني',
                            'warning' => 'تنبيه: حصة البريد الإلكتروني',
                            default => 'إشعار: حصة البريد الإلكتروني'
                        };

                        $message = "عزيزي {$admin->name},\n\n";
                        $message .= "نود إعلامك بأن استخدام البريد الإلكتروني لعمارة {$building->name} قد وصل إلى مستوى يتطلب انتباهك:\n\n";
                        $message .= "• {$warning['message']}\n\n";
                        $message .= "يرجى مراجعة استخدام البريد الإلكتروني أو النظر في ترقية الباقة إذا لزم الأمر.\n\n";
                        $message .= "شكراً لك على استخدام نظام إدارة العمارات أمارة.";

                        $priority = match($warning['level']) {
                            'critical' => 'high',
                            'warning' => 'medium',
                            default => 'low'
                        };

                        try {
                            $notificationService->createNotification(
                                $admin,
                                'email_quota_warning',
                                $title,
                                $message,
                                [
                                    'warning_type' => $warning['type'],
                                    'warning_level' => $warning['level'],
                                    'building_id' => $building->id,
                                ],
                                $priority
                            );

                            $warningsSent++;
                            $this->info("    Sent warning to admin: {$admin->name} ({$admin->email})");

                        } catch (\Exception $e) {
                            $this->error("    Failed to send warning to {$admin->name}: " . $e->getMessage());
                        }
                    }
                }
            }
        }

        if ($dryRun) {
            $this->info('Dry run completed. Use without --dry-run to actually send these warnings.');
        } else {
            $this->info("Quota warnings completed: {$warningsSent} warnings sent.");
        }

        return Command::SUCCESS;
    }
}
