<?php

namespace App\Console\Commands;

use App\Services\CachingService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CacheManagement extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cache:manage 
                            {action : The action to perform (warm, clear, stats, optimize)}
                            {--pattern= : Pattern for selective cache operations}
                            {--building= : Building ID for building-specific operations}';

    /**
     * The console command description.
     */
    protected $description = 'Manage application caches (warm up, clear, stats, optimize)';

    protected CachingService $cachingService;

    public function __construct(CachingService $cachingService)
    {
        parent::__construct();
        $this->cachingService = $cachingService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'warm':
                return $this->warmUpCaches();
            case 'clear':
                return $this->clearCaches();
            case 'stats':
                return $this->showCacheStats();
            case 'optimize':
                return $this->optimizeCaches();
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }
    }

    /**
     * Warm up application caches.
     */
    private function warmUpCaches(): int
    {
        $this->info('Warming up application caches...');

        try {
            $this->cachingService->warmUpCaches();
            $this->info('✅ Caches warmed up successfully');
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to warm up caches: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Clear application caches.
     */
    private function clearCaches(): int
    {
        $pattern = $this->option('pattern');
        $buildingId = $this->option('building');

        if ($buildingId) {
            $this->info("Clearing caches for building {$buildingId}...");
            $this->cachingService->invalidateBuildingCaches((int) $buildingId);
            $this->info('✅ Building caches cleared successfully');
            return 0;
        }

        if ($pattern) {
            $this->info("Clearing caches matching pattern: {$pattern}");
            $this->clearCachesByPattern($pattern);
            return 0;
        }

        // Clear all application caches
        $this->info('Clearing all application caches...');
        
        try {
            // Clear Laravel caches
            $this->call('cache:clear');
            $this->call('config:clear');
            $this->call('route:clear');
            $this->call('view:clear');

            // Clear custom caches
            if (config('cache.default') === 'redis') {
                Redis::flushdb();
                $this->info('✅ Redis cache cleared');
            }

            $this->info('✅ All caches cleared successfully');
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to clear caches: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Show cache statistics.
     */
    private function showCacheStats(): int
    {
        $this->info('Cache Statistics:');
        $this->line('================');

        try {
            $stats = $this->cachingService->getCacheStats();
            
            foreach ($stats as $key => $value) {
                $this->line(sprintf('%-25s: %s', ucfirst(str_replace('_', ' ', $key)), $value));
            }

            // Additional Redis stats if available
            if (config('cache.default') === 'redis') {
                $this->line('');
                $this->info('Redis Statistics:');
                $this->line('================');
                
                $info = Redis::info();
                $importantStats = [
                    'redis_version' => 'Redis Version',
                    'used_memory_human' => 'Used Memory',
                    'connected_clients' => 'Connected Clients',
                    'total_commands_processed' => 'Total Commands',
                    'keyspace_hits' => 'Keyspace Hits',
                    'keyspace_misses' => 'Keyspace Misses',
                ];

                foreach ($importantStats as $key => $label) {
                    if (isset($info[$key])) {
                        $this->line(sprintf('%-25s: %s', $label, $info[$key]));
                    }
                }

                // Calculate hit rate
                if (isset($info['keyspace_hits']) && isset($info['keyspace_misses'])) {
                    $hits = (int) $info['keyspace_hits'];
                    $misses = (int) $info['keyspace_misses'];
                    $total = $hits + $misses;
                    $hitRate = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
                    $this->line(sprintf('%-25s: %s%%', 'Hit Rate', $hitRate));
                }
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to get cache stats: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Optimize caches.
     */
    private function optimizeCaches(): int
    {
        $this->info('Optimizing application caches...');

        try {
            // Clear expired caches
            $this->info('Clearing expired caches...');
            
            // Warm up critical caches
            $this->info('Warming up critical caches...');
            $this->cachingService->warmUpCaches();

            // Optimize Laravel caches
            $this->info('Optimizing Laravel caches...');
            $this->call('config:cache');
            $this->call('route:cache');
            $this->call('view:cache');

            $this->info('✅ Cache optimization completed');
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to optimize caches: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Clear caches by pattern.
     */
    private function clearCachesByPattern(string $pattern): void
    {
        if (config('cache.default') === 'redis') {
            $keys = Redis::keys($pattern);
            if (!empty($keys)) {
                Redis::del($keys);
                $this->info("✅ Cleared " . count($keys) . " cache entries");
            } else {
                $this->info("No cache entries found matching pattern: {$pattern}");
            }
        } else {
            $this->warn('Pattern-based cache clearing is only supported with Redis');
        }
    }
}
