<?php

namespace App\Console\Commands;

use App\Models\Building;
use App\Services\ArchiveService;
use Illuminate\Console\Command;

class ArchiveOldRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'archive:old-records 
                            {--months=12 : Number of months old records should be to be archived}
                            {--building= : Specific building ID to process}
                            {--dry-run : Show what would be archived without actually archiving}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Archive old expense and income records for buildings with Premium packages';

    protected $archiveService;

    public function __construct(ArchiveService $archiveService)
    {
        parent::__construct();
        $this->archiveService = $archiveService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $months = (int) $this->option('months');
        $buildingId = $this->option('building');
        $dryRun = $this->option('dry-run');

        $this->info("Starting archive process for records older than {$months} months...");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No records will actually be archived');
        }

        // Get buildings to process
        $buildingsQuery = Building::with('currentPackage');
        
        if ($buildingId) {
            $buildingsQuery->where('id', $buildingId);
        }

        $buildings = $buildingsQuery->get();

        if ($buildings->isEmpty()) {
            $this->error('No buildings found to process.');
            return 1;
        }

        $totalProcessed = 0;
        $totalArchived = 0;
        $buildingsWithFeature = 0;

        foreach ($buildings as $building) {
            $this->info("Processing building: {$building->name} (ID: {$building->id})");

            // Check if building has archive feature
            if (!$this->archiveService->hasArchiveFeature($building)) {
                $this->warn("  Skipping - Archive feature not available for package: " . 
                           ($building->currentPackage->name ?? 'No package'));
                continue;
            }

            $buildingsWithFeature++;

            if ($dryRun) {
                // Show what would be archived
                $oldExpenses = $building->expenses()
                    ->where('is_archived', false)
                    ->where('created_at', '<', now()->subMonths($months))
                    ->count();

                $oldIncomes = $building->incomes()
                    ->where('is_archived', false)
                    ->where('created_at', '<', now()->subMonths($months))
                    ->count();

                $this->line("  Would archive: {$oldExpenses} expenses, {$oldIncomes} incomes");
                $totalProcessed++;
                continue;
            }

            // Actually archive records
            $result = $this->archiveService->autoArchiveOldRecords($building, $months);

            if ($result['success']) {
                $this->info("  ✓ Archived {$result['archived_count']} records");
                $totalArchived += $result['archived_count'];
            } else {
                $this->error("  ✗ Failed: {$result['message']}");
            }

            $totalProcessed++;
        }

        // Summary
        $this->newLine();
        $this->info('Archive process completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total buildings processed', $totalProcessed],
                ['Buildings with archive feature', $buildingsWithFeature],
                ['Total records archived', $dryRun ? 'N/A (dry run)' : $totalArchived],
            ]
        );

        if ($dryRun) {
            $this->warn('This was a dry run. Use without --dry-run to actually archive records.');
        }

        return 0;
    }
}
