<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\NotificationMail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email? : Email address to send test email to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration by sending a test email';

    protected NotificationService $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        if (!$email) {
            $email = $this->ask('Enter email address to send test email to:');
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address provided.');
            return Command::FAILURE;
        }

        $this->info("Testing email configuration...");
        $this->info("Sending test email to: {$email}");

        try {
            // Test basic email sending
            $this->testBasicEmail($email);
            
            // Test notification email
            $this->testNotificationEmail($email);
            
            $this->info("✅ Email test completed successfully!");
            $this->info("Check the recipient's inbox for test emails.");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("❌ Email test failed: " . $e->getMessage());
            $this->error("Please check your email configuration in .env file.");
            
            $this->displayEmailConfig();
            
            return Command::FAILURE;
        }
    }

    /**
     * Test basic email sending
     */
    private function testBasicEmail($email)
    {
        $this->info("1. Testing basic email sending...");
        
        Mail::raw('This is a test email from Amara Building Management System.', function ($message) use ($email) {
            $message->to($email)
                    ->subject('Test Email - Amara Building Management')
                    ->from(config('mail.from.address'), config('mail.from.name'));
        });
        
        $this->info("   ✅ Basic email sent successfully");
    }

    /**
     * Test notification email
     */
    private function testNotificationEmail($email)
    {
        $this->info("2. Testing notification email template...");
        
        // Create a test notification
        $testNotification = new Notification([
            'type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
            'title' => 'Test Notification Email',
            'message' => 'This is a test notification email to verify that the email notification system is working correctly.',
            'data' => [
                'test' => true,
                'timestamp' => now()->toISOString()
            ],
            'priority' => Notification::PRIORITY_MEDIUM,
            'created_at' => now()
        ]);

        // Create a test user object
        $testUser = new User([
            'name' => 'Test User',
            'email' => $email
        ]);

        $testNotification->setRelation('user', $testUser);

        // Send the notification email
        Mail::to($email)->send(new NotificationMail($testNotification));
        
        $this->info("   ✅ Notification email sent successfully");
    }

    /**
     * Display current email configuration
     */
    private function displayEmailConfig()
    {
        $this->info("\nCurrent email configuration:");
        $this->table(
            ['Setting', 'Value'],
            [
                ['MAIL_MAILER', config('mail.default')],
                ['MAIL_HOST', config('mail.mailers.smtp.host')],
                ['MAIL_PORT', config('mail.mailers.smtp.port')],
                ['MAIL_USERNAME', config('mail.mailers.smtp.username') ? '***' : 'Not set'],
                ['MAIL_PASSWORD', config('mail.mailers.smtp.password') ? '***' : 'Not set'],
                ['MAIL_ENCRYPTION', config('mail.mailers.smtp.encryption') ?? 'None'],
                ['MAIL_FROM_ADDRESS', config('mail.from.address')],
                ['MAIL_FROM_NAME', config('mail.from.name')],
            ]
        );

        $this->info("\nTroubleshooting tips:");
        $this->info("1. Make sure your .env file has correct email settings");
        $this->info("2. For Gmail, use an App Password instead of your regular password");
        $this->info("3. Check if your email provider requires specific settings");
        $this->info("4. Verify that your server can connect to the SMTP server");
        $this->info("5. Check Laravel logs in storage/logs/laravel.log for detailed errors");
    }
}
