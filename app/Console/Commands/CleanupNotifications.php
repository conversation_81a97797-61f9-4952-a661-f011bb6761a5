<?php

namespace App\Console\Commands;

use App\Models\Notification;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:cleanup {--days=30 : Number of days to keep notifications} {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old notifications to keep the database tidy';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("Cleaning up notifications older than {$days} days (before {$cutoffDate->format('Y-m-d H:i:s')})...");
        
        // Get notifications to be deleted
        $query = Notification::where('created_at', '<', $cutoffDate);
        
        // Show statistics
        $totalCount = $query->count();
        $readCount = $query->clone()->whereNotNull('read_at')->count();
        $unreadCount = $totalCount - $readCount;
        
        if ($totalCount === 0) {
            $this->info("No old notifications found to clean up.");
            return Command::SUCCESS;
        }
        
        $this->info("Found {$totalCount} notifications to clean up:");
        $this->info("  - Read notifications: {$readCount}");
        $this->info("  - Unread notifications: {$unreadCount}");
        
        // Show breakdown by type
        $typeBreakdown = $query->clone()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
            
        if (!empty($typeBreakdown)) {
            $this->info("\nBreakdown by type:");
            foreach ($typeBreakdown as $type => $count) {
                $this->info("  - {$type}: {$count}");
            }
        }
        
        if ($dryRun) {
            $this->info("\n🔍 DRY RUN: No notifications were actually deleted.");
            $this->info("Remove the --dry-run flag to perform the actual cleanup.");
            return Command::SUCCESS;
        }
        
        // Confirm deletion for large numbers
        if ($totalCount > 100) {
            if (!$this->confirm("This will delete {$totalCount} notifications. Are you sure you want to continue?")) {
                $this->info("Cleanup cancelled.");
                return Command::SUCCESS;
            }
        }
        
        // Perform the cleanup
        $deletedCount = $query->delete();
        
        $this->info("✅ Successfully deleted {$deletedCount} old notifications.");
        
        // Show remaining notification count
        $remainingCount = Notification::count();
        $this->info("Remaining notifications in database: {$remainingCount}");
        
        return Command::SUCCESS;
    }
}
