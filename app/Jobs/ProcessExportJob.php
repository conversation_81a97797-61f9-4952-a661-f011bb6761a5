<?php

namespace App\Jobs;

use App\Models\ExportRequest;
use App\Services\ReportGeneratorService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public ExportRequest $exportRequest;
    public int $timeout;
    public int $tries;

    /**
     * Create a new job instance.
     */
    public function __construct(ExportRequest $exportRequest)
    {
        $this->exportRequest = $exportRequest;
        $this->timeout = config('export.limits.export_timeout_minutes', 10) * 60;
        $this->tries = config('export.queue.max_tries', 3);
    }

    /**
     * Execute the job.
     */
    public function handle(ReportGeneratorService $reportGenerator): void
    {
        try {
            // Mark export as started
            $this->exportRequest->markAsStarted();

            Log::info('Starting export processing', [
                'export_id' => $this->exportRequest->id,
                'type' => $this->exportRequest->type,
                'format' => $this->exportRequest->format,
            ]);

            // Set memory and time limits for shared hosting
            $this->setResourceLimits();

            // Generate the report
            Log::info('Generating report with parameters', [
                'type' => $this->exportRequest->type,
                'format' => $this->exportRequest->format,
                'building_id' => $this->exportRequest->building->id,
                'parameters' => $this->exportRequest->parameters ?? []
            ]);

            $result = $reportGenerator->generateReport(
                $this->exportRequest->type,
                $this->exportRequest->format,
                $this->exportRequest->building,
                $this->exportRequest->parameters ?? []
            );

            Log::info('Report generated successfully', [
                'content_length' => strlen($result['content']),
                'record_count' => $result['record_count'] ?? 0
            ]);

            // Store the file
            $fileName = $this->generateFileName();
            $filePath = $this->storeFile($result['content'], $fileName);
            $fileSize = strlen($result['content']);

            // Mark export as completed
            $this->exportRequest->markAsCompleted(
                $fileName,
                $filePath,
                $fileSize,
                $result['record_count'] ?? null
            );

            // Send notification if enabled
            $this->sendCompletionNotification();

            Log::info('Export processing completed', [
                'export_id' => $this->exportRequest->id,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'record_count' => $result['record_count'] ?? null,
            ]);

        } catch (\Exception $e) {
            $this->exportRequest->markAsFailed($e->getMessage());

            Log::error('Export processing failed', [
                'export_id' => $this->exportRequest->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Send failure notification if enabled
            $this->sendFailureNotification($e->getMessage());

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $this->exportRequest->markAsFailed($exception->getMessage());

        Log::error('Export job failed permanently', [
            'export_id' => $this->exportRequest->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        $this->sendFailureNotification($exception->getMessage());
    }

    /**
     * Set resource limits for shared hosting.
     */
    private function setResourceLimits(): void
    {
        // Set memory limit based on export format
        $memoryLimit = $this->exportRequest->format === 'excel' 
            ? config('export.excel.memory_limit', '256M')
            : config('export.pdf.dompdf.memory_limit', '128M');

        ini_set('memory_limit', $memoryLimit);

        // Set time limit
        $timeLimit = $this->exportRequest->format === 'excel'
            ? config('export.excel.time_limit', 120)
            : config('export.pdf.dompdf.time_limit', 60);

        set_time_limit($timeLimit);
    }

    /**
     * Generate a unique file name for the export.
     */
    private function generateFileName(): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $extension = $this->exportRequest->format === 'excel' ? 'xlsx' : 'pdf';
        
        return sprintf(
            '%s_%s_%s_%s.%s',
            $this->exportRequest->building->name,
            $this->exportRequest->type,
            $this->exportRequest->id,
            $timestamp,
            $extension
        );
    }

    /**
     * Store the generated file.
     */
    private function storeFile(string $content, string $fileName): string
    {
        $disk = Storage::disk(config('export.storage.disk', 'local'));
        $path = config('export.storage.path', 'exports');
        $filePath = $path . '/' . $fileName;

        // Ensure directory exists
        $disk->makeDirectory($path);

        // Store the file
        $disk->put($filePath, $content);

        return $filePath;
    }

    /**
     * Send completion notification.
     */
    private function sendCompletionNotification(): void
    {
        if (!config('export.notifications.enabled', true)) {
            return;
        }

        // You can implement notification sending here
        // For now, we'll just log it
        Log::info('Export completion notification should be sent', [
            'export_id' => $this->exportRequest->id,
            'user_id' => $this->exportRequest->user_id,
        ]);
    }

    /**
     * Send failure notification.
     */
    private function sendFailureNotification(string $errorMessage): void
    {
        if (!config('export.notifications.enabled', true)) {
            return;
        }

        // You can implement notification sending here
        // For now, we'll just log it
        Log::warning('Export failure notification should be sent', [
            'export_id' => $this->exportRequest->id,
            'user_id' => $this->exportRequest->user_id,
            'error' => $errorMessage,
        ]);
    }
}
