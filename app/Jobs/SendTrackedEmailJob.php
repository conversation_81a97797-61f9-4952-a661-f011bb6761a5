<?php

namespace App\Jobs;

use App\Models\EmailUsage;
use App\Services\EmailUsageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendTrackedEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public EmailUsage $emailUsage;
    public Mailable $mailable;
    public int $maxRetries;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailUsage $emailUsage, Mailable $mailable, int $maxRetries = 3)
    {
        $this->emailUsage = $emailUsage;
        $this->mailable = $mailable;
        $this->maxRetries = $maxRetries;
        
        // Set queue properties
        $this->tries = $maxRetries;
        $this->timeout = 60;
    }

    /**
     * Execute the job.
     */
    public function handle(EmailUsageService $emailUsageService): void
    {
        try {
            // Send the email
            Mail::to($this->emailUsage->recipient_email)->send($this->mailable);
            
            // Mark as sent
            $emailUsageService->markEmailAsSent($this->emailUsage);
            
            Log::info('Tracked email sent successfully', [
                'email_usage_id' => $this->emailUsage->id,
                'building_id' => $this->emailUsage->building_id,
                'email_type' => $this->emailUsage->email_type,
                'recipient' => $this->emailUsage->recipient_email,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send tracked email', [
                'email_usage_id' => $this->emailUsage->id,
                'building_id' => $this->emailUsage->building_id,
                'email_type' => $this->emailUsage->email_type,
                'recipient' => $this->emailUsage->recipient_email,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);
            
            // If this is the final attempt, mark as failed
            if ($this->attempts() >= $this->maxRetries) {
                $emailUsageService->markEmailAsFailed($this->emailUsage, $e->getMessage());
            }
            
            // Re-throw the exception to trigger retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $emailUsageService = app(EmailUsageService::class);
        $emailUsageService->markEmailAsFailed($this->emailUsage, $exception->getMessage());
        
        Log::error('Tracked email job failed permanently', [
            'email_usage_id' => $this->emailUsage->id,
            'building_id' => $this->emailUsage->building_id,
            'email_type' => $this->emailUsage->email_type,
            'recipient' => $this->emailUsage->recipient_email,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
