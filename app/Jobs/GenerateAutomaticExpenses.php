<?php

namespace App\Jobs;

use App\Models\ExpenseTemplate;
use App\Models\Building;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class GenerateAutomaticExpenses implements ShouldQueue
{
    use Queueable;

    protected ?int $buildingId;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $buildingId = null)
    {
        $this->buildingId = $buildingId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting automatic expense generation job', [
            'building_id' => $this->buildingId,
            'timestamp' => now(),
        ]);

        $query = ExpenseTemplate::active()
            ->autoGenerate()
            ->dueForGeneration()
            ->with(['building', 'expenseType']);

        // Filter by specific building if provided
        if ($this->buildingId) {
            $query->forBuilding($this->buildingId);
        }

        $templates = $query->get();

        $totalGenerated = 0;
        $processedBuildings = [];
        $errors = [];

        foreach ($templates as $template) {
            try {
                Log::info('Processing expense template', [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'building_id' => $template->building_id,
                    'building_name' => $template->building->name,
                ]);

                $expenses = $template->generateExpenses();
                $generatedCount = count($expenses);
                $totalGenerated += $generatedCount;

                // Track processed buildings
                if (!in_array($template->building_id, $processedBuildings)) {
                    $processedBuildings[] = $template->building_id;
                }

                Log::info('Successfully generated expenses from template', [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'expenses_generated' => $generatedCount,
                    'building_id' => $template->building_id,
                ]);

            } catch (\Exception $e) {
                $errors[] = [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'building_id' => $template->building_id,
                    'error' => $e->getMessage(),
                ];

                Log::error('Failed to generate expenses from template', [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'building_id' => $template->building_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        Log::info('Completed automatic expense generation job', [
            'templates_processed' => count($templates),
            'total_expenses_generated' => $totalGenerated,
            'buildings_affected' => count($processedBuildings),
            'errors_count' => count($errors),
            'errors' => $errors,
        ]);

        // If there were errors, you might want to notify administrators
        if (!empty($errors)) {
            $this->notifyAdministrators($errors, $totalGenerated, count($templates));
        }
    }

    /**
     * Notify administrators about errors during automatic generation.
     */
    private function notifyAdministrators(array $errors, int $totalGenerated, int $templatesProcessed): void
    {
        // This could send emails, create notifications, etc.
        // For now, we'll just log it
        Log::warning('Automatic expense generation completed with errors', [
            'total_generated' => $totalGenerated,
            'templates_processed' => $templatesProcessed,
            'errors_count' => count($errors),
            'errors' => $errors,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Automatic expense generation job failed', [
            'building_id' => $this->buildingId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
