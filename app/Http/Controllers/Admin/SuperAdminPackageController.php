<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class SuperAdminPackageController extends Controller
{
    /**
     * Get all packages with optional filtering.
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'active_only' => ['nullable', 'boolean'],
            'search' => ['nullable', 'string', 'max:255'],
        ]);

        $query = Package::query();

        if (!empty($validated['active_only'])) {
            $query->where('is_active', true);
        }

        if (!empty($validated['search'])) {
            $search = $validated['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('description_en', 'like', "%{$search}%");
            });
        }

        $packages = $query->ordered()->get();

        return response()->json($packages);
    }

    /**
     * Store a new package.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'name_en' => ['nullable', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:packages,slug'],
            'description' => ['required', 'string'],
            'description_en' => ['nullable', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'annual_price' => ['nullable', 'numeric', 'min:0'],
            'max_neighbors' => ['nullable', 'integer', 'min:1'],
            'notifications_enabled' => ['nullable', 'boolean'],
            'email_notifications_enabled' => ['nullable', 'boolean'],
            'email_limit_per_month' => ['nullable', 'integer', 'min:0'],
            'email_limit_per_day' => ['nullable', 'integer', 'min:0'],
            'email_quota_warnings_enabled' => ['nullable', 'boolean'],
            'sms_notifications_enabled' => ['nullable', 'boolean'],
            'priority_support' => ['nullable', 'boolean'],
            'exports_enabled' => ['nullable', 'boolean'],
            'exports_per_month' => ['nullable', 'integer', 'min:0'],
            'max_records_per_export' => ['nullable', 'integer', 'min:1'],
            'export_formats' => ['nullable', 'array'],
            'export_formats.*' => ['string', 'in:pdf,excel'],
            'file_attachments_enabled' => ['nullable', 'boolean'],
            'storage_limit_gb' => ['nullable', 'integer', 'min:1'],
            'multi_admin_enabled' => ['nullable', 'boolean'],
            'max_admins' => ['nullable', 'integer', 'min:1', 'max:50'],
            'features' => ['nullable', 'array'],
            'limitations' => ['nullable', 'array'],
            'is_active' => ['nullable', 'boolean'],
            'is_popular' => ['nullable', 'boolean'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'trial_days' => ['nullable', 'integer', 'min:0', 'max:365'],
        ]);

        try {
            // Generate slug if not provided
            if (empty($validated['slug'])) {
                $validated['slug'] = Str::slug($validated['name']);

                // Ensure slug is unique
                $originalSlug = $validated['slug'];
                $counter = 1;
                while (Package::where('slug', $validated['slug'])->exists()) {
                    $validated['slug'] = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            // Set default values
            $validated['notifications_enabled'] = $validated['notifications_enabled'] ?? true;
            $validated['email_notifications_enabled'] = $validated['email_notifications_enabled'] ?? true;
            $validated['email_quota_warnings_enabled'] = $validated['email_quota_warnings_enabled'] ?? true;
            $validated['sms_notifications_enabled'] = $validated['sms_notifications_enabled'] ?? false;
            $validated['priority_support'] = $validated['priority_support'] ?? false;
            $validated['file_attachments_enabled'] = $validated['file_attachments_enabled'] ?? true;
            $validated['multi_admin_enabled'] = $validated['multi_admin_enabled'] ?? false;
            $validated['max_admins'] = $validated['max_admins'] ?? 1;
            $validated['is_active'] = $validated['is_active'] ?? true;
            $validated['is_popular'] = $validated['is_popular'] ?? false;
            $validated['sort_order'] = $validated['sort_order'] ?? 0;

            $package = Package::create($validated);

            Log::info('Package created by super admin', [
                'package_id' => $package->id,
                'package_name' => $package->name,
                'created_by' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Package created successfully',
                'package' => $package
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create package', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);

            return response()->json([
                'message' => 'Failed to create package'
            ], 500);
        }
    }

    /**
     * Show a specific package.
     */
    public function show(Package $package): JsonResponse
    {
        return response()->json([
            'package' => $package,
            'subscription_count' => $package->subscriptions()->count(),
            'active_subscription_count' => $package->subscriptions()
                ->whereIn('status', ['active', 'trial'])
                ->count(),
            'total_revenue' => $package->subscriptions()
                ->whereIn('status', ['active', 'cancelled'])
                ->sum('amount')
        ]);
    }

    /**
     * Update a package.
     */
    public function update(Request $request, Package $package): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'name_en' => ['nullable', 'string', 'max:255'],
            'slug' => ['sometimes', 'string', 'max:255', Rule::unique('packages', 'slug')->ignore($package->id)],
            'description' => ['sometimes', 'string'],
            'description_en' => ['nullable', 'string'],
            'price' => ['sometimes', 'numeric', 'min:0'],
            'annual_price' => ['nullable', 'numeric', 'min:0'],
            'max_neighbors' => ['nullable', 'integer', 'min:1'],
            'notifications_enabled' => ['nullable', 'boolean'],
            'email_notifications_enabled' => ['nullable', 'boolean'],
            'email_limit_per_month' => ['nullable', 'integer', 'min:0'],
            'email_limit_per_day' => ['nullable', 'integer', 'min:0'],
            'email_quota_warnings_enabled' => ['nullable', 'boolean'],
            'sms_notifications_enabled' => ['nullable', 'boolean'],
            'priority_support' => ['nullable', 'boolean'],
            'exports_enabled' => ['nullable', 'boolean'],
            'exports_per_month' => ['nullable', 'integer', 'min:0'],
            'max_records_per_export' => ['nullable', 'integer', 'min:1'],
            'export_formats' => ['nullable', 'array'],
            'export_formats.*' => ['string', 'in:pdf,excel'],
            'file_attachments_enabled' => ['nullable', 'boolean'],
            'storage_limit_gb' => ['nullable', 'integer', 'min:1'],
            'multi_admin_enabled' => ['nullable', 'boolean'],
            'max_admins' => ['nullable', 'integer', 'min:1', 'max:50'],
            'features' => ['nullable', 'array'],
            'limitations' => ['nullable', 'array'],
            'is_active' => ['nullable', 'boolean'],
            'is_popular' => ['nullable', 'boolean'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'trial_days' => ['nullable', 'integer', 'min:0', 'max:365'],
        ]);

        try {
            // Update slug if name changed and slug not provided
            if (isset($validated['name']) && !isset($validated['slug'])) {
                $newSlug = Str::slug($validated['name']);

                // Ensure slug is unique
                $originalSlug = $newSlug;
                $counter = 1;
                while (Package::where('slug', $newSlug)->where('id', '!=', $package->id)->exists()) {
                    $newSlug = $originalSlug . '-' . $counter;
                    $counter++;
                }
                $validated['slug'] = $newSlug;
            }

            $package->update($validated);

            Log::info('Package updated by super admin', [
                'package_id' => $package->id,
                'package_name' => $package->name,
                'updated_by' => $request->user()->id,
                'changes' => $validated
            ]);

            return response()->json([
                'message' => 'Package updated successfully',
                'package' => $package->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update package', [
                'package_id' => $package->id,
                'error' => $e->getMessage(),
                'data' => $validated
            ]);

            return response()->json([
                'message' => 'Failed to update package'
            ], 500);
        }
    }

    /**
     * Delete a package.
     */
    public function destroy(Request $request, Package $package): JsonResponse
    {
        // Check if package has active subscriptions
        $activeSubscriptions = $package->subscriptions()
            ->whereIn('status', ['active', 'trial'])
            ->count();

        if ($activeSubscriptions > 0) {
            return response()->json([
                'message' => 'Cannot delete package with active subscriptions',
                'active_subscriptions' => $activeSubscriptions
            ], 400);
        }

        try {
            $packageName = $package->name;
            $packageId = $package->id;

            $package->delete();

            Log::info('Package deleted by super admin', [
                'package_id' => $packageId,
                'package_name' => $packageName,
                'deleted_by' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Package deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete package', [
                'package_id' => $package->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to delete package'
            ], 500);
        }
    }

    /**
     * Toggle package active status.
     */
    public function toggleStatus(Request $request, Package $package): JsonResponse
    {
        try {
            $package->update(['is_active' => !$package->is_active]);

            Log::info('Package status toggled by super admin', [
                'package_id' => $package->id,
                'package_name' => $package->name,
                'new_status' => $package->is_active ? 'active' : 'inactive',
                'updated_by' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Package status updated successfully',
                'package' => $package->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to toggle package status', [
                'package_id' => $package->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to update package status'
            ], 500);
        }
    }

    /**
     * Duplicate a package.
     */
    public function duplicate(Request $request, Package $package): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
        ]);

        try {
            $newPackage = $package->replicate();
            $newPackage->name = $validated['name'];
            $newPackage->slug = Str::slug($validated['name']);

            // Ensure slug is unique
            $originalSlug = $newPackage->slug;
            $counter = 1;
            while (Package::where('slug', $newPackage->slug)->exists()) {
                $newPackage->slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $newPackage->is_active = false; // New packages start as inactive
            $newPackage->save();

            Log::info('Package duplicated by super admin', [
                'original_package_id' => $package->id,
                'new_package_id' => $newPackage->id,
                'new_package_name' => $newPackage->name,
                'created_by' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Package duplicated successfully',
                'package' => $newPackage
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to duplicate package', [
                'package_id' => $package->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to duplicate package'
            ], 500);
        }
    }
}
