<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BuildingExpenseType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SuperAdminBuildingExpenseTypeController extends Controller
{
    /**
     * Get all building expense types with optional filtering.
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'search' => ['nullable', 'string', 'max:255'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        $query = BuildingExpenseType::query();

        if (!empty($validated['search'])) {
            $search = $validated['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Add usage count
        $query->withCount('buildingExpenses');

        $perPage = $validated['per_page'] ?? 15;
        $buildingExpenseTypes = $query->orderBy('name')->paginate($perPage);

        return response()->json($buildingExpenseTypes);
    }

    /**
     * Store a new building expense type.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:building_expense_types,name'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            $buildingExpenseType = BuildingExpenseType::create($validated);

            DB::commit();

            Log::info('Building expense type created by super admin', [
                'building_expense_type_id' => $buildingExpenseType->id,
                'name' => $buildingExpenseType->name,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Building expense type created successfully.',
                'building_expense_type' => $buildingExpenseType->loadCount('buildingExpenses'),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create building expense type', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'data' => $validated,
            ]);

            return response()->json([
                'message' => 'Failed to create building expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show a specific building expense type.
     */
    public function show(BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        $buildingExpenseType->loadCount('buildingExpenses');
        $buildingExpenseType->load(['buildingExpenses' => function ($query) {
            $query->with(['building:id,name'])
                  ->latest()
                  ->limit(10);
        }]);

        return response()->json($buildingExpenseType);
    }

    /**
     * Update a building expense type.
     */
    public function update(Request $request, BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('building_expense_types', 'name')->ignore($buildingExpenseType->id)],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            $oldData = $buildingExpenseType->toArray();
            $buildingExpenseType->update($validated);

            DB::commit();

            Log::info('Building expense type updated by super admin', [
                'building_expense_type_id' => $buildingExpenseType->id,
                'old_data' => $oldData,
                'new_data' => $validated,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Building expense type updated successfully.',
                'building_expense_type' => $buildingExpenseType->fresh()->loadCount('buildingExpenses'),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update building expense type', [
                'building_expense_type_id' => $buildingExpenseType->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'data' => $validated,
            ]);

            return response()->json([
                'message' => 'Failed to update building expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a building expense type.
     */
    public function destroy(BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        try {
            // Check if building expense type is being used
            $expenseCount = $buildingExpenseType->buildingExpenses()->count();

            if ($expenseCount > 0) {
                return response()->json([
                    'message' => "Cannot delete building expense type. It is being used by {$expenseCount} building expense(s).",
                    'expense_count' => $expenseCount,
                ], 422);
            }

            DB::beginTransaction();

            $buildingExpenseTypeData = $buildingExpenseType->toArray();
            $buildingExpenseType->delete();

            DB::commit();

            Log::info('Building expense type deleted by super admin', [
                'building_expense_type_data' => $buildingExpenseTypeData,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Building expense type deleted successfully.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete building expense type', [
                'building_expense_type_id' => $buildingExpenseType->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Failed to delete building expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get statistics for building expense types.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_building_expense_types' => BuildingExpenseType::count(),
            'most_used' => BuildingExpenseType::withCount('buildingExpenses')
                ->orderBy('building_expenses_count', 'desc')
                ->limit(5)
                ->get(),
            'unused' => BuildingExpenseType::doesntHave('buildingExpenses')->count(),
            'recent' => BuildingExpenseType::latest()->limit(5)->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Bulk delete building expense types.
     */
    public function bulkDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['required', 'array', 'min:1'],
            'ids.*' => ['required', 'integer', 'exists:building_expense_types,id'],
        ]);

        try {
            DB::beginTransaction();

            $buildingExpenseTypes = BuildingExpenseType::whereIn('id', $validated['ids'])->get();
            $deletedCount = 0;
            $errors = [];

            foreach ($buildingExpenseTypes as $buildingExpenseType) {
                $expenseCount = $buildingExpenseType->buildingExpenses()->count();

                if ($expenseCount > 0) {
                    $errors[] = "Cannot delete '{$buildingExpenseType->name}' - used by {$expenseCount} building expense(s)";
                    continue;
                }

                $buildingExpenseType->delete();
                $deletedCount++;
            }

            DB::commit();

            Log::info('Bulk delete building expense types by super admin', [
                'deleted_count' => $deletedCount,
                'errors' => $errors,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => "Successfully deleted {$deletedCount} building expense type(s).",
                'deleted_count' => $deletedCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to bulk delete building expense types', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'ids' => $validated['ids'],
            ]);

            return response()->json([
                'message' => 'Failed to delete building expense types.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}