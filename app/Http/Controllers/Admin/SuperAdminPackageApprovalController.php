<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PackageChangeRequest;
use App\Mail\PackageUpgradeNotificationMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SuperAdminPackageApprovalController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'nullable|in:pending,approved,rejected',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = PackageChangeRequest::with([
            'building',
            'requestedBy',
            'currentPackage',
            'requestedPackage',
            'approvedBy'
        ]);

        if (!empty($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        $requests = $query->latest()->paginate($validated['per_page'] ?? 15);

        return response()->json($requests);
    }

    public function show(PackageChangeRequest $request): JsonResponse
    {
        $request->load([
            'building',
            'requestedBy',
            'currentPackage',
            'requestedPackage',
            'approvedBy'
        ]);

        return response()->json($request);
    }

    public function approve(Request $request, PackageChangeRequest $packageRequest): JsonResponse
    {
        $validated = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if (!$packageRequest->isPending()) {
            return response()->json(['message' => 'Request is not pending.'], 400);
        }

        try {
            DB::beginTransaction();

            // Approve the request
            $packageRequest->approve($request->user());

            // Update the building's package
            $building = $packageRequest->building;
            $newPackage = $packageRequest->requestedPackage;
            
            $result = $building->updatePackage($newPackage, $packageRequest->billing_cycle);

            if (!$result) {
                throw new \Exception('Failed to update building package');
            }

            // Send notification email to the building admin
            try {
                Mail::to($packageRequest->requestedBy->email)
                    ->send(new PackageUpgradeNotificationMail($packageRequest, 'approved'));
            } catch (\Exception $e) {
                Log::error('Failed to send package approval email', [
                    'request_id' => $packageRequest->id,
                    'error' => $e->getMessage()
                ]);
            }

            Log::info('Package change request approved', [
                'request_id' => $packageRequest->id,
                'approved_by' => $request->user()->id,
                'building_id' => $building->id,
                'new_package' => $newPackage->name,
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Package change request approved successfully.',
                'request' => $packageRequest->load(['building', 'requestedPackage'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to approve package change request', [
                'request_id' => $packageRequest->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to approve request.'], 500);
        }
    }

    public function reject(Request $request, PackageChangeRequest $packageRequest): JsonResponse
    {
        $validated = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ]);

        if (!$packageRequest->isPending()) {
            return response()->json(['message' => 'Request is not pending.'], 400);
        }

        try {
            DB::beginTransaction();

            // Reject the request
            $packageRequest->reject($request->user(), $validated['admin_notes']);

            // Send notification email to the building admin
            try {
                Mail::to($packageRequest->requestedBy->email)
                    ->send(new PackageUpgradeNotificationMail($packageRequest, 'rejected'));
            } catch (\Exception $e) {
                Log::error('Failed to send package rejection email', [
                    'request_id' => $packageRequest->id,
                    'error' => $e->getMessage()
                ]);
            }

            Log::info('Package change request rejected', [
                'request_id' => $packageRequest->id,
                'rejected_by' => $request->user()->id,
                'admin_notes' => $validated['admin_notes'],
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Package change request rejected successfully.',
                'request' => $packageRequest->load(['building', 'requestedPackage'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to reject package change request', [
                'request_id' => $packageRequest->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to reject request.'], 500);
        }
    }

    public function statistics(): JsonResponse
    {
        $stats = [
            'total_requests' => PackageChangeRequest::count(),
            'pending_requests' => PackageChangeRequest::where('status', 'pending')->count(),
            'approved_requests' => PackageChangeRequest::where('status', 'approved')->count(),
            'rejected_requests' => PackageChangeRequest::where('status', 'rejected')->count(),
        ];

        return response()->json($stats);
    }
} 