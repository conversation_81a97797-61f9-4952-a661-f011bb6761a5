<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Package;

class AdvancedReportingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,super_admin');
    }

    /**
     * Get advanced reporting statistics and capabilities
     */
    public function getStats()
    {
        try {
            $user = Auth::user();
            $building = $user->building;

            // Debug information
            $package = $building ? $building->getEffectivePackage() : null;
            $debugInfo = [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'has_building' => !!$building,
                'building_id' => $building ? $building->id : null,
                'has_package' => !!$package,
                'package_slug' => $package ? $package->slug : null,
                'package_name' => $package ? $package->name : null,
                'package_advanced_reporting' => $package ? $package->advanced_reporting : null
            ];

            // Check if user has advanced reporting capability
            $hasAdvancedReporting = $this->hasAdvancedReporting($user);

            if (!$hasAdvancedReporting) {
                return response()->json([
                    'has_advanced_reporting' => false,
                    'message' => 'Advanced reporting not available for your package',
                    'debug' => $debugInfo
                ]);
            }

            // Get reporting statistics
            $stats = [
                'has_advanced_reporting' => true,
                'can_create_more' => true, // TODO: Check package limits
                'custom_reports_count' => 0, // TODO: Get from database
                'max_custom_reports' => $this->getMaxCustomReports($user),
                'total_generations' => 0, // TODO: Get from database
                'recent_generations' => 0, // TODO: Get from database (last 30 days)
                'available_chart_types' => ['bar', 'line', 'pie', 'table'],
                'debug' => $debugInfo
            ];

            return response()->json($stats);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load reporting stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get analytics data for reporting dashboard
     */
    public function getAnalytics(Request $request)
    {
        try {
            $user = Auth::user();
            $timeRange = $request->get('range', '30d');

            if (!$this->hasAdvancedReporting($user)) {
                return response()->json(['error' => 'Advanced reporting not available'], 403);
            }

            // Mock analytics data for now
            $analytics = [
                'reportsGenerated' => 24,
                'reportsGeneratedChange' => 15,
                'avgGenerationTime' => '2.3s',
                'mostUsedChart' => 'bar_chart',
                'exportsCount' => 8,
                'popularReports' => [
                    [
                        'id' => 1,
                        'name' => 'Monthly Financial Summary',
                        'category' => 'financial',
                        'chart_type' => 'bar',
                        'usage_count' => 12
                    ],
                    [
                        'id' => 2,
                        'name' => 'User Activity Report',
                        'category' => 'analytics',
                        'chart_type' => 'line',
                        'usage_count' => 8
                    ]
                ],
                'insights' => [
                    'bar_charts_most_popular_this_month',
                    'report_generation_increased_15_percent',
                    'financial_reports_generated_most_frequently'
                ]
            ];

            return response()->json($analytics);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load analytics',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Debug endpoint to check user's package information
     */
    public function debugPackageInfo()
    {
        try {
            $user = Auth::user();
            $building = $user->building;

            $debugInfo = [
                'user' => [
                    'id' => $user->id,
                    'role' => $user->role,
                    'building_id' => $user->building_id,
                ],
                'building' => null,
                'current_package' => null,
                'effective_package' => null,
                'active_subscription' => null,
            ];

            if ($building) {
                $debugInfo['building'] = [
                    'id' => $building->id,
                    'name' => $building->name,
                    'current_package_id' => $building->current_package_id,
                ];

                // Check current package
                $currentPackage = $building->currentPackage;
                if ($currentPackage) {
                    $debugInfo['current_package'] = [
                        'id' => $currentPackage->id,
                        'name' => $currentPackage->name,
                        'slug' => $currentPackage->slug,
                        'advanced_reporting' => $currentPackage->advanced_reporting,
                    ];
                }

                // Check effective package
                $effectivePackage = $building->getEffectivePackage();
                if ($effectivePackage) {
                    $debugInfo['effective_package'] = [
                        'id' => $effectivePackage->id,
                        'name' => $effectivePackage->name,
                        'slug' => $effectivePackage->slug,
                        'advanced_reporting' => $effectivePackage->advanced_reporting,
                    ];
                }

                // Check active subscription
                $activeSubscription = $building->getCurrentSubscription();
                if ($activeSubscription) {
                    $debugInfo['active_subscription'] = [
                        'id' => $activeSubscription->id,
                        'status' => $activeSubscription->status,
                        'package_id' => $activeSubscription->package_id,
                        'package_name' => $activeSubscription->package->name ?? null,
                    ];
                }
            }

            $debugInfo['has_advanced_reporting'] = $this->hasAdvancedReporting($user);

            return response()->json($debugInfo);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Debug failed',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Get available data sources for report building
     */
    public function getDataSources()
    {
        try {
            $user = Auth::user();

            if (!$this->hasAdvancedReporting($user)) {
                return response()->json(['error' => 'Advanced reporting not available'], 403);
            }

            $dataSources = [
                [
                    'id' => 'expenses',
                    'name' => 'Expenses',
                    'description' => 'Building expenses and costs',
                    'fields' => [
                        ['id' => 'amount', 'name' => 'Amount', 'type' => 'number'],
                        ['id' => 'category', 'name' => 'Category', 'type' => 'string'],
                        ['id' => 'date', 'name' => 'Date', 'type' => 'date'],
                        ['id' => 'description', 'name' => 'Description', 'type' => 'string']
                    ]
                ],
                [
                    'id' => 'income',
                    'name' => 'Income',
                    'description' => 'Building income and revenue',
                    'fields' => [
                        ['id' => 'amount', 'name' => 'Amount', 'type' => 'number'],
                        ['id' => 'source', 'name' => 'Source', 'type' => 'string'],
                        ['id' => 'date', 'name' => 'Date', 'type' => 'date'],
                        ['id' => 'description', 'name' => 'Description', 'type' => 'string']
                    ]
                ]
            ];

            return response()->json($dataSources);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load data sources',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user has advanced reporting capability
     */
    private function hasAdvancedReporting($user)
    {
        if ($user->role === 'super_admin') {
            return true;
        }

        if ($user->building) {
            $package = $user->building->getEffectivePackage();

            // If no package is found and user is admin, assign Pro package as default
            if (!$package && in_array($user->role, ['admin', 'super_admin'])) {
                $proPackage = \App\Models\Package::where('slug', 'pro')->first();
                if ($proPackage) {
                    $user->building->update(['current_package_id' => $proPackage->id]);
                    $package = $proPackage;
                    \Log::info('Auto-assigned Pro package to admin user', [
                        'user_id' => $user->id,
                        'building_id' => $user->building->id,
                        'package_id' => $proPackage->id
                    ]);
                }
            }

            if ($package) {
                // Debug logging
                \Log::info('Advanced Reporting Check', [
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'building_id' => $user->building->id,
                    'package_slug' => $package->slug,
                    'package_name' => $package->name,
                    'advanced_reporting' => $package->advanced_reporting
                ]);

                return $package->advanced_reporting;
            }
        }

        $package = $user->building ? $user->building->getEffectivePackage() : null;
        \Log::info('Advanced Reporting Check - No building or package', [
            'user_id' => $user->id,
            'user_role' => $user->role,
            'has_building' => !!$user->building,
            'has_package' => !!$package,
            'package_slug' => $package ? $package->slug : null,
            'package_advanced_reporting' => $package ? $package->advanced_reporting : null
        ]);

        return false;
    }

    /**
     * Get maximum custom reports allowed for user's package
     */
    private function getMaxCustomReports($user)
    {
        if ($user->role === 'super_admin') {
            return null; // unlimited
        }

        if ($user->building && $user->building->package) {
            // TODO: Add max_custom_reports field to packages table
            return 10; // default limit
        }

        return 0;
    }
}
