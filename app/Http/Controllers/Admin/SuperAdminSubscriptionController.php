<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Building;
use App\Models\Package;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SuperAdminSubscriptionController extends Controller
{
    /**
     * Get all subscriptions with filtering and pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'status' => ['nullable', Rule::in(['active', 'inactive', 'cancelled', 'expired', 'trial'])],
            'package_id' => ['nullable', 'exists:packages,id'],
            'building_id' => ['nullable', 'exists:buildings,id'],
            'search' => ['nullable', 'string', 'max:255'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        $query = Subscription::with(['building', 'package', 'user'])
            ->latest();

        // Apply filters
        if (!empty($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        if (!empty($validated['package_id'])) {
            $query->where('package_id', $validated['package_id']);
        }

        if (!empty($validated['building_id'])) {
            $query->where('building_id', $validated['building_id']);
        }

        if (!empty($validated['search'])) {
            $search = $validated['search'];
            $query->whereHas('building', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        $perPage = $validated['per_page'] ?? 20;
        $subscriptions = $query->paginate($perPage);

        return response()->json($subscriptions);
    }

    /**
     * Get subscription statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_subscriptions' => Subscription::count(),
            'active_subscriptions' => Subscription::where('status', 'active')->count(),
            'trial_subscriptions' => Subscription::where('status', 'trial')->count(),
            'cancelled_subscriptions' => Subscription::where('status', 'cancelled')->count(),
            'expired_subscriptions' => Subscription::where('status', 'expired')->count(),
            'total_revenue' => Subscription::whereIn('status', ['active', 'cancelled'])
                ->sum('amount'),
            'monthly_revenue' => Subscription::whereIn('status', ['active', 'cancelled'])
                ->where('billing_cycle', 'monthly')
                ->sum('amount'),
            'annual_revenue' => Subscription::whereIn('status', ['active', 'cancelled'])
                ->where('billing_cycle', 'annual')
                ->sum('amount'),
        ];

        // Package distribution
        $packageStats = Package::withCount(['subscriptions' => function ($query) {
            $query->whereIn('status', ['active', 'trial']);
        }])->get()->map(function ($package) {
            return [
                'package_name' => $package->name,
                'subscription_count' => $package->subscriptions_count,
                'revenue' => $package->subscriptions()
                    ->whereIn('status', ['active', 'cancelled'])
                    ->sum('amount'),
            ];
        });

        $stats['package_distribution'] = $packageStats;

        return response()->json($stats);
    }

    /**
     * Create a new subscription for a building.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'building_id' => ['required', 'exists:buildings,id'],
            'package_id' => ['required', 'exists:packages,id'],
            'billing_cycle' => ['required', Rule::in(['monthly', 'annual'])],
            'status' => ['nullable', Rule::in(['active', 'trial'])],
            'trial_days' => ['nullable', 'integer', 'min:0', 'max:365'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $building = Building::findOrFail($validated['building_id']);
        $package = Package::findOrFail($validated['package_id']);

        // Check if building already has an active subscription
        $existingSubscription = $building->getCurrentSubscription();
        if ($existingSubscription && $existingSubscription->isActive()) {
            return response()->json([
                'message' => 'Building already has an active subscription'
            ], 400);
        }

        try {
            DB::beginTransaction();

            $billingCycle = $validated['billing_cycle'];
            $amount = $package->getEffectivePrice($billingCycle);
            $duration = $billingCycle === 'annual' ? 12 : 1;
            $trialDays = $validated['trial_days'] ?? $package->trial_days ?? 0;
            $status = $validated['status'] ?? ($trialDays > 0 ? 'trial' : 'active');

            $subscription = $building->subscriptions()->create([
                'package_id' => $package->id,
                'user_id' => $request->user()->id,
                'status' => $status,
                'billing_cycle' => $billingCycle,
                'amount' => $amount,
                'currency' => 'USD',
                'starts_at' => now(),
                'ends_at' => now()->addMonths($duration),
                'trial_ends_at' => $trialDays > 0 ? now()->addDays($trialDays) : null,
                'next_payment_due' => $status === 'trial' ? now()->addDays($trialDays) : now()->addMonths($duration),
                'auto_renew' => true,
                'notes' => $validated['notes'] ?? null,
            ]);

            // Update building's current package
            $building->update(['current_package_id' => $package->id]);

            DB::commit();

            Log::info('Subscription created by super admin', [
                'subscription_id' => $subscription->id,
                'building_id' => $building->id,
                'package_id' => $package->id,
                'created_by' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Subscription created successfully',
                'subscription' => $subscription->load(['building', 'package', 'user'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to create subscription', [
                'building_id' => $validated['building_id'],
                'package_id' => $validated['package_id'],
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to create subscription'
            ], 500);
        }
    }

    /**
     * Update a subscription.
     */
    public function update(Request $request, Subscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'package_id' => ['nullable', 'exists:packages,id'],
            'billing_cycle' => ['nullable', Rule::in(['monthly', 'annual'])],
            'status' => ['nullable', Rule::in(['active', 'inactive', 'cancelled', 'expired', 'trial'])],
            'auto_renew' => ['nullable', 'boolean'],
            'ends_at' => ['nullable', 'date', 'after:today'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            $updateData = [];

            // Update package if provided
            if (!empty($validated['package_id'])) {
                $newPackage = Package::findOrFail($validated['package_id']);
                $billingCycle = $validated['billing_cycle'] ?? $subscription->billing_cycle;

                $updateData['package_id'] = $newPackage->id;
                $updateData['amount'] = $newPackage->getEffectivePrice($billingCycle);
                $updateData['billing_cycle'] = $billingCycle;

                // Update building's current package
                $subscription->building->update(['current_package_id' => $newPackage->id]);
            }

            // Update other fields
            if (isset($validated['status'])) {
                $updateData['status'] = $validated['status'];
            }

            if (isset($validated['auto_renew'])) {
                $updateData['auto_renew'] = $validated['auto_renew'];
            }

            if (isset($validated['ends_at'])) {
                $updateData['ends_at'] = $validated['ends_at'];
            }

            if (isset($validated['notes'])) {
                $updateData['notes'] = $validated['notes'];
            }

            $subscription->update($updateData);

            DB::commit();

            Log::info('Subscription updated by super admin', [
                'subscription_id' => $subscription->id,
                'updated_by' => $request->user()->id,
                'changes' => $updateData
            ]);

            return response()->json([
                'message' => 'Subscription updated successfully',
                'subscription' => $subscription->fresh(['building', 'package', 'user'])
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to update subscription'
            ], 500);
        }
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(Request $request, Subscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'reason' => ['nullable', 'string', 'max:500'],
            'immediate' => ['nullable', 'boolean'],
        ]);

        if ($subscription->isCancelled()) {
            return response()->json([
                'message' => 'Subscription is already cancelled'
            ], 400);
        }

        try {
            $immediate = $validated['immediate'] ?? false;

            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancellation_reason' => $validated['reason'] ?? 'Cancelled by super admin',
                'auto_renew' => false,
                'ends_at' => $immediate ? now() : $subscription->ends_at,
            ]);

            Log::info('Subscription cancelled by super admin', [
                'subscription_id' => $subscription->id,
                'cancelled_by' => $request->user()->id,
                'reason' => $validated['reason'] ?? 'Cancelled by super admin',
                'immediate' => $immediate
            ]);

            return response()->json([
                'message' => 'Subscription cancelled successfully',
                'subscription' => $subscription->fresh(['building', 'package', 'user'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to cancel subscription'
            ], 500);
        }
    }

    /**
     * Renew a subscription.
     */
    public function renew(Request $request, Subscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'months' => ['nullable', 'integer', 'min:1', 'max:24'],
        ]);

        try {
            $months = $validated['months'] ?? ($subscription->billing_cycle === 'annual' ? 12 : 1);

            $success = $subscription->renew($months);

            if (!$success) {
                return response()->json([
                    'message' => 'Failed to renew subscription'
                ], 500);
            }

            Log::info('Subscription renewed by super admin', [
                'subscription_id' => $subscription->id,
                'renewed_by' => $request->user()->id,
                'months' => $months
            ]);

            return response()->json([
                'message' => 'Subscription renewed successfully',
                'subscription' => $subscription->fresh(['building', 'package', 'user'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to renew subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to renew subscription'
            ], 500);
        }
    }

    /**
     * Get subscription details.
     */
    public function show(Subscription $subscription): JsonResponse
    {
        return response()->json([
            'subscription' => $subscription->load(['building', 'package', 'user']),
            'summary' => $subscription->getSummary()
        ]);
    }
}
