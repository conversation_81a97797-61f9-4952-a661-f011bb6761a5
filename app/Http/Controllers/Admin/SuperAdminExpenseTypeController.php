<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExpenseType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SuperAdminExpenseTypeController extends Controller
{
    /**
     * Get all expense types with optional filtering.
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'search' => ['nullable', 'string', 'max:255'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        $query = ExpenseType::query();

        if (!empty($validated['search'])) {
            $search = $validated['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Add usage count
        $query->withCount('expenses');

        $perPage = $validated['per_page'] ?? 15;
        $expenseTypes = $query->orderBy('name')->paginate($perPage);

        return response()->json($expenseTypes);
    }

    /**
     * Store a new expense type.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:expense_types,name'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            $expenseType = ExpenseType::create($validated);

            DB::commit();

            Log::info('Expense type created by super admin', [
                'expense_type_id' => $expenseType->id,
                'name' => $expenseType->name,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Expense type created successfully.',
                'expense_type' => $expenseType->load('expenses:id,expense_type_id')->loadCount('expenses'),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create expense type', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'data' => $validated,
            ]);

            return response()->json([
                'message' => 'Failed to create expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show a specific expense type.
     */
    public function show(ExpenseType $expenseType): JsonResponse
    {
        $expenseType->loadCount('expenses');
        $expenseType->load(['expenses' => function ($query) {
            $query->with(['building:id,name', 'user:id,name'])
                  ->latest()
                  ->limit(10);
        }]);

        return response()->json($expenseType);
    }

    /**
     * Update an expense type.
     */
    public function update(Request $request, ExpenseType $expenseType): JsonResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('expense_types', 'name')->ignore($expenseType->id)],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        try {
            DB::beginTransaction();

            $oldData = $expenseType->toArray();
            $expenseType->update($validated);

            DB::commit();

            Log::info('Expense type updated by super admin', [
                'expense_type_id' => $expenseType->id,
                'old_data' => $oldData,
                'new_data' => $validated,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Expense type updated successfully.',
                'expense_type' => $expenseType->fresh()->loadCount('expenses'),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update expense type', [
                'expense_type_id' => $expenseType->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'data' => $validated,
            ]);

            return response()->json([
                'message' => 'Failed to update expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete an expense type.
     */
    public function destroy(ExpenseType $expenseType): JsonResponse
    {
        try {
            // Check if expense type is being used
            $expenseCount = $expenseType->expenses()->count();

            if ($expenseCount > 0) {
                return response()->json([
                    'message' => "Cannot delete expense type. It is being used by {$expenseCount} expense(s).",
                    'expense_count' => $expenseCount,
                ], 422);
            }

            DB::beginTransaction();

            $expenseTypeData = $expenseType->toArray();
            $expenseType->delete();

            DB::commit();

            Log::info('Expense type deleted by super admin', [
                'expense_type_data' => $expenseTypeData,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Expense type deleted successfully.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete expense type', [
                'expense_type_id' => $expenseType->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => 'Failed to delete expense type.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get statistics for expense types.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_expense_types' => ExpenseType::count(),
            'most_used' => ExpenseType::withCount('expenses')
                ->orderBy('expenses_count', 'desc')
                ->limit(5)
                ->get(),
            'unused' => ExpenseType::doesntHave('expenses')->count(),
            'recent' => ExpenseType::latest()->limit(5)->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Bulk delete expense types.
     */
    public function bulkDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['required', 'array', 'min:1'],
            'ids.*' => ['required', 'integer', 'exists:expense_types,id'],
        ]);

        try {
            DB::beginTransaction();

            $expenseTypes = ExpenseType::whereIn('id', $validated['ids'])->get();
            $deletedCount = 0;
            $errors = [];

            foreach ($expenseTypes as $expenseType) {
                $expenseCount = $expenseType->expenses()->count();

                if ($expenseCount > 0) {
                    $errors[] = "Cannot delete '{$expenseType->name}' - used by {$expenseCount} expense(s)";
                    continue;
                }

                $expenseType->delete();
                $deletedCount++;
            }

            DB::commit();

            Log::info('Bulk delete expense types by super admin', [
                'deleted_count' => $deletedCount,
                'errors' => $errors,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => "Successfully deleted {$deletedCount} expense type(s).",
                'deleted_count' => $deletedCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to bulk delete expense types', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
                'ids' => $validated['ids'],
            ]);

            return response()->json([
                'message' => 'Failed to delete expense types.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}