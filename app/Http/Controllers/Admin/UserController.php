<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Building;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if ($user->role === 'super_admin') {
            // Super admin sees all users with building relationship
            $users = User::with('building')->latest();
        } else {
            // Regular admin sees only users in their building
            $users = User::with('building')
                ->where('building_id', $user->building_id)
                ->latest();
            
        }
        // Paginate results with optimized page size
        $perPage = min($request->integer('per_page', 50), 100); // Max 100 items per page
        $users = $users->paginate($perPage);

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json($users);
        }

        // Return view for web requests
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:super_admin,admin,neighbor'],
            'building_id' => ['nullable'],
        ]);
        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($validated['role'] === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can create Super Admin users.'], 403);
            }

            // Auto-set building_id for non-super admins
            $validated['building_id'] = $requestingUser->building_id;

            // Check package restrictions for admin role creation
            if ($validated['role'] === 'admin') {
                $building = $requestingUser->building;
                if (!$building) {
                    return response()->json(['message' => 'Building not found.'], 400);
                }

                $package = $building->currentPackage;
                if (!$package) {
                    return response()->json(['message' => 'No package found for building.'], 400);
                }

                // Check if package supports multi-admin using dynamic field
                if (!$package->hasMultiAdminSupport()) {
                    return response()->json(['message' => 'Your package does not support multiple admins.'], 403);
                }

                // Get admin limit from package
                $adminLimit = $package->getMaxAdmins();
                $currentAdminCount = User::where('building_id', $building->id)
                    ->where('role', 'admin')
                    ->count();

                if ($currentAdminCount >= $adminLimit) {
                    return response()->json([
                        'message' => "Admin limit reached ({$currentAdminCount}/{$adminLimit}) for your current package."
                    ], 403);
                }
            }
        }

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone_number' => $validated['phone_number'] ?? null,
            'password' => Hash::make($validated['password']),
            'apartment_number' => $validated['apartment_number'],
            'role' => $validated['role'],
            'building_id' => $validated['role'] === 'super_admin' ? null : ($validated['building_id'] ?? null),
        ]);

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User created successfully.',
                'user' => $user
            ], 201);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:super_admin,admin,neighbor'],
            'building_id' => ['nullable', 'exists:buildings,id'],
            'is_active' => ['nullable', 'boolean']
        ]);

        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($validated['role'] === 'super_admin' || $user->role === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can modify Super Admin users.'], 403);
            }
            if ($user->building_id !== $requestingUser->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update users in your building.'], 403);
            }
            // Auto-set building_id for non-super admins
            $validated['building_id'] = $requestingUser->building_id;

            // Check package restrictions when changing role to admin
            if ($validated['role'] === 'admin' && $user->role !== 'admin') {
                $building = $requestingUser->building;
                if (!$building) {
                    return response()->json(['message' => 'Building not found.'], 400);
                }

                $package = $building->currentPackage;
                if (!$package) {
                    return response()->json(['message' => 'No package found for building.'], 400);
                }

                // Check if package supports multi-admin using dynamic field
                if (!$package->hasMultiAdminSupport()) {
                    return response()->json(['message' => 'Your package does not support multiple admins.'], 403);
                }

                // Get admin limit from package
                $adminLimit = $package->getMaxAdmins();
                $currentAdminCount = User::where('building_id', $building->id)
                    ->where('role', 'admin')
                    ->where('id', '!=', $user->id) // Exclude current user from count
                    ->count();

                if ($currentAdminCount >= $adminLimit) {
                    return response()->json([
                        'message' => "Admin limit reached ({$currentAdminCount}/{$adminLimit}) for your current package."
                    ], 403);
                }
            }
        } else {
            // Super admin is editing - still need to check package limits for the target building
            if ($validated['role'] === 'admin' && $user->role !== 'admin') {
                $targetBuildingId = $validated['building_id'] ?? $user->building_id;
                if ($targetBuildingId) {
                    $building = Building::find($targetBuildingId);
                    if ($building) {
                        $package = $building->currentPackage;
                        if ($package) {
                            // Check if package supports multi-admin using dynamic field
                            if (!$package->hasMultiAdminSupport()) {
                                return response()->json(['message' => 'The target building\'s package does not support multiple admins.'], 403);
                            }

                            // Get admin limit from package
                            $adminLimit = $package->getMaxAdmins();
                            $currentAdminCount = User::where('building_id', $building->id)
                                ->where('role', 'admin')
                                ->where('id', '!=', $user->id) // Exclude current user from count
                                ->count();

                            if ($currentAdminCount >= $adminLimit) {
                                return response()->json([
                                    'message' => "Admin limit reached ({$currentAdminCount}/{$adminLimit}) for the target building's package."
                                ], 403);
                            }
                        }
                    }
                }
            }
        }

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->is_active = $validated['is_active'];
        $user->phone_number = $validated['phone_number'] ?? null;
        $user->apartment_number = $validated['apartment_number'];
        $user->role = $validated['role'];
        $user->building_id = $validated['role'] === 'super_admin' ? null : ($validated['building_id'] ?? $user->building_id);

        if ($request->filled('password')) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User updated successfully.',
                'user' => $user
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(Request $request, User $user)
    {
        // Check if the requesting user is admin (not super_admin) and enforce building_id match
        $requestingUser = $request->user();
        if ($requestingUser->role !== 'super_admin') {
            if ($user->role === 'super_admin') {
                return response()->json(['message' => 'Unauthorized. Only Super Admin can delete Super Admin users.'], 403);
            }
            if ($user->building_id !== $requestingUser->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete users in your building.'], 403);
            }
        }

        $user->delete();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User deleted successfully.'
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Transfer an admin to another building (Super Admin only).
     */
    public function transferAdmin(Request $request, User $user)
    {
        $requestingUser = $request->user();

        // Only super admin can transfer admins
        if ($requestingUser->role !== 'super_admin') {
            return response()->json(['message' => 'Unauthorized. Only Super Admin can transfer admins.'], 403);
        }

        // Can only transfer admin users
        if ($user->role !== 'admin') {
            return response()->json(['message' => 'Can only transfer admin users.'], 400);
        }

        $validated = $request->validate([
            'building_id' => ['required', 'exists:buildings,id'],
        ]);

        $oldBuildingId = $user->building_id;
        $newBuildingId = $validated['building_id'];

        if ($oldBuildingId == $newBuildingId) {
            return response()->json(['message' => 'Admin is already assigned to this building.'], 400);
        }

        try {
            $user->update(['building_id' => $newBuildingId]);

            Log::info('Admin transferred between buildings', [
                'admin_id' => $user->id,
                'admin_email' => $user->email,
                'old_building_id' => $oldBuildingId,
                'new_building_id' => $newBuildingId,
                'transferred_by' => $requestingUser->id
            ]);

            return response()->json([
                'message' => 'Admin transferred successfully',
                'user' => $user->fresh(['building'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to transfer admin', [
                'admin_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to transfer admin'], 500);
        }
    }
}
