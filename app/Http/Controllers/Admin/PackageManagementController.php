<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Building;
use App\Models\Package;
use App\Models\Subscription;
use App\Models\PackageChangeRequest;
use App\Services\StorageUsageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PackageManagementController extends Controller
{
    protected StorageUsageService $storageService;

    public function __construct(StorageUsageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * Get current package information for the admin's building.
     */
    public function getCurrentPackage(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if ($user->role === 'super_admin') {
            return response()->json([
                'message' => 'Super admin does not have package restrictions.',
                'is_super_admin' => true
            ]);
        }

        $building = $user->building;
        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();
        $package = $building->getEffectivePackage();

        // If no package is found and user is admin, assign Pro package as default
        if (!$package && in_array($user->role, ['admin', 'super_admin'])) {
            $proPackage = Package::where('slug', 'pro')->first();
            if ($proPackage) {
                $building->update(['current_package_id' => $proPackage->id]);
                $package = $proPackage;
                \Log::info('Auto-assigned Pro package to admin user in PackageManagementController', [
                    'user_id' => $user->id,
                    'building_id' => $building->id,
                    'package_id' => $proPackage->id
                ]);
            }
        }

        if (!$package) {
            return response()->json(['message' => 'No package found for building.'], 404);
        }

        $storageUsage = $this->storageService->getBuildingStorageUsage($building);

        return response()->json([
            'current_package' => [
                'id' => $package->id,
                'name' => $package->name,
                'name_en' => $package->name_en,
                'slug' => $package->slug,
                'description' => $package->description,
                'description_en' => $package->description_en,
                'price' => $package->price,
                'annual_price' => $package->annual_price,
                'billing_cycle' => $subscription ? $subscription->billing_cycle : 'monthly',
                'features' => $package->getFormattedFeatures(),
                'limitations' => $package->getFormattedLimitations(),
                'max_neighbors' => $package->max_neighbors,
                'unlimited_neighbors' => $package->hasUnlimitedNeighbors(),
                'storage_limit_gb' => $package->storage_limit_gb,
                'unlimited_storage' => $package->hasUnlimitedStorage(),
                'file_attachments_enabled' => $package->file_attachments_enabled,
                'max_file_size_mb' => $package->getMaxFileSizeMb(),
                'max_files_per_record' => $package->getMaxFilesPerRecord(),
                'notifications_enabled' => $package->notifications_enabled,
                'email_notifications_enabled' => $package->email_notifications_enabled,
                'sms_notifications_enabled' => $package->sms_notifications_enabled,
                'priority_support' => $package->priority_support,
                'exports_enabled' => $package->exports_enabled,
            ],
            'subscription' => $subscription ? [
                'id' => $subscription->id,
                'status' => $subscription->status,
                'billing_cycle' => $subscription->billing_cycle,
                'amount' => $subscription->amount,
                'currency' => $subscription->currency,
                'starts_at' => $subscription->starts_at->toISOString(),
                'ends_at' => $subscription->ends_at->toISOString(),
                'days_remaining' => $subscription->getDaysRemaining(),
                'is_trial' => $subscription->isInTrial(),
                'trial_days_remaining' => $subscription->getTrialDaysRemaining(),
                'auto_renew' => $subscription->auto_renew,
                'next_payment_due' => $subscription->next_payment_due ? $subscription->next_payment_due->toISOString() : null,
            ] : null,
            'storage_usage' => $storageUsage,
            'usage_stats' => [
                'neighbors' => [
                    'current' => $building->users()->count(),
                    'limit' => $package->max_neighbors,
                    'percentage' => $package->hasUnlimitedNeighbors() ? 0 : 
                        round(($building->users()->count() / max($package->max_neighbors, 1)) * 100, 1),
                    'unlimited' => $package->hasUnlimitedNeighbors(),
                ],
                'storage' => [
                    'current_gb' => $storageUsage['used_gb'],
                    'limit_gb' => $package->storage_limit_gb,
                    'percentage' => $storageUsage['usage_percentage'],
                    'unlimited' => $package->hasUnlimitedStorage(),
                ],
            ]
        ]);
    }

    /**
     * Get available packages for upgrade/downgrade.
     */
    public function getAvailablePackages(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if ($user->role === 'super_admin') {
            return response()->json([
                'message' => 'Super admin does not need package upgrades.',
                'packages' => []
            ]);
        }

        $building = $user->building;
        $currentPackage = $building ? $building->getEffectivePackage() : null;

        $packages = Package::active()
            ->ordered()
            ->get()
            ->map(function ($package) use ($currentPackage) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'name_en' => $package->name_en,
                    'slug' => $package->slug,
                    'description' => $package->description,
                    'description_en' => $package->description_en,
                    'price' => $package->price,
                    'annual_price' => $package->annual_price,
                    'monthly_equivalent' => $package->getMonthlyEquivalentPrice(),
                    'annual_savings' => $package->getAnnualSavings(),
                    'annual_savings_percentage' => $package->getAnnualSavingsPercentage(),
                    'features' => $package->getFormattedFeatures(),
                    'max_neighbors' => $package->max_neighbors,
                    'unlimited_neighbors' => $package->hasUnlimitedNeighbors(),
                    'storage_limit_gb' => $package->storage_limit_gb,
                    'unlimited_storage' => $package->hasUnlimitedStorage(),
                    'file_attachments_enabled' => $package->file_attachments_enabled,
                    'max_file_size_mb' => $package->getMaxFileSizeMb(),
                    'max_files_per_record' => $package->getMaxFilesPerRecord(),
                    'notifications_enabled' => $package->notifications_enabled,
                    'email_notifications_enabled' => $package->email_notifications_enabled,
                    'priority_support' => $package->priority_support,
                    'is_current' => $currentPackage && $currentPackage->id === $package->id,
                    'is_upgrade' => $currentPackage && $package->price > $currentPackage->price,
                    'is_downgrade' => $currentPackage && $package->price < $currentPackage->price,
                    'is_popular' => $package->is_popular,
                ];
            });

        return response()->json([
            'packages' => $packages,
            'current_package_id' => $currentPackage ? $currentPackage->id : null
        ]);
    }

    /**
     * Get detailed storage usage statistics.
     */
    public function getStorageUsage(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->role === 'super_admin') {
            return response()->json([
                'message' => 'Super admin has unlimited storage.',
                'unlimited' => true
            ]);
        }

        $building = $user->building;
        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $storageUsage = $this->storageService->getBuildingStorageUsage($building);
        $fileTypeBreakdown = $this->storageService->getFileTypeBreakdown($building);
        $monthlyUsage = $this->storageService->getMonthlyStorageUsage($building);

        return response()->json([
            'storage_usage' => $storageUsage,
            'file_type_breakdown' => $fileTypeBreakdown,
            'monthly_usage' => $monthlyUsage,
            'package_limit' => $building->getEffectivePackage()?->storage_limit_gb,
        ]);
    }

    /**
     * Request package upgrade/downgrade.
     */
    public function requestPackageChange(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'package_id' => 'required|exists:packages,id',
            'billing_cycle' => 'required|in:monthly,annual',
            'payment_method' => 'required|in:bank_transfer,cash,credit_card',
            'reason' => 'nullable|string|max:1000',
        ]);

        $user = $request->user();

        if ($user->role === 'super_admin') {
            return response()->json([
                'message' => 'Super admin does not need package changes.'
            ], 400);
        }

        $building = $user->building;
        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        // Check if there's already a pending request
        if ($building->hasPendingPackageChangeRequest()) {
            return response()->json(['message' => 'You already have a pending package change request.'], 400);
        }

        $newPackage = Package::findOrFail($validated['package_id']);
        $currentPackage = $building->getEffectivePackage();

        if ($currentPackage && $currentPackage->id === $newPackage->id) {
            return response()->json(['message' => 'Package is already active.'], 400);
        }

        try {
            DB::beginTransaction();

            // Create package change request instead of immediate upgrade
            $packageChangeRequest = PackageChangeRequest::create([
                'building_id' => $building->id,
                'requested_by_user_id' => $user->id,
                'current_package_id' => $currentPackage?->id,
                'requested_package_id' => $newPackage->id,
                'billing_cycle' => $validated['billing_cycle'],
                'payment_method' => $validated['payment_method'],
                'reason' => $validated['reason'] ?? null,
                'status' => 'pending',
            ]);

            // Log the package change request
            \Log::info('Package change request created', [
                'request_id' => $packageChangeRequest->id,
                'user_id' => $user->id,
                'building_id' => $building->id,
                'old_package' => $currentPackage ? $currentPackage->name : null,
                'new_package' => $newPackage->name,
                'billing_cycle' => $validated['billing_cycle'],
                'payment_method' => $validated['payment_method'],
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Package change request submitted successfully. Awaiting super admin approval.',
                'request_id' => $packageChangeRequest->id,
                'new_package' => $newPackage->name,
                'billing_cycle' => $validated['billing_cycle'],
                'requires_payment' => !$newPackage->isFree(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'An error occurred while processing the request.'], 500);
        }
    }

    /**
     * Get package usage restrictions for current building.
     */
    public function getPackageRestrictions(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->role === 'super_admin') {
            return response()->json([
                'restrictions' => [],
                'unlimited_access' => true
            ]);
        }

        $building = $user->building;
        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $package = $building->getEffectivePackage();
        if (!$package) {
            return response()->json(['message' => 'No package found for building.'], 404);
        }

        $restrictions = [];

        // Check neighbor limits
        if (!$package->hasUnlimitedNeighbors()) {
            $currentNeighbors = $building->users()->count();
            $restrictions['neighbors'] = [
                'limited' => true,
                'current' => $currentNeighbors,
                'limit' => $package->max_neighbors,
                'can_add_more' => $currentNeighbors < $package->max_neighbors,
                'remaining' => max(0, $package->max_neighbors - $currentNeighbors),
            ];
        }

        // Check storage limits
        if (!$package->hasUnlimitedStorage()) {
            $storageUsage = $this->storageService->getBuildingStorageUsage($building);
            $restrictions['storage'] = [
                'limited' => true,
                'current_gb' => $storageUsage['used_gb'],
                'limit_gb' => $package->storage_limit_gb,
                'percentage_used' => $storageUsage['usage_percentage'],
                'remaining_gb' => max(0, $package->storage_limit_gb - $storageUsage['used_gb']),
                'near_limit' => $storageUsage['usage_percentage'] > 80,
            ];
        }

        // Feature restrictions
        $restrictions['features'] = [
            'notifications' => $package->notifications_enabled,
            'email_notifications' => $package->email_notifications_enabled,
            'sms_notifications' => $package->sms_notifications_enabled,
            'file_attachments' => $package->file_attachments_enabled,
            'priority_support' => $package->priority_support,
        ];

        if ($package->file_attachments_enabled) {
            $restrictions['file_limits'] = [
                'max_file_size_mb' => $package->getMaxFileSizeMb(),
                'max_files_per_record' => $package->getMaxFilesPerRecord(),
            ];
        }

        return response()->json([
            'restrictions' => $restrictions,
            'package_name' => $package->name,
            'package_name_en' => $package->name_en,
            'unlimited_access' => false
        ]);
    }

    public function getPendingRequests(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $pendingRequest = $building->pendingPackageChangeRequests()
            ->with(['requestedPackage', 'currentPackage', 'requestedBy'])
            ->first();

        return response()->json([
            'pending_request' => $pendingRequest,
        ]);
    }
}
