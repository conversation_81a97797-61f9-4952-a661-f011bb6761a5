<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class UserPreferencesController extends Controller
{
    /**
     * Get user's email preferences.
     */
    public function getEmailPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'preferences' => $user->getEmailPreferences()
        ]);
    }

    /**
     * Update user's email preferences.
     */
    public function updateEmailPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validated = $request->validate([
            'email_notifications_enabled' => ['boolean'],
            'email_payment_reminders' => ['boolean'],
            'email_expense_notifications' => ['boolean'],
            'email_income_notifications' => ['boolean'],
            'email_general_announcements' => ['boolean'],
            'email_overdue_notifications' => ['boolean'],
            'email_frequency' => ['string', Rule::in(['immediate', 'daily', 'weekly'])],
        ]);

        $user->update($validated);

        return response()->json([
            'message' => 'Email preferences updated successfully.',
            'preferences' => $user->fresh()->getEmailPreferences()
        ]);
    }

    /**
     * Get all user preferences (can be extended for other preferences).
     */
    public function getAllPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'email_preferences' => $user->getEmailPreferences(),
            // Future: Add other preference types here
            // 'notification_preferences' => $user->getNotificationPreferences(),
            // 'privacy_preferences' => $user->getPrivacyPreferences(),
        ]);
    }

    /**
     * Reset email preferences to default values.
     */
    public function resetEmailPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $defaultPreferences = [
            'email_notifications_enabled' => true,
            'email_payment_reminders' => true,
            'email_expense_notifications' => true,
            'email_income_notifications' => true,
            'email_general_announcements' => true,
            'email_overdue_notifications' => true,
            'email_frequency' => 'immediate',
            'last_email_sent_at' => null,
        ];

        $user->update($defaultPreferences);

        return response()->json([
            'message' => 'Email preferences reset to default values.',
            'preferences' => $user->fresh()->getEmailPreferences()
        ]);
    }

    /**
     * Test email notification for the user.
     */
    public function testEmailNotification(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user->email) {
            return response()->json([
                'message' => 'No email address found for your account.'
            ], 400);
        }

        try {
            $emailService = app(\App\Services\EmailService::class);
            $success = $emailService->sendTestEmail($user->email, false);
            
            if ($success) {
                return response()->json([
                    'message' => 'Test email sent successfully. Please check your inbox.',
                    'email' => $user->email
                ]);
            } else {
                return response()->json([
                    'message' => 'Failed to send test email. Please check your email configuration.'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error sending test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get email notification statistics for the user.
     */
    public function getEmailStats(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $stats = [
            'total_notifications' => $user->notifications()->count(),
            'email_sent' => $user->notifications()->where('email_sent', true)->count(),
            'email_pending' => $user->notifications()->where('email_sent', false)->count(),
            'last_email_sent' => $user->last_email_sent_at?->toISOString(),
            'preferences' => $user->getEmailPreferences()
        ];

        $stats['email_success_rate'] = $stats['total_notifications'] > 0 
            ? round(($stats['email_sent'] / $stats['total_notifications']) * 100, 2)
            : 0;

        return response()->json($stats);
    }
}
