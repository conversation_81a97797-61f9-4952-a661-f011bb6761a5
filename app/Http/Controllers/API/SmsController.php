<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Building;
use App\Models\BuildingSmsSettings;
use App\Models\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SmsController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Get SMS settings for building.
     */
    public function getSmsSettings(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $smsSettings = BuildingSmsSettings::getForBuilding($building);
        $supportedProviders = $this->smsService->getSupportedProviders();
        $statistics = $this->smsService->getSmsStatistics($building);

        return response()->json([
            'settings' => $smsSettings,
            'supported_providers' => $supportedProviders,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Update SMS settings for building.
     */
    public function updateSmsSettings(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'sms_enabled' => 'sometimes|boolean',
            'sms_provider' => 'sometimes|string|in:twilio,aws_sns',
            'provider_config' => 'sometimes|array',
            'monthly_sms_limit' => 'sometimes|integer|min:0|max:10000',
            'cost_per_sms' => 'sometimes|numeric|min:0|max:1',
            'notification_types' => 'sometimes|array',
            'require_user_opt_in' => 'sometimes|boolean',
            'default_country_code' => 'sometimes|string|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $smsSettings = BuildingSmsSettings::getForBuilding($building);
            $smsSettings->update($request->only([
                'sms_enabled',
                'sms_provider',
                'provider_config',
                'monthly_sms_limit',
                'cost_per_sms',
                'notification_types',
                'require_user_opt_in',
                'default_country_code',
            ]));

            return response()->json([
                'message' => 'SMS settings updated successfully',
                'settings' => $smsSettings->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update SMS settings',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get user's SMS preferences.
     */
    public function getUserSmsPreferences(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'phone_number' => $user->phone_number,
            'phone_country_code' => $user->phone_country_code,
            'sms_notifications_enabled' => $user->sms_notifications_enabled,
            'sms_preferences' => $user->getSmsPreferences(),
            'formatted_phone_number' => $user->getFormattedPhoneNumber(),
            'sms_stats' => $user->getSmsStats(),
        ]);
    }

    /**
     * Update user's SMS preferences.
     */
    public function updateUserSmsPreferences(Request $request): JsonResponse
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'phone_number' => 'sometimes|string|max:20',
            'phone_country_code' => 'sometimes|string|max:5',
            'sms_notifications_enabled' => 'sometimes|boolean',
            'sms_preferences' => 'sometimes|array',
            'sms_preferences.payment_reminders' => 'sometimes|boolean',
            'sms_preferences.expense_notifications' => 'sometimes|boolean',
            'sms_preferences.income_notifications' => 'sometimes|boolean',
            'sms_preferences.general_announcements' => 'sometimes|boolean',
            'sms_preferences.overdue_notifications' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate phone number if provided
            if ($request->has('phone_number')) {
                $phoneNumber = $request->get('phone_number');
                $countryCode = $request->get('phone_country_code', $user->phone_country_code ?? '+1');
                
                if (!$this->smsService->validatePhoneNumber($phoneNumber, $countryCode)) {
                    return response()->json([
                        'message' => 'Invalid phone number format'
                    ], 422);
                }
            }

            $updateData = $request->only([
                'phone_number',
                'phone_country_code',
                'sms_notifications_enabled',
            ]);

            if ($request->has('sms_preferences')) {
                $user->updateSmsPreferences($request->get('sms_preferences'));
            }

            $user->update($updateData);

            return response()->json([
                'message' => 'SMS preferences updated successfully',
                'user' => [
                    'phone_number' => $user->phone_number,
                    'phone_country_code' => $user->phone_country_code,
                    'sms_notifications_enabled' => $user->sms_notifications_enabled,
                    'sms_preferences' => $user->getSmsPreferences(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update SMS preferences',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Send bulk SMS to building users.
     */
    public function sendBulkSms(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1600',
            'user_ids' => 'sometimes|array',
            'user_ids.*' => 'integer|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->smsService->sendBulkSms(
                $building,
                $request->get('message'),
                $request->get('user_ids')
            );

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send bulk SMS',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get SMS templates for building.
     */
    public function getSmsTemplates(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $templates = SmsTemplate::where(function ($query) use ($building) {
            $query->where('building_id', $building->id)
                  ->orWhere('is_system_template', true);
        })
        ->active()
        ->orderBy('is_system_template', 'asc')
        ->orderBy('name')
        ->get();

        return response()->json([
            'templates' => $templates,
            'character_distribution' => SmsTemplate::getCharacterCountDistribution(),
        ]);
    }

    /**
     * Create SMS template.
     */
    public function createSmsTemplate(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'notification_type' => 'required|string|max:255',
            'template' => 'required|string|max:1600',
            'variables' => 'sometimes|array',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = SmsTemplate::create([
                'building_id' => $building->id,
                'name' => $request->get('name'),
                'notification_type' => $request->get('notification_type'),
                'template' => $request->get('template'),
                'variables' => $request->get('variables', []),
                'is_active' => $request->get('is_active', true),
                'is_system_template' => false,
                'character_count' => mb_strlen($request->get('template')),
            ]);

            // Validate template
            $errors = $template->validateTemplate();
            if (!empty($errors)) {
                return response()->json([
                    'message' => 'Template validation failed',
                    'errors' => $errors
                ], 422);
            }

            return response()->json([
                'message' => 'SMS template created successfully',
                'template' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create SMS template',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get SMS delivery logs.
     */
    public function getDeliveryLogs(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $query = $building->smsDeliveryLogs()
            ->with(['user', 'notification'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('provider')) {
            $query->where('provider', $request->get('provider'));
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->has('days')) {
            $days = (int) $request->get('days', 30);
            $query->where('created_at', '>=', now()->subDays($days));
        }

        $logs = $query->paginate($request->get('per_page', 50));

        return response()->json($logs);
    }

    /**
     * Get SMS statistics.
     */
    public function getSmsStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $days = (int) $request->get('days', 30);
        $statistics = $this->smsService->getSmsStatistics($building, $days);

        return response()->json($statistics);
    }

    /**
     * Test SMS configuration.
     */
    public function testSmsConfiguration(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string',
            'message' => 'sometimes|string|max:160',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $smsSettings = BuildingSmsSettings::getForBuilding($building);
            
            if (!$smsSettings->isSmsEnabled()) {
                return response()->json([
                    'message' => 'SMS is not enabled for this building'
                ], 400);
            }

            if (!$smsSettings->isProviderConfigured()) {
                return response()->json([
                    'message' => 'SMS provider is not properly configured'
                ], 400);
            }

            // Create a test notification
            $notification = \App\Models\Notification::create([
                'user_id' => $user->id,
                'building_id' => $building->id,
                'type' => \App\Models\Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'title' => 'SMS Test',
                'message' => $request->get('message', 'This is a test SMS from your building management system.'),
                'priority' => \App\Models\Notification::PRIORITY_LOW,
            ]);

            // Create test delivery log
            $deliveryLog = \App\Models\SmsDeliveryLog::create([
                'notification_id' => $notification->id,
                'user_id' => $user->id,
                'building_id' => $building->id,
                'phone_number' => $this->smsService->formatPhoneNumber($request->get('phone_number')),
                'provider' => $smsSettings->sms_provider,
                'status' => \App\Models\SmsDeliveryLog::STATUS_PENDING,
                'message_content' => $notification->message,
                'cost' => $smsSettings->cost_per_sms,
            ]);

            // Send test SMS
            $success = $this->smsService->sendViaSmsProvider($deliveryLog, $smsSettings);

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Test SMS sent successfully' : 'Failed to send test SMS',
                'delivery_log' => $deliveryLog->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'SMS test failed',
                'error' => $e->getMessage()
            ], 400);
        }
    }
}
