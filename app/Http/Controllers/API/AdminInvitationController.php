<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\AdminInvitation;
use App\Models\User;
use App\Mail\AdminInvitationMail;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AdminInvitationController extends Controller
{
    /**
     * Get all invitations for the current admin's building.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // Only admins can manage invitations
        if (!in_array($user->role, ['admin', 'super_admin'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = AdminInvitation::with(['building', 'invitedBy']);

        // Super admin can see all invitations, regular admin only their building's
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $invitations = $query->latest()->paginate(20);

        return response()->json($invitations);
    }

    /**
     * Send an admin invitation.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        // Only admins can send invitations
        if (!in_array($user->role, ['admin', 'super_admin'])) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'building_id' => 'nullable|exists:buildings,id',
            'admin_level' => 'nullable|in:primary,secondary',
            'permissions' => 'nullable|array',
        ]);

        // Regular admins can only invite to their own building
        if ($user->role !== 'super_admin') {
            $validated['building_id'] = $user->building_id;
        } else {
            // Super admin must specify a building
            if (empty($validated['building_id'])) {
                return response()->json([
                    'message' => 'Building is required for admin invitations'
                ], 400);
            }
        }

        // Check if user already exists
        $existingUser = User::where('email', $validated['email'])->first();
        if ($existingUser) {
            return response()->json([
                'message' => 'A user with this email already exists'
            ], 400);
        }

        // Check if there's already a pending invitation
        $existingInvitation = AdminInvitation::where('email', $validated['email'])
            ->where('building_id', $validated['building_id'])
            ->where('status', 'pending')
            ->first();

        if ($existingInvitation) {
            return response()->json([
                'message' => 'An invitation has already been sent to this email for this building'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Create the invitation
            $invitation = AdminInvitation::create([
                'email' => $validated['email'],
                'name' => $validated['name'],
                'token' => AdminInvitation::generateToken(),
                'building_id' => $validated['building_id'],
                'invited_by' => $user->id,
                'role' => 'admin',
                'admin_level' => $validated['admin_level'] ?? 'secondary',
                'permissions' => $validated['permissions'] ?? null,
                'expires_at' => now()->addDays(7), // 7 days to accept
            ]);

            // Send invitation email
            Mail::to($validated['email'])->queue(new AdminInvitationMail($invitation));

            DB::commit();

            Log::info('Admin invitation sent', [
                'invitation_id' => $invitation->id,
                'email' => $validated['email'],
                'invited_by' => $user->id,
                'building_id' => $validated['building_id']
            ]);

            return response()->json([
                'message' => 'Invitation sent successfully',
                'invitation' => $invitation->load(['building', 'invitedBy'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to send admin invitation', [
                'error' => $e->getMessage(),
                'email' => $validated['email'],
                'invited_by' => $user->id
            ]);

            return response()->json([
                'message' => 'Failed to send invitation'
            ], 500);
        }
    }

    /**
     * Accept an admin invitation.
     */
    public function accept(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'token' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $invitation = AdminInvitation::where('token', $validated['token'])->first();

        if (!$invitation) {
            return response()->json(['message' => 'Invalid invitation token'], 400);
        }

        if (!$invitation->isValid()) {
            if ($invitation->isExpired()) {
                $invitation->markAsExpired();
                return response()->json(['message' => 'Invitation has expired'], 400);
            }
            return response()->json(['message' => 'Invitation is no longer valid'], 400);
        }

        // Check if user already exists
        $existingUser = User::where('email', $invitation->email)->first();
        if ($existingUser) {
            return response()->json(['message' => 'User already exists'], 400);
        }

        try {
            DB::beginTransaction();

            // Create the admin user
            $user = User::create([
                'name' => $invitation->name,
                'email' => $invitation->email,
                'password' => Hash::make($validated['password']),
                'role' => $invitation->role,
                'building_id' => $invitation->building_id,
                'admin_level' => $invitation->admin_level,
                'admin_permissions' => $invitation->permissions,
                'is_active' => true,
                'apartment_number' => null, // Admins don't need apartment numbers
                'email_verified_at' => now(), // Auto-verify invited admins
            ]);

            // Mark invitation as accepted
            $invitation->markAsAccepted();

            DB::commit();

            Log::info('Admin invitation accepted', [
                'invitation_id' => $invitation->id,
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return response()->json([
                'message' => 'Invitation accepted successfully',
                'user' => $user
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to accept admin invitation', [
                'invitation_id' => $invitation->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to accept invitation'
            ], 500);
        }
    }

    /**
     * Cancel an admin invitation.
     */
    public function cancel(Request $request, AdminInvitation $invitation): JsonResponse
    {
        $user = $request->user();

        // Check access permissions
        if (!$this->canManageInvitation($user, $invitation)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($invitation->status !== 'pending') {
            return response()->json(['message' => 'Cannot cancel this invitation'], 400);
        }

        $invitation->markAsCancelled();

        return response()->json(['message' => 'Invitation cancelled successfully']);
    }

    /**
     * Resend an admin invitation.
     */
    public function resend(Request $request, AdminInvitation $invitation): JsonResponse
    {
        $user = $request->user();

        // Check access permissions
        if (!$this->canManageInvitation($user, $invitation)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($invitation->status !== 'pending') {
            return response()->json(['message' => 'Cannot resend this invitation'], 400);
        }

        try {
            // Update expiration date
            $invitation->update(['expires_at' => now()->addDays(7)]);

            // Resend invitation email
            Mail::to($invitation->email)->queue(new AdminInvitationMail($invitation));

            return response()->json(['message' => 'Invitation resent successfully']);

        } catch (\Exception $e) {
            Log::error('Failed to resend admin invitation', [
                'invitation_id' => $invitation->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to resend invitation'], 500);
        }
    }

    /**
     * Get invitation details by token (for the invitation acceptance page).
     */
    public function show(Request $request): JsonResponse
    {
        $token = $request->query('token');

        if (!$token) {
            return response()->json(['message' => 'Token is required'], 400);
        }

        $invitation = AdminInvitation::with(['building', 'invitedBy'])
            ->where('token', $token)
            ->first();

        if (!$invitation) {
            return response()->json(['message' => 'Invalid invitation token'], 400);
        }

        return response()->json([
            'invitation' => [
                'id' => $invitation->id,
                'name' => $invitation->name,
                'email' => $invitation->email,
                'role' => $invitation->role,
                'status' => $invitation->status,
                'expires_at' => $invitation->expires_at,
                'is_valid' => $invitation->isValid(),
                'is_expired' => $invitation->isExpired(),
                'building' => $invitation->building,
                'invited_by' => $invitation->invitedBy->name,
            ]
        ]);
    }

    /**
     * Check if the user can manage the given invitation.
     */
    private function canManageInvitation(User $user, AdminInvitation $invitation): bool
    {
        // Super admin can manage all invitations
        if ($user->role === 'super_admin') {
            return true;
        }

        // Regular admin can only manage invitations they sent or for their building
        return $invitation->invited_by === $user->id ||
               $invitation->building_id === $user->building_id;
    }
}
