<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\AdminActivityLog;
use App\Models\Expense;
use App\Models\Income;
use App\Models\User;
use App\Services\AdminPermissionService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminManagementController extends Controller
{
    protected $adminPermissionService;

    public function __construct(AdminPermissionService $adminPermissionService)
    {
        $this->adminPermissionService = $adminPermissionService;
    }

    /**
     * Get admin statistics for the current building.
     */
    public function getAdminStats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $stats = $this->adminPermissionService->getAdminStats($building);

        return response()->json($stats);
    }

    /**
     * Get neighbor financial summary with totals for expenses and incomes.
     */
    public function getNeighborFinancialSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Get all neighbors in the building with their financial totals
        $neighbors = User::where('building_id', $building->id)
            ->where('role', 'neighbor')
            ->select('id', 'name', 'apartment_number')
            ->with([
                'expenses' => function ($query) {
                    $query->select('user_id', 'amount');
                },
                'incomes' => function ($query) {
                    $query->select('user_id', 'amount');
                }
            ])
            ->get()
            ->map(function ($neighbor) {
                $totalExpenses = $neighbor->expenses->sum('amount');
                $totalIncomes = $neighbor->incomes->sum('amount');

                return [
                    'neighbor_name' => $neighbor->name,
                    'apartment_number' => $neighbor->apartment_number,
                    'expenses' => (float) $totalExpenses,
                    'incomes' => (float) $totalIncomes,
                    'outstanding_balance' => (float) ($totalExpenses - $totalIncomes)
                ];
            })
            ->sortBy('apartment_number')
            ->values();

        return response()->json($neighbors);
    }

    /**
     * Get enhanced financial summary with charts data.
     */
    public function getFinancialSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $days = $request->get('days', 30);
        $fromDate = $request->get('from_date');
        $toDate = $request->get('to_date');
        $comparisonPeriod = $request->get('comparison_period');

        // Calculate date range
        $endDate = $toDate ? Carbon::parse($toDate) : Carbon::now();
        $startDate = $fromDate ? Carbon::parse($fromDate) : $endDate->copy()->subDays($days);

        // Get expenses and incomes for the period
        $expenses = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $incomes = Income::where('building_id', $building->id)
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->get();

        // Calculate totals
        $totalExpenses = $expenses->sum('amount');
        $totalIncome = $incomes->sum('amount');
        $netBalance = $totalIncome - $totalExpenses;

        // Calculate average monthly expense
        $monthsDiff = max(1, $startDate->diffInMonths($endDate));
        $avgMonthlyExpense = $totalExpenses / $monthsDiff;

        // Get comparison data if requested
        $comparisonData = null;
        if ($comparisonPeriod) {
            $comparisonData = $this->getComparisonData($building, $startDate, $endDate, $comparisonPeriod);
        }

        // Generate chart data
        $chartData = $this->generateChartData($building, $startDate, $endDate);
        $monthlyData = $this->generateMonthlyComparisonData($building, $startDate, $endDate, $comparisonData);

        return response()->json([
            'totalExpenses' => $totalExpenses,
            'totalIncome' => $totalIncome,
            'netBalance' => $netBalance,
            'avgMonthlyExpense' => $avgMonthlyExpense,
            'expensesChange' => $comparisonData['expensesChange'] ?? null,
            'incomeChange' => $comparisonData['incomeChange'] ?? null,
            'balanceChange' => $comparisonData['balanceChange'] ?? null,
            'avgExpenseChange' => $comparisonData['avgExpenseChange'] ?? null,
            'chartData' => $chartData,
            'monthlyData' => $monthlyData,
        ]);
    }

    /**
     * Get comparison data for the specified period.
     */
    private function getComparisonData($building, $startDate, $endDate, $comparisonPeriod): array
    {
        $comparisonStart = null;
        $comparisonEnd = null;

        switch ($comparisonPeriod) {
            case 'previous_month':
                $comparisonStart = $startDate->copy()->subMonth();
                $comparisonEnd = $endDate->copy()->subMonth();
                break;
            case 'same_month_last_year':
                $comparisonStart = $startDate->copy()->subYear();
                $comparisonEnd = $endDate->copy()->subYear();
                break;
            case 'previous_period':
                $daysDiff = $startDate->diffInDays($endDate);
                $comparisonEnd = $startDate->copy()->subDay();
                $comparisonStart = $comparisonEnd->copy()->subDays($daysDiff);
                break;
            default:
                return [];
        }

        // Get comparison expenses and incomes
        $comparisonExpenses = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$comparisonStart, $comparisonEnd])
            ->sum('amount');

        $comparisonIncomes = Income::where('building_id', $building->id)
            ->whereBetween('payment_date', [$comparisonStart, $comparisonEnd])
            ->sum('amount');

        // Get current period data
        $currentExpenses = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $currentIncomes = Income::where('building_id', $building->id)
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->sum('amount');

        // Calculate percentage changes
        $expensesChange = $comparisonExpenses > 0 ? (($currentExpenses - $comparisonExpenses) / $comparisonExpenses) * 100 : 0;
        $incomeChange = $comparisonIncomes > 0 ? (($currentIncomes - $comparisonIncomes) / $comparisonIncomes) * 100 : 0;
        $balanceChange = ($currentIncomes - $currentExpenses) - ($comparisonIncomes - $comparisonExpenses);

        return [
            'expensesChange' => $expensesChange,
            'incomeChange' => $incomeChange,
            'balanceChange' => $balanceChange,
            'avgExpenseChange' => $expensesChange, // Simplified for now
        ];
    }

    /**
     * Generate chart data for expenses vs income trend.
     */
    private function generateChartData($building, $startDate, $endDate): array
    {
        $labels = [];
        $expensesData = [];
        $incomeData = [];

        // Generate daily data for the period
        $current = $startDate->copy();
        while ($current <= $endDate) {
            $labels[] = $current->format('M d');

            $dayExpenses = Expense::where('building_id', $building->id)
                ->whereDate('created_at', $current)
                ->sum('amount');

            $dayIncomes = Income::where('building_id', $building->id)
                ->whereDate('payment_date', $current)
                ->sum('amount');

            $expensesData[] = $dayExpenses;
            $incomeData[] = $dayIncomes;

            $current->addDay();
        }

        return [
            'labels' => $labels,
            'expenses' => $expensesData,
            'income' => $incomeData,
        ];
    }

    /**
     * Generate monthly comparison data.
     */
    private function generateMonthlyComparisonData($building, $startDate, $endDate, $comparisonData): array
    {
        $labels = [];
        $currentData = [];
        $comparisonDataArray = [];

        // Generate monthly data
        $current = $startDate->copy()->startOfMonth();
        while ($current <= $endDate) {
            $labels[] = $current->format('M Y');

            $monthExpenses = Expense::where('building_id', $building->id)
                ->whereYear('created_at', $current->year)
                ->whereMonth('created_at', $current->month)
                ->sum('amount');

            $currentData[] = $monthExpenses;

            // Add comparison data if available
            if ($comparisonData) {
                $comparisonMonth = $current->copy()->subYear();
                $comparisonMonthExpenses = Expense::where('building_id', $building->id)
                    ->whereYear('created_at', $comparisonMonth->year)
                    ->whereMonth('created_at', $comparisonMonth->month)
                    ->sum('amount');
                $comparisonDataArray[] = $comparisonMonthExpenses;
            }

            $current->addMonth();
        }

        return [
            'labels' => $labels,
            'current' => $currentData,
            'comparison' => $comparisonDataArray,
        ];
    }

    /**
     * Get all admins for the current building.
     */
    public function getAdmins(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $admins = $this->adminPermissionService->getBuildingAdmins($building);

        return response()->json($admins);
    }

    /**
     * Update admin level.
     */
    public function updateAdminLevel(Request $request, User $admin): JsonResponse
    {
        $user = $request->user();

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        // Validate that admin belongs to same building
        if ($admin->building_id !== $user->building_id) {
            return response()->json(['message' => 'Admin not found in your building'], 404);
        }

        $validator = Validator::make($request->all(), [
            'admin_level' => 'required|in:primary,secondary'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $success = $this->adminPermissionService->setAdminLevel($admin, $request->get('admin_level'));

        if (!$success) {
            return response()->json(['message' => 'Failed to update admin level'], 400);
        }

        // Log the action
        AdminActivityLog::logAction(
            $user,
            'changed_admin_level',
            'user',
            $admin->id,
            ['new_level' => $request->get('admin_level')],
            $request->ip(),
            $request->userAgent()
        );

        return response()->json([
            'message' => 'Admin level updated successfully',
            'admin' => $admin->fresh()
        ]);
    }

    /**
     * Update admin permissions.
     */
    public function updateAdminPermissions(Request $request, User $admin): JsonResponse
    {
        $user = $request->user();

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        // Validate that admin belongs to same building
        if ($admin->building_id !== $user->building_id) {
            return response()->json(['message' => 'Admin not found in your building'], 404);
        }

        $availablePermissions = $this->adminPermissionService->getAvailablePermissions();
        $permissionKeys = array_keys($availablePermissions);

        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $permissions = $request->get('permissions');
        
        // Filter to only valid permissions
        $filteredPermissions = array_intersect_key($permissions, array_flip($permissionKeys));

        $success = $this->adminPermissionService->updatePermissions($admin, $filteredPermissions);

        if (!$success) {
            return response()->json(['message' => 'Failed to update permissions'], 400);
        }

        // Log the action
        AdminActivityLog::logAction(
            $user,
            'changed_permissions',
            'user',
            $admin->id,
            ['permissions' => $filteredPermissions],
            $request->ip(),
            $request->userAgent()
        );

        return response()->json([
            'message' => 'Admin permissions updated successfully',
            'admin' => $admin->fresh()
        ]);
    }

    /**
     * Activate/deactivate admin.
     */
    public function toggleAdminStatus(Request $request, User $admin): JsonResponse
    {
        $user = $request->user();

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        // Validate that admin belongs to same building
        if ($admin->building_id !== $user->building_id) {
            return response()->json(['message' => 'Admin not found in your building'], 404);
        }

        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $isActive = $request->boolean('is_active');
        
        if ($isActive) {
            $success = $this->adminPermissionService->activateAdmin($admin);
            $action = 'activated_admin';
        } else {
            $success = $this->adminPermissionService->deactivateAdmin($admin);
            $action = 'deactivated_admin';
        }

        if (!$success) {
            $message = $isActive ? 'Failed to activate admin' : 'Failed to deactivate admin';
            return response()->json(['message' => $message], 400);
        }

        // Log the action
        AdminActivityLog::logAction(
            $user,
            $action,
            'user',
            $admin->id,
            ['is_active' => $isActive],
            $request->ip(),
            $request->userAgent()
        );

        return response()->json([
            'message' => $isActive ? 'Admin activated successfully' : 'Admin deactivated successfully',
            'admin' => $admin->fresh()
        ]);
    }

    /**
     * Get admin activity logs.
     */
    public function getAdminActivity(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $query = AdminActivityLog::with(['admin:id,name,email,admin_level'])
            ->forBuilding($building->id);

        // Apply filters
        if ($request->has('admin_id')) {
            $query->byAdmin($request->get('admin_id'));
        }

        if ($request->has('action')) {
            $query->forAction($request->get('action'));
        }

        if ($request->has('resource_type')) {
            $query->forResourceType($request->get('resource_type'));
        }

        if ($request->has('days')) {
            $query->recent($request->get('days', 30));
        }

        $activities = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json($activities);
    }

    /**
     * Get admin activity summary.
     */
    public function getActivitySummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $days = $request->get('days', 30);
        $summary = AdminActivityLog::getActivitySummary($building->id, $days);

        return response()->json($summary);
    }

    /**
     * Get available permissions.
     */
    public function getAvailablePermissions(Request $request): JsonResponse
    {
        $user = $request->user();

        // Check permission
        if (!$this->adminPermissionService->hasPermission($user, 'manage_admins')) {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $permissions = $this->adminPermissionService->getAvailablePermissions();

        return response()->json($permissions);
    }

    /**
     * Check if building can add more admins.
     */
    public function checkAdminLimits(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $package = $building->currentPackage;
        if (!$package) {
            return response()->json([
                'can_add_more' => false,
                'current_count' => 0,
                'limit' => 1,
                'has_multi_admin_feature' => false,
                'package_name' => 'No package'
            ]);
        }

        // Use dynamic package fields instead of hardcoded values
        $hasMultiAdminFeature = $package->hasMultiAdminSupport();
        $limit = $package->getMaxAdmins();

        // Count current admins
        $currentCount = \App\Models\User::where('building_id', $building->id)
            ->where('role', 'admin')
            ->count();

        $canAddMore = $hasMultiAdminFeature && ($currentCount < $limit);

        return response()->json([
            'can_add_more' => $canAddMore,
            'current_count' => $currentCount,
            'limit' => $limit,
            'has_multi_admin_feature' => $hasMultiAdminFeature,
            'package_name' => $package->name ?? 'No package'
        ]);
    }
}
