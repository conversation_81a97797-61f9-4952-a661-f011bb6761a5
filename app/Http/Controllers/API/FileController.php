<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\FileAttachment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class FileController extends Controller
{
    /**
     * Upload a file and create attachment record.
     */
    public function upload(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'file' => [
                'required',
                'file',
                'max:' . (FileAttachment::getMaxFileSize() / 1024), // Convert to KB for validation
            ],
            'attachable_type' => ['required', 'string', Rule::in(['App\Models\Expense', 'App\Models\Income', 'App\Models\BuildingExpense'])],
            'attachable_id' => ['required', 'integer'],
            'description' => ['nullable', 'string', 'max:500'],
            'is_public' => ['boolean'],
        ]);

        $user = $request->user();
        $file = $request->file('file');

        // Validate file extension
        $extension = $file->getClientOriginalExtension();
        if (!FileAttachment::isExtensionAllowed($extension)) {
            return response()->json([
                'message' => 'File type not allowed.',
                'allowed_types' => FileAttachment::getAllowedExtensions(),
            ], 422);
        }

        // Verify user has access to the attachable resource
        $attachableClass = $validated['attachable_type'];
        $attachable = $attachableClass::find($validated['attachable_id']);

        if (!$attachable) {
            return response()->json(['message' => 'Resource not found.'], 404);
        }

        // Check if user has permission to attach files to this resource
        if ($validated['attachable_type'] === 'App\Models\BuildingExpense') {
            // Building expenses can only be managed by admins
            if ($user->role !== 'admin' && $user->role !== 'super_admin') {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
            // Check building scope for non-super admins
            if ($user->role === 'admin' && $attachable->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
        } else {
            // Regular expenses and incomes - check user ownership
            if ($attachable->user_id !== $user->id && $user->role !== 'admin' && $user->role !== 'super_admin') {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
        }

        // Generate unique filename
        $originalName = $file->getClientOriginalName();
        $filename = Str::uuid() . '.' . $extension;

        // Determine storage path
        $directory = 'attachments/' . date('Y/m');
        $filePath = $directory . '/' . $filename;

        // Store the file
        $storedPath = $file->storeAs($directory, $filename, 'local');

        if (!$storedPath) {
            return response()->json(['message' => 'Failed to upload file.'], 500);
        }

        // Debug logging
        \Log::info('File uploaded successfully', [
            'original_name' => $originalName,
            'filename' => $filename,
            'stored_path' => $storedPath,
            'directory' => $directory,
            'file_exists_after_upload' => Storage::disk('local')->exists($storedPath),
        ]);

        // Create file attachment record
        $fileAttachment = FileAttachment::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'attachable_type' => $validated['attachable_type'],
            'attachable_id' => $validated['attachable_id'],
            'original_name' => $originalName,
            'filename' => $filename,
            'file_path' => $storedPath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'file_extension' => $extension,
            'description' => $validated['description'] ?? null,
            'is_public' => $validated['is_public'] ?? false,
        ]);

        return response()->json([
            'message' => 'File uploaded successfully.',
            'file' => [
                'id' => $fileAttachment->id,
                'original_name' => $fileAttachment->original_name,
                'filename' => $fileAttachment->filename,
                'file_size' => $fileAttachment->file_size,
                'file_extension' => $fileAttachment->file_extension,
                'mime_type' => $fileAttachment->mime_type,
                'description' => $fileAttachment->description,
                'is_public' => $fileAttachment->is_public,
                'created_at' => $fileAttachment->created_at,
                'updated_at' => $fileAttachment->updated_at,
                'url' => $fileAttachment->url,
            ],
        ], 201);
    }

    /**
     * Download a file.
     */
    public function download(Request $request, FileAttachment $fileAttachment): mixed
    {
        $user = $request->user();

        // Check permissions
        if (!$this->canAccessFile($user, $fileAttachment)) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        // Debug logging
        \Log::info('Download attempt', [
            'file_id' => $fileAttachment->id,
            'file_path' => $fileAttachment->file_path,
            'original_name' => $fileAttachment->original_name,
            'mime_type' => $fileAttachment->mime_type,
            'file_exists' => $fileAttachment->fileExists(),
            'storage_exists' => Storage::exists($fileAttachment->file_path),
        ]);

        // Check if file exists
        if (!$fileAttachment->fileExists()) {
            \Log::error('File not found on disk', [
                'file_id' => $fileAttachment->id,
                'file_path' => $fileAttachment->file_path,
            ]);
            return response()->json(['message' => 'File not found.'], 404);
        }

        try {
            // Return file download response
            return Storage::download(
                $fileAttachment->file_path,
                $fileAttachment->original_name,
                [
                    'Content-Type' => $fileAttachment->mime_type,
                ]
            );
        } catch (\Exception $e) {
            \Log::error('Download failed', [
                'file_id' => $fileAttachment->id,
                'error' => $e->getMessage(),
                'file_path' => $fileAttachment->file_path,
            ]);
            return response()->json(['message' => 'Download failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get file information.
     */
    public function show(Request $request, FileAttachment $fileAttachment): JsonResponse
    {
        $user = $request->user();

        // Check permissions
        if (!$this->canAccessFile($user, $fileAttachment)) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        return response()->json([
            'file' => $fileAttachment->load(['user', 'building', 'attachable']),
        ]);
    }

    /**
     * Update file information.
     */
    public function update(Request $request, FileAttachment $fileAttachment): JsonResponse
    {
        $user = $request->user();

        // Check permissions
        if (!$this->canModifyFile($user, $fileAttachment)) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'description' => ['nullable', 'string', 'max:500'],
            'is_public' => ['boolean'],
        ]);

        $fileAttachment->update($validated);

        return response()->json([
            'message' => 'File updated successfully.',
            'file' => $fileAttachment->fresh(['user', 'building', 'attachable']),
        ]);
    }

    /**
     * Delete a file.
     */
    public function destroy(Request $request, FileAttachment $fileAttachment): JsonResponse
    {
        $user = $request->user();

        // Check permissions
        if (!$this->canModifyFile($user, $fileAttachment)) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        // Delete the physical file
        $fileAttachment->deleteFile();

        // Delete the database record
        $fileAttachment->delete();

        return response()->json([
            'message' => 'File deleted successfully.',
        ]);
    }

    /**
     * Get files for a specific attachable resource.
     */
    public function getAttachments(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'attachable_type' => ['required', 'string', Rule::in(['App\Models\Expense', 'App\Models\Income', 'App\Models\BuildingExpense'])],
            'attachable_id' => ['required', 'integer'],
        ]);

        $user = $request->user();

        // Verify user has access to the attachable resource
        $attachableClass = $validated['attachable_type'];
        $attachable = $attachableClass::find($validated['attachable_id']);

        if (!$attachable) {
            return response()->json(['message' => 'Resource not found.'], 404);
        }

        // Check if user has permission to view attachments for this resource
        if ($validated['attachable_type'] === 'App\Models\BuildingExpense') {
            // Building expenses can only be viewed by admins
            if ($user->role !== 'admin' && $user->role !== 'super_admin') {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
            // Check building scope for non-super admins
            if ($user->role === 'admin' && $attachable->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
        } else {
            // Regular expenses and incomes - check user ownership
            if ($attachable->user_id !== $user->id && $user->role !== 'admin' && $user->role !== 'super_admin') {
                return response()->json(['message' => 'Unauthorized.'], 403);
            }
        }

        $attachments = FileAttachment::where('attachable_type', $validated['attachable_type'])
            ->where('attachable_id', $validated['attachable_id'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($attachment) {
                // Generate URL manually to avoid any accessor issues
                $url = $attachment->is_public
                    ? Storage::url($attachment->file_path)
                    : "/api/files/{$attachment->id}/download";

                return [
                    'id' => $attachment->id,
                    'original_name' => (string) $attachment->original_name,
                    'filename' => (string) $attachment->filename,
                    'file_size' => (int) $attachment->file_size,
                    'file_extension' => (string) $attachment->file_extension,
                    'mime_type' => (string) $attachment->mime_type,
                    'description' => (string) ($attachment->description ?? ''),
                    'is_public' => (bool) $attachment->is_public,
                    'created_at' => $attachment->created_at,
                    'updated_at' => $attachment->updated_at,
                    'url' => (string) $url,
                ];
            });

        return response()->json([
            'files' => $attachments,
        ]);
    }

    /**
     * Get file upload configuration.
     */
    public function getConfig(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building found for user.'], 404);
        }

        $package = $building->getEffectivePackage();

        if (!$package) {
            return response()->json(['message' => 'No package found for building.'], 404);
        }

        return response()->json([
            'max_file_size_mb' => $package->getMaxFileSizeMb(),
            'max_files_per_record' => $package->getMaxFilesPerRecord(),
            'allowed_extensions' => FileAttachment::getAllowedExtensions(),
            'file_attachments_enabled' => $package->file_attachments_enabled,
        ]);
    }

    /**
     * Check if user can access a file.
     */
    private function canAccessFile($user, FileAttachment $fileAttachment): bool
    {
        // Public files can be accessed by anyone in the same building
        if ($fileAttachment->is_public && $fileAttachment->building_id === $user->building_id) {
            return true;
        }

        // File owner can always access
        if ($fileAttachment->user_id === $user->id) {
            return true;
        }

        // Admins can access files in their building
        if (($user->role === 'admin' || $user->role === 'super_admin') &&
            $fileAttachment->building_id === $user->building_id) {
            return true;
        }

        // Super admins can access any file
        if ($user->role === 'super_admin') {
            return true;
        }

        return false;
    }

    /**
     * Check if user can modify a file.
     */
    private function canModifyFile($user, FileAttachment $fileAttachment): bool
    {
        // File owner can modify
        if ($fileAttachment->user_id === $user->id) {
            return true;
        }

        // Admins can modify files in their building
        if (($user->role === 'admin' || $user->role === 'super_admin') &&
            $fileAttachment->building_id === $user->building_id) {
            return true;
        }

        // Super admins can modify any file
        if ($user->role === 'super_admin') {
            return true;
        }

        return false;
    }
}
