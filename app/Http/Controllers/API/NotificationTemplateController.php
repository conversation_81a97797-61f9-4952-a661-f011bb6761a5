<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\NotificationTemplate;
use App\Models\Notification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NotificationTemplateController extends Controller
{
    /**
     * Display a listing of notification templates.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // Super admins can see all templates, regular admins only see their building's templates
        if ($user->role === 'super_admin') {
            $query = NotificationTemplate::with(['creator', 'building'])
                ->orderBy('created_at', 'desc');
        } else {
            $building = $user->building;
            if (!$building) {
                return response()->json(['message' => 'No building assigned'], 400);
            }

            $query = NotificationTemplate::forBuilding($building->id)
                ->with(['creator'])
                ->orderBy('created_at', 'desc');
        }

        // Filter by type
        if ($request->has('type')) {
            $query->ofType($request->type);
        }

        // Filter by active status
        if ($request->has('active')) {
            if ($request->boolean('active')) {
                $query->active();
            } else {
                $query->where('is_active', false);
            }
        }

        // Filter by building for super admin
        if ($user->role === 'super_admin' && $request->has('building_id')) {
            $query->forBuilding($request->building_id);
        }

        $templates = $query->paginate($request->get('per_page', 15));

        return response()->json($templates);
    }

    /**
     * Store a newly created notification template.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'type' => ['required', Rule::in(Notification::getTypes())],
            'title_template' => ['required', 'string', 'max:255'],
            'message_template' => ['required', 'string'],
            'variables' => ['nullable', 'array'],
            'variables.*.name' => ['required', 'string'],
            'variables.*.required' => ['boolean'],
            'variables.*.description' => ['nullable', 'string'],
            'priority' => ['nullable', Rule::in(Notification::getPriorities())],
            'is_active' => ['boolean'],
            'building_id' => ['nullable', 'exists:buildings,id'], // For super admin
        ]);

        // Determine building ID
        if ($user->role === 'super_admin') {
            // Super admin must specify building_id
            if (!$validated['building_id']) {
                return response()->json(['message' => 'Building ID is required for super admin'], 400);
            }
            $buildingId = $validated['building_id'];
        } else {
            // Regular admin uses their assigned building
            $building = $user->building;
            if (!$building) {
                return response()->json(['message' => 'No building assigned'], 400);
            }
            $buildingId = $building->id;
        }

        $template = NotificationTemplate::create([
            'building_id' => $buildingId,
            'created_by' => $user->id,
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'type' => $validated['type'],
            'title_template' => $validated['title_template'],
            'message_template' => $validated['message_template'],
            'variables' => $validated['variables'] ?? null,
            'priority' => $validated['priority'] ?? 'medium',
            'is_active' => $validated['is_active'] ?? true,
        ]);

        return response()->json([
            'message' => 'Notification template created successfully.',
            'template' => $template->load(['creator']),
        ], 201);
    }

    /**
     * Display the specified notification template.
     */
    public function show(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        return response()->json([
            'template' => $template->load(['creator']),
        ]);
    }

    /**
     * Update the specified notification template.
     */
    public function update(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'type' => ['sometimes', Rule::in(Notification::getTypes())],
            'title_template' => ['sometimes', 'string', 'max:255'],
            'message_template' => ['sometimes', 'string'],
            'variables' => ['nullable', 'array'],
            'variables.*.name' => ['required', 'string'],
            'variables.*.required' => ['boolean'],
            'variables.*.description' => ['nullable', 'string'],
            'priority' => ['nullable', Rule::in(Notification::getPriorities())],
            'is_active' => ['boolean'],
        ]);

        $template->update($validated);

        return response()->json([
            'message' => 'Notification template updated successfully.',
            'template' => $template->load(['creator']),
        ]);
    }

    /**
     * Remove the specified notification template.
     */
    public function destroy(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $template->delete();

        return response()->json([
            'message' => 'Notification template deleted successfully.',
        ]);
    }

    /**
     * Create notifications from template.
     */
    public function createNotifications(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'user_ids' => ['nullable', 'array'],
            'user_ids.*' => ['exists:users,id'],
            'send_to_all' => ['boolean'],
            'variables' => ['required', 'array'],
            'scheduled_for' => ['nullable', 'date', 'after:now'],
        ]);

        // Validate template variables
        $errors = $template->validateVariables($validated['variables']);
        if (!empty($errors)) {
            return response()->json([
                'message' => 'Template validation failed.',
                'errors' => $errors,
            ], 422);
        }

        $building = $user->building;
        $notifications = [];

        if ($validated['send_to_all'] ?? false) {
            // Send to all building users
            $users = $building->users;
        } else {
            // Send to specific users
            $userIds = $validated['user_ids'] ?? [];
            $users = $building->users()->whereIn('id', $userIds)->get();
        }

        foreach ($users as $targetUser) {
            $rendered = $template->render($validated['variables']);
            
            $notifications[] = Notification::create([
                'user_id' => $targetUser->id,
                'building_id' => $building->id,
                'type' => $rendered['type'],
                'title' => $rendered['title'],
                'message' => $rendered['message'],
                'priority' => $rendered['priority'],
                'scheduled_for' => $validated['scheduled_for'] ?? null,
                'data' => [
                    'template_id' => $template->id,
                    'template_variables' => $validated['variables'],
                ],
            ]);
        }

        return response()->json([
            'message' => 'Notifications created successfully.',
            'count' => count($notifications),
            'notifications' => $notifications,
        ], 201);
    }

    /**
     * Preview template with variables.
     */
    public function preview(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'variables' => ['required', 'array'],
        ]);

        try {
            $rendered = $template->render($validated['variables']);
            
            return response()->json([
                'preview' => $rendered,
                'template' => $template,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Template preview failed.',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get default templates.
     */
    public function getDefaults(Request $request): JsonResponse
    {
        $defaults = NotificationTemplate::getDefaultTemplates();

        return response()->json([
            'templates' => $defaults,
        ]);
    }

    /**
     * Get template statistics.
     */
    public function getStats(Request $request): JsonResponse
    {
        $user = $request->user();

        // Super admins can see stats for all templates or specific building
        if ($user->role === 'super_admin') {
            $query = NotificationTemplate::query();

            // Filter by building if specified
            if ($request->has('building_id')) {
                $query->forBuilding($request->building_id);
            }

            $stats = [
                'total_templates' => $query->count(),
                'active_templates' => (clone $query)->active()->count(),
                'inactive_templates' => (clone $query)->where('is_active', false)->count(),
                'templates_by_type' => (clone $query)
                    ->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray(),
            ];
        } else {
            // Regular admin sees only their building's stats
            $building = $user->building;
            if (!$building) {
                return response()->json(['message' => 'No building assigned'], 400);
            }

            $stats = [
                'total_templates' => NotificationTemplate::forBuilding($building->id)->count(),
                'active_templates' => NotificationTemplate::forBuilding($building->id)->active()->count(),
                'inactive_templates' => NotificationTemplate::forBuilding($building->id)->where('is_active', false)->count(),
                'templates_by_type' => NotificationTemplate::forBuilding($building->id)
                    ->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray(),
            ];
        }

        return response()->json($stats);
    }

    /**
     * Create default templates for the building.
     */
    public function createDefaults(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $templates = NotificationTemplate::createDefaultTemplatesForBuilding($building, $user);

        return response()->json([
            'message' => 'Default templates created successfully.',
            'count' => count($templates),
            'templates' => $templates,
        ], 201);
    }
}
