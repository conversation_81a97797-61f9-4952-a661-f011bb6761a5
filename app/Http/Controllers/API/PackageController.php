<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PackageController extends Controller
{
    /**
     * Display a listing of active packages for public access (pricing page).
     */
    public function publicIndex(): JsonResponse
    {
        $packages = Package::active()
            ->ordered()
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'slug' => $package->slug,
                    'description' => $package->description,
                    'description_en' => $package->description_en,
                    'price' => $package->price,
                    'annual_price' => $package->annual_price,
                    'max_neighbors' => $package->max_neighbors,
                    'storage_limit_gb' => $package->storage_limit_gb,
                    'features' => $package->features,
                    'limitations' => $package->limitations,
                    'is_popular' => $package->is_popular,
                    'trial_days' => $package->trial_days,
                    'billing_cycle' => $package->billing_cycle,
                ];
            });

        return response()->json([
            'success' => true,
            'packages' => $packages
        ]);
    }

    /**
     * Display a listing of active packages.
     */
    public function index(Request $request): JsonResponse
    {
        $packages = Package::active()
            ->ordered()
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'slug' => $package->slug,
                    'description' => $package->description,
                    'description_en' => $package->description_en,
                    'price' => $package->price,
                    'annual_price' => $package->annual_price,
                    'monthly_equivalent' => $package->getMonthlyEquivalentPrice(),
                    'annual_savings' => $package->getAnnualSavings(),
                    'annual_savings_percentage' => $package->getAnnualSavingsPercentage(),
                    'max_neighbors' => $package->max_neighbors,
                    'unlimited_neighbors' => $package->hasUnlimitedNeighbors(),
                    'storage_limit_gb' => $package->storage_limit_gb,
                    'unlimited_storage' => $package->hasUnlimitedStorage(),
                    'features' => $package->getFormattedFeatures(),
                    'limitations' => $package->getFormattedLimitations(),
                    'notifications_enabled' => $package->notifications_enabled,
                    'email_notifications_enabled' => $package->email_notifications_enabled,
                    'sms_notifications_enabled' => $package->sms_notifications_enabled,
                    'file_attachments_enabled' => $package->file_attachments_enabled,
                    'max_file_size_mb' => $package->max_file_size_mb,
                    'max_files_per_record' => $package->max_files_per_record,
                    'priority_support' => $package->priority_support,
                    'advanced_reporting' => $package->advanced_reporting,
                    'is_popular' => $package->is_popular,
                    'is_free' => $package->isFree(),
                    'billing_cycle' => $package->billing_cycle,
                    'trial_days' => $package->trial_days,
                ];
            });

        return response()->json([
            'packages' => $packages
        ]);
    }

    /**
     * Display the specified package.
     */
    public function show(Package $package): JsonResponse
    {
        if (!$package->is_active) {
            return response()->json(['message' => 'Package not found.'], 404);
        }

        return response()->json([
            'package' => [
                'id' => $package->id,
                'name' => $package->name,
                'slug' => $package->slug,
                'description' => $package->description,
                'description_en' => $package->description_en,
                'price' => $package->price,
                'annual_price' => $package->annual_price,
                'monthly_equivalent' => $package->getMonthlyEquivalentPrice(),
                'annual_savings' => $package->getAnnualSavings(),
                'annual_savings_percentage' => $package->getAnnualSavingsPercentage(),
                'max_neighbors' => $package->max_neighbors,
                'unlimited_neighbors' => $package->hasUnlimitedNeighbors(),
                'storage_limit_gb' => $package->storage_limit_gb,
                'unlimited_storage' => $package->hasUnlimitedStorage(),
                'features' => $package->getFormattedFeatures(),
                'limitations' => $package->getFormattedLimitations(),
                'notifications_enabled' => $package->notifications_enabled,
                'email_notifications_enabled' => $package->email_notifications_enabled,
                'sms_notifications_enabled' => $package->sms_notifications_enabled,
                'file_attachments_enabled' => $package->file_attachments_enabled,
                'max_file_size_mb' => $package->max_file_size_mb,
                'max_files_per_record' => $package->max_files_per_record,
                'priority_support' => $package->priority_support,
                'advanced_reporting' => $package->advanced_reporting,
                'is_popular' => $package->is_popular,
                'is_free' => $package->isFree(),
                'billing_cycle' => $package->billing_cycle,
                'trial_days' => $package->trial_days,
                'created_at' => $package->created_at->toISOString(),
                'updated_at' => $package->updated_at->toISOString(),
            ]
        ]);
    }

    /**
     * Compare packages for selection.
     */
    public function compare(Request $request): JsonResponse
    {
        $packageIds = $request->validate([
            'package_ids' => ['required', 'array', 'min:2', 'max:3'],
            'package_ids.*' => ['integer', 'exists:packages,id']
        ])['package_ids'];

        $packages = Package::whereIn('id', $packageIds)
            ->active()
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'price' => $package->price,
                    'annual_price' => $package->annual_price,
                    'features' => $package->getFormattedFeatures(),
                    'limitations' => $package->getFormattedLimitations(),
                    'max_neighbors' => $package->max_neighbors,
                    'unlimited_neighbors' => $package->hasUnlimitedNeighbors(),
                    'storage_limit_gb' => $package->storage_limit_gb,
                    'unlimited_storage' => $package->hasUnlimitedStorage(),
                    'notifications_enabled' => $package->notifications_enabled,
                    'email_notifications_enabled' => $package->email_notifications_enabled,
                    'file_attachments_enabled' => $package->file_attachments_enabled,
                    'priority_support' => $package->priority_support,
                    'trial_days' => $package->trial_days,
                ];
            });

        return response()->json([
            'packages' => $packages
        ]);
    }
}
