<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\PaymentGatewayManager;
use App\Models\Payment;
use App\Models\Building;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    private $paymentGatewayManager;

    public function __construct(PaymentGatewayManager $paymentGatewayManager)
    {
        $this->paymentGatewayManager = $paymentGatewayManager;
    }

    /**
     * Handle PayPal webhooks
     */
    public function handlePayPalWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $headers = $request->headers->all();

            Log::info('PayPal webhook received', [
                'headers' => $headers,
                'payload' => $payload,
            ]);

            // PayPal webhook verification would go here
            $data = json_decode($payload, true);

            if (!$data) {
                return response()->json(['error' => 'Invalid payload'], 400);
            }

            $eventType = $data['event_type'] ?? null;

            switch ($eventType) {
                case 'PAYMENT.SALE.COMPLETED':
                    return $this->handlePayPalPaymentCompleted($data);

                case 'PAYMENT.SALE.DENIED':
                    return $this->handlePayPalPaymentDenied($data);

                case 'BILLING.SUBSCRIPTION.CREATED':
                    return $this->handlePayPalSubscriptionCreated($data);

                case 'BILLING.SUBSCRIPTION.CANCELLED':
                    return $this->handlePayPalSubscriptionCancelled($data);

                default:
                    Log::info('Unhandled PayPal webhook event', ['event_type' => $eventType]);
                    return response()->json(['message' => 'Event not handled'], 200);
            }

        } catch (\Exception $e) {
            Log::error('PayPal webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle Stripe webhooks
     */
    public function handleStripeWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');

            if (!$signature) {
                return response()->json(['error' => 'Missing signature'], 400);
            }

            $stripeService = $this->paymentGatewayManager->gateway('stripe');
            $result = $stripeService->handleWebhook($payload, $signature);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle Amazon Payment Services webhooks
     */
    public function handleAmazonWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->all();

            Log::info('Amazon Payment Services webhook received', [
                'payload' => $payload,
            ]);

            // Amazon Payment Services webhook verification would go here
            $responseCode = $payload['response_code'] ?? null;
            $merchantReference = $payload['merchant_reference'] ?? null;

            if (!$merchantReference) {
                return response()->json(['error' => 'Missing merchant reference'], 400);
            }

            switch ($responseCode) {
                case '14000': // Success
                    return $this->handleAmazonPaymentSuccess($payload);

                case '14001': // Failed
                case '14002': // Declined
                    return $this->handleAmazonPaymentFailed($payload);

                default:
                    Log::info('Unhandled Amazon webhook response code', ['response_code' => $responseCode]);
                    return response()->json(['message' => 'Event not handled'], 200);
            }

        } catch (\Exception $e) {
            Log::error('Amazon webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle PayPal payment completed
     */
    private function handlePayPalPaymentCompleted(array $data): JsonResponse
    {
        $paymentId = $data['resource']['parent_payment'] ?? null;
        $amount = $data['resource']['amount']['total'] ?? null;
        $currency = $data['resource']['amount']['currency'] ?? null;

        if ($paymentId) {
            $this->updatePaymentStatus($paymentId, 'completed', [
                'gateway' => 'paypal',
                'amount' => $amount,
                'currency' => $currency,
                'webhook_data' => $data,
            ]);
        }

        return response()->json(['message' => 'Payment completed processed'], 200);
    }

    /**
     * Handle PayPal payment denied
     */
    private function handlePayPalPaymentDenied(array $data): JsonResponse
    {
        $paymentId = $data['resource']['parent_payment'] ?? null;

        if ($paymentId) {
            $this->updatePaymentStatus($paymentId, 'failed', [
                'gateway' => 'paypal',
                'webhook_data' => $data,
            ]);
        }

        return response()->json(['message' => 'Payment denied processed'], 200);
    }

    /**
     * Handle PayPal subscription created
     */
    private function handlePayPalSubscriptionCreated(array $data): JsonResponse
    {
        $subscriptionId = $data['resource']['id'] ?? null;
        $planId = $data['resource']['plan_id'] ?? null;

        Log::info('PayPal subscription created', [
            'subscription_id' => $subscriptionId,
            'plan_id' => $planId,
        ]);

        // Update building subscription status
        // This would typically involve finding the building and updating its subscription

        return response()->json(['message' => 'Subscription created processed'], 200);
    }

    /**
     * Handle PayPal subscription cancelled
     */
    private function handlePayPalSubscriptionCancelled(array $data): JsonResponse
    {
        $subscriptionId = $data['resource']['id'] ?? null;

        Log::info('PayPal subscription cancelled', [
            'subscription_id' => $subscriptionId,
        ]);

        // Update building subscription status
        // This would typically involve finding the building and updating its subscription

        return response()->json(['message' => 'Subscription cancelled processed'], 200);
    }

    /**
     * Handle Amazon payment success
     */
    private function handleAmazonPaymentSuccess(array $data): JsonResponse
    {
        $merchantReference = $data['merchant_reference'];
        $amount = $data['amount'] ?? null;
        $currency = $data['currency'] ?? null;

        $this->updatePaymentStatus($merchantReference, 'completed', [
            'gateway' => 'amazon',
            'amount' => $amount ? $amount / 100 : null, // Convert from minor units
            'currency' => $currency,
            'webhook_data' => $data,
        ]);

        return response()->json(['message' => 'Payment success processed'], 200);
    }

    /**
     * Handle Amazon payment failed
     */
    private function handleAmazonPaymentFailed(array $data): JsonResponse
    {
        $merchantReference = $data['merchant_reference'];
        $responseMessage = $data['response_message'] ?? 'Payment failed';

        $this->updatePaymentStatus($merchantReference, 'failed', [
            'gateway' => 'amazon',
            'error_message' => $responseMessage,
            'webhook_data' => $data,
        ]);

        return response()->json(['message' => 'Payment failure processed'], 200);
    }

    /**
     * Update payment status in database
     */
    private function updatePaymentStatus(string $paymentId, string $status, array $data = []): void
    {
        try {
            // Find payment by gateway payment ID or merchant reference
            $payment = Payment::where('gateway_payment_id', $paymentId)
                ->orWhere('reference', $paymentId)
                ->first();

            if ($payment) {
                $payment->update([
                    'status' => $status,
                    'gateway_response' => array_merge($payment->gateway_response ?? [], $data),
                    'paid_at' => $status === 'completed' ? now() : null,
                ]);

                // If this is a setup fee payment and it's completed, activate the building
                if ($status === 'completed' && $payment->type === 'setup_fee') {
                    $this->activateBuilding($payment->building_id);
                }

                Log::info('Payment status updated', [
                    'payment_id' => $payment->id,
                    'gateway_payment_id' => $paymentId,
                    'status' => $status,
                ]);
            } else {
                Log::warning('Payment not found for webhook', [
                    'gateway_payment_id' => $paymentId,
                    'status' => $status,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to update payment status', [
                'payment_id' => $paymentId,
                'status' => $status,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Activate building after successful setup fee payment
     */
    private function activateBuilding(int $buildingId): void
    {
        try {
            $building = Building::find($buildingId);
            
            if ($building && !$building->is_active) {
                $building->update([
                    'is_active' => true,
                    'setup_fee_paid' => true,
                    'setup_fee_paid_at' => now(),
                ]);

                Log::info('Building activated after setup fee payment', [
                    'building_id' => $buildingId,
                    'building_name' => $building->name,
                ]);

                // You could also trigger notifications, welcome emails, etc. here
            }

        } catch (\Exception $e) {
            Log::error('Failed to activate building', [
                'building_id' => $buildingId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
