<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\FileFolder;
use App\Models\FileAttachment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FileFolderController extends Controller
{
    /**
     * Display a listing of folders.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $query = FileFolder::forBuilding($building->id)
            ->with(['creator', 'parent', 'children'])
            ->withCount(['files'])
            ->orderBy('name');

        // Filter by parent folder
        if ($request->has('parent_id')) {
            if ($request->parent_id === 'null' || $request->parent_id === '') {
                $query->root();
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        } else {
            // Default to root folders
            $query->root();
        }

        // Filter by system/user created
        if ($request->has('system')) {
            if ($request->boolean('system')) {
                $query->where('is_system', true);
            } else {
                $query->userCreated();
            }
        }

        $folders = $query->get();

        // Add statistics for each folder
        $folders->each(function ($folder) {
            $folder->statistics = $folder->getStatistics();
        });

        return response()->json([
            'folders' => $folders,
            'building_id' => $building->id,
        ]);
    }

    /**
     * Store a newly created folder.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'color' => ['nullable', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'parent_id' => ['nullable', 'exists:file_folders,id'],
        ]);

        // Verify parent folder belongs to the same building
        if ($validated['parent_id']) {
            $parentFolder = FileFolder::find($validated['parent_id']);
            if (!$parentFolder || $parentFolder->building_id !== $building->id) {
                return response()->json(['message' => 'Invalid parent folder'], 422);
            }
        }

        $folder = FileFolder::create([
            'building_id' => $building->id,
            'created_by' => $user->id,
            'is_system' => false,
            ...$validated,
        ]);

        return response()->json([
            'message' => 'Folder created successfully.',
            'folder' => $folder->load(['creator', 'parent']),
        ], 201);
    }

    /**
     * Display the specified folder.
     */
    public function show(Request $request, FileFolder $folder): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($folder->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $folder->load(['creator', 'parent', 'children.creator']);
        $folder->statistics = $folder->getStatistics();

        return response()->json([
            'folder' => $folder,
        ]);
    }

    /**
     * Update the specified folder.
     */
    public function update(Request $request, FileFolder $folder): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($folder->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        // System folders cannot be renamed or moved
        if ($folder->is_system && $request->has(['name', 'parent_id'])) {
            return response()->json(['message' => 'System folders cannot be renamed or moved'], 422);
        }

        $validated = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'color' => ['nullable', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'parent_id' => ['nullable', 'exists:file_folders,id'],
        ]);

        // Verify parent folder belongs to the same building and prevent loops
        if (isset($validated['parent_id'])) {
            if ($validated['parent_id']) {
                $parentFolder = FileFolder::find($validated['parent_id']);
                if (!$parentFolder || $parentFolder->building_id !== $folder->building_id) {
                    return response()->json(['message' => 'Invalid parent folder'], 422);
                }

                // Check for circular reference
                if (!$folder->moveTo($parentFolder)) {
                    return response()->json(['message' => 'Cannot move folder to its descendant'], 422);
                }
                unset($validated['parent_id']); // Remove from validated as we handled it above
            } else {
                $folder->parent_id = null;
                $folder->save();
                unset($validated['parent_id']);
            }
        }

        $folder->update($validated);

        return response()->json([
            'message' => 'Folder updated successfully.',
            'folder' => $folder->load(['creator', 'parent']),
        ]);
    }

    /**
     * Remove the specified folder.
     */
    public function destroy(Request $request, FileFolder $folder): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($folder->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        // Check if folder can be deleted
        if (!$folder->canBeDeleted()) {
            return response()->json([
                'message' => 'Cannot delete folder. It contains files or subfolders, or it is a system folder.',
            ], 422);
        }

        $folder->delete();

        return response()->json([
            'message' => 'Folder deleted successfully.',
        ]);
    }

    /**
     * Get files in a specific folder.
     */
    public function getFiles(Request $request, FileFolder $folder): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($folder->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $query = $folder->files()
            ->with(['user', 'attachable'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('original_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->has('type')) {
            $query->where('file_type', $request->type);
        }

        $files = $query->paginate($request->get('per_page', 20));

        return response()->json($files);
    }

    /**
     * Move files to a folder.
     */
    public function moveFiles(Request $request, FileFolder $folder): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($folder->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'file_ids' => ['required', 'array'],
            'file_ids.*' => ['exists:file_attachments,id'],
        ]);

        $files = FileAttachment::whereIn('id', $validated['file_ids'])
            ->where('building_id', $user->building_id)
            ->get();

        $movedCount = 0;
        foreach ($files as $file) {
            $file->folder_id = $folder->id;
            if ($file->save()) {
                $movedCount++;
            }
        }

        return response()->json([
            'message' => "Moved {$movedCount} files to folder '{$folder->name}'.",
            'moved_count' => $movedCount,
        ]);
    }

    /**
     * Get folder tree structure.
     */
    public function getTree(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $folders = FileFolder::forBuilding($building->id)
            ->with(['children.children', 'creator'])
            ->withCount(['files'])
            ->root()
            ->orderBy('name')
            ->get();

        return response()->json([
            'tree' => $folders,
            'building_id' => $building->id,
        ]);
    }

    /**
     * Create default folders for the building.
     */
    public function createDefaults(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $folders = FileFolder::createDefaultFoldersForBuilding($building, $user);

        return response()->json([
            'message' => 'Default folders created successfully.',
            'count' => count($folders),
            'folders' => $folders,
        ], 201);
    }
}
