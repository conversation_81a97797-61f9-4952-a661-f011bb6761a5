<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Building;
use App\Models\CurrencyExchangeRate;
use App\Services\CurrencyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CurrencyController extends Controller
{
    protected $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(Request $request): JsonResponse
    {
        $currencies = $this->currencyService->getSupportedCurrencies();

        return response()->json([
            'currencies' => $currencies,
            'count' => count($currencies),
        ]);
    }

    /**
     * Get building's currency settings.
     */
    public function getBuildingCurrencySettings(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $settings = $this->currencyService->getBuildingCurrencySettings($building);
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();

        return response()->json([
            'settings' => $settings,
            'supported_currencies' => $supportedCurrencies,
            'available_providers' => $this->currencyService->getAvailableProviders(),
        ]);
    }

    /**
     * Update building's currency settings.
     */
    public function updateBuildingCurrencySettings(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'primary_currency' => 'sometimes|string|size:3',
            'accepted_currencies' => 'sometimes|array',
            'accepted_currencies.*' => 'string|size:3',
            'auto_convert' => 'sometimes|boolean',
            'exchange_rate_provider' => 'sometimes|string|in:manual,fixer,exchangerate,currencylayer',
            'conversion_markup' => 'sometimes|numeric|min:0|max:10',
            'regional_settings' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updateData = $request->only([
                'primary_currency',
                'accepted_currencies',
                'auto_convert',
                'exchange_rate_provider',
                'conversion_markup',
                'regional_settings',
            ]);

            // Validate currencies
            if (isset($updateData['primary_currency'])) {
                if (!$this->currencyService->validateCurrency($updateData['primary_currency'])) {
                    return response()->json([
                        'message' => 'Invalid primary currency'
                    ], 422);
                }
            }

            if (isset($updateData['accepted_currencies'])) {
                foreach ($updateData['accepted_currencies'] as $currency) {
                    if (!$this->currencyService->validateCurrency($currency)) {
                        return response()->json([
                            'message' => "Invalid currency: {$currency}"
                        ], 422);
                    }
                }
            }

            $settings = $this->currencyService->updateBuildingCurrencySettings($building, $updateData);

            return response()->json([
                'message' => 'Currency settings updated successfully',
                'settings' => $settings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update currency settings',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get exchange rates for building's currencies.
     */
    public function getExchangeRates(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $settings = $this->currencyService->getBuildingCurrencySettings($building);
        $primaryCurrency = $settings->primary_currency;
        $acceptedCurrencies = $settings->getAllAcceptedCurrencies();

        $rates = [];
        foreach ($acceptedCurrencies as $currency) {
            if ($currency !== $primaryCurrency) {
                $rate = $this->currencyService->getExchangeRate(
                    $currency,
                    $primaryCurrency,
                    null,
                    $settings->exchange_rate_provider
                );

                $rates[] = [
                    'from_currency' => $currency,
                    'to_currency' => $primaryCurrency,
                    'rate' => $rate,
                    'formatted' => $rate ? "1 {$currency} = {$rate} {$primaryCurrency}" : 'Rate not available',
                ];
            }
        }

        return response()->json([
            'primary_currency' => $primaryCurrency,
            'exchange_rates' => $rates,
            'last_updated' => now(),
        ]);
    }

    /**
     * Convert amount between currencies.
     */
    public function convertAmount(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $conversion = $this->currencyService->convertAmount(
                $request->get('amount'),
                $request->get('from_currency'),
                $request->get('to_currency'),
                $building
            );

            return response()->json($conversion);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Conversion failed',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get currency conversion statistics.
     */
    public function getConversionStats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $days = $request->get('days', 30);
        $stats = $this->currencyService->getConversionStats($building, $days);

        return response()->json($stats);
    }

    /**
     * Update exchange rates manually.
     */
    public function updateExchangeRates(Request $request): JsonResponse
    {
        $user = $request->user();

        // Check admin permission
        if ($user->role !== 'admin' && $user->role !== 'super_admin') {
            return response()->json(['message' => 'Insufficient permissions'], 403);
        }

        $validator = Validator::make($request->all(), [
            'rates' => 'required|array|min:1',
            'rates.*.from_currency' => 'required|string|size:3',
            'rates.*.to_currency' => 'required|string|size:3',
            'rates.*.rate' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $rates = $request->get('rates');
            $updated = $this->currencyService->updateExchangeRates($rates, 'manual');

            return response()->json([
                'message' => 'Exchange rates updated successfully',
                'updated_count' => $updated
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update exchange rates',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get historical exchange rates.
     */
    public function getHistoricalRates(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'limit' => 'sometimes|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $rates = CurrencyExchangeRate::getHistoricalRates(
                $request->get('from_currency'),
                $request->get('to_currency'),
                \Carbon\Carbon::parse($request->get('start_date')),
                \Carbon\Carbon::parse($request->get('end_date')),
                $request->get('limit', 100)
            );

            return response()->json([
                'rates' => $rates,
                'count' => $rates->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get historical rates',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Format amount according to building settings.
     */
    public function formatAmount(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'currency' => 'sometimes|string|size:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $formattedAmount = $this->currencyService->formatAmount(
                $request->get('amount'),
                $request->get('currency', $building?->getPrimaryCurrency() ?? 'USD'),
                $building,
                $user
            );

            return response()->json([
                'formatted_amount' => $formattedAmount,
                'original_amount' => $request->get('amount'),
                'currency' => $request->get('currency', $building?->getPrimaryCurrency() ?? 'USD'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to format amount',
                'error' => $e->getMessage()
            ], 400);
        }
    }
}
