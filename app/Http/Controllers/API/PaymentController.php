<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Services\PaymentGatewayManager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    private $paymentManager;

    public function __construct(PaymentGatewayManager $paymentManager)
    {
        $this->paymentManager = $paymentManager;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Payment::with(['user', 'expense']);

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $payments = $query->get();
        return response()->json($payments);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            // Check if the expense belongs to the user's building
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create payments for expenses in your building.'], 403);
            }

            // Check if the user belongs to the same building
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create payments for users in your building.'], 403);
            }
        }

        $payment = Payment::create($validated);
        return response()->json($payment, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only view payments in your building.'], 403);
            }
        }

        $payment->load(['user', 'expense']);
        return response()->json($payment);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update payments in your building.'], 403);
            }
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        // Validate building scope for new relationships (unless super admin)
        if ($user->role !== 'super_admin') {
            // Check if the new expense belongs to the user's building
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign payments to expenses in your building.'], 403);
            }

            // Check if the new user belongs to the same building
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign payments to users in your building.'], 403);
            }
        }

        $payment->update($validated);
        return response()->json($payment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Payment $payment): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $payment->load(['expense']);
            if (!$payment->expense || $payment->expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete payments in your building.'], 403);
            }
        }

        $payment->delete();
        return response()->json(null, 204);
    }

    public function getUserPayments(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only view payments for users in your building.'], 403);
            }
        }

        $query = Payment::with(['expense'])
            ->where('user_id', $validated['user_id']);

        // Additional building scope filter (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $payments = $query->get();
        return response()->json($payments);
    }

    public function getExpensePayments(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'expense_id' => 'required|exists:expenses,id',
        ]);

        // Validate building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            $expense = \App\Models\Expense::find($validated['expense_id']);
            if (!$expense || $expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only view payments for expenses in your building.'], 403);
            }
        }

        $payments = Payment::with(['user'])
            ->where('expense_id', $validated['expense_id'])
            ->get();

        return response()->json($payments);
    }

    public function getPaymentSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Payment::selectRaw('
            payment_method,
            status,
            COUNT(*) as total_payments,
            SUM(amount) as total_amount
        ');

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->whereHas('expense', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        $summary = $query->groupBy('payment_method', 'status')->get();
        return response()->json($summary);
    }

    /**
     * Create payment for setup fee using the best available gateway
     */
    public function createSetupFeePayment(Request $request)
    {
        try {
            $request->validate([
                'amount' => 'required|numeric|min:0.01',
                'currency' => 'required|string|size:3',
                'building_id' => 'required|exists:buildings,id',
                'country_code' => 'string|size:2', // Optional country code for gateway selection
            ]);

            $countryCode = $request->country_code ?? 'US'; // Default to US if not provided
            $description = 'Building Committee Setup Fee - Building ID: ' . $request->building_id;

            // Use the payment manager to create payment with the best gateway for the country
            $paymentResult = $this->paymentManager->createPayment(
                $request->amount,
                $request->currency,
                $description,
                $countryCode,
                [
                    'building_id' => $request->building_id,
                    'payment_type' => 'setup_fee'
                ]
            );

            // Store payment info in session for later verification
            session([
                'payment_id' => $paymentResult['payment_id'],
                'building_id' => $request->building_id,
                'payment_amount' => $request->amount,
                'payment_currency' => $request->currency,
                'payment_type' => 'setup_fee',
                'gateway' => $paymentResult['gateway']
            ]);

            return response()->json([
                'success' => true,
                'payment_id' => $paymentResult['payment_id'],
                'approval_url' => $paymentResult['approval_url'],
                'gateway' => $paymentResult['gateway'],
                'message' => 'Payment created successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Setup fee payment creation failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment success from any gateway
     */
    public function paymentSuccess(Request $request)
    {
        try {
            $paymentId = $request->get('paymentId');
            $payerId = $request->get('PayerID');
            $gateway = session('gateway', 'paypal'); // Get gateway from session

            if (!$paymentId || !$payerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing payment parameters'
                ], 400);
            }

            // Execute the payment using the appropriate gateway
            $result = $this->paymentManager->executePayment($paymentId, $payerId, $gateway);

            if ($result['success'] && ($result['state'] === 'approved' || $result['state'] === 'completed')) {
                // Payment successful - update building payment status
                $buildingId = session('building_id');
                $paymentType = session('payment_type');

                if ($buildingId && $paymentType === 'setup_fee') {
                    // Update building to mark setup fee as paid
                    \App\Models\Building::where('id', $buildingId)->update([
                        'setup_fee_paid' => true,
                        'setup_fee_payment_id' => $paymentId,
                        'setup_fee_paid_at' => now(),
                        'payment_gateway' => $gateway
                    ]);
                }

                // Clear session data
                session()->forget(['payment_id', 'building_id', 'payment_amount', 'payment_currency', 'payment_type', 'gateway']);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully',
                    'payment_id' => $paymentId,
                    'transaction_id' => $result['transaction_id'] ?? $payerId,
                    'gateway' => $gateway
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment was not approved'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Payment execution failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Payment execution failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment cancellation from any gateway
     */
    public function paymentCancel(Request $request)
    {
        // Clear session data
        session()->forget(['payment_id', 'building_id', 'payment_amount', 'payment_currency', 'payment_type', 'gateway']);

        return response()->json([
            'success' => false,
            'message' => 'Payment was cancelled by user'
        ]);
    }

    /**
     * Get available payment gateways for a country
     */
    public function getAvailableGateways(Request $request)
    {
        $request->validate([
            'country_code' => 'required|string|size:2'
        ]);

        $gateways = $this->paymentManager->getGatewaysForCountry($request->country_code);
        $recommendedCurrency = $this->paymentManager->getRecommendedCurrency($request->country_code);

        return response()->json([
            'success' => true,
            'gateways' => $gateways,
            'recommended_currency' => $recommendedCurrency,
            'country_code' => $request->country_code
        ]);
    }

    /**
     * Get payment gateway settings (super admin only)
     */
    public function getPaymentGatewaySettings(Request $request)
    {
        try {
            $settings = [
                'stripe' => [
                    'enabled' => config('payments.gateways.stripe.enabled', false),
                    'publishable_key' => config('payments.gateways.stripe.public_key', ''),
                    'secret_key' => config('payments.gateways.stripe.secret_key', ''),
                    'webhook_secret' => config('payments.gateways.stripe.webhook_secret', ''),
                    'test_mode' => config('payments.gateways.stripe.test_mode', true)
                ],
                'paypal' => [
                    'enabled' => config('payments.gateways.paypal.enabled', false),
                    'client_id' => config('payments.gateways.paypal.client_id', ''),
                    'client_secret' => config('payments.gateways.paypal.client_secret', ''),
                    'sandbox_mode' => config('payments.gateways.paypal.sandbox_mode', true)
                ],
                'bank_transfer' => [
                    'enabled' => config('payments.gateways.bank_transfer.enabled', false),
                    'bank_name' => config('payments.gateways.bank_transfer.bank_name', ''),
                    'account_number' => config('payments.gateways.bank_transfer.account_number', ''),
                    'account_holder' => config('payments.gateways.bank_transfer.account_holder', ''),
                    'iban' => config('payments.gateways.bank_transfer.iban', ''),
                    'swift_code' => config('payments.gateways.bank_transfer.swift_code', ''),
                    'instructions' => config('payments.gateways.bank_transfer.instructions', '')
                ],
                'general' => [
                    'default_currency' => config('payments.default_currency', 'USD'),
                    'payment_timeout' => config('payments.timeout_minutes', 30),
                    'auto_confirm_payments' => config('payments.auto_confirm', false),
                    'send_payment_receipts' => config('payments.send_receipts', true)
                ]
            ];

            return response()->json($settings);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load payment gateway settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update payment gateway settings (super admin only)
     */
    public function updatePaymentGatewaySettings(Request $request)
    {
        try {
            // Note: In a real application, you would save these to a database
            // or update environment variables. For now, we'll just return success.

            return response()->json([
                'message' => 'Payment gateway settings updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update payment gateway settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test payment gateway connection (super admin only)
     */
    public function testPaymentGatewayConnection(Request $request)
    {
        try {
            // Note: In a real application, you would test the actual gateway connection
            // For now, we'll just return success.

            return response()->json([
                'message' => 'Connection test successful'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
