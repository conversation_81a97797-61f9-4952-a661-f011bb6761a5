<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\BuildingExpenseType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BuildingExpenseTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $buildingExpenseTypes = BuildingExpenseType::all();
        return response()->json($buildingExpenseTypes);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:building_expense_types',
            'description' => 'nullable|string',
        ]);

        $buildingExpenseType = BuildingExpenseType::create($validated);
        return response()->json($buildingExpenseType, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        return response()->json($buildingExpenseType);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:building_expense_types,name,' . $buildingExpenseType->id,
            'description' => 'nullable|string',
        ]);

        $buildingExpenseType->update($validated);
        return response()->json($buildingExpenseType);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BuildingExpenseType $buildingExpenseType): JsonResponse
    {
        $buildingExpenseType->delete();
        return response()->json(null, 204);
    }
}
