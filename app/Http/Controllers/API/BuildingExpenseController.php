<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\BuildingExpense;
use App\Services\QueryOptimizationService;
use App\Services\CachingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BuildingExpenseController extends Controller
{
    protected QueryOptimizationService $queryOptimization;
    protected CachingService $caching;

    public function __construct(QueryOptimizationService $queryOptimization, CachingService $caching)
    {
        $this->queryOptimization = $queryOptimization;
        $this->caching = $caching;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // Determine building ID for query
        $buildingId = $user->role === 'super_admin' && $request->filled('building_id')
            ? $request->integer('building_id')
            : $user->building_id;

        // Prepare filters for optimized query
        $filters = [
            'is_archived' => $request->boolean('include_archived') ? null : false,
            'month' => $request->filled('month') ? $request->integer('month') : null,
            'year' => $request->filled('year') ? $request->integer('year') : null,
            'building_expense_type_id' => null, // Will be handled separately if needed
        ];

        // Remove null filters
        $filters = array_filter($filters, fn($value) => $value !== null);

        // Build query for building expenses
        $query = BuildingExpense::with(['buildingExpenseType', 'building'])
            ->where('building_id', $buildingId);

        // Apply filters
        foreach ($filters as $field => $value) {
            $query->where($field, $value);
        }

        // Handle building expense type filter separately for better performance
        if ($request->filled('type')) {
            $query->whereHas('buildingExpenseType', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->type . '%');
            });
        }



        // Paginate results with optimized page size
        $perPage = min($request->integer('per_page', 25), 100); // Max 100 items per page
        $buildingExpenses = $query->paginate($perPage);

        return response()->json($buildingExpenses);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'building_expense_type_id' => 'required|exists:building_expense_types,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
        ]);

        // Auto-set building_id from logged-in user
        $validated['building_id'] = $user->building_id;

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];

        // Auto-generate due_date as end of month
        $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();

        $buildingExpense = BuildingExpense::create($validated);
        $buildingExpense->load(['buildingExpenseType', 'building']);
        return response()->json($buildingExpense, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, BuildingExpense $buildingExpense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($buildingExpense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only view building expenses in your building.'], 403);
            }
        }

        $buildingExpense->load(['buildingExpenseType', 'building']);
        return response()->json($buildingExpense);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BuildingExpense $buildingExpense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($buildingExpense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update building expenses in your building.'], 403);
            }
        }

        $validated = $request->validate([
            'building_expense_type_id' => 'required|exists:building_expense_types,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
        ]);

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];

        // Keep the original building_id (don't allow changing it unless super admin)
        if ($user->role !== 'super_admin') {
            $validated['building_id'] = $buildingExpense->building_id;
        }

        // Auto-generate due_date as end of month if not provided
        if (!isset($validated['due_date'])) {
            $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();
        }

        $buildingExpense->update($validated);
        $buildingExpense->load(['buildingExpenseType', 'building']);
        return response()->json($buildingExpense);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, BuildingExpense $buildingExpense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($buildingExpense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete building expenses in your building.'], 403);
            }
        }

        $buildingExpense->delete();
        return response()->json(null, 204);
    }

    public function getMonthlyBuildingExpenses(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        $query = BuildingExpense::with(['buildingExpenseType'])
            ->where('month', $validated['month'])
            ->where('year', $validated['year'])
            ->active(); // Exclude archived records

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $buildingExpenses = $query->get();
        return response()->json($buildingExpenses);
    }

    public function getBuildingExpenseSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = BuildingExpense::selectRaw('
            building_expense_type_id,
            month,
            year,
            SUM(amount) as total_amount,
            COUNT(*) as total_expenses,
            COUNT(*) as total_count
        ')
        ->active(); // Exclude archived records

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $summary = $query->groupBy('building_expense_type_id', 'month', 'year')
            ->with('buildingExpenseType')
            ->get();

        return response()->json($summary);
    }
}
