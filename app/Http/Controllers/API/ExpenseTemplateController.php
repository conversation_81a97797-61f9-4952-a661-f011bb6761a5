<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ExpenseTemplate;
use App\Models\ExpenseType;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ExpenseTemplateController extends Controller
{
    /**
     * Display a listing of expense templates.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $query = ExpenseTemplate::forBuilding($building->id)
            ->with(['expenseType', 'creator'])
            ->orderBy('created_at', 'desc');

        // Filter by active status
        if ($request->has('active')) {
            if ($request->boolean('active')) {
                $query->active();
            } else {
                $query->where('is_active', false);
            }
        }

        // Filter by auto-generate
        if ($request->has('auto_generate')) {
            if ($request->boolean('auto_generate')) {
                $query->autoGenerate();
            } else {
                $query->where('auto_generate', false);
            }
        }

        // Filter by due for generation
        if ($request->has('due_for_generation') && $request->boolean('due_for_generation')) {
            $query->dueForGeneration();
        }

        $templates = $query->paginate($request->get('per_page', 15));

        // Add statistics for each template
        $templates->getCollection()->each(function ($template) {
            $template->statistics = $template->getStatistics();
        });

        return response()->json($templates);
    }

    /**
     * Store a newly created expense template.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $validated = $request->validate([
            'expense_type_id' => ['required', 'exists:expense_types,id'],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'amount' => ['required', 'numeric', 'min:0'],
            'currency' => ['nullable', 'string', 'size:3'],
            'recurrence_type' => ['required', Rule::in(ExpenseTemplate::getRecurrenceTypes())],
            'recurrence_day' => ['nullable', 'integer', 'min:1', 'max:31'],
            'recurrence_month' => ['nullable', 'integer', 'min:1', 'max:12'],
            'due_days_after' => ['nullable', 'integer', 'min:1', 'max:365'],
            'notes_template' => ['nullable', 'string'],
            'is_active' => ['boolean'],
            'auto_generate' => ['boolean'],
            'next_generation_date' => ['nullable', 'date', 'after:today'],
        ]);

        $template = ExpenseTemplate::create([
            'building_id' => $building->id,
            'created_by' => $user->id,
            'currency' => $validated['currency'] ?? $building->currency ?? 'USD',
            'due_days_after' => $validated['due_days_after'] ?? 30,
            ...$validated,
        ]);

        // Calculate next generation date if not provided
        if (!$template->next_generation_date) {
            $template->next_generation_date = $template->calculateNextGenerationDate();
            $template->save();
        }

        return response()->json([
            'message' => 'Expense template created successfully.',
            'template' => $template->load(['expenseType', 'creator']),
        ], 201);
    }

    /**
     * Display the specified expense template.
     */
    public function show(Request $request, ExpenseTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $template->load(['expenseType', 'creator', 'generatedExpenses.user']);
        $template->statistics = $template->getStatistics();

        return response()->json([
            'template' => $template,
        ]);
    }

    /**
     * Update the specified expense template.
     */
    public function update(Request $request, ExpenseTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'expense_type_id' => ['sometimes', 'exists:expense_types,id'],
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'amount' => ['sometimes', 'numeric', 'min:0'],
            'currency' => ['nullable', 'string', 'size:3'],
            'recurrence_type' => ['sometimes', Rule::in(ExpenseTemplate::getRecurrenceTypes())],
            'recurrence_day' => ['nullable', 'integer', 'min:1', 'max:31'],
            'recurrence_month' => ['nullable', 'integer', 'min:1', 'max:12'],
            'due_days_after' => ['nullable', 'integer', 'min:1', 'max:365'],
            'notes_template' => ['nullable', 'string'],
            'is_active' => ['boolean'],
            'auto_generate' => ['boolean'],
            'next_generation_date' => ['nullable', 'date'],
        ]);

        $template->update($validated);

        // Recalculate next generation date if recurrence settings changed
        if (isset($validated['recurrence_type']) || isset($validated['recurrence_day']) || isset($validated['recurrence_month'])) {
            $template->next_generation_date = $template->calculateNextGenerationDate();
            $template->save();
        }

        return response()->json([
            'message' => 'Expense template updated successfully.',
            'template' => $template->load(['expenseType', 'creator']),
        ]);
    }

    /**
     * Remove the specified expense template.
     */
    public function destroy(Request $request, ExpenseTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        // Check if template has generated expenses
        $generatedCount = $template->generatedExpenses()->count();
        if ($generatedCount > 0) {
            return response()->json([
                'message' => "Cannot delete template. It has generated {$generatedCount} expenses.",
            ], 422);
        }

        $template->delete();

        return response()->json([
            'message' => 'Expense template deleted successfully.',
        ]);
    }

    /**
     * Generate expenses from template.
     */
    public function generateExpenses(Request $request, ExpenseTemplate $template): JsonResponse
    {
        $user = $request->user();

        // Check building access
        if ($template->building_id !== $user->building_id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $validated = $request->validate([
            'for_date' => ['nullable', 'date'],
        ]);

        $forDate = $validated['for_date'] ? Carbon::parse($validated['for_date']) : null;

        try {
            $expenses = $template->generateExpenses($forDate);

            return response()->json([
                'message' => 'Expenses generated successfully.',
                'count' => count($expenses),
                'expenses' => $expenses,
                'template' => $template->fresh(['expenseType']),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate expenses.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get expense types for template creation.
     */
    public function getExpenseTypes(Request $request): JsonResponse
    {
        $expenseTypes = ExpenseType::orderBy('name')->get();

        return response()->json([
            'expense_types' => $expenseTypes,
        ]);
    }

    /**
     * Get available recurrence types.
     */
    public function getRecurrenceTypes(Request $request): JsonResponse
    {
        $types = ExpenseTemplate::getRecurrenceTypes();

        return response()->json([
            'recurrence_types' => $types,
        ]);
    }

    /**
     * Create default templates for the building.
     */
    public function createDefaults(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $templates = ExpenseTemplate::createDefaultTemplatesForBuilding($building, $user);

        return response()->json([
            'message' => 'Default expense templates created successfully.',
            'count' => count($templates),
            'templates' => $templates,
        ], 201);
    }

    /**
     * Run automatic expense generation for all due templates.
     */
    public function runAutoGeneration(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $templates = ExpenseTemplate::forBuilding($building->id)
            ->active()
            ->autoGenerate()
            ->dueForGeneration()
            ->get();

        $totalGenerated = 0;
        $results = [];

        foreach ($templates as $template) {
            try {
                $expenses = $template->generateExpenses();
                $totalGenerated += count($expenses);
                
                $results[] = [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'expenses_generated' => count($expenses),
                    'status' => 'success',
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'expenses_generated' => 0,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return response()->json([
            'message' => "Auto-generation completed. Generated {$totalGenerated} expenses from " . count($templates) . " templates.",
            'total_generated' => $totalGenerated,
            'templates_processed' => count($templates),
            'results' => $results,
        ]);
    }
}
