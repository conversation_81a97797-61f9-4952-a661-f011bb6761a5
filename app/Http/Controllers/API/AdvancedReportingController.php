<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Building;
use App\Models\CustomReport;
use App\Models\ReportAnalytics;
use App\Models\ReportTemplate;
use App\Services\AdvancedReportingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdvancedReportingController extends Controller
{
    protected $reportingService;

    public function __construct(AdvancedReportingService $reportingService)
    {
        $this->reportingService = $reportingService;
    }

    /**
     * Get reporting statistics and capabilities.
     */
    public function getReportingStats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $stats = $this->reportingService->getReportingStats($building);

        return response()->json($stats);
    }

    /**
     * Get available report templates.
     */
    public function getTemplates(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->reportingService->hasAdvancedReporting($building)) {
            return response()->json([
                'message' => 'Advanced reporting is not available for your current package'
            ], 403);
        }

        $templates = $this->reportingService->getAvailableTemplates($building);

        return response()->json($templates);
    }

    /**
     * Get a specific template with details.
     */
    public function getTemplate(Request $request, ReportTemplate $template): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->reportingService->hasAdvancedReporting($building)) {
            return response()->json([
                'message' => 'Advanced reporting is not available for your current package'
            ], 403);
        }

        return response()->json([
            'template' => $template,
            'available_fields' => $template->getAvailableFields(),
            'available_filters' => $template->getAvailableFilters(),
            'available_grouping' => $template->getAvailableGrouping(),
            'available_charts' => $template->getAvailableCharts(),
            'default_config' => $template->getDefaultConfig(),
        ]);
    }

    /**
     * Get custom reports for the building.
     */
    public function getCustomReports(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->reportingService->hasCustomReports($building)) {
            return response()->json([
                'message' => 'Custom reports are not available for your current package'
            ], 403);
        }

        $query = CustomReport::with(['template', 'creator'])
            ->forBuilding($building->id);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        } else {
            $query->active();
        }

        // Filter by accessibility
        if ($request->has('show_all') && $request->boolean('show_all')) {
            // Show all reports user can access
            $query->where(function ($q) use ($user) {
                $q->where('created_by', $user->id)
                  ->orWhere('is_public', true);
            });
        } else {
            // Show only user's reports by default
            $query->createdBy($user->id);
        }

        $reports = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json($reports);
    }

    /**
     * Create a new custom report.
     */
    public function createCustomReport(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->reportingService->hasCustomReports($building)) {
            return response()->json([
                'message' => 'Custom reports are not available for your current package'
            ], 403);
        }

        if (!$this->reportingService->canCreateMoreReports($building)) {
            return response()->json([
                'message' => 'Custom report limit reached for your package'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:report_templates,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'configuration' => 'required|array',
            'configuration.fields' => 'required|array|min:1',
            'configuration.filters' => 'nullable|array',
            'configuration.grouping' => 'nullable|array',
            'chart_config' => 'nullable|array',
            'is_public' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = ReportTemplate::findOrFail($request->get('template_id'));
            
            $config = array_merge($request->get('configuration'), [
                'name' => $request->get('name'),
                'description' => $request->get('description'),
                'chart_config' => $request->get('chart_config'),
            ]);

            $report = $this->reportingService->createCustomReport(
                $building,
                $user,
                $template,
                $config
            );

            if ($request->has('is_public')) {
                $report->update(['is_public' => $request->boolean('is_public')]);
            }

            return response()->json([
                'message' => 'Custom report created successfully',
                'report' => $report->load(['template', 'creator'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create custom report',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get a specific custom report.
     */
    public function getCustomReport(Request $request, CustomReport $report): JsonResponse
    {
        $user = $request->user();

        if (!$report->canBeAccessedBy($user)) {
            return response()->json(['message' => 'Report not found or access denied'], 404);
        }

        // Log analytics
        ReportAnalytics::logEvent(
            $report->building_id,
            $report->id,
            $user->id,
            'viewed'
        );

        return response()->json([
            'report' => $report->load(['template', 'creator']),
            'analytics' => $report->getAnalyticsSummary(),
            'recent_generations' => $report->getRecentGenerations(5),
        ]);
    }

    /**
     * Update a custom report.
     */
    public function updateCustomReport(Request $request, CustomReport $report): JsonResponse
    {
        $user = $request->user();

        if (!$report->canBeEditedBy($user)) {
            return response()->json(['message' => 'Report not found or access denied'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'configuration' => 'sometimes|array',
            'chart_config' => 'nullable|array',
            'is_public' => 'nullable|boolean',
            'status' => 'sometimes|in:draft,active,archived',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate configuration if provided
            if ($request->has('configuration') && $report->template) {
                $errors = $report->template->validateConfiguration($request->get('configuration'));
                if (!empty($errors)) {
                    return response()->json([
                        'message' => 'Invalid configuration',
                        'errors' => $errors
                    ], 422);
                }
            }

            $report->update($request->only([
                'name', 'description', 'configuration', 'chart_config', 'is_public', 'status'
            ]));

            return response()->json([
                'message' => 'Custom report updated successfully',
                'report' => $report->fresh(['template', 'creator'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update custom report',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Delete a custom report.
     */
    public function deleteCustomReport(Request $request, CustomReport $report): JsonResponse
    {
        $user = $request->user();

        if (!$report->canBeEditedBy($user)) {
            return response()->json(['message' => 'Report not found or access denied'], 404);
        }

        try {
            $report->delete();

            return response()->json([
                'message' => 'Custom report deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete custom report',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Generate report data.
     */
    public function generateReportData(Request $request, CustomReport $report): JsonResponse
    {
        $user = $request->user();

        if (!$report->canBeAccessedBy($user)) {
            return response()->json(['message' => 'Report not found or access denied'], 404);
        }

        $validator = Validator::make($request->all(), [
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'parameters' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $parameters = $request->get('parameters', []);
            
            // Add date parameters if provided
            if ($request->has('date_from')) {
                $parameters['date_from'] = $request->get('date_from');
            }
            if ($request->has('date_to')) {
                $parameters['date_to'] = $request->get('date_to');
            }

            $data = $this->reportingService->generateReportData($report, $parameters);

            $report->markAsGenerated();

            return response()->json($data);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate report data',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Download a generated report as PDF.
     */
    public function downloadReport(Request $request, CustomReport $report)
    {
        $user = $request->user();

        if (!$report->canBeAccessedBy($user)) {
            return response()->json(['message' => 'Report not found or access denied'], 404);
        }

        try {
            // Get building and user info
            $building = $user->building;

            if (!$building) {
                return response()->json(['message' => 'No building assigned'], 400);
            }

            // Get date parameters
            $dateFrom = $request->get('date_from') ? \Carbon\Carbon::parse($request->get('date_from')) : now()->startOfMonth();
            $dateTo = $request->get('date_to') ? \Carbon\Carbon::parse($request->get('date_to')) : now()->endOfMonth();

            \Log::info('Starting report download', [
                'report_id' => $report->id,
                'report_name' => $report->name,
                'building_id' => $building->id,
                'date_from' => $dateFrom->toDateString(),
                'date_to' => $dateTo->toDateString()
            ]);

            // Generate report data using the AdvancedReportingService
            $parameters = [
                'date_from' => $dateFrom->toDateString(),
                'date_to' => $dateTo->toDateString()
            ];

            // Generate the report data using the custom report configuration
            $reportData = $this->reportingService->generateReportData($report, $parameters);

            \Log::info('Report data generated', [
                'record_count' => $reportData['record_count'] ?? 0,
                'data_keys' => array_keys($reportData)
            ]);

            // Use ReportGeneratorService to create PDF
            $reportGenerator = app(\App\Services\ReportGeneratorService::class);

            // Determine report type based on the report configuration
            $config = $report->getFullConfiguration();
            $dataSource = $config['data_source'] ?? 'expenses';

            $reportType = 'financial_summary'; // Default
            if ($dataSource === 'expenses') {
                $reportType = 'expense_report';
            } elseif ($dataSource === 'incomes') {
                $reportType = 'income_report';
            }

            \Log::info('Report type determined', [
                'report_type' => $reportType,
                'data_source' => $dataSource
            ]);

            // Convert our report data to the format expected by ReportGeneratorService
            $formattedData = $this->formatDataForPdfGeneration($reportData, $dataSource, $building, $dateFrom, $dateTo);

            // Generate PDF
            $pdfContent = $reportGenerator->generatePdfReport($reportType, $formattedData, $building, [
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'report_name' => $report->name,
                'report_description' => $report->description
            ]);

            \Log::info('PDF generated', ['pdf_size' => strlen($pdfContent)]);

            // Return PDF as download
            $filename = str_replace([' ', '/'], ['_', '_'], $report->name) . '-' . date('Y-m-d') . '.pdf';

            return response($pdfContent)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            \Log::error('Report download failed', [
                'report_id' => $report->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to generate report PDF',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get report analytics for building.
     */
    public function getReportAnalytics(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $days = $request->get('days', 30);

        $summary = ReportAnalytics::getBuildingSummary($building->id, $days);
        $topReports = ReportAnalytics::getTopReports($building->id, $days, 10);
        $activeUsers = ReportAnalytics::getMostActiveUsers($building->id, $days, 10);
        $timeline = ReportAnalytics::getActivityTimeline($building->id, $days);

        return response()->json([
            'summary' => $summary,
            'top_reports' => $topReports,
            'most_active_users' => $activeUsers,
            'activity_timeline' => $timeline,
        ]);
    }

    /**
     * Quick generate a report without creating a custom report.
     */
    public function quickGenerate(Request $request)
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $type = $request->get('type', 'financial');
        
        try {
            // Get date parameters (last month by default)
            $dateFrom = now()->startOfMonth();
            $dateTo = now()->endOfMonth();

            // Generate report data using ReportGeneratorService directly
            $reportGenerator = app(\App\Services\ReportGeneratorService::class);
            
            // Determine report type based on the request
            $reportType = 'financial_summary'; // Default type
            
            if ($type === 'expenses') {
                $reportType = 'expense_report';
            } elseif ($type === 'incomes') {
                $reportType = 'income_report';
            }

            // Generate the report data
            $data = $reportGenerator->getFinancialSummaryData($building, $dateFrom, $dateTo);
            
            // Generate PDF
            $pdfContent = $reportGenerator->generatePdfReport($reportType, $data, $building, [
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]);

            // Return PDF directly with proper headers
            $filename = "{$type}-report-" . now()->format('Y-m-d') . ".pdf";
            
            return response($pdfContent)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Content-Length', strlen($pdfContent));

        } catch (\Exception $e) {
            Log::error('Quick generate report failed', [
                'error' => $e->getMessage(),
                'building_id' => $building->id,
                'user_id' => $user->id,
                'type' => $type
            ]);

            return response()->json([
                'message' => 'Failed to generate report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format report data for PDF generation.
     */
    private function formatDataForPdfGeneration(array $reportData, string $dataSource, \App\Models\Building $building, $dateFrom, $dateTo): array
    {
        // Get the actual data from the database to ensure we have all required fields
        $expenses = collect();
        $incomes = collect();
        $totalExpenses = 0;
        $totalIncomes = 0;

        if ($dataSource === 'expenses' || $dataSource === 'financial') {
            $expenses = $building->expenses()
                ->with(['expenseType', 'user'])
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->get();
            $totalExpenses = $expenses->sum('amount');
        }

        if ($dataSource === 'incomes' || $dataSource === 'financial') {
            $incomes = $building->incomes()
                ->with(['user'])
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->get();
            $totalIncomes = $incomes->sum('amount');
        }

        // If no specific data source, get both (financial summary)
        if (!in_array($dataSource, ['expenses', 'incomes'])) {
            $expenses = $building->expenses()
                ->with(['expenseType', 'user'])
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->get();
            $incomes = $building->incomes()
                ->with(['user'])
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->get();
            $totalExpenses = $expenses->sum('amount');
            $totalIncomes = $incomes->sum('amount');
        }

        $netBalance = $totalIncomes - $totalExpenses;
        $recordCount = $expenses->count() + $incomes->count();

        // Format data according to what the PDF template expects
        $formattedData = [
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'record_count' => $recordCount,
        ];

        // Add expenses if we have them
        if ($expenses->count() > 0) {
            $formattedData['expenses'] = $expenses;
            $formattedData['total_expenses'] = $totalExpenses;
        }

        // Add incomes if we have them
        if ($incomes->count() > 0) {
            $formattedData['incomes'] = $incomes;
            $formattedData['total_incomes'] = $totalIncomes;
        }

        // Add net balance for financial summaries
        if ($dataSource === 'financial' || (!in_array($dataSource, ['expenses', 'incomes']))) {
            $formattedData['net_balance'] = $netBalance;
        }

        return $formattedData;
    }
}
