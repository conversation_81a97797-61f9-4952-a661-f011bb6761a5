<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use App\Models\Income;
use App\Services\ArchiveService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ArchiveController extends Controller
{
    protected $archiveService;

    public function __construct(ArchiveService $archiveService)
    {
        $this->archiveService = $archiveService;
    }

    /**
     * Get archive statistics for the current building.
     */
    public function stats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $stats = $this->archiveService->getArchiveStats($building);

        return response()->json($stats);
    }

    /**
     * Get archived expenses.
     */
    public function getArchivedExpenses(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->archiveService->hasArchiveFeature($building)) {
            return response()->json([
                'message' => 'Archive feature is not available for your current package'
            ], 403);
        }

        $query = Expense::with(['expenseType', 'user', 'archivedBy'])
            ->where('building_id', $building->id)
            ->archived();

        // Apply filters
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('notes', 'like', "%{$search}%")
                  ->orWhereHas('expenseType', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->has('archived_from')) {
            $query->where('archived_at', '>=', $request->get('archived_from'));
        }

        if ($request->has('archived_to')) {
            $query->where('archived_at', '<=', $request->get('archived_to'));
        }

        $expenses = $query->orderBy('archived_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json($expenses);
    }

    /**
     * Get archived incomes.
     */
    public function getArchivedIncomes(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        if (!$this->archiveService->hasArchiveFeature($building)) {
            return response()->json([
                'message' => 'Archive feature is not available for your current package'
            ], 403);
        }

        $query = Income::with(['user', 'archivedBy'])
            ->where('building_id', $building->id)
            ->archived();

        // Apply filters
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('notes', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->has('archived_from')) {
            $query->where('archived_at', '>=', $request->get('archived_from'));
        }

        if ($request->has('archived_to')) {
            $query->where('archived_at', '<=', $request->get('archived_to'));
        }

        $incomes = $query->orderBy('archived_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json($incomes);
    }

    /**
     * Archive expenses.
     */
    public function archiveExpenses(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'expense_ids' => 'required|array|min:1',
            'expense_ids.*' => 'required|integer|exists:expenses,id',
            'reason' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $result = $this->archiveService->archiveExpenses(
            $request->get('expense_ids'),
            $user,
            $request->get('reason')
        );

        $status = $result['success'] ? 200 : 403;
        return response()->json($result, $status);
    }

    /**
     * Archive incomes.
     */
    public function archiveIncomes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'income_ids' => 'required|array|min:1',
            'income_ids.*' => 'required|integer|exists:incomes,id',
            'reason' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $result = $this->archiveService->archiveIncomes(
            $request->get('income_ids'),
            $user,
            $request->get('reason')
        );

        $status = $result['success'] ? 200 : 403;
        return response()->json($result, $status);
    }

    /**
     * Unarchive expenses.
     */
    public function unarchiveExpenses(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'expense_ids' => 'required|array|min:1',
            'expense_ids.*' => 'required|integer|exists:expenses,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $result = $this->archiveService->unarchiveExpenses(
            $request->get('expense_ids'),
            $user
        );

        $status = $result['success'] ? 200 : 403;
        return response()->json($result, $status);
    }

    /**
     * Unarchive incomes.
     */
    public function unarchiveIncomes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'income_ids' => 'required|array|min:1',
            'income_ids.*' => 'required|integer|exists:incomes,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $result = $this->archiveService->unarchiveIncomes(
            $request->get('income_ids'),
            $user
        );

        $status = $result['success'] ? 200 : 403;
        return response()->json($result, $status);
    }

    /**
     * Auto-archive old records.
     */
    public function autoArchive(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'months_old' => 'nullable|integer|min:1|max:60'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building assigned'], 400);
        }

        $monthsOld = $request->get('months_old', 12);
        $result = $this->archiveService->autoArchiveOldRecords($building, $monthsOld);

        $status = $result['success'] ? 200 : 403;
        return response()->json($result, $status);
    }
}
