<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Mail\EmailVerificationMail;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class EmailVerificationController extends Controller
{
    /**
     * Send email verification link.
     */
    public function send(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email already verified'
            ], 400);
        }

        // Rate limiting - 3 attempts per hour per user
        $key = 'email-verification:' . $user->id;
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'message' => 'Too many verification emails sent. Please try again in ' . ceil($seconds / 60) . ' minutes.'
            ], 429);
        }

        try {
            // Generate verification token
            $token = $user->generateEmailVerificationToken();

            // Send verification email
            Mail::to($user->email)->send(new EmailVerificationMail($user, $token));

            // Increment rate limiter
            RateLimiter::hit($key, 3600); // 1 hour

            Log::info('Email verification sent', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return response()->json([
                'message' => 'Verification email sent successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send verification email', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to send verification email'
            ], 500);
        }
    }

    /**
     * Verify email with token.
     */
    public function verify(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
            'email' => 'required|email'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json([
                'message' => 'Invalid verification link'
            ], 400);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => 'Email already verified'
            ], 400);
        }

        if (!$user->isEmailVerificationTokenValid($request->token)) {
            if ($user->isEmailVerificationTokenExpired()) {
                return response()->json([
                    'message' => 'Verification link has expired. Please request a new one.'
                ], 400);
            }

            return response()->json([
                'message' => 'Invalid verification link'
            ], 400);
        }

        // Mark email as verified
        $user->markEmailAsVerified();

        Log::info('Email verified successfully', [
            'user_id' => $user->id,
            'email' => $user->email
        ]);

        return response()->json([
            'message' => 'Email verified successfully',
            'user' => $user->fresh()
        ]);
    }

    /**
     * Check verification status.
     */
    public function status(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'verified' => $user->hasVerifiedEmail(),
            'email' => $user->email,
            'verification_sent_at' => $user->email_verification_token_expires_at?->subHours(24),
            'expires_at' => $user->email_verification_token_expires_at
        ]);
    }

    /**
     * Resend verification email.
     */
    public function resend(Request $request): JsonResponse
    {
        return $this->send($request);
    }
}
