<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Payment;
use App\Models\ExpenseType;
use App\Models\Building;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    /**
     * Global search across all entities.
     */
    public function search(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'query' => ['required', 'string', 'min:2', 'max:100'],
            'type' => ['nullable', 'string', 'in:all,users,expenses,incomes,payments,expense_types'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:50'],
            'building_id' => ['nullable', 'integer'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date'],
            'amount_min' => ['nullable', 'numeric', 'min:0'],
            'amount_max' => ['nullable', 'numeric', 'min:0'],
        ]);

        $user = $request->user();
        $query = $validated['query'];
        $type = $validated['type'] ?? 'all';
        $limit = $validated['limit'] ?? 20;

        // Determine building scope
        $buildingId = $user->role === 'super_admin'
            ? ($validated['building_id'] ?? null)
            : $user->building_id;

        $results = [];

        if ($type === 'all' || $type === 'users') {
            $results['users'] = $this->searchUsers($query, $buildingId, $user, $limit);
        }

        if ($type === 'all' || $type === 'expenses') {
            $results['expenses'] = $this->searchExpenses($query, $buildingId, $user, $validated, $limit);
        }

        if ($type === 'all' || $type === 'incomes') {
            $results['incomes'] = $this->searchIncomes($query, $buildingId, $user, $validated, $limit);
        }

        if ($type === 'all' || $type === 'payments') {
            $results['payments'] = $this->searchPayments($query, $buildingId, $user, $validated, $limit);
        }

        if ($type === 'all' || $type === 'expense_types') {
            $results['expense_types'] = $this->searchExpenseTypes($query, $buildingId, $user, $limit);
        }

        // Calculate total results
        $totalResults = collect($results)->sum(function ($items) {
            return is_array($items) ? count($items) : $items->count();
        });

        return response()->json([
            'query' => $query,
            'total_results' => $totalResults,
            'results' => $results,
            'filters_applied' => array_filter([
                'type' => $type !== 'all' ? $type : null,
                'building_id' => $buildingId,
                'date_from' => $validated['date_from'] ?? null,
                'date_to' => $validated['date_to'] ?? null,
                'amount_min' => $validated['amount_min'] ?? null,
                'amount_max' => $validated['amount_max'] ?? null,
            ]),
        ]);
    }

    /**
     * Get search suggestions/autocomplete.
     */
    public function suggestions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'query' => ['required', 'string', 'min:1', 'max:50'],
            'type' => ['nullable', 'string', 'in:all,users,expenses,incomes,expense_types'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:10'],
        ]);

        $user = $request->user();
        $query = $validated['query'];
        $type = $validated['type'] ?? 'all';
        $limit = $validated['limit'] ?? 5;
        $buildingId = $user->role === 'super_admin' ? null : $user->building_id;

        $suggestions = [];

        if ($type === 'all' || $type === 'users') {
            $userSuggestions = User::where('building_id', $buildingId)
                ->where(function ($q) use ($query) {
                    $q->where('name', 'LIKE', "%{$query}%")
                      ->orWhere('apartment_number', 'LIKE', "%{$query}%");
                })
                ->select('id', 'name', 'apartment_number')
                ->limit($limit)
                ->get()
                ->map(function ($user) {
                    return [
                        'type' => 'user',
                        'id' => $user->id,
                        'text' => $user->name . ' (' . $user->apartment_number . ')',
                        'value' => $user->name,
                    ];
                });

            $suggestions = array_merge($suggestions, $userSuggestions->toArray());
        }

        if ($type === 'all' || $type === 'expense_types') {
            $expenseTypeSuggestions = ExpenseType::where('building_id', $buildingId)
                ->where('name', 'LIKE', "%{$query}%")
                ->select('id', 'name')
                ->limit($limit)
                ->get()
                ->map(function ($expenseType) {
                    return [
                        'type' => 'expense_type',
                        'id' => $expenseType->id,
                        'text' => $expenseType->name,
                        'value' => $expenseType->name,
                    ];
                });

            $suggestions = array_merge($suggestions, $expenseTypeSuggestions->toArray());
        }

        // Limit total suggestions
        $suggestions = array_slice($suggestions, 0, $limit * 2);

        return response()->json([
            'query' => $query,
            'suggestions' => $suggestions,
        ]);
    }

    /**
     * Search users.
     */
    private function searchUsers(string $query, ?int $buildingId, $user, int $limit)
    {
        $usersQuery = User::query()
            ->with(['building'])
            ->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('email', 'LIKE', "%{$query}%")
                  ->orWhere('apartment_number', 'LIKE', "%{$query}%");
            });

        // Apply building scope
        if ($buildingId) {
            $usersQuery->where('building_id', $buildingId);
        } elseif ($user->role !== 'super_admin') {
            $usersQuery->where('building_id', $user->building_id);
        }

        return $usersQuery->limit($limit)->get();
    }

    /**
     * Search expenses.
     */
    private function searchExpenses(string $query, ?int $buildingId, $user, array $filters, int $limit)
    {
        $expensesQuery = Expense::query()
            ->with(['user', 'expenseType', 'building'])
            ->where(function ($q) use ($query) {
                $q->where('notes', 'LIKE', "%{$query}%")
                  ->orWhere('amount', 'LIKE', "%{$query}%")
                  ->orWhereHas('expenseType', function ($subQ) use ($query) {
                      $subQ->where('name', 'LIKE', "%{$query}%");
                  })
                  ->orWhereHas('user', function ($subQ) use ($query) {
                      $subQ->where('name', 'LIKE', "%{$query}%")
                           ->orWhere('apartment_number', 'LIKE', "%{$query}%");
                  });
            });

        // Apply building scope
        if ($buildingId) {
            $expensesQuery->where('building_id', $buildingId);
        } elseif ($user->role !== 'super_admin') {
            $expensesQuery->where('building_id', $user->building_id);
        }

        // Apply additional filters
        if (!empty($filters['date_from'])) {
            $expensesQuery->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $expensesQuery->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (!empty($filters['amount_min'])) {
            $expensesQuery->where('amount', '>=', $filters['amount_min']);
        }

        if (!empty($filters['amount_max'])) {
            $expensesQuery->where('amount', '<=', $filters['amount_max']);
        }

        return $expensesQuery->orderBy('created_at', 'desc')->limit($limit)->get();
    }

    /**
     * Search incomes.
     */
    private function searchIncomes(string $query, ?int $buildingId, $user, array $filters, int $limit)
    {
        $incomesQuery = Income::query()
            ->with(['user', 'building'])
            ->where(function ($q) use ($query) {
                $q->where('notes', 'LIKE', "%{$query}%")
                  ->orWhere('amount', 'LIKE', "%{$query}%")
                  ->orWhere('payment_method', 'LIKE', "%{$query}%")
                  ->orWhereHas('user', function ($subQ) use ($query) {
                      $subQ->where('name', 'LIKE', "%{$query}%")
                           ->orWhere('apartment_number', 'LIKE', "%{$query}%");
                  });
            });

        // Apply building scope
        if ($buildingId) {
            $incomesQuery->where('building_id', $buildingId);
        } elseif ($user->role !== 'super_admin') {
            $incomesQuery->where('building_id', $user->building_id);
        }

        // Apply additional filters
        if (!empty($filters['date_from'])) {
            $incomesQuery->whereDate('payment_date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $incomesQuery->whereDate('payment_date', '<=', $filters['date_to']);
        }

        if (!empty($filters['amount_min'])) {
            $incomesQuery->where('amount', '>=', $filters['amount_min']);
        }

        if (!empty($filters['amount_max'])) {
            $incomesQuery->where('amount', '<=', $filters['amount_max']);
        }

        return $incomesQuery->orderBy('payment_date', 'desc')->limit($limit)->get();
    }

    /**
     * Search payments.
     */
    private function searchPayments(string $query, ?int $buildingId, $user, array $filters, int $limit)
    {
        $paymentsQuery = Payment::query()
            ->with(['user', 'expense.expenseType'])
            ->where(function ($q) use ($query) {
                $q->where('amount', 'LIKE', "%{$query}%")
                  ->orWhere('payment_method', 'LIKE', "%{$query}%")
                  ->orWhere('status', 'LIKE', "%{$query}%")
                  ->orWhereHas('user', function ($subQ) use ($query) {
                      $subQ->where('name', 'LIKE', "%{$query}%")
                           ->orWhere('apartment_number', 'LIKE', "%{$query}%");
                  })
                  ->orWhereHas('expense.expenseType', function ($subQ) use ($query) {
                      $subQ->where('name', 'LIKE', "%{$query}%");
                  });
            });

        // Apply building scope through user relationship
        if ($buildingId) {
            $paymentsQuery->whereHas('user', function ($q) use ($buildingId) {
                $q->where('building_id', $buildingId);
            });
        } elseif ($user->role !== 'super_admin') {
            $paymentsQuery->whereHas('user', function ($q) use ($user) {
                $q->where('building_id', $user->building_id);
            });
        }

        // Apply additional filters
        if (!empty($filters['date_from'])) {
            $paymentsQuery->whereDate('payment_date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $paymentsQuery->whereDate('payment_date', '<=', $filters['date_to']);
        }

        if (!empty($filters['amount_min'])) {
            $paymentsQuery->where('amount', '>=', $filters['amount_min']);
        }

        if (!empty($filters['amount_max'])) {
            $paymentsQuery->where('amount', '<=', $filters['amount_max']);
        }

        return $paymentsQuery->orderBy('payment_date', 'desc')->limit($limit)->get();
    }

    /**
     * Search expense types.
     */
    private function searchExpenseTypes(string $query, ?int $buildingId, $user, int $limit)
    {
        $expenseTypesQuery = ExpenseType::query()
            ->with(['building'])
            ->where('name', 'LIKE', "%{$query}%");

        // Apply building scope
        if ($buildingId) {
            $expenseTypesQuery->where('building_id', $buildingId);
        } elseif ($user->role !== 'super_admin') {
            $expenseTypesQuery->where('building_id', $user->building_id);
        }

        return $expenseTypesQuery->orderBy('name')->limit($limit)->get();
    }
}
