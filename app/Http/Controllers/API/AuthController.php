<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Building;
use App\Models\Expense;
use App\Models\ExpenseType;
use App\Mail\EmailVerificationMail;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class AuthController extends Controller
{
    public function register(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone_number' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'apartment_number' => 'required|string|max:10',
            'building_id' => 'nullable|exists:buildings,id',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone_number' => $validated['phone_number'] ?? null,
            'password' => Hash::make($validated['password']),
            'apartment_number' => $validated['apartment_number'],
            'building_id' => $validated['building_id'] ?? null,
        ]);

        // Generate verification token and send email
        $verificationToken = $user->generateEmailVerificationToken();
        Mail::to($user->email)->queue(new EmailVerificationMail($user, $verificationToken));

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'message' => 'Registration successful. Please check your email to verify your account.'
        ], 201);
    }

    public function signup(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone_number' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'building' => 'required|array',
            'building.name' => 'required|string|max:255',
            'building.address' => 'nullable|string',
            'building.city' => 'nullable|string',
            'building.postal_code' => 'nullable|string',
            'building.monthly_fee' => 'required|numeric|min:0',
            'building.currency' => 'required|string|size:3',
        ]);

        DB::beginTransaction();

        try {
            // Create the building first
            $building = Building::create([
                'name' => $validated['building']['name'],
                'address' => $validated['building']['address'],
                'city' => $validated['building']['city'],
                'postal_code' => $validated['building']['postal_code'],
                'monthly_fee' => $validated['building']['monthly_fee'],
                'currency' => $validated['building']['currency'],
                'package_id' => 1, // Default package
                'package_updated_at' => now(),
            ]);

            // Create the admin user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'] ?? null,
                'password' => Hash::make($validated['password']),
                'apartment_number' => null, // Admins don't need apartment numbers
                'role' => 'admin',
                'building_id' => $building->id,
            ]);

            // Generate verification token and send email
            $verificationToken = $user->generateEmailVerificationToken();
            Mail::to($user->email)->queue(new EmailVerificationMail($user, $verificationToken));

            // Create authentication token
            $token = $user->createToken('auth_token')->plainTextToken;

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Account created successfully. Please check your email to verify your account.',
                'user' => $user,
                'building' => $building,
                'token' => $token,
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function login(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if (!Auth::attempt($validated)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = User::where('email', $validated['email'])->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
        ]);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json(['message' => 'Logged out successfully']);
    }

    public function user(Request $request): JsonResponse
    {
        return response()->json($request->user());
    }

    public function updateProfile(Request $request): JsonResponse
    {
        $user = $request->user();

        // Only allow neighbors to update their own profile
        if ($user->role !== 'neighbor') {
            return response()->json(['message' => 'Unauthorized. Only neighbors can update their profile.'], 403);
        }

        $validated = $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', 'min:8'],
        ]);

        $user->email = $validated['email'];

        if ($request->filled('password')) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        return response()->json([
            'message' => 'Profile updated successfully.',
            'user' => $user
        ]);
    }


}
