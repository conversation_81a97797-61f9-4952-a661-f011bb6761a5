<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Models\Subscription;
use App\Models\Building;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class SubscriptionController extends Controller
{
    /**
     * Get current subscription for the user's building.
     */
    public function current(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();

        if (!$subscription) {
            return response()->json([
                'subscription' => null,
                'package' => $building->currentPackage,
                'message' => 'No active subscription found.'
            ]);
        }

        return response()->json([
            'subscription' => $subscription->getSummary(),
            'package' => $subscription->package
        ]);
    }

    /**
     * Subscribe to a package.
     */
    public function subscribe(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $validated = $request->validate([
            'package_id' => ['required', 'exists:packages,id'],
            'billing_cycle' => ['required', Rule::in(['monthly', 'annual'])],
        ]);

        $package = Package::findOrFail($validated['package_id']);

        if (!$package->is_active) {
            return response()->json(['message' => 'Package is not available.'], 422);
        }

        // Check if building already has an active subscription
        $existingSubscription = $building->getCurrentSubscription();
        if ($existingSubscription && $existingSubscription->package_id === $package->id) {
            return response()->json(['message' => 'Already subscribed to this package.'], 422);
        }

        // Cancel existing subscription if any
        if ($existingSubscription) {
            $existingSubscription->cancel('Upgraded to new package');
        }

        // Create new subscription
        $billingCycle = $validated['billing_cycle'];
        $amount = $package->getEffectivePrice($billingCycle);
        $duration = $billingCycle === 'annual' ? 12 : 1;

        $subscription = $building->subscriptions()->create([
            'package_id' => $package->id,
            'user_id' => $user->id,
            'status' => $package->isFree() ? 'active' : 'trial',
            'billing_cycle' => $billingCycle,
            'amount' => $amount,
            'currency' => 'USD',
            'starts_at' => now(),
            'ends_at' => now()->addMonths($duration),
            'trial_ends_at' => $package->trial_days ? now()->addDays($package->trial_days) : null,
            'next_payment_due' => $package->isFree() ? null : now()->addDays($package->trial_days ?: 30),
            'auto_renew' => true,
        ]);

        // Update building's current package
        $building->update([
            'current_package_id' => $package->id,
            'package_updated_at' => now(),
        ]);

        return response()->json([
            'message' => 'Successfully subscribed to package.',
            'subscription' => $subscription->getSummary()
        ], 201);
    }

    /**
     * Upgrade/downgrade subscription.
     */
    public function upgrade(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();
        if (!$subscription) {
            return response()->json(['message' => 'No active subscription found.'], 404);
        }

        $validated = $request->validate([
            'package_id' => ['required', 'exists:packages,id'],
            'billing_cycle' => ['nullable', Rule::in(['monthly', 'annual'])],
        ]);

        $newPackage = Package::findOrFail($validated['package_id']);

        if (!$newPackage->is_active) {
            return response()->json(['message' => 'Package is not available.'], 422);
        }

        if ($subscription->package_id === $newPackage->id) {
            return response()->json(['message' => 'Already subscribed to this package.'], 422);
        }

        $billingCycle = $validated['billing_cycle'] ?? $subscription->billing_cycle;

        // Upgrade the subscription
        $subscription->upgradeTo($newPackage, $billingCycle);

        // Update building's current package
        $building->update([
            'current_package_id' => $newPackage->id,
            'package_updated_at' => now(),
        ]);

        return response()->json([
            'message' => 'Package updated successfully.',
            'subscription' => $subscription->fresh()->getSummary()
        ]);
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();
        if (!$subscription) {
            return response()->json(['message' => 'No active subscription found.'], 404);
        }

        $validated = $request->validate([
            'reason' => ['nullable', 'string', 'max:500'],
        ]);

        $subscription->cancel($validated['reason'] ?? 'User requested cancellation');

        return response()->json([
            'message' => 'Subscription cancelled successfully.',
            'subscription' => $subscription->fresh()->getSummary()
        ]);
    }

    /**
     * Renew subscription.
     */
    public function renew(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();
        if (!$subscription) {
            return response()->json(['message' => 'No active subscription found.'], 404);
        }

        if (!$subscription->isExpired() && !$subscription->isApproachingExpiration()) {
            return response()->json(['message' => 'Subscription is not due for renewal.'], 422);
        }

        $validated = $request->validate([
            'months' => ['nullable', 'integer', 'min:1', 'max:12'],
        ]);

        $months = $validated['months'] ?? ($subscription->billing_cycle === 'annual' ? 12 : 1);

        $subscription->renew($months);

        return response()->json([
            'message' => 'Subscription renewed successfully.',
            'subscription' => $subscription->fresh()->getSummary()
        ]);
    }

    /**
     * Get subscription history.
     */
    public function history(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscriptions = $building->subscriptions()
            ->with('package')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'package_name' => $subscription->package->name,
                    'status' => $subscription->status,
                    'billing_cycle' => $subscription->billing_cycle,
                    'amount' => $subscription->amount,
                    'currency' => $subscription->currency,
                    'starts_at' => $subscription->starts_at->toISOString(),
                    'ends_at' => $subscription->ends_at->toISOString(),
                    'cancelled_at' => $subscription->cancelled_at?->toISOString(),
                    'cancellation_reason' => $subscription->cancellation_reason,
                    'created_at' => $subscription->created_at->toISOString(),
                ];
            });

        return response()->json([
            'subscriptions' => $subscriptions
        ]);
    }

    /**
     * Get usage statistics.
     */
    public function usage(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $subscription = $building->getCurrentSubscription();
        if (!$subscription) {
            return response()->json(['message' => 'No active subscription found.'], 404);
        }

        // Update current usage
        $currentNeighbors = $building->users()->count();
        $storageUsed = $building->fileAttachments()->sum('file_size') ?? 0;

        $subscription->updateUsage([
            'neighbors_count' => $currentNeighbors,
            'storage_used' => $storageUsed,
        ]);

        $usage = [
            'neighbors' => [
                'current' => $currentNeighbors,
                'limit' => $subscription->package->max_neighbors,
                'percentage' => $subscription->getNeighborsUsagePercentage(),
                'unlimited' => $subscription->package->hasUnlimitedNeighbors(),
                'limit_exceeded' => $subscription->isNeighborsLimitExceeded(),
            ],
            'storage' => [
                'used_bytes' => $storageUsed,
                'used_gb' => round($storageUsed / (1024 * 1024 * 1024), 2),
                'limit_gb' => $subscription->package->storage_limit_gb,
                'percentage' => $subscription->getStorageUsagePercentage(),
                'unlimited' => $subscription->package->hasUnlimitedStorage(),
                'limit_exceeded' => $subscription->isStorageLimitExceeded(),
            ],
            'notifications' => [
                'sent_this_month' => $subscription->notifications_sent_this_month,
                'enabled' => $subscription->package->notifications_enabled,
            ],
            'emails' => [
                'sent_this_month' => $subscription->emails_sent_this_month,
                'enabled' => $subscription->package->email_notifications_enabled,
            ],
            'features' => [
                'file_attachments' => $subscription->package->file_attachments_enabled,
                'max_file_size_mb' => $subscription->package->getMaxFileSizeMb(),
                'max_files_per_record' => $subscription->package->getMaxFilesPerRecord(),
                'priority_support' => $subscription->package->priority_support,
                'advanced_reporting' => $subscription->package->advanced_reporting,
            ],
        ];

        return response()->json([
            'usage' => $usage,
            'subscription' => $subscription->getSummary()
        ]);
    }

    /**
     * Check if building can perform an action based on package limits.
     */
    public function checkLimits(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json(['message' => 'No building associated with user.'], 404);
        }

        $validated = $request->validate([
            'action' => ['required', Rule::in(['add_neighbor', 'send_notification', 'upload_file', 'send_email'])],
            'count' => ['nullable', 'integer', 'min:1'],
        ]);

        $action = $validated['action'];
        $count = $validated['count'] ?? 1;

        $package = $building->getEffectivePackage();
        if (!$package) {
            return response()->json(['message' => 'No package found for building.'], 404);
        }

        $canPerform = false;
        $reason = '';

        switch ($action) {
            case 'add_neighbor':
                $canPerform = $building->canAddNeighbors($count);
                $reason = $canPerform ? '' : 'Neighbor limit exceeded for current package';
                break;
            case 'send_notification':
                $canPerform = $building->canUseNotifications();
                $reason = $canPerform ? '' : 'Notifications not enabled for current package';
                break;
            case 'upload_file':
                $canPerform = $building->canUseFileAttachments();
                $reason = $canPerform ? '' : 'File attachments not enabled for current package';
                break;
            case 'send_email':
                $canPerform = $building->canUseEmailNotifications();
                $reason = $canPerform ? '' : 'Email notifications not enabled for current package';
                break;
        }

        return response()->json([
            'can_perform' => $canPerform,
            'reason' => $reason,
            'package' => [
                'name' => $package->name,
                'slug' => $package->slug,
            ],
            'upgrade_required' => !$canPerform,
        ]);
    }
}
