<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\EmailUsageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmailUsageController extends Controller
{
    protected EmailUsageService $emailUsageService;

    public function __construct(EmailUsageService $emailUsageService)
    {
        $this->emailUsageService = $emailUsageService;
    }

    /**
     * Get email usage statistics for the current user's building.
     */
    public function getUsageStats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $period = $request->get('period', 'month'); // today, week, month, year
        
        if (!in_array($period, ['today', 'week', 'month', 'year'])) {
            return response()->json([
                'message' => 'Invalid period. Must be one of: today, week, month, year'
            ], 400);
        }

        $stats = $this->emailUsageService->getBuildingUsageStats($building, $period);

        return response()->json([
            'building_id' => $building->id,
            'building_name' => $building->name,
            'usage_stats' => $stats,
        ]);
    }

    /**
     * Get current email quota status for the building.
     */
    public function getQuotaStatus(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $quotaCheck = $this->emailUsageService->canSendEmail($building);
        $package = $building->getEffectivePackage();

        $response = [
            'building_id' => $building->id,
            'building_name' => $building->name,
            'can_send_email' => $quotaCheck['allowed'],
            'message' => $quotaCheck['message'],
            'quota_info' => $quotaCheck['quota_info'],
        ];

        if ($package) {
            $response['package_info'] = [
                'name' => $package->name,
                'daily_limit' => $package->email_limit_per_day,
                'monthly_limit' => $package->email_limit_per_month,
                'unlimited_daily' => $package->hasUnlimitedDailyEmails(),
                'unlimited_monthly' => $package->hasUnlimitedMonthlyEmails(),
                'warnings_enabled' => $package->email_quota_warnings_enabled,
            ];

            // Get quota warnings
            $warnings = $this->emailUsageService->getQuotaWarnings($building, $package);
            $response['warnings'] = $warnings;
        }

        return response()->json($response);
    }

    /**
     * Get detailed email usage history for the building.
     */
    public function getUsageHistory(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        // Only allow admins to view detailed usage history
        if ($user->role !== 'admin') {
            return response()->json([
                'message' => 'Access denied. Admin role required.'
            ], 403);
        }

        $perPage = min((int) $request->get('per_page', 20), 100);
        $emailType = $request->get('email_type');
        $status = $request->get('status');

        $query = $building->emailUsage()->orderBy('created_at', 'desc');

        if ($emailType) {
            $query->where('email_type', $emailType);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $usage = $query->paginate($perPage);

        return response()->json([
            'building_id' => $building->id,
            'building_name' => $building->name,
            'usage_history' => $usage,
        ]);
    }

    /**
     * Get email usage summary by type for charts/graphs.
     */
    public function getUsageSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $period = $request->get('period', 'month');
        
        if (!in_array($period, ['today', 'week', 'month', 'year'])) {
            return response()->json([
                'message' => 'Invalid period. Must be one of: today, week, month, year'
            ], 400);
        }

        $usageByType = \App\Models\EmailUsage::getBuildingUsageByType($building->id, $period);
        $stats = \App\Models\EmailUsage::getBuildingStats($building->id, $period);

        return response()->json([
            'building_id' => $building->id,
            'building_name' => $building->name,
            'period' => $period,
            'summary' => [
                'total_emails' => $stats['total'],
                'sent_emails' => $stats['sent'],
                'failed_emails' => $stats['failed'],
                'queued_emails' => $stats['queued'],
                'success_rate' => $stats['success_rate'],
                'usage_by_type' => $usageByType,
            ],
        ]);
    }
}
