<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ExportRequest;
use App\Services\ExportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ExportController extends Controller
{
    protected ExportService $exportService;

    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Get available export types for the current user's building.
     */
    public function getAvailableTypes(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $availableTypes = $this->exportService->getAvailableExportTypes($building);

        return response()->json([
            'building_id' => $building->id,
            'building_name' => $building->name,
            'available_types' => $availableTypes,
        ]);
    }

    /**
     * Create a new export request.
     */
    public function createExport(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $availableTypes = array_keys($this->exportService->getAvailableExportTypes($building));
        
        $request->validate([
            'type' => ['required', Rule::in($availableTypes)],
            'format' => ['required', Rule::in(['pdf', 'excel'])],
            'parameters' => 'sometimes|array',
            'parameters.date_from' => 'sometimes|date',
            'parameters.date_to' => 'sometimes|date|after_or_equal:parameters.date_from',
            'parameters.include_attachments' => 'sometimes|boolean',
        ]);

        // Check if user can create export
        $canExport = $this->exportService->canCreateExport(
            $building,
            $request->input('type'),
            $request->input('format')
        );

        if (!$canExport['allowed']) {
            return response()->json([
                'message' => $canExport['message'],
                'quota_info' => $canExport['quota_info'],
            ], 403);
        }

        try {
            $exportRequest = $this->exportService->createExportRequest(
                $building,
                $user,
                $request->input('type'),
                $request->input('format'),
                $request->input('parameters', [])
            );

            return response()->json([
                'message' => 'Export request created successfully.',
                'export_request' => [
                    'id' => $exportRequest->id,
                    'type' => $exportRequest->type,
                    'format' => $exportRequest->format,
                    'status' => $exportRequest->status,
                    'created_at' => $exportRequest->created_at->toISOString(),
                ],
                'quota_info' => $canExport['quota_info'],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create export request.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get export requests for the current user's building.
     */
    public function getExports(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $perPage = min((int) $request->get('per_page', 20), 100);
        $status = $request->get('status');
        $type = $request->get('type');

        $query = ExportRequest::forBuilding($building->id)
            ->with('user:id,name,email')
            ->orderBy('created_at', 'desc');

        if ($status) {
            $query->where('status', $status);
        }

        if ($type) {
            $query->where('type', $type);
        }

        $exports = $query->paginate($perPage);

        // Add additional computed fields
        $exports->getCollection()->transform(function ($export) {
            return [
                'id' => $export->id,
                'type' => $export->type,
                'type_display_name' => $export->type_display_name,
                'format' => $export->format,
                'status' => $export->status,
                'file_name' => $export->file_name,
                'file_size_human' => $export->file_size_human,
                'record_count' => $export->record_count,
                'download_count' => $export->download_count,
                'processing_time' => $export->processing_time,
                'created_at' => $export->created_at->toISOString(),
                'completed_at' => $export->completed_at?->toISOString(),
                'expires_at' => $export->expires_at?->toISOString(),
                'last_downloaded_at' => $export->last_downloaded_at?->toISOString(),
                'is_expired' => $export->isExpired(),
                'file_exists' => $export->fileExists(),
                'user' => $export->user,
                'error_message' => $export->error_message,
            ];
        });

        return response()->json([
            'building_id' => $building->id,
            'building_name' => $building->name,
            'exports' => $exports,
        ]);
    }

    /**
     * Get a specific export request.
     */
    public function getExport(Request $request, int $exportId): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $export = ExportRequest::forBuilding($building->id)
            ->with('user:id,name,email')
            ->find($exportId);

        if (!$export) {
            return response()->json([
                'message' => 'Export request not found.'
            ], 404);
        }

        return response()->json([
            'export' => [
                'id' => $export->id,
                'type' => $export->type,
                'type_display_name' => $export->type_display_name,
                'format' => $export->format,
                'status' => $export->status,
                'parameters' => $export->parameters,
                'file_name' => $export->file_name,
                'file_size' => $export->file_size,
                'file_size_human' => $export->file_size_human,
                'record_count' => $export->record_count,
                'download_count' => $export->download_count,
                'processing_time' => $export->processing_time,
                'created_at' => $export->created_at->toISOString(),
                'started_at' => $export->started_at?->toISOString(),
                'completed_at' => $export->completed_at?->toISOString(),
                'expires_at' => $export->expires_at?->toISOString(),
                'last_downloaded_at' => $export->last_downloaded_at?->toISOString(),
                'is_expired' => $export->isExpired(),
                'file_exists' => $export->fileExists(),
                'user' => $export->user,
                'error_message' => $export->error_message,
            ],
        ]);
    }

    /**
     * Download an export file.
     */
    public function downloadExport(Request $request, int $exportId): Response
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response('No building associated with user.', 400);
        }

        $export = ExportRequest::forBuilding($building->id)->find($exportId);

        if (!$export) {
            return response('Export request not found.', 404);
        }

        if ($export->status !== 'completed') {
            return response('Export is not completed yet.', 400);
        }

        if ($export->isExpired()) {
            return response('Export file has expired.', 410);
        }

        if (!$export->fileExists()) {
            return response('Export file not found.', 404);
        }

        // Record the download
        $export->recordDownload();

        $disk = Storage::disk(config('export.storage.disk', 'local'));
        $content = $disk->get($export->file_path);

        $mimeType = $export->format === 'excel' 
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf';

        return response($content, 200, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'attachment; filename="' . $export->file_name . '"',
            'Content-Length' => strlen($content),
        ]);
    }

    /**
     * Get export statistics for the building.
     */
    public function getExportStats(Request $request): JsonResponse
    {
        $user = $request->user();
        $building = $user->building;

        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $period = $request->get('period', 'month');
        
        if (!in_array($period, ['today', 'week', 'month', 'year'])) {
            return response()->json([
                'message' => 'Invalid period. Must be one of: today, week, month, year'
            ], 400);
        }

        $stats = $this->exportService->getBuildingExportStats($building, $period);
        $package = $building->getEffectivePackage();

        $response = [
            'building_id' => $building->id,
            'building_name' => $building->name,
            'period' => $period,
            'stats' => $stats,
        ];

        if ($package) {
            $response['package_limits'] = [
                'exports_enabled' => $package->exports_enabled,
                'exports_per_month' => $package->exports_per_month,
                'max_records_per_export' => $package->max_records_per_export,
                'export_formats' => $package->export_formats,
            ];

            // Get current quota status
            $quotaCheck = $this->exportService->canCreateExport($building, 'financial_summary', 'pdf');
            $response['quota_status'] = $quotaCheck;
        }

        return response()->json($response);
    }
}
