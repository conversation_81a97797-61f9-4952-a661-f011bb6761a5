<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications for the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        $query = $user->notifications()
            ->with(['building'])
            ->orderBy('created_at', 'desc');

        // Filter by read status
        if ($request->has('unread_only') && $request->boolean('unread_only')) {
            $query->unread();
        }

        // Filter by type
        if ($request->has('type')) {
            $query->ofType($request->type);
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->ofPriority($request->priority);
        }

        $notifications = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Store a newly created notification (Admin only).
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => ['nullable', 'exists:users,id'],
            'building_id' => ['nullable', 'exists:buildings,id'],
            'type' => ['required', Rule::in(Notification::getTypes())],
            'title' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string'],
            'data' => ['nullable', 'array'],
            'priority' => ['nullable', Rule::in(Notification::getPriorities())],
            'scheduled_for' => ['nullable', 'date', 'after:now'],
            'send_to_all_building' => ['boolean'],
        ]);

        $user = $request->user();

        // If sending to all building users
        if ($request->boolean('send_to_all_building')) {
            $buildingId = $validated['building_id'] ?? $user->building_id;

            if (!$buildingId) {
                return response()->json(['message' => 'Building ID is required when sending to all building users.'], 400);
            }

            // Get all users in the building
            $buildingUsers = \App\Models\User::where('building_id', $buildingId)->get();

            $notifications = [];
            foreach ($buildingUsers as $buildingUser) {
                $notifications[] = Notification::create([
                    'user_id' => $buildingUser->id,
                    'building_id' => $buildingId,
                    'type' => $validated['type'],
                    'title' => $validated['title'],
                    'message' => $validated['message'],
                    'data' => $validated['data'] ?? null,
                    'priority' => $validated['priority'] ?? 'medium',
                    'scheduled_for' => $validated['scheduled_for'] ?? null,
                ]);
            }

            return response()->json([
                'message' => 'Notifications sent to all building users.',
                'count' => count($notifications),
            ], 201);
        }

        // Single user notification
        $notification = Notification::create([
            'user_id' => $validated['user_id'],
            'building_id' => $validated['building_id'] ?? $user->building_id,
            'type' => $validated['type'],
            'title' => $validated['title'],
            'message' => $validated['message'],
            'data' => $validated['data'] ?? null,
            'priority' => $validated['priority'] ?? 'medium',
            'scheduled_for' => $validated['scheduled_for'] ?? null,
        ]);

        return response()->json([
            'message' => 'Notification created successfully.',
            'notification' => $notification->load(['user', 'building']),
        ], 201);
    }

    /**
     * Display the specified notification.
     */
    public function show(Request $request, Notification $notification): JsonResponse
    {
        $user = $request->user();

        // Ensure user can only see their own notifications
        if ($notification->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        return response()->json([
            'notification' => $notification->load(['building']),
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, Notification $notification): JsonResponse
    {
        $user = $request->user();

        // Ensure user can only mark their own notifications as read
        if ($notification->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $notification->markAsRead();

        return response()->json([
            'message' => 'Notification marked as read.',
            'notification' => $notification->fresh(),
        ]);
    }

    /**
     * Mark notification as unread.
     */
    public function markAsUnread(Request $request, Notification $notification): JsonResponse
    {
        $user = $request->user();

        // Ensure user can only mark their own notifications as unread
        if ($notification->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $notification->markAsUnread();

        return response()->json([
            'message' => 'Notification marked as unread.',
            'notification' => $notification->fresh(),
        ]);
    }

    /**
     * Mark all notifications as read for the authenticated user.
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = $request->user();

        $count = $user->notifications()->unread()->update(['read_at' => now()]);

        return response()->json([
            'message' => 'All notifications marked as read.',
            'count' => $count,
        ]);
    }

    /**
     * Get unread notifications count.
     */
    public function unreadCount(Request $request): JsonResponse
    {
        $user = $request->user();

        $count = $user->unreadNotifications()->count();

        return response()->json([
            'unread_count' => $count,
        ]);
    }

    /**
     * Remove the specified notification.
     */
    public function destroy(Request $request, Notification $notification): JsonResponse
    {
        $user = $request->user();

        // Ensure user can only delete their own notifications
        if ($notification->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized.'], 403);
        }

        $notification->delete();

        return response()->json([
            'message' => 'Notification deleted successfully.',
        ]);
    }
}
