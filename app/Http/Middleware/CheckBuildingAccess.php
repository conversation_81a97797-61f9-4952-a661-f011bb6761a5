<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckBuildingAccess
{
    /**
     * Handle an incoming request.
     *
     * This middleware ensures that regular admins can only access resources
     * related to their own building, while super admins have full access.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Super admins have access to everything
        if ($user && $user->role === 'super_admin') {
            return $next($request);
        }

        // Regular admins must have a building_id
        if ($user && $user->role === 'admin' && !$user->building_id) {
            return response()->json([
                'message' => 'Admin user must be assigned to a building'
            ], 403);
        }

        // Add building_id to request for use in controllers
        if ($user && $user->role === 'admin') {
            $request->merge(['user_building_id' => $user->building_id]);
        }

        return $next($request);
    }
}
