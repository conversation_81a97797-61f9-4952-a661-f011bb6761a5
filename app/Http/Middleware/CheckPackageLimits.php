<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPackageLimits
{
    /**
     * Handle an incoming request.
     *
     * This middleware checks package limits for various features.
     * Super admin users bypass all package limits and have unlimited access.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $feature  The feature to check (neighbors, notifications, files, etc.)
     */
    public function handle(Request $request, Closure $next, string $feature = null): Response
    {
        $user = $request->user();

        // Super admin users bypass all package limits
        if ($user && $user->isSuperAdmin()) {
            return $next($request);
        }

        if (!$user || !$user->building) {
            return response()->json(['message' => 'No building associated with user.'], 403);
        }

        $building = $user->building;
        $package = $building->getEffectivePackage();
        $subscription = $building->getCurrentSubscription();

        if (!$package) {
            // If no package is assigned, assign the free package by default
            $freePackage = \App\Models\Package::where('slug', 'free')->first();
            if ($freePackage) {
                $building->update(['current_package_id' => $freePackage->id]);
                $package = $freePackage;
            } else {
                return response()->json(['message' => 'No package found for building and no default package available.'], 403);
            }
        }

        // Check if subscription is in grace period and apply restrictions
        $isInGracePeriod = $subscription && $subscription->isInGracePeriod();
        if ($isInGracePeriod) {
            $gracePeriodFeatures = config('subscription.grace_period_features', []);

            // Apply grace period restrictions for certain features
            if ($feature && !$this->isFeatureAllowedInGracePeriod($feature, $gracePeriodFeatures)) {
                return response()->json([
                    'message' => 'This feature is not available during the grace period. Please renew your subscription.',
                    'feature' => $feature,
                    'grace_period' => true,
                    'days_remaining' => $subscription->getGracePeriodDaysRemaining(),
                    'upgrade_required' => true,
                ], 403);
            }
        }

        // Check specific feature limits
        if ($feature) {
            $canUse = $this->checkFeatureLimit($building, $package, $feature, $request);

            if (!$canUse['allowed']) {
                return response()->json([
                    'message' => $canUse['message'],
                    'feature' => $feature,
                    'package' => $package->name,
                    'upgrade_required' => true,
                    'current_package' => [
                        'id' => $package->id,
                        'name' => $package->name,
                        'slug' => $package->slug,
                    ]
                ], 403);
            }
        }

        return $next($request);
    }

    /**
     * Check if a specific feature is allowed for the current package.
     */
    private function checkFeatureLimit($building, $package, string $feature, Request $request): array
    {
        switch ($feature) {
            case 'neighbors':
                return $this->checkNeighborLimit($building, $package, $request);

            case 'admins':
                return $this->checkAdminLimit($building, $package, $request);

            case 'notifications':
                return $this->checkNotificationLimit($package);

            case 'email_notifications':
                return $this->checkEmailNotificationLimit($package);

            case 'file_attachments':
                return $this->checkFileAttachmentLimit($package, $request);

            case 'storage':
                return $this->checkStorageLimit($building, $package, $request);

            case 'sms_notifications':
                return $this->checkSmsNotificationLimit($package);

            default:
                return ['allowed' => true, 'message' => ''];
        }
    }

    /**
     * Check neighbor limit.
     */
    private function checkNeighborLimit($building, $package, Request $request): array
    {
        if ($package->hasUnlimitedNeighbors()) {
            return ['allowed' => true, 'message' => ''];
        }

        $currentCount = $building->users()->count();
        $addingCount = 1; // Default to adding 1 neighbor

        // Try to get count from request
        if ($request->has('count')) {
            $addingCount = (int) $request->input('count', 1);
        } elseif ($request->has('users') && is_array($request->input('users'))) {
            $addingCount = count($request->input('users'));
        }

        if (($currentCount + $addingCount) > $package->max_neighbors) {
            return [
                'allowed' => false,
                'message' => "Neighbor limit exceeded. Current: {$currentCount}, Limit: {$package->max_neighbors}"
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check admin limit.
     */
    private function checkAdminLimit($building, $package, Request $request): array
    {
        // Only check admin limits if the request is trying to create/update an admin
        $role = $request->input('role');
        if ($role !== 'admin') {
            return ['allowed' => true, 'message' => ''];
        }

        // Check if package supports multi-admin
        if (!$package->hasMultiAdminSupport()) {
            return [
                'allowed' => false,
                'message' => 'Your package does not support multiple admins.'
            ];
        }

        $currentAdminCount = $building->users()->where('role', 'admin')->count();
        $adminLimit = $package->getMaxAdmins();

        // For updates, exclude the current user being updated
        if ($request->isMethod('PUT') || $request->isMethod('PATCH')) {
            $userId = $request->route('user')?->id;
            if ($userId) {
                $currentUser = $building->users()->where('role', 'admin')->where('id', $userId)->first();
                if ($currentUser) {
                    $currentAdminCount--; // Don't count the user being updated
                }
            }
        }

        if ($currentAdminCount >= $adminLimit) {
            return [
                'allowed' => false,
                'message' => "Admin limit reached ({$currentAdminCount}/{$adminLimit}) for your current package."
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check notification limit.
     */
    private function checkNotificationLimit($package): array
    {
        if (!$package->notifications_enabled) {
            return [
                'allowed' => false,
                'message' => 'Notifications are not enabled for your current package.'
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check email notification limit.
     */
    private function checkEmailNotificationLimit($package): array
    {
        if (!$package->email_notifications_enabled) {
            return [
                'allowed' => false,
                'message' => 'Email notifications are not enabled for your current package.'
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check SMS notification limit.
     */
    private function checkSmsNotificationLimit($package): array
    {
        if (!$package->sms_notifications_enabled) {
            return [
                'allowed' => false,
                'message' => 'SMS notifications are not enabled for your current package.'
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check file attachment limit.
     */
    private function checkFileAttachmentLimit($package, Request $request): array
    {
        if (!$package->file_attachments_enabled) {
            return [
                'allowed' => false,
                'message' => 'File attachments are not enabled for your current package.'
            ];
        }

        // Check file size if files are being uploaded
        if ($request->hasFile('files')) {
            $files = $request->file('files');
            if (!is_array($files)) {
                $files = [$files];
            }

            foreach ($files as $file) {
                $fileSizeMB = $file->getSize() / (1024 * 1024);
                $maxFileSizeMb = $package->getMaxFileSizeMb();
                if ($fileSizeMB > $maxFileSizeMb) {
                    return [
                        'allowed' => false,
                        'message' => "File size exceeds limit. Max allowed: {$maxFileSizeMb}MB"
                    ];
                }
            }

            // Check number of files
            $maxFilesPerRecord = $package->getMaxFilesPerRecord();
            if (count($files) > $maxFilesPerRecord) {
                return [
                    'allowed' => false,
                    'message' => "Too many files. Max allowed: {$maxFilesPerRecord} files per record"
                ];
            }
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check storage limit.
     */
    private function checkStorageLimit($building, $package, Request $request): array
    {
        if ($package->hasUnlimitedStorage()) {
            return ['allowed' => true, 'message' => ''];
        }

        $currentStorageBytes = $building->fileAttachments()->sum('file_size') ?? 0;
        $currentStorageGB = $currentStorageBytes / (1024 * 1024 * 1024);

        // Check if adding new files would exceed limit
        if ($request->hasFile('files')) {
            $files = $request->file('files');
            if (!is_array($files)) {
                $files = [$files];
            }

            $additionalBytes = 0;
            foreach ($files as $file) {
                $additionalBytes += $file->getSize();
            }

            $newTotalGB = ($currentStorageBytes + $additionalBytes) / (1024 * 1024 * 1024);

            if ($newTotalGB > $package->storage_limit_gb) {
                return [
                    'allowed' => false,
                    'message' => "Storage limit exceeded. Current: " . round($currentStorageGB, 2) . "GB, Limit: {$package->storage_limit_gb}GB"
                ];
            }
        } elseif ($currentStorageGB > $package->storage_limit_gb) {
            return [
                'allowed' => false,
                'message' => "Storage limit exceeded. Current: " . round($currentStorageGB, 2) . "GB, Limit: {$package->storage_limit_gb}GB"
            ];
        }

        return ['allowed' => true, 'message' => ''];
    }

    /**
     * Check if a feature is allowed during grace period.
     */
    private function isFeatureAllowedInGracePeriod(string $feature, array $gracePeriodFeatures): bool
    {
        // Map features to grace period settings
        $featureMap = [
            'neighbors' => 'new_users',
            'admins' => 'new_users',
            'file_attachments' => 'new_uploads',
            'storage' => 'new_uploads',
            'exports' => 'data_export',
            'notifications' => 'basic_access',
            'email_notifications' => 'premium_features',
            'sms_notifications' => 'premium_features',
        ];

        $gracePeriodKey = $featureMap[$feature] ?? 'premium_features';

        return $gracePeriodFeatures[$gracePeriodKey] ?? false;
    }
}
