<?php

namespace App\Http\Middleware;

use App\Models\AdminActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TrackAdminActivity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track for authenticated admin users
        $user = $request->user();
        if (!$user || !in_array($user->role, ['admin', 'super_admin'])) {
            return $response;
        }

        // Update last activity timestamp
        if ($user->role === 'admin') {
            $user->updateLastActivity();
        }

        // Track specific actions based on route and method
        $this->trackAction($request, $user, $response);

        return $response;
    }

    /**
     * Track admin actions based on request details.
     */
    private function trackAction(Request $request, $user, Response $response): void
    {
        // Only track successful requests (2xx status codes)
        if ($response->getStatusCode() < 200 || $response->getStatusCode() >= 300) {
            return;
        }

        $route = $request->route();
        if (!$route) {
            return;
        }

        $routeName = $route->getName();
        $method = $request->method();
        $uri = $request->getRequestUri();

        // Map routes to actions
        $action = $this->mapRouteToAction($routeName, $method, $uri);
        
        if (!$action) {
            return;
        }

        // Extract resource information
        $resourceInfo = $this->extractResourceInfo($request, $route);

        // Log the action
        AdminActivityLog::logAction(
            $user,
            $action,
            $resourceInfo['type'] ?? null,
            $resourceInfo['id'] ?? null,
            $resourceInfo['details'] ?? null,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * Map route to action name.
     */
    private function mapRouteToAction(?string $routeName, string $method, string $uri): ?string
    {
        // Direct route name mappings
        $routeActions = [
            'api.expenses.store' => 'created_expense',
            'api.expenses.update' => 'updated_expense',
            'api.expenses.destroy' => 'deleted_expense',
            'api.incomes.store' => 'created_income',
            'api.incomes.update' => 'updated_income',
            'api.incomes.destroy' => 'deleted_income',
            'api.payments.store' => 'created_payment',
            'api.payments.update' => 'updated_payment',
            'api.payments.destroy' => 'deleted_payment',
            'api.notifications.store' => 'sent_notification',
            'api.exports.create' => 'exported_data',
        ];

        if (isset($routeActions[$routeName])) {
            return $routeActions[$routeName];
        }

        // Pattern-based mappings
        $patterns = [
            // User management
            '/api\/admin\/users$/' => $method === 'POST' ? 'created_user' : null,
            '/api\/admin\/users\/\d+$/' => match($method) {
                'PUT', 'PATCH' => 'updated_user',
                'DELETE' => 'deleted_user',
                default => null
            },

            // Admin invitations
            '/api\/admin\/invitations$/' => $method === 'POST' ? 'invited_admin' : null,
            '/api\/admin\/invitations\/\d+\/cancel$/' => 'cancelled_invitation',
            '/api\/admin\/invitations\/\d+\/resend$/' => 'resent_invitation',

            // Archive operations
            '/api\/archive\/expenses$/' => 'archived_expenses',
            '/api\/archive\/incomes$/' => 'archived_incomes',
            '/api\/archive\/expenses\/unarchive$/' => 'unarchived_expenses',
            '/api\/archive\/incomes\/unarchive$/' => 'unarchived_incomes',

            // Building settings
            '/api\/my-building$/' => $method === 'PUT' ? 'updated_building_settings' : null,

            // Package management
            '/api\/admin\/package\/change$/' => 'requested_package_change',

            // File operations
            '/api\/files\/upload$/' => 'uploaded_file',
            '/api\/files\/\d+$/' => $method === 'DELETE' ? 'deleted_file' : null,
        ];

        foreach ($patterns as $pattern => $action) {
            if (preg_match($pattern, $uri)) {
                return $action;
            }
        }

        return null;
    }

    /**
     * Extract resource information from request.
     */
    private function extractResourceInfo(Request $request, $route): array
    {
        $info = [];

        // Get route parameters
        $parameters = $route->parameters();

        // Extract resource type and ID from route parameters
        foreach ($parameters as $key => $value) {
            if (in_array($key, ['expense', 'income', 'payment', 'user', 'notification', 'building'])) {
                $info['type'] = $key;
                $info['id'] = is_object($value) ? $value->id : $value;
                break;
            }
        }

        // Extract additional details from request data
        $details = [];
        
        // For creation/update operations, include relevant data
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $sensitiveFields = ['password', 'token', 'secret'];
            $requestData = $request->all();
            
            // Remove sensitive fields
            foreach ($sensitiveFields as $field) {
                unset($requestData[$field]);
            }
            
            // Limit the amount of data stored
            if (count($requestData) > 0) {
                $details['request_data'] = array_slice($requestData, 0, 10, true);
            }
        }

        // For bulk operations, include count
        if ($request->has('expense_ids')) {
            $details['expense_count'] = count($request->get('expense_ids', []));
        }
        if ($request->has('income_ids')) {
            $details['income_count'] = count($request->get('income_ids', []));
        }

        if (!empty($details)) {
            $info['details'] = $details;
        }

        return $info;
    }
}
