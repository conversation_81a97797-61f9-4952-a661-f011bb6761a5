<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CacheMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        // Only cache GET requests
        if ($request->method() !== 'GET') {
            return $next($request);
        }

        // Parse parameters
        $ttl = isset($parameters[0]) ? (int) $parameters[0] : 300; // Default 5 minutes
        $varyBy = isset($parameters[1]) ? explode(',', $parameters[1]) : [];

        // Generate cache key
        $cacheKey = $this->generateCacheKey($request, $varyBy);

        // Check if response is cached
        $cachedResponse = Cache::get($cacheKey);
        if ($cachedResponse) {
            return response($cachedResponse['content'], $cachedResponse['status'])
                ->withHeaders($cachedResponse['headers'])
                ->header('X-Cache', 'HIT');
        }

        // Process request
        $response = $next($request);

        // Only cache successful responses
        if ($response->getStatusCode() === 200) {
            $this->cacheResponse($cacheKey, $response, $ttl);
        }

        return $response->header('X-Cache', 'MISS');
    }

    /**
     * Generate cache key based on request and vary parameters.
     */
    private function generateCacheKey(Request $request, array $varyBy): string
    {
        $keyParts = [
            'route' => $request->route()->getName() ?? $request->path(),
            'query' => $request->query(),
        ];

        // Add user-specific data if needed
        if (in_array('user', $varyBy) && Auth::check()) {
            $keyParts['user'] = Auth::id();
        }

        // Add building-specific data if needed
        if (in_array('building', $varyBy) && Auth::check()) {
            $keyParts['building'] = Auth::user()->building_id;
        }

        // Add role-specific data if needed
        if (in_array('role', $varyBy) && Auth::check()) {
            $keyParts['role'] = Auth::user()->role;
        }

        // Add custom vary parameters
        foreach ($varyBy as $param) {
            if (!in_array($param, ['user', 'building', 'role']) && $request->has($param)) {
                $keyParts[$param] = $request->get($param);
            }
        }

        return 'api_cache:' . md5(serialize($keyParts));
    }

    /**
     * Cache the response.
     */
    private function cacheResponse(string $cacheKey, Response $response, int $ttl): void
    {
        $cacheData = [
            'content' => $response->getContent(),
            'status' => $response->getStatusCode(),
            'headers' => $response->headers->all(),
            'cached_at' => now()->toISOString(),
        ];

        Cache::put($cacheKey, $cacheData, $ttl);
    }
}
