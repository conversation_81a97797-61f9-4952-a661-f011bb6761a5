<?php

namespace App\Http\Middleware;

use App\Services\ExportService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckExportPermissions
{
    protected ExportService $exportService;

    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }

        $building = $user->building;
        
        if (!$building) {
            return response()->json([
                'message' => 'No building associated with user.'
            ], 400);
        }

        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return response()->json([
                'message' => 'No package found for building.'
            ], 400);
        }

        // Check if exports are enabled for this package
        if (!$package->exports_enabled) {
            return response()->json([
                'message' => 'Export functionality is not enabled for your current package.',
                'package_name' => $package->name,
                'upgrade_required' => true,
            ], 403);
        }

        // For export creation requests, check additional limits
        if ($request->isMethod('POST') && $request->routeIs('api.exports.create')) {
            $type = $request->input('type', 'financial_summary');
            $format = $request->input('format', 'pdf');
            
            $canExport = $this->exportService->canCreateExport($building, $type, $format);
            
            if (!$canExport['allowed']) {
                return response()->json([
                    'message' => $canExport['message'],
                    'quota_info' => $canExport['quota_info'],
                    'package_name' => $package->name,
                    'upgrade_suggested' => true,
                ], 403);
            }

            // Add quota info to request for controller use
            $request->merge(['quota_info' => $canExport['quota_info']]);
        }

        return $next($request);
    }
}
