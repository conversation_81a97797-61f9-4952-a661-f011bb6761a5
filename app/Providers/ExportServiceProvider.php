<?php

namespace App\Providers;

use App\Services\ExportService;
use App\Services\ReportGeneratorService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;

class ExportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ExportService::class, function ($app) {
            return new ExportService();
        });

        $this->app->singleton(ReportGeneratorService::class, function ($app) {
            return new ReportGeneratorService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Ensure export directories exist
        $this->ensureExportDirectoriesExist();

        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\CleanupExpiredExports::class,
                \App\Console\Commands\ExportQueueStatus::class,
                \App\Console\Commands\RetryFailedExports::class,
            ]);
        }
    }

    /**
     * Ensure export directories exist.
     */
    private function ensureExportDirectoriesExist(): void
    {
        $disk = Storage::disk(config('export.storage.disk', 'local'));
        $exportPath = config('export.storage.path', 'exports');

        // Create main export directory
        if (!$disk->exists($exportPath)) {
            $disk->makeDirectory($exportPath);
        }

        // Create PDF temp directory
        $pdfTempDir = config('export.pdf.dompdf.temp_dir');
        if ($pdfTempDir && !file_exists($pdfTempDir)) {
            mkdir($pdfTempDir, 0755, true);
        }

        // Create PDF font cache directory
        $pdfFontCache = config('export.pdf.dompdf.font_cache');
        if ($pdfFontCache && !file_exists($pdfFontCache)) {
            mkdir($pdfFontCache, 0755, true);
        }

        // Create Excel temp directory
        $excelTempDir = config('export.excel.temp_dir');
        if ($excelTempDir && !file_exists($excelTempDir)) {
            mkdir($excelTempDir, 0755, true);
        }
    }
}
