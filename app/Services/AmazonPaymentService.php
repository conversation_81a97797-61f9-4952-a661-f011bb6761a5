<?php

namespace App\Services;

use App\Contracts\PaymentGatewayInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AmazonPaymentService implements PaymentGatewayInterface
{
    private $merchantIdentifier;
    private $accessCode;
    private $shaRequestPhrase;
    private $shaResponsePhrase;
    private $sandboxMode;
    private $baseUrl;

    public function __construct()
    {
        $this->merchantIdentifier = config('amazon_payments.merchant_identifier');
        $this->accessCode = config('amazon_payments.access_code');
        $this->shaRequestPhrase = config('amazon_payments.sha_request_phrase');
        $this->shaResponsePhrase = config('amazon_payments.sha_response_phrase');
        $this->sandboxMode = config('amazon_payments.sandbox_mode', true);
        $this->baseUrl = $this->sandboxMode 
            ? 'https://sbcheckout.payfort.com/FortAPI/paymentApi'
            : 'https://checkout.payfort.com/FortAPI/paymentApi';
    }

    /**
     * Create a payment
     */
    public function createPayment(float $amount, string $currency, string $description, array $metadata = []): array
    {
        try {
            $merchantReference = 'SETUP_' . time() . '_' . rand(1000, 9999);
            $amountInMinorUnits = $this->convertToMinorUnits($amount, $currency);

            $requestData = [
                'command' => 'AUTHORIZATION',
                'access_code' => $this->accessCode,
                'merchant_identifier' => $this->merchantIdentifier,
                'merchant_reference' => $merchantReference,
                'amount' => $amountInMinorUnits,
                'currency' => $currency,
                'language' => 'en',
                'customer_email' => $metadata['email'] ?? '',
                'order_description' => $description,
                'return_url' => url('/payment/success'),
            ];

            // Generate signature
            $requestData['signature'] = $this->generateSignature($requestData, $this->shaRequestPhrase);

            // For Amazon Payment Services, we need to redirect to their hosted payment page
            $hostedPaymentUrl = $this->sandboxMode 
                ? 'https://sbcheckout.payfort.com/FortAPI/paymentPage'
                : 'https://checkout.payfort.com/FortAPI/paymentPage';

            return [
                'success' => true,
                'payment_id' => $merchantReference,
                'approval_url' => $hostedPaymentUrl . '?' . http_build_query($requestData),
                'gateway' => $this->getGatewayName(),
                'form_data' => $requestData // For POST form submission
            ];

        } catch (\Exception $e) {
            Log::error('Amazon Payment Service error: ' . $e->getMessage());
            throw new \Exception('Failed to create payment: ' . $e->getMessage());
        }
    }

    /**
     * Execute/complete a payment
     */
    public function executePayment(string $paymentId, string $payerId): array
    {
        // For Amazon Payment Services, payment is completed via webhook/callback
        // This method would typically verify the payment status
        return [
            'success' => true,
            'payment_id' => $paymentId,
            'transaction_id' => $payerId,
            'state' => 'approved'
        ];
    }

    /**
     * Get payment details
     */
    public function getPayment(string $paymentId): array
    {
        try {
            $requestData = [
                'command' => 'PAYMENT_STATUS',
                'access_code' => $this->accessCode,
                'merchant_identifier' => $this->merchantIdentifier,
                'merchant_reference' => $paymentId,
                'query_command' => 'AUTHORIZATION',
                'language' => 'en'
            ];

            $requestData['signature'] = $this->generateSignature($requestData, $this->shaRequestPhrase);

            $response = Http::post($this->baseUrl, $requestData);
            $result = $response->json();

            return [
                'id' => $paymentId,
                'state' => $result['status'] ?? 'unknown',
                'create_time' => now()->toISOString(),
                'update_time' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Amazon Payment Service get payment error: ' . $e->getMessage());
            throw new \Exception('Failed to get payment: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a payment
     */
    public function cancelPayment(string $paymentId): array
    {
        return [
            'success' => true,
            'message' => 'Payment cancelled',
            'payment_id' => $paymentId
        ];
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return 'Amazon Payment Services';
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return ['JOD', 'USD',];
    }

    /**
     * Check if available in country
     */
    public function isAvailableInCountry(string $countryCode): bool
    {
        // Amazon Payment Services is primarily for MENA region
        $supportedCountries = ['AE', 'SA', 'KW', 'BH', 'OM', 'QA', 'JO', 'EG', 'LB'];
        return in_array(strtoupper($countryCode), $supportedCountries);
    }

    /**
     * Format amount according to gateway requirements
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Convert amount to minor units (cents/fils)
     */
    private function convertToMinorUnits(float $amount, string $currency): int
    {
        // Most currencies use 2 decimal places, but some use 3
        $decimalPlaces = in_array($currency, ['KWD', 'BHD', 'OMR']) ? 3 : 2;
        return (int) ($amount * pow(10, $decimalPlaces));
    }

    /**
     * Generate SHA signature
     */
    private function generateSignature(array $data, string $shaPhrase): string
    {
        ksort($data);
        $signatureString = '';
        
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $signatureString .= $key . '=' . $value;
            }
        }
        
        $signatureString = $shaPhrase . $signatureString . $shaPhrase;
        return hash('sha256', $signatureString);
    }
}
