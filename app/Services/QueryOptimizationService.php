<?php

namespace App\Services;

use App\Models\Building;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class QueryOptimizationService
{
    /**
     * Get optimized expenses query with eager loading.
     */
    public function getOptimizedExpensesQuery(int $buildingId, array $filters = []): Builder
    {
        $query = Expense::with([
            'expenseType:id,name',
            'user:id,name,apartment_number',
            'payments' => function ($query) {
                $query->select('id', 'expense_id', 'amount', 'status', 'payment_date')
                      ->where('status', 'completed');
            }
        ])
        ->select('id', 'expense_type_id', 'user_id', 'amount', 'due_date', 'month', 'year', 'notes', 'is_archived', 'created_at')
        ->where('building_id', $buildingId);

        // Apply filters efficiently
        if (isset($filters['is_archived'])) {
            $query->where('is_archived', $filters['is_archived']);
        } else {
            $query->where('is_archived', false); // Default to active records
        }

        if (isset($filters['month']) && isset($filters['year'])) {
            $query->where('month', $filters['month'])
                  ->where('year', $filters['year']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['expense_type_id'])) {
            $query->where('expense_type_id', $filters['expense_type_id']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Get optimized incomes query with eager loading.
     */
    public function getOptimizedIncomesQuery(int $buildingId, array $filters = []): Builder
    {
        $query = Income::with([
            'user:id,name,apartment_number'
        ])
        ->select('id', 'user_id', 'amount', 'payment_date', 'payment_method', 'notes', 'created_at')
        ->where('building_id', $buildingId);

        // Apply date range filter efficiently
        if (isset($filters['date_from'])) {
            $query->where('payment_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('payment_date', '<=', $filters['date_to']);
        }

        if (isset($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }

        return $query->orderBy('payment_date', 'desc');
    }

    /**
     * Get building financial summary with caching.
     */
    public function getBuildingFinancialSummary(int $buildingId, int $month, int $year): array
    {
        $cacheKey = "building_financial_summary_{$buildingId}_{$month}_{$year}";
        
        return Cache::remember($cacheKey, 3600, function () use ($buildingId, $month, $year) {
            // Use raw queries for better performance
            $expenses = DB::table('expenses')
                ->where('building_id', $buildingId)
                ->where('month', $month)
                ->where('year', $year)
                ->where('is_archived', false)
                ->selectRaw('
                    COUNT(*) as total_expenses,
                    SUM(amount) as total_expense_amount,
                    SUM(CASE WHEN is_automatic = 1 THEN amount ELSE 0 END) as automatic_expenses
                ')
                ->first();

            $incomes = DB::table('incomes')
                ->where('building_id', $buildingId)
                ->whereMonth('payment_date', $month)
                ->whereYear('payment_date', $year)
                ->selectRaw('
                    COUNT(*) as total_incomes,
                    SUM(amount) as total_income_amount
                ')
                ->first();

            $payments = DB::table('payments')
                ->join('expenses', 'payments.expense_id', '=', 'expenses.id')
                ->where('expenses.building_id', $buildingId)
                ->where('expenses.month', $month)
                ->where('expenses.year', $year)
                ->where('payments.status', 'completed')
                ->selectRaw('
                    COUNT(*) as total_payments,
                    SUM(payments.amount) as total_paid_amount
                ')
                ->first();

            return [
                'expenses' => [
                    'count' => $expenses->total_expenses ?? 0,
                    'total_amount' => $expenses->total_expense_amount ?? 0,
                    'automatic_amount' => $expenses->automatic_expenses ?? 0,
                ],
                'incomes' => [
                    'count' => $incomes->total_incomes ?? 0,
                    'total_amount' => $incomes->total_income_amount ?? 0,
                ],
                'payments' => [
                    'count' => $payments->total_payments ?? 0,
                    'total_amount' => $payments->total_paid_amount ?? 0,
                ],
                'balance' => ($incomes->total_income_amount ?? 0) - ($expenses->total_expense_amount ?? 0),
                'collection_rate' => $expenses->total_expense_amount > 0 
                    ? round((($payments->total_paid_amount ?? 0) / $expenses->total_expense_amount) * 100, 2)
                    : 0,
            ];
        });
    }

    /**
     * Get user payment history with optimized query.
     */
    public function getUserPaymentHistory(int $userId, int $limit = 50): array
    {
        return DB::table('payments')
            ->join('expenses', 'payments.expense_id', '=', 'expenses.id')
            ->join('expense_types', 'expenses.expense_type_id', '=', 'expense_types.id')
            ->where('payments.user_id', $userId)
            ->select([
                'payments.id',
                'payments.amount',
                'payments.payment_date',
                'payments.payment_method',
                'payments.status',
                'expenses.month',
                'expenses.year',
                'expense_types.name as expense_type'
            ])
            ->orderBy('payments.payment_date', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get building users with optimized query and caching.
     */
    public function getBuildingUsers(int $buildingId, bool $activeOnly = true): array
    {
        $cacheKey = "building_users_{$buildingId}_" . ($activeOnly ? 'active' : 'all');
        
        return Cache::remember($cacheKey, 1800, function () use ($buildingId, $activeOnly) {
            $query = User::select('id', 'name', 'email', 'apartment_number', 'role', 'email_notifications_enabled')
                ->where('building_id', $buildingId);

            if ($activeOnly) {
                $query->whereNotNull('email_verified_at');
            }

            return $query->orderBy('apartment_number')
                ->get()
                ->toArray();
        });
    }

    /**
     * Clear related caches when data changes.
     */
    public function clearBuildingCaches(int $buildingId): void
    {
        $patterns = [
            "building_financial_summary_{$buildingId}_*",
            "building_users_{$buildingId}_*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Get expense statistics with efficient aggregation.
     */
    public function getExpenseStatistics(int $buildingId, int $months = 12): array
    {
        $cacheKey = "expense_statistics_{$buildingId}_{$months}";
        
        return Cache::remember($cacheKey, 7200, function () use ($buildingId, $months) {
            $startDate = now()->subMonths($months);
            
            return DB::table('expenses')
                ->join('expense_types', 'expenses.expense_type_id', '=', 'expense_types.id')
                ->where('expenses.building_id', $buildingId)
                ->where('expenses.created_at', '>=', $startDate)
                ->where('expenses.is_archived', false)
                ->selectRaw('
                    expense_types.name as type_name,
                    COUNT(*) as count,
                    SUM(expenses.amount) as total_amount,
                    AVG(expenses.amount) as avg_amount,
                    MIN(expenses.amount) as min_amount,
                    MAX(expenses.amount) as max_amount
                ')
                ->groupBy('expense_types.id', 'expense_types.name')
                ->orderBy('total_amount', 'desc')
                ->get()
                ->toArray();
        });
    }

    /**
     * Optimize search queries with full-text search when available.
     */
    public function optimizedSearch(string $query, int $buildingId, array $types = ['expenses', 'incomes', 'users']): array
    {
        $results = [];
        $searchTerm = '%' . $query . '%';

        if (in_array('expenses', $types)) {
            $results['expenses'] = DB::table('expenses')
                ->join('expense_types', 'expenses.expense_type_id', '=', 'expense_types.id')
                ->join('users', 'expenses.user_id', '=', 'users.id')
                ->where('expenses.building_id', $buildingId)
                ->where('expenses.is_archived', false)
                ->where(function ($q) use ($searchTerm) {
                    $q->where('expenses.notes', 'LIKE', $searchTerm)
                      ->orWhere('expense_types.name', 'LIKE', $searchTerm)
                      ->orWhere('users.name', 'LIKE', $searchTerm)
                      ->orWhere('users.apartment_number', 'LIKE', $searchTerm);
                })
                ->select([
                    'expenses.id',
                    'expenses.amount',
                    'expenses.notes',
                    'expense_types.name as type',
                    'users.name as user_name',
                    'users.apartment_number'
                ])
                ->limit(20)
                ->get()
                ->toArray();
        }

        return $results;
    }
}
