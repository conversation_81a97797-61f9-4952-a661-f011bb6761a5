<?php

namespace App\Services;

use App\Models\Building;
use App\Models\Payment;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SubscriptionBillingService
{
    private $paymentGatewayManager;

    public function __construct(PaymentGatewayManager $paymentGatewayManager)
    {
        $this->paymentGatewayManager = $paymentGatewayManager;
    }

    /**
     * Create a subscription for a building
     */
    public function createSubscription(Building $building, string $gateway = null): array
    {
        try {
            $gateway = $gateway ?? $this->getBestGatewayForBuilding($building);
            $gatewayService = $this->paymentGatewayManager->gateway($gateway);

            // Calculate subscription amount based on building package
            $monthlyAmount = $this->calculateMonthlyAmount($building);
            $currency = $building->currency ?? 'USD';

            // Create subscription based on gateway type
            switch ($gateway) {
                case 'stripe':
                    return $this->createStripeSubscription($building, $gatewayService, $monthlyAmount, $currency);
                
                case 'paypal':
                    return $this->createPayPalSubscription($building, $gatewayService, $monthlyAmount, $currency);
                
                default:
                    // For gateways that don't support subscriptions, we'll handle billing manually
                    return $this->createManualSubscription($building, $monthlyAmount, $currency);
            }

        } catch (\Exception $e) {
            Log::error('Failed to create subscription', [
                'building_id' => $building->id,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to create subscription: ' . $e->getMessage());
        }
    }

    /**
     * Create Stripe subscription
     */
    private function createStripeSubscription(Building $building, $gatewayService, float $amount, string $currency): array
    {
        // Create or get customer
        $customerData = $gatewayService->createCustomer(
            $building->admin_email,
            $building->name,
            ['building_id' => $building->id]
        );

        // Create product and price
        $productData = $gatewayService->createSubscriptionProduct(
            "Building Committee Subscription - {$building->name}",
            $amount,
            $currency
        );

        // Create subscription
        $subscriptionData = $gatewayService->createSubscription(
            $customerData['customer_id'],
            $productData['price_id'],
            ['building_id' => $building->id]
        );

        // Save subscription to database
        $subscription = Subscription::create([
            'building_id' => $building->id,
            'gateway' => 'stripe',
            'gateway_subscription_id' => $subscriptionData['subscription_id'],
            'gateway_customer_id' => $customerData['customer_id'],
            'gateway_price_id' => $productData['price_id'],
            'amount' => $amount,
            'currency' => $currency,
            'status' => $subscriptionData['status'],
            'current_period_start' => Carbon::createFromTimestamp($subscriptionData['current_period_start']),
            'current_period_end' => Carbon::createFromTimestamp($subscriptionData['current_period_end']),
            'next_billing_date' => Carbon::createFromTimestamp($subscriptionData['current_period_end']),
        ]);

        return [
            'success' => true,
            'subscription_id' => $subscription->id,
            'gateway_subscription_id' => $subscriptionData['subscription_id'],
            'client_secret' => $subscriptionData['client_secret'],
            'status' => $subscriptionData['status'],
        ];
    }

    /**
     * Create PayPal subscription (placeholder - would need PayPal subscription API)
     */
    private function createPayPalSubscription(Building $building, $gatewayService, float $amount, string $currency): array
    {
        // PayPal subscription creation would go here
        // For now, we'll create a manual subscription
        return $this->createManualSubscription($building, $amount, $currency);
    }

    /**
     * Create manual subscription for gateways that don't support automatic billing
     */
    private function createManualSubscription(Building $building, float $amount, string $currency): array
    {
        $subscription = Subscription::create([
            'building_id' => $building->id,
            'gateway' => 'manual',
            'amount' => $amount,
            'currency' => $currency,
            'status' => 'active',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'next_billing_date' => now()->addMonth(),
        ]);

        return [
            'success' => true,
            'subscription_id' => $subscription->id,
            'status' => 'active',
            'billing_type' => 'manual',
        ];
    }

    /**
     * Process monthly billing for all active subscriptions
     */
    public function processMonthlyBilling(): array
    {
        $results = [];
        $subscriptions = Subscription::where('status', 'active')
            ->where('next_billing_date', '<=', now())
            ->with('building')
            ->get();

        foreach ($subscriptions as $subscription) {
            try {
                $result = $this->processBillingForSubscription($subscription);
                $results[] = [
                    'subscription_id' => $subscription->id,
                    'building_id' => $subscription->building_id,
                    'building_name' => $subscription->building->name,
                    'status' => 'success',
                    'result' => $result,
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'subscription_id' => $subscription->id,
                    'building_id' => $subscription->building_id,
                    'building_name' => $subscription->building->name,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];

                Log::error('Failed to process billing for subscription', [
                    'subscription_id' => $subscription->id,
                    'building_id' => $subscription->building_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Process billing for a specific subscription
     */
    private function processBillingForSubscription(Subscription $subscription): array
    {
        if ($subscription->gateway === 'manual') {
            return $this->processManualBilling($subscription);
        }

        // For automatic subscriptions (Stripe, PayPal), the gateway handles billing
        // We just need to update our records based on webhook notifications
        $subscription->update([
            'current_period_start' => $subscription->current_period_end,
            'current_period_end' => $subscription->current_period_end->addMonth(),
            'next_billing_date' => $subscription->current_period_end->addMonth(),
        ]);

        return [
            'type' => 'automatic',
            'message' => 'Billing period updated for automatic subscription',
        ];
    }

    /**
     * Process manual billing (create payment record)
     */
    private function processManualBilling(Subscription $subscription): array
    {
        // Create a payment record for manual processing
        $payment = Payment::create([
            'building_id' => $subscription->building_id,
            'subscription_id' => $subscription->id,
            'type' => 'subscription',
            'amount' => $subscription->amount,
            'currency' => $subscription->currency,
            'status' => 'pending',
            'due_date' => now()->addDays(7), // Give 7 days to pay
            'description' => "Monthly subscription for {$subscription->building->name}",
        ]);

        // Update subscription billing period
        $subscription->update([
            'current_period_start' => $subscription->current_period_end,
            'current_period_end' => $subscription->current_period_end->addMonth(),
            'next_billing_date' => $subscription->current_period_end->addMonth(),
        ]);

        return [
            'type' => 'manual',
            'payment_id' => $payment->id,
            'message' => 'Payment record created for manual billing',
        ];
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(Subscription $subscription): array
    {
        try {
            if ($subscription->gateway !== 'manual' && $subscription->gateway_subscription_id) {
                $gatewayService = $this->paymentGatewayManager->gateway($subscription->gateway);
                
                // Cancel subscription at gateway level
                if (method_exists($gatewayService, 'cancelSubscription')) {
                    $gatewayService->cancelSubscription($subscription->gateway_subscription_id);
                }
            }

            // Update local subscription status
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            return [
                'success' => true,
                'message' => 'Subscription cancelled successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to cancel subscription: ' . $e->getMessage());
        }
    }

    /**
     * Calculate monthly amount based on building package
     */
    private function calculateMonthlyAmount(Building $building): float
    {
        // Get the building's current subscription package
        $subscription = $building->subscription;
        if ($subscription && $subscription->package) {
            return (float) $subscription->package->price;
        }

        // Fallback to basic package price if no subscription found
        $basicPackage = \App\Models\Package::where('slug', 'basic')->first();
        return $basicPackage ? (float) $basicPackage->price : 9.99;
    }

    /**
     * Get the best payment gateway for a building
     */
    private function getBestGatewayForBuilding(Building $building): string
    {
        $countryCode = $building->country_code ?? 'US';
        $gateway = $this->paymentGatewayManager->getBestGatewayForCountry($countryCode);
        return $gateway->getGatewayName();
    }

    /**
     * Get subscription statistics
     */
    public function getSubscriptionStatistics(): array
    {
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $cancelledSubscriptions = Subscription::where('status', 'cancelled')->count();
        $totalRevenue = Subscription::where('status', 'active')->sum('amount');

        return [
            'total_subscriptions' => $totalSubscriptions,
            'active_subscriptions' => $activeSubscriptions,
            'cancelled_subscriptions' => $cancelledSubscriptions,
            'monthly_recurring_revenue' => $totalRevenue,
            'cancellation_rate' => $totalSubscriptions > 0 ? ($cancelledSubscriptions / $totalSubscriptions) * 100 : 0,
        ];
    }
}
