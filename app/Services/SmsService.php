<?php

namespace App\Services;

use App\Models\Building;
use App\Models\BuildingSmsSettings;
use App\Models\Notification;
use App\Models\SmsDeliveryLog;
use App\Models\SmsTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Twilio\Rest\Client as TwilioClient;
use Aws\Sns\SnsClient;

class SmsService
{
    /**
     * Send SMS notification.
     */
    public function sendSmsNotification(Notification $notification): bool
    {
        $user = $notification->user;
        $building = $notification->building;

        if (!$user || !$building) {
            Log::error('SMS Service: Missing user or building for notification', [
                'notification_id' => $notification->id
            ]);
            return false;
        }

        // Check if user has SMS enabled and phone number
        if (!$this->canSendSmsToUser($user)) {
            return false;
        }

        // Get building SMS settings
        $smsSettings = BuildingSmsSettings::getForBuilding($building);

        // Check if SMS is enabled for this building and notification type
        if (!$this->canSendSmsForNotification($smsSettings, $notification)) {
            return false;
        }

        // Get SMS template and render message
        $message = $this->renderSmsMessage($notification, $smsSettings);
        if (!$message) {
            Log::error('SMS Service: Could not render SMS message', [
                'notification_id' => $notification->id
            ]);
            return false;
        }

        // Create delivery log
        $deliveryLog = SmsDeliveryLog::create([
            'notification_id' => $notification->id,
            'user_id' => $user->id,
            'building_id' => $building->id,
            'phone_number' => $this->formatPhoneNumber($user->phone_number, $user->phone_country_code),
            'provider' => $smsSettings->sms_provider,
            'status' => SmsDeliveryLog::STATUS_PENDING,
            'message_content' => $message,
            'cost' => $smsSettings->cost_per_sms,
        ]);

        // Send SMS via provider
        $success = $this->sendViaSmsProvider($deliveryLog, $smsSettings);

        if ($success) {
            // Update notification
            $notification->update([
                'sms_sent' => true,
                'sms_sent_at' => now(),
                'sms_provider' => $smsSettings->sms_provider,
                'sms_message_id' => $deliveryLog->provider_message_id,
                'sms_status' => $deliveryLog->status,
            ]);

            // Increment SMS count
            $smsSettings->incrementSmsSent();

            // Update usage statistics
            $this->updateUsageStatistics($building, $smsSettings->sms_provider, $smsSettings->cost_per_sms);
        }

        return $success;
    }

    /**
     * Check if SMS can be sent to user.
     */
    public function canSendSmsToUser(User $user): bool
    {
        return $user->sms_notifications_enabled 
            && !empty($user->phone_number)
            && !empty($user->phone_country_code);
    }

    /**
     * Check if SMS can be sent for notification.
     */
    public function canSendSmsForNotification(BuildingSmsSettings $smsSettings, Notification $notification): bool
    {
        return $smsSettings->isSmsEnabled()
            && $smsSettings->isProviderConfigured()
            && $smsSettings->isNotificationTypeEnabled($notification->type)
            && !$smsSettings->hasReachedSmsLimit();
    }

    /**
     * Render SMS message from template.
     */
    public function renderSmsMessage(Notification $notification, BuildingSmsSettings $smsSettings): ?string
    {
        $template = SmsTemplate::getForNotification($notification->type, $notification->building_id);
        
        if (!$template) {
            // Fallback to notification message
            return $this->truncateMessage($notification->message);
        }

        $variables = $this->getTemplateVariables($notification);
        $message = $template->render($variables);

        return $this->truncateMessage($message);
    }

    /**
     * Get template variables from notification.
     */
    private function getTemplateVariables(Notification $notification): array
    {
        $user = $notification->user;
        $building = $notification->building;
        $data = $notification->data ?? [];

        $variables = [
            'user_name' => $user->name,
            'building_name' => $building->name,
            'title' => $notification->title,
            'message' => $notification->message,
        ];

        // Add notification-specific variables
        switch ($notification->type) {
            case Notification::TYPE_PAYMENT_REMINDER:
            case Notification::TYPE_OVERDUE_PAYMENT:
                if (isset($data['expense_id'])) {
                    $expense = \App\Models\Expense::find($data['expense_id']);
                    if ($expense) {
                        $variables['expense_type'] = $expense->expenseType->name ?? 'Expense';
                        $variables['amount'] = $building->formatAmount($expense->amount, $expense->currency ?? 'USD');
                        $variables['due_date'] = $expense->due_date->format('Y-m-d');
                        $variables['days_overdue'] = $data['days_overdue'] ?? 0;
                    }
                }
                break;

            case Notification::TYPE_EXPENSE_CREATED:
                if (isset($data['expense_id'])) {
                    $expense = \App\Models\Expense::find($data['expense_id']);
                    if ($expense) {
                        $variables['expense_type'] = $expense->expenseType->name ?? 'Expense';
                        $variables['amount'] = $building->formatAmount($expense->amount, $expense->currency ?? 'USD');
                        $variables['due_date'] = $expense->due_date->format('Y-m-d');
                    }
                }
                break;

            case Notification::TYPE_PAYMENT_RECEIVED:
                $variables['amount'] = $building->formatAmount($data['amount'] ?? 0);
                $variables['expense_type'] = $data['expense_type'] ?? 'Payment';
                break;

            case Notification::TYPE_INCOME_RECEIVED:
                $variables['amount'] = $building->formatAmount($data['amount'] ?? 0);
                break;
        }

        return $variables;
    }

    /**
     * Send SMS via provider.
     */
    private function sendViaSmsProvider(SmsDeliveryLog $deliveryLog, BuildingSmsSettings $smsSettings): bool
    {
        try {
            switch ($smsSettings->sms_provider) {
                case 'twilio':
                    return $this->sendViaTwilio($deliveryLog, $smsSettings);
                case 'aws_sns':
                    return $this->sendViaAwsSns($deliveryLog, $smsSettings);
                default:
                    Log::error('SMS Service: Unsupported SMS provider', [
                        'provider' => $smsSettings->sms_provider
                    ]);
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS Service: Failed to send SMS', [
                'delivery_log_id' => $deliveryLog->id,
                'error' => $e->getMessage()
            ]);

            $deliveryLog->markAsFailed('PROVIDER_ERROR', $e->getMessage());
            return false;
        }
    }

    /**
     * Send SMS via Twilio.
     */
    private function sendViaTwilio(SmsDeliveryLog $deliveryLog, BuildingSmsSettings $smsSettings): bool
    {
        $config = $smsSettings->getProviderConfig();
        
        if (empty($config['account_sid']) || empty($config['auth_token']) || empty($config['from_number'])) {
            $deliveryLog->markAsFailed('CONFIG_ERROR', 'Twilio configuration incomplete');
            return false;
        }

        try {
            $twilio = new TwilioClient($config['account_sid'], $config['auth_token']);
            
            $message = $twilio->messages->create(
                $deliveryLog->phone_number,
                [
                    'from' => $config['from_number'],
                    'body' => $deliveryLog->message_content,
                ]
            );

            $deliveryLog->markAsSent($message->sid, [
                'status' => $message->status,
                'price' => $message->price,
                'price_unit' => $message->priceUnit,
            ]);

            return true;

        } catch (\Exception $e) {
            $deliveryLog->markAsFailed('TWILIO_ERROR', $e->getMessage());
            return false;
        }
    }

    /**
     * Send SMS via AWS SNS.
     */
    private function sendViaAwsSns(SmsDeliveryLog $deliveryLog, BuildingSmsSettings $smsSettings): bool
    {
        $config = $smsSettings->getProviderConfig();
        
        if (empty($config['access_key']) || empty($config['secret_key']) || empty($config['region'])) {
            $deliveryLog->markAsFailed('CONFIG_ERROR', 'AWS SNS configuration incomplete');
            return false;
        }

        try {
            $sns = new SnsClient([
                'version' => 'latest',
                'region' => $config['region'],
                'credentials' => [
                    'key' => $config['access_key'],
                    'secret' => $config['secret_key'],
                ],
            ]);

            $result = $sns->publish([
                'PhoneNumber' => $deliveryLog->phone_number,
                'Message' => $deliveryLog->message_content,
                'MessageAttributes' => [
                    'AWS.SNS.SMS.SMSType' => [
                        'DataType' => 'String',
                        'StringValue' => 'Transactional',
                    ],
                ],
            ]);

            $deliveryLog->markAsSent($result['MessageId'], [
                'message_id' => $result['MessageId'],
            ]);

            return true;

        } catch (\Exception $e) {
            $deliveryLog->markAsFailed('AWS_SNS_ERROR', $e->getMessage());
            return false;
        }
    }

    /**
     * Format phone number.
     */
    public function formatPhoneNumber(string $phoneNumber, string $countryCode = '+1'): string
    {
        // Remove any non-digit characters except +
        $phoneNumber = preg_replace('/[^\d+]/', '', $phoneNumber);
        
        // If phone number doesn't start with +, add country code
        if (!str_starts_with($phoneNumber, '+')) {
            $phoneNumber = $countryCode . $phoneNumber;
        }

        return $phoneNumber;
    }

    /**
     * Truncate message to SMS limits.
     */
    private function truncateMessage(string $message, int $maxLength = 1600): string
    {
        if (mb_strlen($message) <= $maxLength) {
            return $message;
        }

        return mb_substr($message, 0, $maxLength - 3) . '...';
    }

    /**
     * Update usage statistics.
     */
    private function updateUsageStatistics(Building $building, string $provider, float $cost): void
    {
        $today = now()->format('Y-m-d');
        
        \App\Models\SmsUsageStats::updateOrCreate(
            [
                'building_id' => $building->id,
                'date' => $today,
            ],
            [
                'sms_sent' => \DB::raw('sms_sent + 1'),
                'total_cost' => \DB::raw("total_cost + {$cost}"),
                'breakdown_by_provider' => \DB::raw("JSON_SET(COALESCE(breakdown_by_provider, '{}'), '$.{$provider}', COALESCE(JSON_EXTRACT(breakdown_by_provider, '$.{$provider}'), 0) + 1)"),
            ]
        );
    }

    /**
     * Send bulk SMS to building users.
     */
    public function sendBulkSms(Building $building, string $message, array $userIds = null): array
    {
        $smsSettings = BuildingSmsSettings::getForBuilding($building);
        
        if (!$smsSettings->isSmsEnabled()) {
            return ['success' => false, 'error' => 'SMS not enabled for building'];
        }

        $query = $building->users()->where('sms_notifications_enabled', true);
        
        if ($userIds) {
            $query->whereIn('id', $userIds);
        }

        $users = $query->get();
        $results = [];

        foreach ($users as $user) {
            if ($smsSettings->hasReachedSmsLimit()) {
                $results[] = [
                    'user_id' => $user->id,
                    'success' => false,
                    'error' => 'SMS limit reached'
                ];
                continue;
            }

            // Create notification for bulk SMS
            $notification = Notification::create([
                'user_id' => $user->id,
                'building_id' => $building->id,
                'type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'title' => 'Bulk SMS',
                'message' => $message,
                'priority' => Notification::PRIORITY_MEDIUM,
            ]);

            $success = $this->sendSmsNotification($notification);
            
            $results[] = [
                'user_id' => $user->id,
                'success' => $success,
                'notification_id' => $notification->id,
            ];
        }

        return [
            'success' => true,
            'total_users' => count($users),
            'results' => $results,
        ];
    }

    /**
     * Get SMS delivery status from provider.
     */
    public function updateDeliveryStatus(SmsDeliveryLog $deliveryLog): bool
    {
        if (!$deliveryLog->provider_message_id) {
            return false;
        }

        $smsSettings = BuildingSmsSettings::getForBuilding($deliveryLog->building);

        try {
            switch ($deliveryLog->provider) {
                case 'twilio':
                    return $this->updateTwilioDeliveryStatus($deliveryLog, $smsSettings);
                case 'aws_sns':
                    // AWS SNS doesn't provide delivery status updates
                    return false;
                default:
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('SMS Service: Failed to update delivery status', [
                'delivery_log_id' => $deliveryLog->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update Twilio delivery status.
     */
    private function updateTwilioDeliveryStatus(SmsDeliveryLog $deliveryLog, BuildingSmsSettings $smsSettings): bool
    {
        $config = $smsSettings->getProviderConfig();
        
        try {
            $twilio = new TwilioClient($config['account_sid'], $config['auth_token']);
            $message = $twilio->messages($deliveryLog->provider_message_id)->fetch();

            switch ($message->status) {
                case 'delivered':
                    $deliveryLog->markAsDelivered();
                    break;
                case 'failed':
                case 'undelivered':
                    $deliveryLog->markAsFailed($message->errorCode, $message->errorMessage);
                    break;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('SMS Service: Failed to fetch Twilio message status', [
                'delivery_log_id' => $deliveryLog->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get SMS statistics for building.
     */
    public function getSmsStatistics(Building $building, int $days = 30): array
    {
        $smsSettings = BuildingSmsSettings::getForBuilding($building);
        $deliveryStats = SmsDeliveryLog::getDeliveryStats($building->id, $days);
        $providerStats = SmsDeliveryLog::getProviderStats($building->id, $days);
        $monthlyUsage = $smsSettings->getCurrentMonthUsage();

        return [
            'settings' => $smsSettings,
            'monthly_usage' => $monthlyUsage,
            'delivery_stats' => $deliveryStats,
            'provider_stats' => $providerStats,
            'delivery_rate' => $smsSettings->getDeliveryRate(),
        ];
    }

    /**
     * Validate phone number format.
     */
    public function validatePhoneNumber(string $phoneNumber, string $countryCode = '+1'): bool
    {
        $formattedNumber = $this->formatPhoneNumber($phoneNumber, $countryCode);
        
        // Basic validation - should start with + and contain only digits
        return preg_match('/^\+\d{10,15}$/', $formattedNumber);
    }

    /**
     * Get supported SMS providers.
     */
    public function getSupportedProviders(): array
    {
        return BuildingSmsSettings::getSupportedProviders();
    }
}
