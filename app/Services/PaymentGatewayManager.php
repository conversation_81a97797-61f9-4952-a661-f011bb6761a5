<?php

namespace App\Services;

use App\Contracts\PaymentGatewayInterface;
use Illuminate\Support\Facades\App;

class PaymentGatewayManager
{
    private $gateways = [];
    private $defaultGateway;

    public function __construct()
    {
        $this->registerGateways();
        $this->defaultGateway = config('payments.default_gateway', 'paypal');
    }

    /**
     * Register available payment gateways
     */
    private function registerGateways(): void
    {
        $this->gateways = [
            'paypal' => PayPalService::class,
            'amazon' => AmazonPaymentService::class,
            'stripe' => StripeService::class,
            // Add more gateways here as needed
            // 'local_bank' => LocalBankService::class,
        ];
    }

    /**
     * Get a payment gateway instance
     */
    public function gateway(string $gatewayName = null): PaymentGatewayInterface
    {
        $gatewayName = $gatewayName ?: $this->defaultGateway;

        if (!isset($this->gateways[$gatewayName])) {
            throw new \InvalidArgumentException("Payment gateway '{$gatewayName}' is not supported.");
        }

        return App::make($this->gateways[$gatewayName]);
    }

    /**
     * Get the best gateway for a specific country
     */
    public function getBestGatewayForCountry(string $countryCode): PaymentGatewayInterface
    {
        // Priority order for different regions
        $gatewayPriority = [
            // MENA region - prioritize Amazon Payment Services
            'JO' => ['amazon', 'paypal'],
            'AE' => ['amazon', 'paypal'],
            'SA' => ['amazon', 'paypal'],
            'KW' => ['amazon', 'paypal'],
            'BH' => ['amazon', 'paypal'],
            'OM' => ['amazon', 'paypal'],
            'QA' => ['amazon', 'paypal'],
            'EG' => ['amazon', 'paypal'],
            'LB' => ['amazon', 'paypal'],
            
            // Default for other countries
            'default' => ['paypal', 'amazon']
        ];

        $priority = $gatewayPriority[$countryCode] ?? $gatewayPriority['default'];

        foreach ($priority as $gatewayName) {
            if (isset($this->gateways[$gatewayName])) {
                $gateway = $this->gateway($gatewayName);
                if ($gateway->isAvailableInCountry($countryCode)) {
                    return $gateway;
                }
            }
        }

        // Fallback to default gateway
        return $this->gateway();
    }

    /**
     * Get all available gateways
     */
    public function getAvailableGateways(): array
    {
        $gateways = [];
        foreach ($this->gateways as $name => $class) {
            $gateway = $this->gateway($name);
            $gateways[$name] = [
                'name' => $gateway->getGatewayName(),
                'supported_currencies' => $gateway->getSupportedCurrencies(),
                'class' => $class
            ];
        }
        return $gateways;
    }

    /**
     * Get gateways available for a specific country
     */
    public function getGatewaysForCountry(string $countryCode): array
    {
        $availableGateways = [];
        
        foreach ($this->gateways as $name => $class) {
            $gateway = $this->gateway($name);
            if ($gateway->isAvailableInCountry($countryCode)) {
                $availableGateways[$name] = [
                    'name' => $gateway->getGatewayName(),
                    'supported_currencies' => $gateway->getSupportedCurrencies()
                ];
            }
        }
        
        return $availableGateways;
    }

    /**
     * Create a payment using the best gateway for the country
     */
    public function createPayment(float $amount, string $currency, string $description, string $countryCode = 'US', array $metadata = []): array
    {
        $gateway = $this->getBestGatewayForCountry($countryCode);
        
        // Check if currency is supported
        if (!in_array($currency, $gateway->getSupportedCurrencies())) {
            // Try to find another gateway that supports this currency
            foreach ($this->gateways as $name => $class) {
                $altGateway = $this->gateway($name);
                if ($altGateway->isAvailableInCountry($countryCode) && 
                    in_array($currency, $altGateway->getSupportedCurrencies())) {
                    $gateway = $altGateway;
                    break;
                }
            }
        }

        return $gateway->createPayment($amount, $currency, $description, $metadata);
    }

    /**
     * Execute payment using the specified gateway
     */
    public function executePayment(string $paymentId, string $payerId, string $gatewayName = null): array
    {
        $gateway = $this->gateway($gatewayName);
        return $gateway->executePayment($paymentId, $payerId);
    }

    /**
     * Get payment details using the specified gateway
     */
    public function getPayment(string $paymentId, string $gatewayName = null): array
    {
        $gateway = $this->gateway($gatewayName);
        return $gateway->getPayment($paymentId);
    }

    /**
     * Cancel payment using the specified gateway
     */
    public function cancelPayment(string $paymentId, string $gatewayName = null): array
    {
        $gateway = $this->gateway($gatewayName);
        return $gateway->cancelPayment($paymentId);
    }

    /**
     * Get recommended currency for a country
     */
    public function getRecommendedCurrency(string $countryCode): string
    {
        $currencyMap = [
            'JO' => 'JOD',            
            'US' => 'USD',
        ];

        return $currencyMap[$countryCode] ?? 'USD';
    }
}
