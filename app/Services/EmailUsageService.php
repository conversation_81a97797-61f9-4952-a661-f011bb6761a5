<?php

namespace App\Services;

use App\Models\Building;
use App\Models\EmailUsage;
use App\Models\Package;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class EmailUsageService
{
    /**
     * Track email usage for a building.
     */
    public function trackEmailUsage(
        Building $building,
        string $emailType,
        string $recipientEmail,
        string $subject,
        User $user = null,
        array $metadata = []
    ): EmailUsage {
        return EmailUsage::create([
            'building_id' => $building->id,
            'user_id' => $user?->id,
            'email_type' => $emailType,
            'recipient_email' => $recipientEmail,
            'subject' => $subject,
            'status' => 'queued',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Check if building can send email based on quota limits.
     */
    public function canSendEmail(Building $building, string $emailType = 'notification'): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
                'quota_info' => null,
            ];
        }

        // Check if email notifications are enabled
        if (!$package->email_notifications_enabled) {
            return [
                'allowed' => false,
                'message' => 'Email notifications are not enabled for your current package.',
                'quota_info' => null,
            ];
        }

        // Check daily limit
        $dailyUsage = $this->getDailyUsage($building);
        if ($package->email_limit_per_day && $dailyUsage >= $package->email_limit_per_day) {
            return [
                'allowed' => false,
                'message' => "Daily email limit reached ({$package->email_limit_per_day} emails per day).",
                'quota_info' => [
                    'daily_used' => $dailyUsage,
                    'daily_limit' => $package->email_limit_per_day,
                    'daily_remaining' => 0,
                ],
            ];
        }

        // Check monthly limit
        $monthlyUsage = $this->getMonthlyUsage($building);
        if ($package->email_limit_per_month && $monthlyUsage >= $package->email_limit_per_month) {
            return [
                'allowed' => false,
                'message' => "Monthly email limit reached ({$package->email_limit_per_month} emails per month).",
                'quota_info' => [
                    'monthly_used' => $monthlyUsage,
                    'monthly_limit' => $package->email_limit_per_month,
                    'monthly_remaining' => 0,
                ],
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Email sending allowed.',
            'quota_info' => [
                'daily_used' => $dailyUsage,
                'daily_limit' => $package->email_limit_per_day,
                'daily_remaining' => $package->email_limit_per_day ? ($package->email_limit_per_day - $dailyUsage) : null,
                'monthly_used' => $monthlyUsage,
                'monthly_limit' => $package->email_limit_per_month,
                'monthly_remaining' => $package->email_limit_per_month ? ($package->email_limit_per_month - $monthlyUsage) : null,
            ],
        ];
    }

    /**
     * Get daily email usage for a building.
     */
    public function getDailyUsage(Building $building): int
    {
        return EmailUsage::sentToday($building->id)->count();
    }

    /**
     * Get monthly email usage for a building.
     */
    public function getMonthlyUsage(Building $building): int
    {
        return EmailUsage::sentThisMonth($building->id)->count();
    }

    /**
     * Get email usage statistics for a building.
     */
    public function getBuildingUsageStats(Building $building, string $period = 'month'): array
    {
        $stats = EmailUsage::getBuildingStats($building->id, $period);
        $usageByType = EmailUsage::getBuildingUsageByType($building->id, $period);
        $package = $building->getEffectivePackage();

        $result = [
            'period' => $period,
            'stats' => $stats,
            'usage_by_type' => $usageByType,
            'package_limits' => null,
            'quota_warnings' => [],
        ];

        if ($package) {
            $result['package_limits'] = [
                'daily_limit' => $package->email_limit_per_day,
                'monthly_limit' => $package->email_limit_per_month,
                'warnings_enabled' => $package->email_quota_warnings_enabled,
            ];

            // Check for quota warnings
            $result['quota_warnings'] = $this->getQuotaWarnings($building, $package);
        }

        return $result;
    }

    /**
     * Get quota warnings for a building.
     */
    public function getQuotaWarnings(Building $building, Package $package): array
    {
        $warnings = [];

        if (!$package->email_quota_warnings_enabled) {
            return $warnings;
        }

        $dailyUsage = $this->getDailyUsage($building);
        $monthlyUsage = $this->getMonthlyUsage($building);

        // Daily quota warnings
        if ($package->email_limit_per_day) {
            $dailyPercentage = ($dailyUsage / $package->email_limit_per_day) * 100;
            
            if ($dailyPercentage >= 90) {
                $warnings[] = [
                    'type' => 'daily_critical',
                    'message' => "Daily email quota is {$dailyPercentage}% used ({$dailyUsage}/{$package->email_limit_per_day})",
                    'level' => 'critical',
                ];
            } elseif ($dailyPercentage >= 80) {
                $warnings[] = [
                    'type' => 'daily_warning',
                    'message' => "Daily email quota is {$dailyPercentage}% used ({$dailyUsage}/{$package->email_limit_per_day})",
                    'level' => 'warning',
                ];
            }
        }

        // Monthly quota warnings
        if ($package->email_limit_per_month) {
            $monthlyPercentage = ($monthlyUsage / $package->email_limit_per_month) * 100;
            
            if ($monthlyPercentage >= 90) {
                $warnings[] = [
                    'type' => 'monthly_critical',
                    'message' => "Monthly email quota is {$monthlyPercentage}% used ({$monthlyUsage}/{$package->email_limit_per_month})",
                    'level' => 'critical',
                ];
            } elseif ($monthlyPercentage >= 80) {
                $warnings[] = [
                    'type' => 'monthly_warning',
                    'message' => "Monthly email quota is {$monthlyPercentage}% used ({$monthlyUsage}/{$package->email_limit_per_month})",
                    'level' => 'warning',
                ];
            }
        }

        return $warnings;
    }

    /**
     * Mark email as sent and update usage tracking.
     */
    public function markEmailAsSent(EmailUsage $emailUsage): void
    {
        $emailUsage->markAsSent();
        
        Log::info('Email usage tracked', [
            'building_id' => $emailUsage->building_id,
            'email_type' => $emailUsage->email_type,
            'recipient' => $emailUsage->recipient_email,
        ]);
    }

    /**
     * Mark email as failed and update usage tracking.
     */
    public function markEmailAsFailed(EmailUsage $emailUsage, string $errorMessage = null): void
    {
        $emailUsage->markAsFailed($errorMessage);
        
        Log::warning('Email sending failed', [
            'building_id' => $emailUsage->building_id,
            'email_type' => $emailUsage->email_type,
            'recipient' => $emailUsage->recipient_email,
            'error' => $errorMessage,
        ]);
    }

    /**
     * Get buildings that need quota warning notifications.
     */
    public function getBuildingsNeedingQuotaWarnings(): array
    {
        $buildings = Building::with('currentPackage')->get();
        $buildingsNeedingWarnings = [];

        foreach ($buildings as $building) {
            $package = $building->getEffectivePackage();
            if (!$package || !$package->email_quota_warnings_enabled) {
                continue;
            }

            $warnings = $this->getQuotaWarnings($building, $package);
            if (!empty($warnings)) {
                $buildingsNeedingWarnings[] = [
                    'building' => $building,
                    'warnings' => $warnings,
                ];
            }
        }

        return $buildingsNeedingWarnings;
    }

    /**
     * Clean up old email usage records (older than specified days).
     */
    public function cleanupOldRecords(int $daysToKeep = 90): int
    {
        $cutoffDate = Carbon::now()->subDays($daysToKeep);
        
        return EmailUsage::where('created_at', '<', $cutoffDate)->delete();
    }
}
