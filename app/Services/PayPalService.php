<?php

namespace App\Services;

use App\Contracts\PaymentGatewayInterface;
use PayPal\Api\Amount;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Transaction;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use PayPal\Exception\PayPalConnectionException;

class PayPalService implements PaymentGatewayInterface
{
    private $apiContext;

    public function __construct()
    {
        $this->apiContext = new ApiContext(
            new OAuthTokenCredential(
                config('paypal.' . config('paypal.mode') . '.client_id'),
                config('paypal.' . config('paypal.mode') . '.client_secret')
            )
        );

        $this->apiContext->setConfig([
            'mode' => config('paypal.mode'),
            'log.LogEnabled' => config('paypal.log.LogEnabled'),
            'log.FileName' => config('paypal.log.FileName'),
            'log.LogLevel' => config('paypal.log.LogLevel'),
            'validation.level' => 'log',
            'cache.enabled' => true,
        ]);
    }

    /**
     * Create a PayPal payment for setup fee
     */
    public function createSetupFeePayment($amount, $currency = 'USD', $description = 'Building Setup Fee')
    {
        try {
            $payer = new Payer();
            $payer->setPaymentMethod('paypal');

            $item = new Item();
            $item->setName('Building Committee Setup Fee')
                ->setCurrency($currency)
                ->setQuantity(1)
                ->setPrice($amount);

            $itemList = new ItemList();
            $itemList->setItems([$item]);

            $amountObj = new Amount();
            $amountObj->setCurrency($currency)
                ->setTotal($amount);

            $transaction = new Transaction();
            $transaction->setAmount($amountObj)
                ->setItemList($itemList)
                ->setDescription($description);

            $redirectUrls = new RedirectUrls();
            $redirectUrls->setReturnUrl(url('/api/payments/success'))
                ->setCancelUrl(url('/api/payments/cancel'));

            $payment = new Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            $payment->create($this->apiContext);

            return $payment;
        } catch (PayPalConnectionException $ex) {
            throw new \Exception('PayPal connection error: ' . $ex->getMessage());
        } catch (\Exception $ex) {
            throw new \Exception('PayPal payment creation error: ' . $ex->getMessage());
        }
    }

    /**
     * Execute a PayPal payment
     */
    public function executePayment($paymentId, $payerId)
    {
        try {
            $payment = Payment::get($paymentId, $this->apiContext);

            $execution = new PaymentExecution();
            $execution->setPayerId($payerId);

            $result = $payment->execute($execution, $this->apiContext);

            return $result;
        } catch (PayPalConnectionException $ex) {
            throw new \Exception('PayPal connection error: ' . $ex->getMessage());
        } catch (\Exception $ex) {
            throw new \Exception('PayPal payment execution error: ' . $ex->getMessage());
        }
    }

    /**
     * Get payment details
     */
    public function getPayment($paymentId)
    {
        try {
            return Payment::get($paymentId, $this->apiContext);
        } catch (PayPalConnectionException $ex) {
            throw new \Exception('PayPal connection error: ' . $ex->getMessage());
        } catch (\Exception $ex) {
            throw new \Exception('PayPal get payment error: ' . $ex->getMessage());
        }
    }

    /**
     * Convert amount to PayPal format (2 decimal places)
     */
    public function formatAmount($amount)
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Create a payment (interface implementation)
     */
    public function createPayment(float $amount, string $currency, string $description, array $metadata = []): array
    {
        $payment = $this->createSetupFeePayment($amount, $currency, $description);

        $approvalUrl = null;
        foreach ($payment->getLinks() as $link) {
            if ($link->getRel() === 'approval_url') {
                $approvalUrl = $link->getHref();
                break;
            }
        }

        return [
            'success' => true,
            'payment_id' => $payment->getId(),
            'approval_url' => $approvalUrl,
            'gateway' => $this->getGatewayName()
        ];
    }

    /**
     * Cancel payment (interface implementation)
     */
    public function cancelPayment(string $paymentId): array
    {
        return [
            'success' => true,
            'message' => 'Payment cancelled',
            'payment_id' => $paymentId
        ];
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return 'PayPal';
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'NOK', 'SEK', 'DKK', 'PLN', 'CZK', 'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP', 'TWD', 'THB', 'TRY'];
    }

    /**
     * Check if available in country
     */
    public function isAvailableInCountry(string $countryCode): bool
    {
        // PayPal is available in most countries, but has limitations in some
        $restrictedCountries = ['AF', 'BD', 'KP', 'IR', 'IQ', 'LY', 'MM', 'SO', 'SD', 'SY', 'YE'];
        return !in_array(strtoupper($countryCode), $restrictedCountries);
    }
}
