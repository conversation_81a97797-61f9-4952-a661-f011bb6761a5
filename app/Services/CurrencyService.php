<?php

namespace App\Services;

use App\Models\Building;
use App\Models\BuildingCurrencySetting;
use App\Models\CurrencyConversion;
use App\Models\CurrencyExchangeRate;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CurrencyService
{
    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD' => ['name' => 'US Dollar', 'symbol' => '$', 'decimals' => 2],
            'JOD' => ['name' => 'Jordanian Dinar', 'symbol' => 'JD', 'decimals' => 3],
            'ILS' => ['name' => 'Israeli Shekel', 'symbol' => '₪', 'decimals' => 2],
        ];
    }

    /**
     * Get currency information.
     */
    public function getCurrencyInfo(string $currency): ?array
    {
        $currencies = $this->getSupportedCurrencies();
        return $currencies[$currency] ?? null;
    }

    /**
     * Check if currency is supported.
     */
    public function isCurrencySupported(string $currency): bool
    {
        return array_key_exists($currency, $this->getSupportedCurrencies());
    }

    /**
     * Get exchange rate between two currencies.
     */
    public function getExchangeRate(
        string $fromCurrency, 
        string $toCurrency, 
        Carbon $date = null,
        string $provider = null
    ): ?float {
        // Same currency
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $date = $date ?? now();
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}_{$date->format('Y-m-d')}";

        return Cache::remember($cacheKey, 3600, function () use ($fromCurrency, $toCurrency, $date, $provider) {
            // Try to get from database first
            $rate = CurrencyExchangeRate::getLatestRate($fromCurrency, $toCurrency, $date);
            
            if ($rate) {
                return $rate;
            }

            // Try to get from external API if provider is configured
            if ($provider && $provider !== 'manual') {
                $rate = $this->fetchExchangeRateFromAPI($fromCurrency, $toCurrency, $provider);
                
                if ($rate) {
                    // Store the rate
                    CurrencyExchangeRate::getOrCreateRate(
                        $fromCurrency,
                        $toCurrency,
                        $rate,
                        $provider,
                        $date
                    );
                    
                    return $rate;
                }
            }

            return null;
        });
    }

    /**
     * Convert amount between currencies.
     */
    public function convertAmount(
        float $amount,
        string $fromCurrency,
        string $toCurrency,
        Building $building = null,
        Carbon $date = null
    ): array {
        if ($fromCurrency === $toCurrency) {
            return [
                'original_amount' => $amount,
                'converted_amount' => $amount,
                'exchange_rate' => 1.0,
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'conversion_date' => $date ?? now(),
            ];
        }

        $settings = $building ? BuildingCurrencySetting::getForBuilding($building) : null;
        $provider = $settings?->exchange_rate_provider ?? 'manual';
        
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency, $date, $provider);
        
        if (!$rate) {
            throw new \Exception("Exchange rate not available for {$fromCurrency} to {$toCurrency}");
        }

        // Apply markup if configured
        $markup = $settings?->getConversionMarkupDecimal() ?? 0;
        $finalRate = $rate * (1 + $markup);
        
        $convertedAmount = $amount * $finalRate;

        return [
            'original_amount' => $amount,
            'converted_amount' => $convertedAmount,
            'exchange_rate' => $rate,
            'final_rate' => $finalRate,
            'markup' => $markup,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'conversion_date' => $date ?? now(),
        ];
    }

    /**
     * Convert and log conversion for a model.
     */
    public function convertAndLog(
        $model,
        string $fromCurrency,
        string $toCurrency,
        Building $building,
        User $user = null
    ): array {
        $conversion = $this->convertAmount(
            $model->amount,
            $fromCurrency,
            $toCurrency,
            $building
        );

        // Update model with converted amount
        $model->update([
            'original_amount' => $model->amount,
            'original_currency' => $fromCurrency,
            'amount' => $conversion['converted_amount'],
            'currency' => $toCurrency,
            'exchange_rate' => $conversion['final_rate'],
            'exchange_rate_date' => $conversion['conversion_date'],
        ]);

        // Log the conversion
        CurrencyConversion::logConversion(
            $building,
            $model,
            $fromCurrency,
            $toCurrency,
            $conversion['original_amount'],
            $conversion['converted_amount'],
            $conversion['final_rate'],
            $building->currencySettings->exchange_rate_provider ?? 'manual',
            $user,
            [
                'markup_applied' => $conversion['markup'],
                'base_rate' => $conversion['exchange_rate'],
            ]
        );

        return $conversion;
    }

    /**
     * Format amount according to building's regional settings.
     */
    public function formatAmount(
        float $amount,
        string $currency,
        Building $building = null,
        User $user = null
    ): string {
        // Use user's preferred currency if available
        if ($user && $user->preferred_currency && $building) {
            $userCurrency = $user->preferred_currency;
            if ($currency !== $userCurrency) {
                try {
                    $conversion = $this->convertAmount($amount, $currency, $userCurrency, $building);
                    $amount = $conversion['converted_amount'];
                    $currency = $userCurrency;
                } catch (\Exception $e) {
                    // Fall back to original currency if conversion fails
                }
            }
        }

        if ($building) {
            $settings = BuildingCurrencySetting::getForBuilding($building);
            return $settings->formatAmount($amount, $currency);
        }

        // Default formatting
        $currencyInfo = $this->getCurrencyInfo($currency);
        $decimals = $currencyInfo['decimals'] ?? 2;
        $symbol = $currencyInfo['symbol'] ?? $currency;

        return $symbol . number_format($amount, $decimals);
    }

    /**
     * Get building's currency settings.
     */
    public function getBuildingCurrencySettings(Building $building): BuildingCurrencySetting
    {
        return BuildingCurrencySetting::getForBuilding($building);
    }

    /**
     * Update building's currency settings.
     */
    public function updateBuildingCurrencySettings(Building $building, array $settings): BuildingCurrencySetting
    {
        $currencySettings = $this->getBuildingCurrencySettings($building);
        $currencySettings->update($settings);
        
        return $currencySettings;
    }

    /**
     * Fetch exchange rate from external API.
     */
    private function fetchExchangeRateFromAPI(string $fromCurrency, string $toCurrency, string $provider): ?float
    {
        try {
            switch ($provider) {
                case 'fixer':
                    return $this->fetchFromFixerAPI($fromCurrency, $toCurrency);
                case 'exchangerate':
                    return $this->fetchFromExchangeRateAPI($fromCurrency, $toCurrency);
                case 'currencylayer':
                    return $this->fetchFromCurrencyLayerAPI($fromCurrency, $toCurrency);
                default:
                    return null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to fetch exchange rate from API', [
                'provider' => $provider,
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Fetch rate from Fixer.io API.
     */
    private function fetchFromFixerAPI(string $fromCurrency, string $toCurrency): ?float
    {
        $apiKey = config('services.fixer.api_key');
        if (!$apiKey) {
            return null;
        }

        $response = Http::get("http://data.fixer.io/api/latest", [
            'access_key' => $apiKey,
            'base' => $fromCurrency,
            'symbols' => $toCurrency,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$toCurrency] ?? null;
        }

        return null;
    }

    /**
     * Fetch rate from ExchangeRate-API.
     */
    private function fetchFromExchangeRateAPI(string $fromCurrency, string $toCurrency): ?float
    {
        $response = Http::get("https://api.exchangerate-api.com/v4/latest/{$fromCurrency}");

        if ($response->successful()) {
            $data = $response->json();
            return $data['rates'][$toCurrency] ?? null;
        }

        return null;
    }

    /**
     * Fetch rate from CurrencyLayer API.
     */
    private function fetchFromCurrencyLayerAPI(string $fromCurrency, string $toCurrency): ?float
    {
        $apiKey = config('services.currencylayer.api_key');
        if (!$apiKey) {
            return null;
        }

        $response = Http::get("http://api.currencylayer.com/live", [
            'access_key' => $apiKey,
            'source' => $fromCurrency,
            'currencies' => $toCurrency,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            $key = $fromCurrency . $toCurrency;
            return $data['quotes'][$key] ?? null;
        }

        return null;
    }

    /**
     * Bulk update exchange rates.
     */
    public function updateExchangeRates(array $rates, string $provider = 'api'): int
    {
        return CurrencyExchangeRate::bulkUpdateRates($rates, $provider);
    }

    /**
     * Get currency conversion statistics for building.
     */
    public function getConversionStats(Building $building, int $days = 30): array
    {
        return CurrencyConversion::getConversionSummary($building, $days);
    }

    /**
     * Get available exchange rate providers.
     */
    public function getAvailableProviders(): array
    {
        return BuildingCurrencySetting::getSupportedProviders();
    }

    /**
     * Validate currency code.
     */
    public function validateCurrency(string $currency): bool
    {
        return $this->isCurrencySupported($currency) && strlen($currency) === 3;
    }
}
