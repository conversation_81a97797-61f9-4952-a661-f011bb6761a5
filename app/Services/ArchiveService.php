<?php

namespace App\Services;

use App\Models\Building;
use App\Models\Expense;
use App\Models\Income;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ArchiveService
{
    /**
     * Check if building has archive feature enabled.
     */
    public function hasArchiveFeature(Building $building): bool
    {
        $package = $building->currentPackage;
        
        if (!$package) {
            return false;
        }

        // Archive feature is available for Premium packages and above
        $premiumPackages = ['premium', 'enterprise'];
        return in_array(strtolower($package->slug), $premiumPackages);
    }

    /**
     * Archive multiple expenses.
     */
    public function archiveExpenses(array $expenseIds, User $user, string $reason = null): array
    {
        $building = $user->building;
        
        if (!$this->hasArchiveFeature($building)) {
            return [
                'success' => false,
                'message' => 'Archive feature is not available for your current package',
                'archived_count' => 0
            ];
        }

        $expenses = Expense::whereIn('id', $expenseIds)
            ->where('building_id', $building->id)
            ->where('is_archived', false)
            ->get();

        $archivedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($expenses as $expense) {
                if ($expense->archive($user, $reason)) {
                    $archivedCount++;
                }
            }

            DB::commit();

            Log::info('Expenses archived', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'archived_count' => $archivedCount,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'message' => "Successfully archived {$archivedCount} expense(s)",
                'archived_count' => $archivedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to archive expenses', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to archive expenses',
                'archived_count' => 0
            ];
        }
    }

    /**
     * Archive multiple incomes.
     */
    public function archiveIncomes(array $incomeIds, User $user, string $reason = null): array
    {
        $building = $user->building;
        
        if (!$this->hasArchiveFeature($building)) {
            return [
                'success' => false,
                'message' => 'Archive feature is not available for your current package',
                'archived_count' => 0
            ];
        }

        $incomes = Income::whereIn('id', $incomeIds)
            ->where('building_id', $building->id)
            ->where('is_archived', false)
            ->get();

        $archivedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($incomes as $income) {
                if ($income->archive($user, $reason)) {
                    $archivedCount++;
                }
            }

            DB::commit();

            Log::info('Incomes archived', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'archived_count' => $archivedCount,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'message' => "Successfully archived {$archivedCount} income(s)",
                'archived_count' => $archivedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to archive incomes', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to archive incomes',
                'archived_count' => 0
            ];
        }
    }

    /**
     * Unarchive multiple expenses.
     */
    public function unarchiveExpenses(array $expenseIds, User $user): array
    {
        $building = $user->building;
        
        if (!$this->hasArchiveFeature($building)) {
            return [
                'success' => false,
                'message' => 'Archive feature is not available for your current package',
                'unarchived_count' => 0
            ];
        }

        $expenses = Expense::whereIn('id', $expenseIds)
            ->where('building_id', $building->id)
            ->where('is_archived', true)
            ->get();

        $unarchivedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($expenses as $expense) {
                if ($expense->unarchive()) {
                    $unarchivedCount++;
                }
            }

            DB::commit();

            Log::info('Expenses unarchived', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'unarchived_count' => $unarchivedCount
            ]);

            return [
                'success' => true,
                'message' => "Successfully unarchived {$unarchivedCount} expense(s)",
                'unarchived_count' => $unarchivedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unarchive expenses', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to unarchive expenses',
                'unarchived_count' => 0
            ];
        }
    }

    /**
     * Unarchive multiple incomes.
     */
    public function unarchiveIncomes(array $incomeIds, User $user): array
    {
        $building = $user->building;
        
        if (!$this->hasArchiveFeature($building)) {
            return [
                'success' => false,
                'message' => 'Archive feature is not available for your current package',
                'unarchived_count' => 0
            ];
        }

        $incomes = Income::whereIn('id', $incomeIds)
            ->where('building_id', $building->id)
            ->where('is_archived', true)
            ->get();

        $unarchivedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($incomes as $income) {
                if ($income->unarchive()) {
                    $unarchivedCount++;
                }
            }

            DB::commit();

            Log::info('Incomes unarchived', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'unarchived_count' => $unarchivedCount
            ]);

            return [
                'success' => true,
                'message' => "Successfully unarchived {$unarchivedCount} income(s)",
                'unarchived_count' => $unarchivedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unarchive incomes', [
                'user_id' => $user->id,
                'building_id' => $building->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to unarchive incomes',
                'unarchived_count' => 0
            ];
        }
    }

    /**
     * Get archive statistics for a building.
     */
    public function getArchiveStats(Building $building): array
    {
        if (!$this->hasArchiveFeature($building)) {
            return [
                'has_feature' => false,
                'archived_expenses' => 0,
                'archived_incomes' => 0,
                'total_archived' => 0
            ];
        }

        $archivedExpenses = Expense::where('building_id', $building->id)
            ->where('is_archived', true)
            ->count();

        $archivedIncomes = Income::where('building_id', $building->id)
            ->where('is_archived', true)
            ->count();

        return [
            'has_feature' => true,
            'archived_expenses' => $archivedExpenses,
            'archived_incomes' => $archivedIncomes,
            'total_archived' => $archivedExpenses + $archivedIncomes
        ];
    }

    /**
     * Auto-archive old records based on age.
     */
    public function autoArchiveOldRecords(Building $building, int $monthsOld = 12): array
    {
        if (!$this->hasArchiveFeature($building)) {
            return [
                'success' => false,
                'message' => 'Archive feature is not available',
                'archived_count' => 0
            ];
        }

        $cutoffDate = now()->subMonths($monthsOld);
        
        $oldExpenses = Expense::where('building_id', $building->id)
            ->where('is_archived', false)
            ->where('created_at', '<', $cutoffDate)
            ->get();

        $oldIncomes = Income::where('building_id', $building->id)
            ->where('is_archived', false)
            ->where('created_at', '<', $cutoffDate)
            ->get();

        $archivedCount = 0;

        DB::beginTransaction();
        try {
            // Create a system user for auto-archiving
            $systemUser = User::where('role', 'super_admin')->first();
            
            foreach ($oldExpenses as $expense) {
                if ($expense->archive($systemUser, "Auto-archived after {$monthsOld} months")) {
                    $archivedCount++;
                }
            }

            foreach ($oldIncomes as $income) {
                if ($income->archive($systemUser, "Auto-archived after {$monthsOld} months")) {
                    $archivedCount++;
                }
            }

            DB::commit();

            Log::info('Auto-archived old records', [
                'building_id' => $building->id,
                'months_old' => $monthsOld,
                'archived_count' => $archivedCount
            ]);

            return [
                'success' => true,
                'message' => "Auto-archived {$archivedCount} old record(s)",
                'archived_count' => $archivedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to auto-archive old records', [
                'building_id' => $building->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to auto-archive old records',
                'archived_count' => 0
            ];
        }
    }
}
