<?php

namespace App\Services;

use App\Models\Building;
use App\Models\BuildingExpense;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Mpdf\Mpdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReportGeneratorService
{
    /**
     * Generate a report.
     */
    public function generateReport(string $type, string $format, Building $building, array $parameters = []): array
    {
        Log::info('Starting report generation', [
            'type' => $type,
            'format' => $format,
            'building_id' => $building->id,
            'parameters' => $parameters
        ]);

        // Get report data
        $data = $this->getReportData($type, $building, $parameters);

        Log::info('Report data retrieved', [
            'record_count' => $data['record_count'] ?? 0,
            'data_keys' => array_keys($data)
        ]);

        // Generate content based on format
        if ($format === 'pdf') {
            $content = $this->generatePdfReport($type, $data, $building, $parameters);
        } else {
            $content = $this->generateExcelReport($type, $data, $building, $parameters);
        }

        Log::info('Report content generated', [
            'content_length' => strlen($content),
            'content_empty' => empty($content)
        ]);

        return [
            'content' => $content,
            'record_count' => $data['record_count'] ?? 0,
        ];
    }

    /**
     * Get report data based on type.
     */
    private function getReportData(string $type, Building $building, array $parameters): array
    {
        // Default to a wider date range that covers the actual data period
        $dateFrom = $parameters['date_from'] ?? Carbon::create(2020, 1, 1)->startOfDay();
        $dateTo = $parameters['date_to'] ?? Carbon::now()->endOfDay();

        if (is_string($dateFrom)) {
            $dateFrom = Carbon::parse($dateFrom);
        }
        if (is_string($dateTo)) {
            $dateTo = Carbon::parse($dateTo);
        }

        switch ($type) {
            case 'financial_summary':
                return $this->getFinancialSummaryData($building, $dateFrom, $dateTo);
            case 'expense_report':
                return $this->getExpenseReportData($building, $dateFrom, $dateTo, $parameters);
            case 'income_report':
                return $this->getIncomeReportData($building, $dateFrom, $dateTo);
            case 'building_expense_report':
                return $this->getBuildingExpenseReportData($building, $dateFrom, $dateTo, $parameters);
            case 'neighbor_payments':
                return $this->getNeighborPaymentsData($building, $dateFrom, $dateTo);
            case 'building_summary':
                return $this->getBuildingSummaryData($building, $dateFrom, $dateTo);
            default:
                throw new \InvalidArgumentException("Unknown report type: {$type}");
        }
    }

    /**
     * Get financial summary data.
     */
    public function getFinancialSummaryData(Building $building, Carbon $dateFrom, Carbon $dateTo): array
    {
        $expenses = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['expenseType', 'user'])
            ->get();

        $incomes = Income::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['user'])
            ->get();

        $buildingExpenses = BuildingExpense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['buildingExpenseType'])
            ->get();

        $totalExpenses = $expenses->sum('amount');
        $totalIncomes = $incomes->sum('amount');
        $totalBuildingExpenses = $buildingExpenses->sum('amount');

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'expenses' => $expenses,
            'incomes' => $incomes,
            'building_expenses' => $buildingExpenses,
            'total_expenses' => $totalExpenses,
            'total_incomes' => $totalIncomes,
            'total_building_expenses' => $totalBuildingExpenses,
            'net_balance' => $totalIncomes - $totalExpenses - $totalBuildingExpenses,
            'record_count' => $expenses->count() + $incomes->count() + $buildingExpenses->count(),
        ];
    }

    /**
     * Get expense report data.
     */
    private function getExpenseReportData(Building $building, Carbon $dateFrom, Carbon $dateTo, array $parameters): array
    {
        $query = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['expenseType', 'user', 'payments']);

        if (isset($parameters['expense_type_id'])) {
            $query->where('expense_type_id', $parameters['expense_type_id']);
        }

        $expenses = $query->get();

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'expenses' => $expenses,
            'total_amount' => $expenses->sum('amount'),
            'paid_amount' => $expenses->sum(function ($expense) {
                return $expense->payments->where('status', 'completed')->sum('amount');
            }),
            'record_count' => $expenses->count(),
        ];
    }

    /**
     * Get income report data.
     */
    private function getIncomeReportData(Building $building, Carbon $dateFrom, Carbon $dateTo): array
    {
        $incomes = Income::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['user'])
            ->get();

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'incomes' => $incomes,
            'total_amount' => $incomes->sum('amount'),
            'record_count' => $incomes->count(),
        ];
    }

    /**
     * Get building expense report data.
     */
    private function getBuildingExpenseReportData(Building $building, Carbon $dateFrom, Carbon $dateTo, array $parameters): array
    {
        $query = BuildingExpense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['buildingExpenseType']);

        if (isset($parameters['building_expense_type_id'])) {
            $query->where('building_expense_type_id', $parameters['building_expense_type_id']);
        }

        $buildingExpenses = $query->get();

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'building_expenses' => $buildingExpenses,
            'total_amount' => $buildingExpenses->sum('amount'),
            'record_count' => $buildingExpenses->count(),
        ];
    }

    /**
     * Get neighbor payments data.
     */
    private function getNeighborPaymentsData(Building $building, Carbon $dateFrom, Carbon $dateTo): array
    {
        $payments = Payment::whereHas('expense', function ($query) use ($building) {
                $query->where('building_id', $building->id);
            })
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with(['expense.expenseType', 'user'])
            ->get();

        $paymentsByUser = $payments->groupBy('user_id');

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'payments' => $payments,
            'payments_by_user' => $paymentsByUser,
            'total_amount' => $payments->sum('amount'),
            'record_count' => $payments->count(),
        ];
    }

    /**
     * Get building summary data.
     */
    private function getBuildingSummaryData(Building $building, Carbon $dateFrom, Carbon $dateTo): array
    {
        $expenses = Expense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $incomes = Income::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $buildingExpenses = BuildingExpense::where('building_id', $building->id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $payments = Payment::whereHas('expense', function ($query) use ($building) {
                $query->where('building_id', $building->id);
            })
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $neighborCount = $building->users()->count();
        $activeNeighbors = $building->users()->where('is_active', true)->count();

        $paymentStats = [
            'total_payments' => $payments->count(),
            'completed_payments' => $payments->where('status', 'completed')->count(),
            'pending_payments' => $payments->where('status', 'pending')->count(),
            'total_amount' => $payments->sum('amount'),
            'completed_amount' => $payments->where('status', 'completed')->sum('amount'),
        ];

        return [
            'building' => $building,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'expenses' => $expenses,
            'incomes' => $incomes,
            'building_expenses' => $buildingExpenses,
            'payments' => $payments,
            'total_expenses' => $expenses->sum('amount'),
            'total_incomes' => $incomes->sum('amount'),
            'total_building_expenses' => $buildingExpenses->sum('amount'),
            'net_balance' => $incomes->sum('amount') - $expenses->sum('amount') - $buildingExpenses->sum('amount'),
            'neighbor_count' => $neighborCount,
            'active_neighbors' => $activeNeighbors,
            'payment_stats' => $paymentStats,
        ];
    }

    /**
     * Generate PDF report using the configured PDF engine.
     */
    public function generatePdfReport(string $type, array $data, Building $building, array $parameters): string
    {
        $engine = config('export.pdf.engine', 'dompdf');

        if ($engine === 'mpdf') {
            return $this->generateMpdfReport($type, $data, $building, $parameters);
        } else {
            return $this->generateDompdfReport($type, $data, $building, $parameters);
        }
    }

    /**
     * Generate PDF report using mPDF (better Arabic support).
     */
    private function generateMpdfReport(string $type, array $data, Building $building, array $parameters): string
    {
        // Configure mPDF for Arabic text support
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 15,
            'margin_bottom' => 15,
            'margin_header' => 10,
            'margin_footer' => 10,
            'orientation' => 'P',
            'default_font' => 'dejavusans',
            'default_font_size' => 10,
            'autoScriptToLang' => true,
            'autoLangToFont' => true,
            'autoArabic' => true,
            'useSubstitutions' => true,
            'fontDir' => [
                storage_path('fonts'),
                __DIR__ . '/../../vendor/mpdf/mpdf/ttfonts',
            ],
        ]);

        // Set RTL direction for Arabic text
        $mpdf->SetDirectionality('rtl');

        Log::info('mPDF configured for Arabic text', [
            'default_font' => 'dejavusans',
            'rtl_enabled' => true
        ]);

        // Generate HTML content
        $html = $this->generatePdfHtml($type, $data, $building, $parameters);

        // Write HTML content with embedded CSS for Arabic support
        $mpdf->WriteHTML($html);

        return $mpdf->Output('', \Mpdf\Output\Destination::STRING_RETURN);
    }

    /**
     * Generate PDF report using DomPDF (with Arabic font fixes).
     */
    private function generateDompdfReport(string $type, array $data, Building $building, array $parameters): string
    {
        // Generate HTML content
        $html = $this->generatePdfHtml($type, $data, $building, $parameters);

        // Configure DomPDF for Arabic text support
        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', false);
        $options->set('isPhpEnabled', false);
        $options->set('isJavascriptEnabled', false);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('fontCache', storage_path('app/dompdf/fonts/'));
        $options->set('tempDir', storage_path('app/dompdf/temp/'));
        $options->set('fontSubsetting', false);
        $options->set('debugKeepTemp', false);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return $dompdf->output();
    }

    /**
     * Generate Excel report.
     */
    private function generateExcelReport(string $type, array $data, Building $building, array $parameters): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set memory-efficient options
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $spreadsheet->getDefaultStyle()->getFont()->setSize(10);

        // Generate Excel content based on type
        $this->populateExcelSheet($sheet, $type, $data, $building, $parameters);

        // Configure writer for shared hosting
        $writer = new Xlsx($spreadsheet);
        $writer->setPreCalculateFormulas(false);

        // Write to string
        ob_start();
        $writer->save('php://output');
        $content = ob_get_clean();

        // Clean up memory
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);

        return $content;
    }

    /**
     * Generate HTML content for PDF reports.
     */
    private function generatePdfHtml(string $type, array $data, Building $building, array $parameters): string
    {
        $viewName = "reports.pdf.{$type}";
        
        // Check if view exists, otherwise use a generic template
        if (!view()->exists($viewName)) {
            $viewName = 'reports.pdf.generic';
        }

        return view($viewName, compact('data', 'building', 'parameters', 'type'))->render();
    }

    /**
     * Populate Excel sheet with data.
     */
    private function populateExcelSheet($sheet, string $type, array $data, Building $building, array $parameters): void
    {
        switch ($type) {
            case 'financial_summary':
                $this->populateFinancialSummaryExcel($sheet, $data);
                break;
            case 'expense_report':
                $this->populateExpenseReportExcel($sheet, $data);
                break;
            case 'income_report':
                $this->populateIncomeReportExcel($sheet, $data);
                break;
            case 'building_expense_report':
                $this->populateBuildingExpenseReportExcel($sheet, $data);
                break;
            case 'neighbor_payments':
                $this->populateNeighborPaymentsExcel($sheet, $data);
                break;
            case 'building_summary':
                $this->populateBuildingSummaryExcel($sheet, $data);
                break;
            default:
                $this->populateGenericExcel($sheet, $data);
        }
    }

    /**
     * Populate financial summary Excel sheet.
     */
    private function populateFinancialSummaryExcel($sheet, array $data): void
    {
        $sheet->setTitle('Financial Summary');
        
        // Header
        $sheet->setCellValue('A1', 'Financial Summary Report');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));
        
        // Summary
        $sheet->setCellValue('A5', 'Summary');
        $sheet->setCellValue('A6', 'Total Expenses:');
        $sheet->setCellValue('B6', $data['total_expenses']);
        $sheet->setCellValue('A7', 'Total Building Expenses:');
        $sheet->setCellValue('B7', $data['total_building_expenses']);
        $sheet->setCellValue('A8', 'Total Incomes:');
        $sheet->setCellValue('B8', $data['total_incomes']);
        $sheet->setCellValue('A9', 'Net Balance:');
        $sheet->setCellValue('B9', $data['net_balance']);
        
        // Expenses section
        $row = 11;
        $sheet->setCellValue('A' . $row, 'Neighbor Expenses');
        $row++;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'User');
        $row++;

        foreach ($data['expenses'] as $expense) {
            $sheet->setCellValue('A' . $row, $expense->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $expense->expenseType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $expense->amount);
            $sheet->setCellValue('D' . $row, $expense->user->name ?? 'N/A');
            $row++;
        }

        // Building Expenses section
        $row += 2;
        $sheet->setCellValue('A' . $row, 'Building Expenses');
        $row++;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'Month/Year');
        $row++;

        foreach ($data['building_expenses'] as $buildingExpense) {
            $sheet->setCellValue('A' . $row, $buildingExpense->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $buildingExpense->buildingExpenseType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $buildingExpense->amount);
            $sheet->setCellValue('D' . $row, $buildingExpense->month . '/' . $buildingExpense->year);
            $row++;
        }
        
        // Incomes section
        $row += 2;
        $sheet->setCellValue('A' . $row, 'Incomes');
        $row++;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'User');
        $row++;
        
        foreach ($data['incomes'] as $income) {
            $sheet->setCellValue('A' . $row, $income->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $income->incomeType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $income->amount);
            $sheet->setCellValue('D' . $row, $income->user->name ?? 'N/A');
            $row++;
        }
    }

    /**
     * Populate expense report Excel sheet.
     */
    private function populateExpenseReportExcel($sheet, array $data): void
    {
        $sheet->setTitle('Expense Report');
        
        // Header
        $sheet->setCellValue('A1', 'Expense Report');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));
        
        // Summary
        $sheet->setCellValue('A5', 'Total Amount: ' . $data['total_amount']);
        $sheet->setCellValue('A6', 'Paid Amount: ' . $data['paid_amount']);
        $sheet->setCellValue('A7', 'Outstanding: ' . ($data['total_amount'] - $data['paid_amount']));
        
        // Details
        $row = 9;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'User');
        $sheet->setCellValue('E' . $row, 'Status');
        $sheet->setCellValue('F' . $row, 'Due Date');
        $row++;
        
        foreach ($data['expenses'] as $expense) {
            $paidAmount = $expense->payments->where('status', 'completed')->sum('amount');
            $status = $paidAmount >= $expense->amount ? 'Paid' : 'Pending';
            
            $sheet->setCellValue('A' . $row, $expense->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $expense->expenseType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $expense->amount);
            $sheet->setCellValue('D' . $row, $expense->user->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $status);
            $sheet->setCellValue('F' . $row, $expense->due_date ? $expense->due_date->format('Y-m-d') : 'N/A');
            $row++;
        }
    }

    /**
     * Populate income report Excel sheet.
     */
    private function populateIncomeReportExcel($sheet, array $data): void
    {
        $sheet->setTitle('Income Report');

        // Header
        $sheet->setCellValue('A1', 'Income Report');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));

        // Summary
        $sheet->setCellValue('A5', 'Total Amount: ' . $data['total_amount']);

        // Details
        $row = 7;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'User');
        $sheet->setCellValue('E' . $row, 'Description');
        $row++;

        foreach ($data['incomes'] as $income) {
            $sheet->setCellValue('A' . $row, $income->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $income->incomeType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $income->amount);
            $sheet->setCellValue('D' . $row, $income->user->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $income->description ?? '');
            $row++;
        }
    }

    /**
     * Populate building expense report Excel sheet.
     */
    private function populateBuildingExpenseReportExcel($sheet, array $data): void
    {
        $sheet->setTitle('Building Expense Report');

        // Header
        $sheet->setCellValue('A1', 'Building Expense Report');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));

        // Summary
        $sheet->setCellValue('A5', 'Total Amount: ' . $data['total_amount']);

        // Details
        $row = 7;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Type');
        $sheet->setCellValue('C' . $row, 'Amount');
        $sheet->setCellValue('D' . $row, 'Month/Year');
        $sheet->setCellValue('E' . $row, 'Due Date');
        $sheet->setCellValue('F' . $row, 'Currency');
        $sheet->setCellValue('G' . $row, 'Notes');
        $row++;

        foreach ($data['building_expenses'] as $buildingExpense) {
            $sheet->setCellValue('A' . $row, $buildingExpense->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $buildingExpense->buildingExpenseType->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $buildingExpense->amount);
            $sheet->setCellValue('D' . $row, $buildingExpense->month . '/' . $buildingExpense->year);
            $sheet->setCellValue('E' . $row, $buildingExpense->due_date ? $buildingExpense->due_date->format('Y-m-d') : 'N/A');
            $sheet->setCellValue('F' . $row, $buildingExpense->currency ?? 'USD');
            $sheet->setCellValue('G' . $row, $buildingExpense->notes ?? '');
            $row++;
        }
    }

    /**
     * Populate neighbor payments Excel sheet.
     */
    private function populateNeighborPaymentsExcel($sheet, array $data): void
    {
        $sheet->setTitle('Neighbor Payments');
        
        // Header
        $sheet->setCellValue('A1', 'Neighbor Payment History');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));
        
        // Summary
        $sheet->setCellValue('A5', 'Total Amount: ' . $data['total_amount']);
        
        // Details
        $row = 7;
        $sheet->setCellValue('A' . $row, 'Date');
        $sheet->setCellValue('B' . $row, 'Neighbor');
        $sheet->setCellValue('C' . $row, 'Expense Type');
        $sheet->setCellValue('D' . $row, 'Amount');
        $sheet->setCellValue('E' . $row, 'Status');
        $sheet->setCellValue('F' . $row, 'Payment Method');
        $row++;
        
        foreach ($data['payments'] as $payment) {
            $sheet->setCellValue('A' . $row, $payment->created_at->format('Y-m-d'));
            $sheet->setCellValue('B' . $row, $payment->user->name ?? 'N/A');
            $sheet->setCellValue('C' . $row, $payment->expense->expenseType->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $payment->amount);
            $sheet->setCellValue('E' . $row, ucfirst($payment->status));
            $sheet->setCellValue('F' . $row, ucfirst($payment->payment_method ?? 'N/A'));
            $row++;
        }
    }

    /**
     * Populate building summary Excel sheet.
     */
    private function populateBuildingSummaryExcel($sheet, array $data): void
    {
        $sheet->setTitle('Building Summary');
        
        // Header
        $sheet->setCellValue('A1', 'Building Summary Report');
        $sheet->setCellValue('A2', 'Building: ' . $data['building']->name);
        $sheet->setCellValue('A3', 'Period: ' . $data['date_from']->format('Y-m-d') . ' to ' . $data['date_to']->format('Y-m-d'));
        
        // Building info
        $sheet->setCellValue('A5', 'Building Information');
        $sheet->setCellValue('A6', 'Total Neighbors: ' . $data['neighbor_count']);
        $sheet->setCellValue('A7', 'Active Neighbors: ' . $data['active_neighbors']);
        
        // Financial summary
        $sheet->setCellValue('A9', 'Financial Summary');
        $sheet->setCellValue('A10', 'Total Neighbor Expenses: ' . $data['total_expenses']);
        $sheet->setCellValue('A11', 'Total Building Expenses: ' . $data['total_building_expenses']);
        $sheet->setCellValue('A12', 'Total Incomes: ' . $data['total_incomes']);
        $sheet->setCellValue('A13', 'Net Balance: ' . $data['net_balance']);
        
        // Payment statistics
        $row = 14;
        $sheet->setCellValue('A' . $row, 'Payment Statistics');
        $row++;
        foreach ($data['payment_stats'] as $status => $stats) {
            $sheet->setCellValue('A' . $row, ucfirst($status) . ' Payments:');
            $sheet->setCellValue('B' . $row, $stats->count . ' (' . $stats->total . ')');
            $row++;
        }
    }

    /**
     * Populate generic Excel sheet.
     */
    private function populateGenericExcel($sheet, array $data): void
    {
        $sheet->setTitle('Report');
        $sheet->setCellValue('A1', 'Report Data');
        
        $row = 3;
        foreach ($data as $key => $value) {
            if (is_scalar($value)) {
                $sheet->setCellValue('A' . $row, ucfirst(str_replace('_', ' ', $key)) . ':');
                $sheet->setCellValue('B' . $row, $value);
                $row++;
            }
        }
    }
}
