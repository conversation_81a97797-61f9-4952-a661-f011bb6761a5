<?php

namespace App\Services;

use App\Models\Building;
use App\Models\FileAttachment;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StorageUsageService
{
    /**
     * Get comprehensive storage usage for a building.
     */
    public function getBuildingStorageUsage(Building $building): array
    {
        $package = $building->getEffectivePackage();
        
        // Get total storage used in bytes
        $totalBytes = $building->fileAttachments()->sum('file_size') ?? 0;
        $totalGB = $totalBytes / (1024 * 1024 * 1024);

        $result = [
            'used_bytes' => $totalBytes,
            'used_gb' => round($totalGB, 3),
            'formatted_size' => $this->formatBytes($totalBytes),
            'total_files' => $building->fileAttachments()->count(),
        ];

        if ($package && !$package->hasUnlimitedStorage()) {
            $limitGB = $package->storage_limit_gb;
            $usagePercentage = $limitGB > 0 ? ($totalGB / $limitGB) * 100 : 0;
            
            $result = array_merge($result, [
                'limit_gb' => $limitGB,
                'limit_bytes' => $limitGB * 1024 * 1024 * 1024,
                'remaining_gb' => max(0, $limitGB - $totalGB),
                'remaining_bytes' => max(0, ($limitGB * 1024 * 1024 * 1024) - $totalBytes),
                'usage_percentage' => round($usagePercentage, 1),
                'is_near_limit' => $usagePercentage > 80,
                'is_over_limit' => $usagePercentage > 100,
                'unlimited' => false,
            ]);
        } else {
            $result = array_merge($result, [
                'limit_gb' => null,
                'limit_bytes' => null,
                'remaining_gb' => null,
                'remaining_bytes' => null,
                'usage_percentage' => 0,
                'is_near_limit' => false,
                'is_over_limit' => false,
                'unlimited' => true,
            ]);
        }

        return $result;
    }

    /**
     * Get storage usage breakdown by file type.
     */
    public function getFileTypeBreakdown(Building $building): array
    {
        $fileTypes = $building->fileAttachments()
            ->select(
                DB::raw('CASE 
                    WHEN mime_type LIKE "image/%" THEN "images"
                    WHEN mime_type = "application/pdf" THEN "pdf"
                    WHEN mime_type LIKE "application/vnd.ms-excel%" OR mime_type LIKE "application/vnd.openxmlformats-officedocument.spreadsheetml%" OR mime_type = "text/csv" THEN "spreadsheets"
                    WHEN mime_type LIKE "application/msword%" OR mime_type LIKE "application/vnd.openxmlformats-officedocument.wordprocessingml%" OR mime_type = "text/plain" THEN "documents"
                    WHEN mime_type LIKE "application/zip%" OR mime_type LIKE "application/x-rar%" THEN "archives"
                    ELSE "other"
                END as file_type'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(file_size) as total_size')
            )
            ->groupBy('file_type')
            ->get();

        $breakdown = [];
        $totalSize = $building->fileAttachments()->sum('file_size') ?? 1; // Avoid division by zero

        foreach ($fileTypes as $type) {
            $sizeGB = $type->total_size / (1024 * 1024 * 1024);
            $percentage = ($type->total_size / $totalSize) * 100;

            $breakdown[] = [
                'type' => $type->file_type,
                'count' => $type->count,
                'size_bytes' => $type->total_size,
                'size_gb' => round($sizeGB, 3),
                'formatted_size' => $this->formatBytes($type->total_size),
                'percentage' => round($percentage, 1),
            ];
        }

        // Sort by size descending
        usort($breakdown, function ($a, $b) {
            return $b['size_bytes'] <=> $a['size_bytes'];
        });

        return $breakdown;
    }

    /**
     * Get monthly storage usage trend.
     */
    public function getMonthlyStorageUsage(Building $building, int $months = 12): array
    {
        $startDate = Carbon::now()->subMonths($months)->startOfMonth();
        
        $monthlyData = $building->fileAttachments()
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('COUNT(*) as files_added'),
                DB::raw('SUM(file_size) as size_added')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $result = [];
        $cumulativeSize = 0;

        // Get initial size before the period
        $initialSize = $building->fileAttachments()
            ->where('created_at', '<', $startDate)
            ->sum('file_size') ?? 0;
        
        $cumulativeSize = $initialSize;

        // Fill in all months, including those with no uploads
        for ($i = $months - 1; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i)->format('Y-m');
            $monthData = $monthlyData->firstWhere('month', $month);
            
            if ($monthData) {
                $cumulativeSize += $monthData->size_added;
                $result[] = [
                    'month' => $month,
                    'month_name' => Carbon::createFromFormat('Y-m', $month)->format('M Y'),
                    'files_added' => $monthData->files_added,
                    'size_added_bytes' => $monthData->size_added,
                    'size_added_gb' => round($monthData->size_added / (1024 * 1024 * 1024), 3),
                    'cumulative_size_bytes' => $cumulativeSize,
                    'cumulative_size_gb' => round($cumulativeSize / (1024 * 1024 * 1024), 3),
                ];
            } else {
                $result[] = [
                    'month' => $month,
                    'month_name' => Carbon::createFromFormat('Y-m', $month)->format('M Y'),
                    'files_added' => 0,
                    'size_added_bytes' => 0,
                    'size_added_gb' => 0,
                    'cumulative_size_bytes' => $cumulativeSize,
                    'cumulative_size_gb' => round($cumulativeSize / (1024 * 1024 * 1024), 3),
                ];
            }
        }

        return $result;
    }

    /**
     * Get storage usage by attachable type (expenses, incomes, etc.).
     */
    public function getUsageByAttachableType(Building $building): array
    {
        $usage = $building->fileAttachments()
            ->select(
                'attachable_type',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(file_size) as total_size')
            )
            ->groupBy('attachable_type')
            ->get();

        $result = [];
        foreach ($usage as $item) {
            $typeName = class_basename($item->attachable_type);
            $result[] = [
                'type' => $typeName,
                'type_label' => $this->getTypeLabel($typeName),
                'count' => $item->count,
                'size_bytes' => $item->total_size,
                'size_gb' => round($item->total_size / (1024 * 1024 * 1024), 3),
                'formatted_size' => $this->formatBytes($item->total_size),
            ];
        }

        return $result;
    }

    /**
     * Check if building can upload additional files.
     */
    public function canUploadFiles(Building $building, int $additionalBytes = 0): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package || $package->hasUnlimitedStorage()) {
            return [
                'can_upload' => true,
                'message' => 'Unlimited storage available.',
                'unlimited' => true,
            ];
        }

        $currentUsage = $this->getBuildingStorageUsage($building);
        $newTotalBytes = $currentUsage['used_bytes'] + $additionalBytes;
        $newTotalGB = $newTotalBytes / (1024 * 1024 * 1024);

        if ($newTotalGB > $package->storage_limit_gb) {
            return [
                'can_upload' => false,
                'message' => 'Storage limit would be exceeded.',
                'current_gb' => $currentUsage['used_gb'],
                'limit_gb' => $package->storage_limit_gb,
                'additional_gb' => round($additionalBytes / (1024 * 1024 * 1024), 3),
                'unlimited' => false,
            ];
        }

        return [
            'can_upload' => true,
            'message' => 'Upload allowed.',
            'remaining_gb' => $package->storage_limit_gb - $newTotalGB,
            'unlimited' => false,
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get human readable type label.
     */
    private function getTypeLabel(string $type): string
    {
        $labels = [
            'Expense' => 'Expenses',
            'Income' => 'Income',
            'Notification' => 'Notifications',
        ];

        return $labels[$type] ?? $type;
    }
}
