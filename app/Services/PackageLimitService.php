<?php

namespace App\Services;

use App\Models\Building;
use App\Models\Package;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class PackageLimitService
{
    /**
     * Check if a building can add more neighbors.
     */
    public function canAddNeighbors(Building $building, int $count = 1): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
                'current_count' => 0,
                'limit' => 0,
            ];
        }

        if ($package->hasUnlimitedNeighbors()) {
            return [
                'allowed' => true,
                'message' => 'Unlimited neighbors allowed.',
                'current_count' => $building->users()->count(),
                'limit' => null,
            ];
        }

        $currentCount = $building->users()->count();
        $allowed = ($currentCount + $count) <= $package->max_neighbors;

        return [
            'allowed' => $allowed,
            'message' => $allowed ? 'Within neighbor limit.' : 'Neighbor limit exceeded.',
            'current_count' => $currentCount,
            'limit' => $package->max_neighbors,
            'adding' => $count,
            'would_be_total' => $currentCount + $count,
        ];
    }

    /**
     * Check if a building can use notifications.
     */
    public function canUseNotifications(Building $building): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
            ];
        }

        return [
            'allowed' => $package->notifications_enabled,
            'message' => $package->notifications_enabled 
                ? 'Notifications are enabled.' 
                : 'Notifications are not enabled for your current package.',
            'feature' => 'notifications',
        ];
    }

    /**
     * Check if a building can use email notifications.
     */
    public function canUseEmailNotifications(Building $building): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
            ];
        }

        return [
            'allowed' => $package->email_notifications_enabled,
            'message' => $package->email_notifications_enabled 
                ? 'Email notifications are enabled.' 
                : 'Email notifications are not enabled for your current package.',
            'feature' => 'email_notifications',
        ];
    }

    /**
     * Check if a building can use file attachments.
     */
    public function canUseFileAttachments(Building $building, int $fileCount = 1, int $totalSizeBytes = 0): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
            ];
        }

        if (!$package->file_attachments_enabled) {
            return [
                'allowed' => false,
                'message' => 'File attachments are not enabled for your current package.',
                'feature' => 'file_attachments',
            ];
        }

        // Check file count limit
        $maxFilesPerRecord = $package->getMaxFilesPerRecord();
        if ($fileCount > $maxFilesPerRecord) {
            return [
                'allowed' => false,
                'message' => "Too many files. Maximum {$maxFilesPerRecord} files allowed per record.",
                'file_count' => $fileCount,
                'max_files' => $maxFilesPerRecord,
            ];
        }

        // Check file size limit
        $maxFileSizeMb = $package->getMaxFileSizeMb();
        $maxSizeBytes = $maxFileSizeMb * 1024 * 1024;
        if ($totalSizeBytes > $maxSizeBytes) {
            return [
                'allowed' => false,
                'message' => "File size exceeds limit. Maximum {$maxFileSizeMb}MB allowed.",
                'total_size_mb' => round($totalSizeBytes / (1024 * 1024), 2),
                'max_size_mb' => $maxFileSizeMb,
            ];
        }

        return [
            'allowed' => true,
            'message' => 'File attachments are allowed.',
            'max_files' => $maxFilesPerRecord,
            'max_size_mb' => $maxFileSizeMb,
        ];
    }

    /**
     * Check storage usage and limits.
     */
    public function checkStorageUsage(Building $building, int $additionalBytes = 0): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
            ];
        }

        $currentBytes = $building->fileAttachments()->sum('file_size') ?? 0;
        $currentGB = $currentBytes / (1024 * 1024 * 1024);

        if ($package->hasUnlimitedStorage()) {
            return [
                'allowed' => true,
                'message' => 'Unlimited storage available.',
                'current_gb' => round($currentGB, 2),
                'limit_gb' => null,
                'unlimited' => true,
            ];
        }

        $newTotalBytes = $currentBytes + $additionalBytes;
        $newTotalGB = $newTotalBytes / (1024 * 1024 * 1024);
        $allowed = $newTotalGB <= $package->storage_limit_gb;

        return [
            'allowed' => $allowed,
            'message' => $allowed 
                ? 'Within storage limit.' 
                : 'Storage limit exceeded.',
            'current_gb' => round($currentGB, 2),
            'new_total_gb' => round($newTotalGB, 2),
            'limit_gb' => $package->storage_limit_gb,
            'usage_percentage' => round(($newTotalGB / $package->storage_limit_gb) * 100, 1),
            'unlimited' => false,
        ];
    }

    /**
     * Update usage statistics for a subscription.
     */
    public function updateUsageStats(Building $building): bool
    {
        $subscription = $building->getCurrentSubscription();
        
        if (!$subscription) {
            return false;
        }

        try {
            $neighborsCount = $building->users()->count();
            $storageBytes = $building->fileAttachments()->sum('file_size') ?? 0;
            
            // Get notifications sent this month (you might need to implement this based on your notification tracking)
            $notificationsSent = $building->notifications()
                ->where('created_at', '>=', now()->startOfMonth())
                ->count();
            
            // Get emails sent this month (you might need to implement this based on your email tracking)
            $emailsSent = $building->notifications()
                ->where('email_sent', true)
                ->where('email_sent_at', '>=', now()->startOfMonth())
                ->count();

            $subscription->updateUsage([
                'neighbors_count' => $neighborsCount,
                'storage_used' => $storageBytes,
                'notifications_sent' => $notificationsSent,
                'emails_sent' => $emailsSent,
                'stats' => [
                    'last_updated' => now()->toISOString(),
                    'storage_gb' => round($storageBytes / (1024 * 1024 * 1024), 2),
                ]
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update usage stats', [
                'building_id' => $building->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get comprehensive usage report for a building.
     */
    public function getUsageReport(Building $building): array
    {
        $package = $building->getEffectivePackage();
        $subscription = $building->getCurrentSubscription();

        if (!$package) {
            return [
                'error' => 'No package found for building.',
            ];
        }

        // Update usage stats
        $this->updateUsageStats($building);

        $neighborsCheck = $this->canAddNeighbors($building, 0);
        $storageCheck = $this->checkStorageUsage($building, 0);
        $notificationsCheck = $this->canUseNotifications($building);
        $emailCheck = $this->canUseEmailNotifications($building);
        $filesCheck = $this->canUseFileAttachments($building, 1, 0);

        return [
            'package' => [
                'id' => $package->id,
                'name' => $package->name,
                'slug' => $package->slug,
                'is_free' => $package->isFree(),
            ],
            'subscription' => $subscription ? [
                'id' => $subscription->id,
                'status' => $subscription->status,
                'ends_at' => $subscription->ends_at->toISOString(),
                'days_remaining' => $subscription->getDaysRemaining(),
                'is_trial' => $subscription->isInTrial(),
            ] : null,
            'usage' => [
                'neighbors' => $neighborsCheck,
                'storage' => $storageCheck,
                'notifications' => $notificationsCheck,
                'email_notifications' => $emailCheck,
                'file_attachments' => $filesCheck,
            ],
            'limits_exceeded' => [
                'neighbors' => !$neighborsCheck['allowed'],
                'storage' => !$storageCheck['allowed'],
                'notifications' => !$notificationsCheck['allowed'],
                'email_notifications' => !$emailCheck['allowed'],
                'file_attachments' => !$filesCheck['allowed'],
            ],
            'upgrade_recommended' => $this->shouldRecommendUpgrade($building, $package),
        ];
    }

    /**
     * Determine if an upgrade should be recommended.
     */
    private function shouldRecommendUpgrade(Building $building, Package $package): bool
    {
        // Check if any limits are being approached (80% usage)
        $neighborsCheck = $this->canAddNeighbors($building, 0);
        if (!$package->hasUnlimitedNeighbors() && 
            $neighborsCheck['current_count'] >= ($package->max_neighbors * 0.8)) {
            return true;
        }

        $storageCheck = $this->checkStorageUsage($building, 0);
        if (!$package->hasUnlimitedStorage() && 
            isset($storageCheck['usage_percentage']) && 
            $storageCheck['usage_percentage'] >= 80) {
            return true;
        }

        // Check if important features are disabled
        if (!$package->notifications_enabled || 
            !$package->email_notifications_enabled || 
            !$package->file_attachments_enabled) {
            return true;
        }

        return false;
    }

    /**
     * Get suggested package for upgrade.
     */
    public function getSuggestedUpgrade(Building $building): ?Package
    {
        $currentPackage = $building->getEffectivePackage();
        
        if (!$currentPackage) {
            return Package::where('slug', 'free')->first();
        }

        // Get next higher package
        return Package::active()
            ->where('price', '>', $currentPackage->price)
            ->orderBy('price')
            ->first();
    }
}
