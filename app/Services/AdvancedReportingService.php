<?php

namespace App\Services;

use App\Models\Building;
use App\Models\CustomReport;
use App\Models\ReportAnalytics;
use App\Models\ReportGeneration;
use App\Models\ReportTemplate;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdvancedReportingService
{
    /**
     * Check if building has advanced reporting features enabled.
     */
    public function hasAdvancedReporting(Building $building): bool
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            // If no package is found and building has admin users, assign Pro package as default
            $adminUsers = $building->users()->whereIn('role', ['admin', 'super_admin'])->count();
            if ($adminUsers > 0) {
                $proPackage = \App\Models\Package::where('slug', 'pro')->first();
                if ($proPackage) {
                    $building->update(['current_package_id' => $proPackage->id]);
                    $package = $proPackage;
                    \Log::info('Auto-assigned Pro package to building with admin users', [
                        'building_id' => $building->id,
                        'package_id' => $proPackage->id
                    ]);
                }
            }
        }

        if (!$package) {
            return false;
        }

        // Advanced reporting is available for Standard+ packages
        return $package->advanced_reporting ?? false;
    }

    /**
     * Check if building has custom reports enabled.
     */
    public function hasCustomReports(Building $building): bool
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            return false;
        }

        return $package->custom_reports_enabled ?? false;
    }

    /**
     * Check if building has report scheduling enabled.
     */
    public function hasReportScheduling(Building $building): bool
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            return false;
        }

        return $package->report_scheduling_enabled ?? false;
    }

    /**
     * Check if building has advanced charts enabled.
     */
    public function hasAdvancedCharts(Building $building): bool
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            return false;
        }

        return $package->advanced_charts_enabled ?? false;
    }

    /**
     * Get available chart types for building.
     */
    public function getAvailableChartTypes(Building $building): array
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            return ['bar', 'line']; // Basic charts only
        }

        if (!$this->hasAdvancedCharts($building)) {
            return ['bar', 'line']; // Basic charts only
        }

        $chartTypes = $package->available_chart_types;
        if (is_string($chartTypes)) {
            $chartTypes = json_decode($chartTypes, true);
        }

        return $chartTypes ?? [
            'bar', 'line', 'pie', 'doughnut', 'area', 'scatter', 'radar'
        ];
    }

    /**
     * Get custom report limits for building.
     */
    public function getCustomReportLimits(Building $building): array
    {
        $package = $building->getEffectivePackage();

        if (!$package) {
            return [
                'max_custom_reports' => 0,
                'max_scheduled_reports' => 0,
                'has_custom_reports' => false,
                'has_scheduling' => false,
            ];
        }

        return [
            'max_custom_reports' => $package->max_custom_reports,
            'max_scheduled_reports' => $package->max_scheduled_reports,
            'has_custom_reports' => $package->custom_reports_enabled ?? false,
            'has_scheduling' => $package->report_scheduling_enabled ?? false,
        ];
    }

    /**
     * Check if building can create more custom reports.
     */
    public function canCreateMoreReports(Building $building): bool
    {
        $limits = $this->getCustomReportLimits($building);
        
        if (!$limits['has_custom_reports']) {
            return false;
        }

        if ($limits['max_custom_reports'] === null) {
            return true; // Unlimited
        }

        $currentCount = CustomReport::forBuilding($building->id)->active()->count();
        return $currentCount < $limits['max_custom_reports'];
    }

    /**
     * Get available report templates for building.
     */
    public function getAvailableTemplates(Building $building): array
    {
        $templates = ReportTemplate::active()->get();
        
        // Filter templates based on package features
        return $templates->filter(function ($template) use ($building) {
            // Basic templates are always available
            if ($template->category === 'financial') {
                return true;
            }

            // Advanced templates require advanced reporting
            if (in_array($template->category, ['analytics', 'custom'])) {
                return $this->hasAdvancedReporting($building);
            }

            return true;
        })->values()->toArray();
    }

    /**
     * Create a custom report from template.
     */
    public function createCustomReport(
        Building $building,
        User $user,
        ReportTemplate $template,
        array $config
    ): CustomReport {
        if (!$this->canCreateMoreReports($building)) {
            throw new \Exception('Custom report limit reached for your package');
        }

        // Validate configuration
        $errors = $template->validateConfiguration($config);
        if (!empty($errors)) {
            throw new \Exception('Invalid configuration: ' . implode(', ', $errors));
        }

        $report = $template->createCustomReport($building, $user, $config);

        // Log analytics - use 'viewed' as the closest equivalent to 'created'
        ReportAnalytics::logEvent(
            $building->id,
            $report->id,
            $user->id,
            'viewed',
            ['template_id' => $template->id, 'action_type' => 'created']
        );

        Log::info('Custom report created', [
            'building_id' => $building->id,
            'user_id' => $user->id,
            'report_id' => $report->id,
            'template_id' => $template->id,
        ]);

        return $report;
    }

    /**
     * Generate report data based on configuration.
     */
    public function generateReportData(CustomReport $report, array $parameters = []): array
    {
        $config = $report->getFullConfiguration();
        $template = $report->template;
        
        if (!$template) {
            throw new \Exception('Report template not found');
        }

        // Build query based on template and configuration
        $data = $this->buildReportQuery($template, $config, $report->building, $parameters);

        // Log analytics
        ReportAnalytics::logEvent(
            $report->building_id,
            $report->id,
            auth()->id(),
            'generated',
            ['parameters' => $parameters]
        );

        return $data;
    }

    /**
     * Build report query based on template and configuration.
     */
    private function buildReportQuery(
        ReportTemplate $template,
        array $config,
        Building $building,
        array $parameters
    ): array {
        $dataSources = $template->getDataSourceModels();
        $selectedFields = $config['fields'] ?? [];
        $filters = $config['filters'] ?? [];
        $grouping = $config['grouping'] ?? [];

        // Start with the primary data source
        $primarySource = array_key_first($dataSources);
        $modelClass = $dataSources[$primarySource];
        
        $query = $modelClass::where('building_id', $building->id);

        // Apply date filters
        if (isset($parameters['date_from']) && isset($parameters['date_to'])) {
            $query->whereBetween('created_at', [
                $parameters['date_from'],
                $parameters['date_to']
            ]);
        }

        // Apply custom filters from configuration
        foreach ($filters as $filterKey => $filterValue) {
            if (empty($filterValue)) continue;

            switch ($filterKey) {
                case 'expense_type_id':
                    if ($primarySource === 'expenses') {
                        $query->where('expense_type_id', $filterValue);
                    }
                    break;
                case 'user_id':
                    $query->where('user_id', $filterValue);
                    break;
                case 'status':
                    $query->where('status', $filterValue);
                    break;
                case 'amount_min':
                    $query->where('amount', '>=', $filterValue);
                    break;
                case 'amount_max':
                    $query->where('amount', '<=', $filterValue);
                    break;
            }
        }

        // Apply grouping
        if (!empty($grouping)) {
            foreach ($grouping as $group) {
                $query->groupBy($group);
            }
        }

        // Load relationships based on selected fields
        $relationships = $this->getRequiredRelationships($selectedFields, $primarySource);
        if (!empty($relationships)) {
            $query->with($relationships);
        }

        // Execute query
        $results = $query->get();

        // Process results based on configuration
        return $this->processReportResults($results, $config, $template);
    }

    /**
     * Get required relationships based on selected fields.
     */
    private function getRequiredRelationships(array $selectedFields, string $primarySource): array
    {
        $relationships = [];

        foreach ($selectedFields as $field) {
            switch ($field) {
                case 'expense_type_name':
                    if ($primarySource === 'expenses') {
                        $relationships[] = 'expenseType';
                    }
                    break;
                case 'user_name':
                case 'user_email':
                case 'apartment_number':
                    $relationships[] = 'user';
                    break;
                case 'payment_status':
                case 'payment_amount':
                    if ($primarySource === 'expenses') {
                        $relationships[] = 'payments';
                    }
                    break;
            }
        }

        return array_unique($relationships);
    }

    /**
     * Process report results based on configuration.
     */
    private function processReportResults($results, array $config, ReportTemplate $template): array
    {
        $processedData = [];
        $summary = [];

        foreach ($results as $result) {
            $row = [];
            
            // Extract selected fields
            foreach ($config['fields'] ?? [] as $field) {
                $row[$field] = $this->extractFieldValue($result, $field);
            }

            $processedData[] = $row;
        }

        // Calculate summary statistics
        if (isset($config['calculate_totals']) && $config['calculate_totals']) {
            $summary = $this->calculateSummaryStatistics($results, $config);
        }

        return [
            'data' => $processedData,
            'summary' => $summary,
            'record_count' => count($processedData),
            'generated_at' => now(),
            'configuration' => $config,
        ];
    }

    /**
     * Extract field value from result object.
     */
    private function extractFieldValue($result, string $field)
    {
        switch ($field) {
            case 'expense_type_name':
                return $result->expenseType->name ?? null;
            case 'user_name':
                return $result->user->name ?? null;
            case 'user_email':
                return $result->user->email ?? null;
            case 'apartment_number':
                return $result->user->apartment_number ?? null;
            case 'payment_status':
                return $result->payments->first()->status ?? 'unpaid';
            case 'payment_amount':
                return $result->payments->sum('amount') ?? 0;
            default:
                return $result->{$field} ?? null;
        }
    }

    /**
     * Calculate summary statistics.
     */
    private function calculateSummaryStatistics($results, array $config): array
    {
        $summary = [];

        // Calculate totals for numeric fields
        $numericFields = ['amount', 'payment_amount'];
        foreach ($numericFields as $field) {
            if (in_array($field, $config['fields'] ?? [])) {
                $summary["total_{$field}"] = $results->sum($field);
                $summary["avg_{$field}"] = $results->avg($field);
                $summary["min_{$field}"] = $results->min($field);
                $summary["max_{$field}"] = $results->max($field);
            }
        }

        // Count records by status if status field is selected
        if (in_array('status', $config['fields'] ?? [])) {
            $summary['status_counts'] = $results->groupBy('status')
                ->map(function ($group) {
                    return $group->count();
                })->toArray();
        }

        return $summary;
    }

    /**
     * Get reporting statistics for building.
     */
    public function getReportingStats(Building $building): array
    {
        $limits = $this->getCustomReportLimits($building);
        
        $customReportsCount = CustomReport::forBuilding($building->id)->active()->count();
        $totalGenerations = ReportGeneration::forBuilding($building->id)->count();
        $recentGenerations = ReportGeneration::forBuilding($building->id)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return [
            'has_advanced_reporting' => $this->hasAdvancedReporting($building),
            'has_custom_reports' => $limits['has_custom_reports'],
            'has_scheduling' => $limits['has_scheduling'],
            'has_advanced_charts' => $this->hasAdvancedCharts($building),
            'custom_reports_count' => $customReportsCount,
            'max_custom_reports' => $limits['max_custom_reports'],
            'can_create_more' => $this->canCreateMoreReports($building),
            'total_generations' => $totalGenerations,
            'recent_generations' => $recentGenerations,
            'available_chart_types' => $this->getAvailableChartTypes($building),
        ];
    }
}
