<?php

namespace App\Services;

use App\Models\Building;
use App\Models\ExportRequest;
use App\Models\Package;
use App\Models\User;
use App\Jobs\ProcessExportJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ExportService
{
    /**
     * Check if user can create an export request.
     */
    public function canCreateExport(Building $building, string $type, string $format): array
    {
        $package = $building->getEffectivePackage();
        
        if (!$package) {
            return [
                'allowed' => false,
                'message' => 'No package found for building.',
                'quota_info' => null,
            ];
        }

        // Check if exports are enabled for this package
        if (!$package->exports_enabled) {
            return [
                'allowed' => false,
                'message' => 'Export functionality is not enabled for your current package.',
                'quota_info' => null,
            ];
        }

        // Check if format is allowed
        $allowedFormats = $package->export_formats ?? [];
        if (!in_array($format, $allowedFormats)) {
            return [
                'allowed' => false,
                'message' => "Export format '{$format}' is not allowed for your current package.",
                'quota_info' => null,
            ];
        }

        // Check monthly export limit
        $monthlyUsage = $this->getMonthlyExportCount($building);
        if ($package->exports_per_month && $monthlyUsage >= $package->exports_per_month) {
            return [
                'allowed' => false,
                'message' => "Monthly export limit reached ({$package->exports_per_month} exports per month).",
                'quota_info' => [
                    'monthly_used' => $monthlyUsage,
                    'monthly_limit' => $package->exports_per_month,
                    'monthly_remaining' => 0,
                ],
            ];
        }

        // Check concurrent export limit
        $concurrentExports = $this->getConcurrentExportCount($building);
        $maxConcurrent = config('export.limits.max_concurrent_exports', 3);
        if ($concurrentExports >= $maxConcurrent) {
            return [
                'allowed' => false,
                'message' => "Maximum concurrent exports limit reached ({$maxConcurrent} exports).",
                'quota_info' => null,
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Export creation allowed.',
            'quota_info' => [
                'monthly_used' => $monthlyUsage,
                'monthly_limit' => $package->exports_per_month,
                'monthly_remaining' => $package->exports_per_month ? ($package->exports_per_month - $monthlyUsage) : null,
                'concurrent_exports' => $concurrentExports,
                'max_concurrent' => $maxConcurrent,
            ],
        ];
    }

    /**
     * Create a new export request.
     */
    public function createExportRequest(
        Building $building,
        User $user,
        string $type,
        string $format,
        array $parameters = []
    ): ExportRequest {
        // Validate export type
        $availableTypes = array_keys(config('export.templates', []));
        if (!in_array($type, $availableTypes)) {
            throw new \InvalidArgumentException("Invalid export type: {$type}");
        }

        // Validate format
        $availableFormats = ['pdf', 'excel'];
        if (!in_array($format, $availableFormats)) {
            throw new \InvalidArgumentException("Invalid export format: {$format}");
        }

        $exportRequest = ExportRequest::create([
            'building_id' => $building->id,
            'user_id' => $user->id,
            'type' => $type,
            'format' => $format,
            'parameters' => $parameters,
            'status' => 'pending',
        ]);

        // Queue the export job if queue is enabled
        if (config('export.queue.enabled', true)) {
            \App\Jobs\ProcessExportJob::dispatch($exportRequest)
                ->onQueue(config('export.queue.queue_name', 'exports'));
        }

        Log::info('Export request created', [
            'export_id' => $exportRequest->id,
            'building_id' => $building->id,
            'user_id' => $user->id,
            'type' => $type,
            'format' => $format,
        ]);

        return $exportRequest;
    }

    /**
     * Get monthly export count for a building.
     */
    public function getMonthlyExportCount(Building $building): int
    {
        return ExportRequest::forBuilding($building->id)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }

    /**
     * Get concurrent export count for a building.
     */
    public function getConcurrentExportCount(Building $building): int
    {
        return ExportRequest::forBuilding($building->id)
            ->whereIn('status', ['pending', 'processing'])
            ->count();
    }

    /**
     * Get export statistics for a building.
     */
    public function getBuildingExportStats(Building $building, string $period = 'month'): array
    {
        $query = ExportRequest::forBuilding($building->id);

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                break;
            case 'week':
                $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', Carbon::now()->month)
                      ->whereYear('created_at', Carbon::now()->year);
                break;
            case 'year':
                $query->whereYear('created_at', Carbon::now()->year);
                break;
        }

        $total = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $failed = $query->where('status', 'failed')->count();
        $pending = $query->where('status', 'pending')->count();
        $processing = $query->where('status', 'processing')->count();

        // Get usage by type
        $usageByType = ExportRequest::forBuilding($building->id)
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Get usage by format
        $usageByFormat = ExportRequest::forBuilding($building->id)
            ->selectRaw('format, COUNT(*) as count')
            ->groupBy('format')
            ->pluck('count', 'format')
            ->toArray();

        return [
            'period' => $period,
            'total' => $total,
            'completed' => $completed,
            'failed' => $failed,
            'pending' => $pending,
            'processing' => $processing,
            'success_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'usage_by_type' => $usageByType,
            'usage_by_format' => $usageByFormat,
        ];
    }

    /**
     * Clean up expired export files.
     */
    public function cleanupExpiredExports(): int
    {
        $expiredExports = ExportRequest::getExportsForCleanup();
        $cleaned = 0;

        foreach ($expiredExports as $export) {
            try {
                if ($export->deleteFile()) {
                    $export->update([
                        'file_path' => null,
                        'file_name' => null,
                        'file_size' => null,
                    ]);
                    $cleaned++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to cleanup export file', [
                    'export_id' => $export->id,
                    'file_path' => $export->file_path,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Export cleanup completed', [
            'total_expired' => $expiredExports->count(),
            'cleaned' => $cleaned,
        ]);

        return $cleaned;
    }

    /**
     * Get available export types for a building.
     */
    public function getAvailableExportTypes(Building $building): array
    {
        $package = $building->getEffectivePackage();
        $templates = config('export.templates', []);
        
        if (!$package || !$package->exports_enabled) {
            return [];
        }

        $allowedFormats = $package->export_formats ?? [];
        $availableTypes = [];

        foreach ($templates as $type => $config) {
            $typeFormats = array_intersect($config['formats'], $allowedFormats);
            if (!empty($typeFormats)) {
                $availableTypes[$type] = [
                    'name' => $config['name'],
                    'description' => $config['description'],
                    'formats' => $typeFormats,
                    'memory_intensive' => $config['memory_intensive'] ?? false,
                ];
            }
        }

        return $availableTypes;
    }

    /**
     * Get storage usage statistics.
     */
    public function getStorageStats(): array
    {
        $disk = Storage::disk(config('export.storage.disk', 'local'));
        $exportPath = config('export.storage.path', 'exports');
        
        $totalSize = 0;
        $fileCount = 0;
        
        try {
            $files = $disk->allFiles($exportPath);
            foreach ($files as $file) {
                $totalSize += $disk->size($file);
                $fileCount++;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get storage stats', ['error' => $e->getMessage()]);
        }

        $maxStorageMB = config('export.storage.max_storage_mb', 500);
        $totalSizeMB = $totalSize / (1024 * 1024);

        return [
            'total_files' => $fileCount,
            'total_size_bytes' => $totalSize,
            'total_size_mb' => round($totalSizeMB, 2),
            'max_storage_mb' => $maxStorageMB,
            'usage_percentage' => $maxStorageMB > 0 ? round(($totalSizeMB / $maxStorageMB) * 100, 2) : 0,
        ];
    }
}
