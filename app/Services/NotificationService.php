<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Expense;
use App\Models\Payment;
use App\Models\Income;
use App\Mail\NotificationMail;
use App\Services\EmailService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }
    /**
     * Create a payment reminder notification.
     */
    public function createPaymentReminder(User $user, Expense $expense, array $additionalData = []): Notification
    {
        $data = array_merge([
            'expense_id' => $expense->id,
            'amount' => $expense->amount,
            'due_date' => $expense->due_date,
        ], $additionalData);

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_PAYMENT_REMINDER,
            'title' => 'Payment Reminder',
            'message' => "You have a payment due for {$expense->expenseType->name}. Amount: {$expense->amount}",
            'data' => $data,
            'priority' => Notification::PRIORITY_HIGH,
        ]);
    }

    /**
     * Create an overdue payment notification.
     */
    public function createOverduePaymentNotification(User $user, Expense $expense): Notification
    {
        $data = [
            'expense_id' => $expense->id,
            'amount' => $expense->amount,
            'due_date' => $expense->due_date,
            'days_overdue' => now()->diffInDays($expense->due_date),
        ];

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_OVERDUE_PAYMENT,
            'title' => 'Overdue Payment',
            'message' => "Your payment for {$expense->expenseType->name} is overdue. Amount: {$expense->amount}",
            'data' => $data,
            'priority' => Notification::PRIORITY_HIGH,
        ]);
    }

    /**
     * Create a payment received notification.
     */
    public function createPaymentReceivedNotification(User $user, Payment $payment): Notification
    {
        $data = [
            'payment_id' => $payment->id,
            'expense_id' => $payment->expense_id,
            'amount' => $payment->amount,
            'payment_date' => $payment->payment_date,
            'payment_method' => $payment->payment_method,
        ];

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_PAYMENT_RECEIVED,
            'title' => 'Payment Received',
            'message' => "Your payment of {$payment->amount} has been received and processed.",
            'data' => $data,
            'priority' => Notification::PRIORITY_MEDIUM,
        ]);
    }

    /**
     * Create an expense created notification.
     */
    public function createExpenseCreatedNotification(User $user, Expense $expense): Notification
    {
        $data = [
            'expense_id' => $expense->id,
            'amount' => $expense->amount,
            'due_date' => $expense->due_date,
            'expense_type' => $expense->expenseType->name,
        ];

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_EXPENSE_CREATED,
            'title' => 'New Expense Created',
            'message' => "A new expense has been created: {$expense->expenseType->name}. Amount: {$expense->amount}",
            'data' => $data,
            'priority' => Notification::PRIORITY_MEDIUM,
        ]);
    }

    /**
     * Create an income received notification.
     */
    public function createIncomeReceivedNotification(User $user, Income $income): Notification
    {
        $data = [
            'income_id' => $income->id,
            'amount' => $income->amount,
            'payment_date' => $income->payment_date,
            'payment_method' => $income->payment_method,
        ];

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_INCOME_RECEIVED,
            'title' => 'Income Recorded',
            'message' => "Income of {$income->amount} has been recorded for your account.",
            'data' => $data,
            'priority' => Notification::PRIORITY_LOW,
        ]);
    }

    /**
     * Create a general announcement notification.
     */
    public function createGeneralAnnouncement(User $user, string $title, string $message, array $data = [], string $priority = 'medium'): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $user->building_id,
            'type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'priority' => $priority,
        ]);
    }

    /**
     * Send notification to all users in a building.
     */
    public function sendToAllBuildingUsers(int $buildingId, string $type, string $title, string $message, array $data = [], string $priority = 'medium'): array
    {
        $users = User::where('building_id', $buildingId)->get();
        $notifications = [];

        foreach ($users as $user) {
            $notifications[] = Notification::create([
                'user_id' => $user->id,
                'building_id' => $buildingId,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'priority' => $priority,
            ]);
        }

        return $notifications;
    }

    /**
     * Send email notification if user has email notifications enabled.
     */
    public function sendEmailNotification(Notification $notification): bool
    {
        return $this->emailService->sendNotificationEmail($notification);
    }

    /**
     * Process scheduled notifications that are due.
     */
    public function processScheduledNotifications(): int
    {
        $dueNotifications = Notification::due()
            ->where('email_sent', false)
            ->get();

        $processed = 0;

        foreach ($dueNotifications as $notification) {
            if ($this->sendEmailNotification($notification)) {
                $processed++;
            }
        }

        return $processed;
    }

    /**
     * Send payment reminders for overdue expenses.
     */
    public function sendPaymentReminders(): int
    {
        $overdueExpenses = Expense::where('due_date', '<', now())
            ->whereDoesntHave('payments', function ($query) {
                $query->where('status', 'completed');
            })
            ->with(['user', 'expenseType'])
            ->get();

        $sent = 0;

        foreach ($overdueExpenses as $expense) {
            if ($expense->user) {
                $this->createOverduePaymentNotification($expense->user, $expense);
                $sent++;
            }
        }

        return $sent;
    }
}
