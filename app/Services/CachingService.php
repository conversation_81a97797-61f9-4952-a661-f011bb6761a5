<?php

namespace App\Services;

use App\Models\Building;
use App\Models\Package;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CachingService
{
    const CACHE_TTL_SHORT = 300;    // 5 minutes
    const CACHE_TTL_MEDIUM = 1800;  // 30 minutes
    const CACHE_TTL_LONG = 3600;    // 1 hour
    const CACHE_TTL_DAILY = 86400;  // 24 hours

    /**
     * Cache package definitions with long TTL.
     */
    public function cachePackages(): array
    {
        return Cache::remember('packages_all', self::CACHE_TTL_DAILY, function () {
            return Package::where('is_active', true)
                ->orderBy('sort_order')
                ->get()
                ->keyBy('id')
                ->toArray();
        });
    }

    /**
     * Get cached package by ID.
     */
    public function getCachedPackage(int $packageId): ?array
    {
        $packages = $this->cachePackages();
        return $packages[$packageId] ?? null;
    }

    /**
     * Cache user permissions and role information.
     */
    public function cacheUserPermissions(int $userId): array
    {
        return Cache::remember("user_permissions_{$userId}", self::CACHE_TTL_MEDIUM, function () use ($userId) {
            $user = User::with(['building.currentPackage'])->find($userId);
            
            if (!$user) {
                return [];
            }

            $package = $user->building?->getEffectivePackage();
            
            return [
                'user_id' => $user->id,
                'role' => $user->role,
                'building_id' => $user->building_id,
                'package_features' => $package ? [
                    'max_neighbors' => $package->max_neighbors,
                    'notifications_enabled' => $package->notifications_enabled,
                    'email_notifications_enabled' => $package->email_notifications_enabled,
                    'exports_enabled' => $package->exports_enabled,
                    'advanced_reporting' => $package->advanced_reporting,
                    'file_attachments_enabled' => $package->file_attachments_enabled,
                    'max_file_size_mb' => $package->max_file_size_mb,
                    'storage_limit_gb' => $package->storage_limit_gb,
                ] : [],
                'cached_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Cache building storage usage calculations.
     */
    public function cacheBuildingStorageUsage(int $buildingId): array
    {
        return Cache::remember("building_storage_{$buildingId}", self::CACHE_TTL_SHORT, function () use ($buildingId) {
            $building = Building::find($buildingId);
            if (!$building) {
                return ['total_size' => 0, 'file_count' => 0];
            }

            $usage = $building->fileAttachments()
                ->selectRaw('SUM(file_size) as total_size, COUNT(*) as file_count')
                ->first();

            return [
                'total_size' => $usage->total_size ?? 0,
                'file_count' => $usage->file_count ?? 0,
                'total_size_mb' => round(($usage->total_size ?? 0) / 1024 / 1024, 2),
                'cached_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Cache building statistics for dashboard.
     */
    public function cacheBuildingStats(int $buildingId): array
    {
        return Cache::remember("building_stats_{$buildingId}", self::CACHE_TTL_MEDIUM, function () use ($buildingId) {
            $building = Building::find($buildingId);
            if (!$building) {
                return [];
            }

            return [
                'total_users' => $building->users()->count(),
                'active_users' => $building->users()->whereNotNull('email_verified_at')->count(),
                'total_expenses' => $building->expenses()->where('is_archived', false)->count(),
                'total_incomes' => $building->incomes()->count(),
                'pending_payments' => $building->expenses()
                    ->where('is_archived', false)
                    ->whereDoesntHave('payments', function ($query) {
                        $query->where('status', 'completed');
                    })
                    ->count(),
                'cached_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Cache email usage for package limit checking.
     */
    public function cacheEmailUsage(int $buildingId, string $period = 'month'): array
    {
        $cacheKey = "email_usage_{$buildingId}_{$period}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL_SHORT, function () use ($buildingId, $period) {
            $query = \App\Models\EmailUsage::where('building_id', $buildingId);
            
            if ($period === 'month') {
                $query->where('month', now()->month)
                      ->where('year', now()->year);
            } elseif ($period === 'day') {
                $query->whereDate('date', now()->toDateString());
            }

            $usage = $query->selectRaw('
                COUNT(*) as total_emails,
                SUM(CASE WHEN email_type = "notification" THEN 1 ELSE 0 END) as notification_emails,
                SUM(CASE WHEN email_type = "reminder" THEN 1 ELSE 0 END) as reminder_emails,
                SUM(CASE WHEN email_type = "report" THEN 1 ELSE 0 END) as report_emails
            ')->first();

            return [
                'total_emails' => $usage->total_emails ?? 0,
                'notification_emails' => $usage->notification_emails ?? 0,
                'reminder_emails' => $usage->reminder_emails ?? 0,
                'report_emails' => $usage->report_emails ?? 0,
                'period' => $period,
                'cached_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Cache export request limits.
     */
    public function cacheExportUsage(int $buildingId): array
    {
        return Cache::remember("export_usage_{$buildingId}", self::CACHE_TTL_SHORT, function () use ($buildingId) {
            $currentMonth = now()->format('Y-m');
            
            $usage = \App\Models\ExportRequest::where('building_id', $buildingId)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->selectRaw('
                    COUNT(*) as total_exports,
                    SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_exports,
                    SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_exports
                ')
                ->first();

            return [
                'total_exports' => $usage->total_exports ?? 0,
                'completed_exports' => $usage->completed_exports ?? 0,
                'failed_exports' => $usage->failed_exports ?? 0,
                'month' => $currentMonth,
                'cached_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Invalidate all caches for a building.
     */
    public function invalidateBuildingCaches(int $buildingId): void
    {
        $keys = [
            "building_storage_{$buildingId}",
            "building_stats_{$buildingId}",
            "email_usage_{$buildingId}_month",
            "email_usage_{$buildingId}_day",
            "export_usage_{$buildingId}",
            "building_financial_summary_{$buildingId}_*",
            "building_users_{$buildingId}_*",
            "expense_statistics_{$buildingId}_*",
        ];

        foreach ($keys as $key) {
            if (str_contains($key, '*')) {
                // For wildcard patterns, we need to use Redis directly
                if (config('cache.default') === 'redis') {
                    $pattern = str_replace('*', '*', $key);
                    $keys = Redis::keys($pattern);
                    if (!empty($keys)) {
                        Redis::del($keys);
                    }
                }
            } else {
                Cache::forget($key);
            }
        }
    }

    /**
     * Invalidate user-specific caches.
     */
    public function invalidateUserCaches(int $userId): void
    {
        Cache::forget("user_permissions_{$userId}");
    }

    /**
     * Invalidate package caches (when packages are updated).
     */
    public function invalidatePackageCaches(): void
    {
        Cache::forget('packages_all');
    }

    /**
     * Warm up critical caches.
     */
    public function warmUpCaches(): void
    {
        // Cache all active packages
        $this->cachePackages();

        // Cache stats for all active buildings
        $buildings = Building::whereHas('users')->pluck('id');
        
        foreach ($buildings as $buildingId) {
            $this->cacheBuildingStats($buildingId);
            $this->cacheBuildingStorageUsage($buildingId);
        }
    }

    /**
     * Get cache statistics.
     */
    public function getCacheStats(): array
    {
        if (config('cache.default') === 'redis') {
            $info = Redis::info();
            return [
                'driver' => 'redis',
                'used_memory' => $info['used_memory_human'] ?? 'N/A',
                'connected_clients' => $info['connected_clients'] ?? 'N/A',
                'total_commands_processed' => $info['total_commands_processed'] ?? 'N/A',
                'keyspace_hits' => $info['keyspace_hits'] ?? 'N/A',
                'keyspace_misses' => $info['keyspace_misses'] ?? 'N/A',
            ];
        }

        return [
            'driver' => config('cache.default'),
            'status' => 'active',
        ];
    }
}
