<?php

namespace App\Services;

use App\Models\Building;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class AdminPermissionService
{
    /**
     * Default permissions for different admin levels.
     */
    const DEFAULT_PERMISSIONS = [
        'primary' => [
            'manage_users' => true,
            'manage_expenses' => true,
            'manage_incomes' => true,
            'manage_payments' => true,
            'manage_notifications' => true,
            'manage_files' => true,
            'manage_admins' => true,
            'view_reports' => true,
            'export_data' => true,
            'manage_building_settings' => true,
            'manage_packages' => true,
        ],
        'secondary' => [
            'manage_users' => false,
            'manage_expenses' => true,
            'manage_incomes' => true,
            'manage_payments' => true,
            'manage_notifications' => true,
            'manage_files' => true,
            'manage_admins' => false,
            'view_reports' => true,
            'export_data' => false,
            'manage_building_settings' => false,
            'manage_packages' => false,
        ],
    ];

    /**
     * Check if building has multi-admin feature enabled.
     */
    public function hasMultiAdminFeature(Building $building): bool
    {
        $package = $building->currentPackage;

        if (!$package) {
            return false;
        }

        // Use dynamic package field instead of hardcoded slugs
        return $package->hasMultiAdminSupport();
    }

    /**
     * Get admin count limit for building's package.
     */
    public function getAdminLimit(Building $building): int
    {
        $package = $building->currentPackage;

        if (!$package) {
            return 1; // Default allows only 1 admin
        }

        // Use dynamic package field instead of hardcoded limits
        return $package->getMaxAdmins();
    }

    /**
     * Check if building can add more admins.
     */
    public function canAddMoreAdmins(Building $building): bool
    {
        if (!$this->hasMultiAdminFeature($building)) {
            return false;
        }

        $currentAdminCount = $this->getAdminCount($building);
        $adminLimit = $this->getAdminLimit($building);

        return $currentAdminCount < $adminLimit;
    }

    /**
     * Get current admin count for building.
     */
    public function getAdminCount(Building $building): int
    {
        return User::where('building_id', $building->id)
            ->where('role', 'admin')
            ->where('is_active', true)
            ->count();
    }

    /**
     * Get all admins for a building.
     */
    public function getBuildingAdmins(Building $building): \Illuminate\Database\Eloquent\Collection
    {
        return User::where('building_id', $building->id)
            ->where('role', 'admin')
            ->orderBy('admin_level', 'asc') // Primary admins first
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Get primary admin for a building.
     */
    public function getPrimaryAdmin(Building $building): ?User
    {
        return User::where('building_id', $building->id)
            ->where('role', 'admin')
            ->where('admin_level', 'primary')
            ->where('is_active', true)
            ->first();
    }

    /**
     * Check if user has specific permission.
     */
    public function hasPermission(User $user, string $permission): bool
    {
        // Super admin has all permissions
        if ($user->role === 'super_admin') {
            return true;
        }

        // Non-admin users have no admin permissions
        if ($user->role !== 'admin') {
            return false;
        }

        // Inactive admins have no permissions
        if (!$user->is_active) {
            return false;
        }

        $permissions = $user->admin_permissions ?? [];
        
        // If no permissions set, use defaults based on admin level
        if (empty($permissions)) {
            $permissions = self::DEFAULT_PERMISSIONS[$user->admin_level ?? 'secondary'] ?? [];
        }

        return $permissions[$permission] ?? false;
    }

    /**
     * Update admin permissions.
     */
    public function updatePermissions(User $admin, array $permissions): bool
    {
        if ($admin->role !== 'admin') {
            return false;
        }

        // Validate permissions
        $validPermissions = array_keys(self::DEFAULT_PERMISSIONS['primary']);
        $filteredPermissions = array_intersect_key($permissions, array_flip($validPermissions));

        $admin->update(['admin_permissions' => $filteredPermissions]);

        Log::info('Admin permissions updated', [
            'admin_id' => $admin->id,
            'building_id' => $admin->building_id,
            'permissions' => $filteredPermissions
        ]);

        return true;
    }

    /**
     * Set admin level and default permissions.
     */
    public function setAdminLevel(User $admin, string $level): bool
    {
        if ($admin->role !== 'admin' || !in_array($level, ['primary', 'secondary'])) {
            return false;
        }

        $building = $admin->building;
        
        // Check if trying to set primary admin when one already exists
        if ($level === 'primary') {
            $existingPrimary = $this->getPrimaryAdmin($building);
            if ($existingPrimary && $existingPrimary->id !== $admin->id) {
                // Demote existing primary to secondary
                $existingPrimary->update([
                    'admin_level' => 'secondary',
                    'admin_permissions' => self::DEFAULT_PERMISSIONS['secondary']
                ]);
            }
        }

        $admin->update([
            'admin_level' => $level,
            'admin_permissions' => self::DEFAULT_PERMISSIONS[$level]
        ]);

        Log::info('Admin level updated', [
            'admin_id' => $admin->id,
            'building_id' => $admin->building_id,
            'level' => $level
        ]);

        return true;
    }

    /**
     * Deactivate admin.
     */
    public function deactivateAdmin(User $admin): bool
    {
        if ($admin->role !== 'admin') {
            return false;
        }

        // Cannot deactivate the last active admin
        $building = $admin->building;
        $activeAdminCount = User::where('building_id', $building->id)
            ->where('role', 'admin')
            ->where('is_active', true)
            ->where('id', '!=', $admin->id)
            ->count();

        if ($activeAdminCount === 0) {
            return false; // Cannot deactivate last admin
        }

        $admin->update(['is_active' => false]);

        Log::info('Admin deactivated', [
            'admin_id' => $admin->id,
            'building_id' => $admin->building_id
        ]);

        return true;
    }

    /**
     * Activate admin.
     */
    public function activateAdmin(User $admin): bool
    {
        if ($admin->role !== 'admin') {
            return false;
        }

        $building = $admin->building;
        
        // Check if building can have more active admins
        if (!$admin->is_active && !$this->canAddMoreAdmins($building)) {
            return false;
        }

        $admin->update(['is_active' => true]);

        Log::info('Admin activated', [
            'admin_id' => $admin->id,
            'building_id' => $admin->building_id
        ]);

        return true;
    }

    /**
     * Get admin statistics for building.
     */
    public function getAdminStats(Building $building): array
    {
        $admins = $this->getBuildingAdmins($building);
        
        return [
            'total_admins' => $admins->count(),
            'active_admins' => $admins->where('is_active', true)->count(),
            'primary_admins' => $admins->where('admin_level', 'primary')->count(),
            'secondary_admins' => $admins->where('admin_level', 'secondary')->count(),
            'admin_limit' => $this->getAdminLimit($building),
            'can_add_more' => $this->canAddMoreAdmins($building),
            'has_multi_admin_feature' => $this->hasMultiAdminFeature($building),
        ];
    }

    /**
     * Get available permissions list.
     */
    public function getAvailablePermissions(): array
    {
        return [
            'manage_users' => 'Manage Users',
            'manage_expenses' => 'Manage Expenses',
            'manage_incomes' => 'Manage Incomes',
            'manage_payments' => 'Manage Payments',
            'manage_notifications' => 'Manage Notifications',
            'manage_files' => 'Manage Files',
            'manage_admins' => 'Manage Admins',
            'view_reports' => 'View Reports',
            'export_data' => 'Export Data',
            'manage_building_settings' => 'Manage Building Settings',
            'manage_packages' => 'Manage Packages',
        ];
    }
}
