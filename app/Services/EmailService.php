<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;
use App\Mail\NotificationMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class EmailService
{
    protected EmailUsageService $emailUsageService;

    public function __construct(EmailUsageService $emailUsageService)
    {
        $this->emailUsageService = $emailUsageService;
    }

    /**
     * Send email notification to a user.
     */
    public function sendNotificationEmail(Notification $notification, bool $queue = true): bool
    {
        try {
            $user = $notification->user;

            if (!$user || !$user->email) {
                Log::warning("Cannot send email notification: user or email not found", [
                    'notification_id' => $notification->id,
                    'user_id' => $notification->user_id
                ]);
                return false;
            }

            // Check if user wants email notifications
            if (!$this->userWantsEmailNotifications($user, $notification)) {
                Log::info("User has disabled email notifications", [
                    'user_id' => $user->id,
                    'notification_id' => $notification->id
                ]);
                return false;
            }

            // Check email quota limits
            $building = $notification->building ?? $user->building;
            if ($building) {
                $quotaCheck = $this->emailUsageService->canSendEmail($building, 'notification');
                if (!$quotaCheck['allowed']) {
                    Log::warning("Email quota limit reached", [
                        'building_id' => $building->id,
                        'message' => $quotaCheck['message'],
                        'notification_id' => $notification->id
                    ]);
                    return false;
                }
            }

            // Track email usage
            $emailUsage = null;
            if ($building) {
                $emailUsage = $this->emailUsageService->trackEmailUsage(
                    $building,
                    'notification',
                    $user->email,
                    $notification->title,
                    $user,
                    ['notification_id' => $notification->id]
                );
            }

            $mail = new NotificationMail($notification);

            if ($queue) {
                // Queue the email for background processing
                Mail::to($user->email)->queue($mail);
            } else {
                // Send immediately
                Mail::to($user->email)->send($mail);
            }

            // Mark email as sent
            $notification->markEmailAsSent();

            // Update user's last email sent timestamp
            $user->markEmailSent();

            // Update email usage tracking
            if ($emailUsage) {
                $this->emailUsageService->markEmailAsSent($emailUsage);
            }

            Log::info("Email notification queued/sent successfully", [
                'user_id' => $user->id,
                'email' => $user->email,
                'notification_id' => $notification->id,
                'queued' => $queue
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send email notification", [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Send bulk email notifications.
     */
    public function sendBulkNotificationEmails(array $notifications, bool $queue = true): array
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'skipped' => 0
        ];

        foreach ($notifications as $notification) {
            if ($this->sendNotificationEmail($notification, $queue)) {
                $results['sent']++;
            } else {
                $results['failed']++;
            }
        }

        Log::info("Bulk email notification results", $results);

        return $results;
    }

    /**
     * Send test email to verify configuration.
     */
    public function sendTestEmail(string $email, bool $queue = false): bool
    {
        try {
            $testData = [
                'subject' => 'Test Email - Amara Building Management',
                'message' => 'This is a test email to verify that your email configuration is working correctly.',
                'timestamp' => now()->format('Y-m-d H:i:s')
            ];

            if ($queue) {
                Mail::to($email)->queue(new \App\Mail\TestMail($testData));
            } else {
                Mail::to($email)->send(new \App\Mail\TestMail($testData));
            }

            Log::info("Test email sent successfully", [
                'email' => $email,
                'queued' => $queue
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send test email", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Check if user wants to receive email notifications.
     */
    private function userWantsEmailNotifications(User $user, Notification $notification = null): bool
    {
        // Check if user has email notifications enabled globally
        if (!$user->email_notifications_enabled) {
            return false;
        }

        // Check frequency preference
        if (!$user->canReceiveEmailNow()) {
            return false;
        }

        // Check specific notification type preference
        if ($notification && !$user->wantsEmailNotification($notification->type)) {
            return false;
        }

        return true;
    }

    /**
     * Get email sending statistics.
     */
    public function getEmailStats(): array
    {
        $stats = [
            'total_notifications' => Notification::count(),
            'email_sent' => Notification::where('email_sent', true)->count(),
            'email_pending' => Notification::where('email_sent', false)->count(),
            'recent_emails' => Notification::where('email_sent', true)
                ->where('email_sent_at', '>=', now()->subDays(7))
                ->count()
        ];

        $stats['email_success_rate'] = $stats['total_notifications'] > 0 
            ? round(($stats['email_sent'] / $stats['total_notifications']) * 100, 2)
            : 0;

        return $stats;
    }

    /**
     * Check email configuration.
     */
    public function checkEmailConfiguration(): array
    {
        $config = [
            'mailer' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'encryption' => config('mail.mailers.smtp.encryption'),
            'username' => config('mail.mailers.smtp.username'),
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
        ];

        $issues = [];

        // Check for common configuration issues
        if ($config['mailer'] === 'log') {
            $issues[] = 'Email is configured to log only (development mode)';
        }

        if (empty($config['host'])) {
            $issues[] = 'MAIL_HOST is not configured';
        }

        if (empty($config['username']) && $config['mailer'] === 'smtp') {
            $issues[] = 'MAIL_USERNAME is not configured for SMTP';
        }

        if (empty($config['from_address']) || $config['from_address'] === '<EMAIL>') {
            $issues[] = 'MAIL_FROM_ADDRESS needs to be configured';
        }

        return [
            'config' => $config,
            'issues' => $issues,
            'is_production_ready' => empty($issues) && $config['mailer'] !== 'log'
        ];
    }

    /**
     * Get queue status for email jobs.
     */
    public function getQueueStatus(): array
    {
        try {
            // This is a basic implementation - you might want to use a more sophisticated queue monitoring
            $pendingJobs = Queue::size();
            
            return [
                'pending_jobs' => $pendingJobs,
                'queue_working' => $pendingJobs >= 0, // Basic check
                'last_checked' => now()->toISOString()
            ];
        } catch (\Exception $e) {
            return [
                'pending_jobs' => 'unknown',
                'queue_working' => false,
                'error' => $e->getMessage(),
                'last_checked' => now()->toISOString()
            ];
        }
    }
}
