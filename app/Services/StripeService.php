<?php

namespace App\Services;

use App\Contracts\PaymentGatewayInterface;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\Subscription;
use Stripe\Price;
use Stripe\Product;

class StripeService implements PaymentGatewayInterface
{
    private $secretKey;
    private $publicKey;
    private $webhookSecret;

    public function __construct()
    {
        $this->secretKey = config('payments.gateways.stripe.secret_key');
        $this->publicKey = config('payments.gateways.stripe.public_key');
        $this->webhookSecret = config('payments.gateways.stripe.webhook_secret');
        
        Stripe::setApiKey($this->secretKey);
    }

    /**
     * Create a payment intent for the given amount
     */
    public function createPayment(float $amount, string $currency, string $description, array $metadata = []): array
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $this->convertToMinorUnits($amount, $currency),
                'currency' => strtolower($currency),
                'description' => $description,
                'metadata' => $metadata,
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            return [
                'success' => true,
                'payment_id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'gateway' => $this->getGatewayName(),
                'public_key' => $this->publicKey,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment creation error: ' . $e->getMessage());
            throw new \Exception('Failed to create payment: ' . $e->getMessage());
        }
    }

    /**
     * Execute/confirm a payment intent
     */
    public function executePayment(string $paymentId, string $payerId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentId);
            
            if ($paymentIntent->status === 'succeeded') {
                return [
                    'success' => true,
                    'payment_id' => $paymentId,
                    'status' => $paymentIntent->status,
                    'amount' => $paymentIntent->amount / 100, // Convert from minor units
                    'currency' => strtoupper($paymentIntent->currency),
                ];
            }

            return [
                'success' => false,
                'payment_id' => $paymentId,
                'status' => $paymentIntent->status,
                'error' => 'Payment not completed',
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment execution error: ' . $e->getMessage());
            throw new \Exception('Failed to execute payment: ' . $e->getMessage());
        }
    }

    /**
     * Get payment details
     */
    public function getPayment(string $paymentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentId);

            return [
                'success' => true,
                'payment_id' => $paymentId,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount / 100,
                'currency' => strtoupper($paymentIntent->currency),
                'description' => $paymentIntent->description,
                'metadata' => $paymentIntent->metadata->toArray(),
                'created' => $paymentIntent->created,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe get payment error: ' . $e->getMessage());
            throw new \Exception('Failed to get payment: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a payment intent
     */
    public function cancelPayment(string $paymentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentId);
            $paymentIntent->cancel();

            return [
                'success' => true,
                'payment_id' => $paymentId,
                'status' => $paymentIntent->status,
                'message' => 'Payment cancelled successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Stripe cancel payment error: ' . $e->getMessage());
            throw new \Exception('Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Create a customer for subscription billing
     */
    public function createCustomer(string $email, string $name, array $metadata = []): array
    {
        try {
            $customer = Customer::create([
                'email' => $email,
                'name' => $name,
                'metadata' => $metadata,
            ]);

            return [
                'success' => true,
                'customer_id' => $customer->id,
                'email' => $customer->email,
                'name' => $customer->name,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe create customer error: ' . $e->getMessage());
            throw new \Exception('Failed to create customer: ' . $e->getMessage());
        }
    }

    /**
     * Create a subscription for recurring billing
     */
    public function createSubscription(string $customerId, string $priceId, array $metadata = []): array
    {
        try {
            $subscription = Subscription::create([
                'customer' => $customerId,
                'items' => [
                    ['price' => $priceId],
                ],
                'metadata' => $metadata,
                'billing_cycle_anchor' => strtotime('first day of next month'),
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id,
                'status' => $subscription->status,
                'current_period_start' => $subscription->current_period_start,
                'current_period_end' => $subscription->current_period_end,
                'client_secret' => $subscription->latest_invoice->payment_intent->client_secret ?? null,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe create subscription error: ' . $e->getMessage());
            throw new \Exception('Failed to create subscription: ' . $e->getMessage());
        }
    }

    /**
     * Create a product and price for subscription
     */
    public function createSubscriptionProduct(string $name, float $amount, string $currency, string $interval = 'month'): array
    {
        try {
            // Create product
            $product = Product::create([
                'name' => $name,
                'type' => 'service',
            ]);

            // Create price
            $price = Price::create([
                'product' => $product->id,
                'unit_amount' => $this->convertToMinorUnits($amount, $currency),
                'currency' => strtolower($currency),
                'recurring' => [
                    'interval' => $interval,
                ],
            ]);

            return [
                'success' => true,
                'product_id' => $product->id,
                'price_id' => $price->id,
                'amount' => $amount,
                'currency' => $currency,
                'interval' => $interval,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe create subscription product error: ' . $e->getMessage());
            throw new \Exception('Failed to create subscription product: ' . $e->getMessage());
        }
    }

    /**
     * Handle webhook events
     */
    public function handleWebhook(string $payload, string $signature): array
    {
        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                $this->webhookSecret
            );

            Log::info('Stripe webhook received', [
                'type' => $event->type,
                'id' => $event->id,
            ]);

            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $paymentIntent = $event->data->object;
                    return $this->handlePaymentSucceeded($paymentIntent);

                case 'payment_intent.payment_failed':
                    $paymentIntent = $event->data->object;
                    return $this->handlePaymentFailed($paymentIntent);

                case 'invoice.payment_succeeded':
                    $invoice = $event->data->object;
                    return $this->handleInvoicePaymentSucceeded($invoice);

                case 'invoice.payment_failed':
                    $invoice = $event->data->object;
                    return $this->handleInvoicePaymentFailed($invoice);

                default:
                    Log::info('Unhandled Stripe webhook event', ['type' => $event->type]);
                    return ['success' => true, 'message' => 'Event not handled'];
            }

        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            throw new \Exception('Failed to handle webhook: ' . $e->getMessage());
        }
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return 'Stripe';
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'USD'
        ];
    }

    /**
     * Check if gateway is available in the given country
     */
    public function isAvailableInCountry(string $countryCode): bool
    {
        // Stripe is available in most countries
        $supportedCountries = [
            'US', 'CA', 'GB', 'AU', 'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE',
            'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL',
            'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'CH', 'NO', 'IS', 'LI', 'JP',
            'SG', 'HK', 'NZ', 'MX', 'BR', 'IN', 'MY', 'TH', 'PH', 'ID', 'AE', 'SA'
        ];
        
        return in_array(strtoupper($countryCode), $supportedCountries);
    }

    /**
     * Format amount according to gateway requirements
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Convert amount to minor units (cents)
     */
    private function convertToMinorUnits(float $amount, string $currency): int
    {
        // Most currencies use 2 decimal places, but some exceptions exist
        $zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP', 'ISK', 'UGX'];
        
        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (int) $amount;
        }
        
        return (int) ($amount * 100);
    }

    /**
     * Handle successful payment webhook
     */
    private function handlePaymentSucceeded($paymentIntent): array
    {
        // This would typically update your database
        Log::info('Payment succeeded', [
            'payment_id' => $paymentIntent->id,
            'amount' => $paymentIntent->amount / 100,
            'currency' => $paymentIntent->currency,
        ]);

        return [
            'success' => true,
            'message' => 'Payment succeeded',
            'payment_id' => $paymentIntent->id,
        ];
    }

    /**
     * Handle failed payment webhook
     */
    private function handlePaymentFailed($paymentIntent): array
    {
        Log::warning('Payment failed', [
            'payment_id' => $paymentIntent->id,
            'amount' => $paymentIntent->amount / 100,
            'currency' => $paymentIntent->currency,
            'last_payment_error' => $paymentIntent->last_payment_error,
        ]);

        return [
            'success' => true,
            'message' => 'Payment failed handled',
            'payment_id' => $paymentIntent->id,
        ];
    }

    /**
     * Handle successful invoice payment webhook
     */
    private function handleInvoicePaymentSucceeded($invoice): array
    {
        Log::info('Invoice payment succeeded', [
            'invoice_id' => $invoice->id,
            'subscription_id' => $invoice->subscription,
            'amount' => $invoice->amount_paid / 100,
            'currency' => $invoice->currency,
        ]);

        return [
            'success' => true,
            'message' => 'Invoice payment succeeded',
            'invoice_id' => $invoice->id,
        ];
    }

    /**
     * Handle failed invoice payment webhook
     */
    private function handleInvoicePaymentFailed($invoice): array
    {
        Log::warning('Invoice payment failed', [
            'invoice_id' => $invoice->id,
            'subscription_id' => $invoice->subscription,
            'amount' => $invoice->amount_due / 100,
            'currency' => $invoice->currency,
        ]);

        return [
            'success' => true,
            'message' => 'Invoice payment failed handled',
            'invoice_id' => $invoice->id,
        ];
    }
}
