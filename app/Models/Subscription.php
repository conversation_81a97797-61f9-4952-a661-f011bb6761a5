<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'package_id',
        'user_id',
        'status',
        'billing_cycle',
        'amount',
        'currency',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'cancellation_reason',
        'last_payment_at',
        'next_payment_due',
        'payment_failures',
        'last_payment_failure_at',
        'current_neighbors_count',
        'storage_used_bytes',
        'notifications_sent_this_month',
        'emails_sent_this_month',
        'usage_stats',
        'auto_renew',
        'payment_method',
        'payment_details',
        'metadata',
        'notes',
        'grace_period_started_at',
        'grace_period_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'last_payment_at' => 'datetime',
        'next_payment_due' => 'datetime',
        'last_payment_failure_at' => 'datetime',
        'payment_failures' => 'integer',
        'current_neighbors_count' => 'integer',
        'storage_used_bytes' => 'integer',
        'notifications_sent_this_month' => 'integer',
        'emails_sent_this_month' => 'integer',
        'usage_stats' => 'array',
        'auto_renew' => 'boolean',
        'payment_details' => 'array',
        'metadata' => 'array',
        'grace_period_started_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';
    const STATUS_TRIAL = 'trial';
    const STATUS_GRACE_PERIOD = 'grace_period';

    /**
     * Billing cycle constants
     */
    const CYCLE_MONTHLY = 'monthly';
    const CYCLE_ANNUAL = 'annual';

    /**
     * Get the building that owns the subscription.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the package for this subscription.
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    /**
     * Get the user who created the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get subscriptions that are due for renewal.
     */
    public function scopeDueForRenewal($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('auto_renew', true)
                    ->where('next_payment_due', '<=', now());
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<', now())
                    ->whereIn('status', [self::STATUS_ACTIVE, self::STATUS_TRIAL]);
    }

    /**
     * Scope to get subscriptions in grace period.
     */
    public function scopeInGracePeriod($query)
    {
        return $query->where('status', self::STATUS_GRACE_PERIOD);
    }

    /**
     * Scope to get subscriptions approaching expiration.
     */
    public function scopeApproachingExpiration($query, int $days = 7)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('ends_at', '>', now())
                    ->where('ends_at', '<=', now()->addDays($days));
    }

    /**
     * Scope to get subscriptions that need grace period processing.
     */
    public function scopeNeedingGracePeriod($query)
    {
        return $query->where('ends_at', '<', now())
                    ->whereIn('status', [self::STATUS_ACTIVE, self::STATUS_TRIAL])
                    ->whereDoesntHave('building', function ($q) {
                        $q->where('current_package_id', function ($subQuery) {
                            $subQuery->select('id')
                                    ->from('packages')
                                    ->where('slug', 'free')
                                    ->limit(1);
                        });
                    });
    }

    /**
     * Scope to get trial subscriptions.
     */
    public function scopeTrial($query)
    {
        return $query->where('status', self::STATUS_TRIAL);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE && $this->ends_at > now();
    }

    /**
     * Check if subscription is in trial period.
     */
    public function isInTrial(): bool
    {
        return $this->status === self::STATUS_TRIAL &&
               $this->trial_ends_at &&
               $this->trial_ends_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at < now();
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemaining(): int
    {
        if ($this->isExpired()) {
            return 0;
        }
        return $this->ends_at->diffInDays(now());
    }

    /**
     * Get days remaining in trial.
     */
    public function getTrialDaysRemaining(): int
    {
        if (!$this->isInTrial()) {
            return 0;
        }
        return $this->trial_ends_at->diffInDays(now());
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(string $reason = null): bool
    {
        return $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false,
        ]);
    }

    /**
     * Renew the subscription.
     */
    public function renew(int $months = null): bool
    {
        $months = $months ?? ($this->billing_cycle === self::CYCLE_ANNUAL ? 12 : 1);

        $newEndDate = $this->isExpired()
            ? now()->addMonths($months)
            : $this->ends_at->addMonths($months);

        return $this->update([
            'status' => self::STATUS_ACTIVE,
            'ends_at' => $newEndDate,
            'next_payment_due' => $newEndDate,
            'last_payment_at' => now(),
            'payment_failures' => 0,
        ]);
    }

    /**
     * Upgrade to a new package.
     */
    public function upgradeTo(Package $newPackage, string $billingCycle = null): bool
    {
        $billingCycle = $billingCycle ?? $this->billing_cycle;
        $newAmount = $newPackage->getEffectivePrice($billingCycle);

        return $this->update([
            'package_id' => $newPackage->id,
            'amount' => $newAmount,
            'billing_cycle' => $billingCycle,
        ]);
    }

    /**
     * Record a payment failure.
     */
    public function recordPaymentFailure(): bool
    {
        return $this->update([
            'payment_failures' => $this->payment_failures + 1,
            'last_payment_failure_at' => now(),
        ]);
    }

    /**
     * Update usage statistics.
     */
    public function updateUsage(array $usage): bool
    {
        $updates = [];

        if (isset($usage['neighbors_count'])) {
            $updates['current_neighbors_count'] = $usage['neighbors_count'];
        }

        if (isset($usage['storage_used'])) {
            $updates['storage_used_bytes'] = $usage['storage_used'];
        }

        if (isset($usage['notifications_sent'])) {
            $updates['notifications_sent_this_month'] = $usage['notifications_sent'];
        }

        if (isset($usage['emails_sent'])) {
            $updates['emails_sent_this_month'] = $usage['emails_sent'];
        }

        if (isset($usage['stats'])) {
            $updates['usage_stats'] = array_merge($this->usage_stats ?? [], $usage['stats']);
        }

        return $this->update($updates);
    }

    /**
     * Check if subscription is approaching expiration.
     */
    public function isApproachingExpiration(int $days = 7): bool
    {
        if ($this->isExpired()) {
            return false;
        }
        return $this->ends_at->diffInDays(now()) <= $days;
    }

    /**
     * Check if subscription is in grace period.
     */
    public function isInGracePeriod(): bool
    {
        if ($this->status !== self::STATUS_GRACE_PERIOD) {
            return false;
        }

        $gracePeriodDays = config('subscription.grace_period_days', 7);
        $graceEndDate = $this->ends_at->addDays($gracePeriodDays);

        return now() <= $graceEndDate;
    }

    /**
     * Get days remaining in grace period.
     */
    public function getGracePeriodDaysRemaining(): int
    {
        if (!$this->isInGracePeriod()) {
            return 0;
        }

        $gracePeriodDays = config('subscription.grace_period_days', 7);
        $graceEndDate = $this->ends_at->addDays($gracePeriodDays);

        return max(0, $graceEndDate->diffInDays(now()));
    }

    /**
     * Enter grace period status.
     */
    public function enterGracePeriod(): bool
    {
        if (!$this->isExpired() || $this->status === self::STATUS_GRACE_PERIOD) {
            return false;
        }

        return $this->update([
            'status' => self::STATUS_GRACE_PERIOD,
            'grace_period_started_at' => now(),
            'grace_period_notes' => 'Subscription entered grace period due to expiration',
        ]);
    }

    /**
     * Exit grace period and downgrade to free package.
     */
    public function exitGracePeriod(): bool
    {
        if ($this->status !== self::STATUS_GRACE_PERIOD) {
            return false;
        }

        // Find free package
        $freePackage = \App\Models\Package::where('slug', 'free')->first();
        if (!$freePackage) {
            Log::error('Free package not found when exiting grace period', [
                'subscription_id' => $this->id,
                'building_id' => $this->building_id,
            ]);
            return false;
        }

        // Update building to use free package
        $this->building->update(['current_package_id' => $freePackage->id]);

        // Update subscription status
        return $this->update([
            'status' => self::STATUS_EXPIRED,
            'package_id' => $freePackage->id,
            'amount' => 0,
        ]);
    }

    /**
     * Check if subscription should enter grace period.
     */
    public function shouldEnterGracePeriod(): bool
    {
        return $this->isExpired() &&
               in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_TRIAL]) &&
               config('subscription.grace_period_days', 7) > 0;
    }

    /**
     * Get usage percentage for neighbors.
     */
    public function getNeighborsUsagePercentage(): float
    {
        if ($this->package->hasUnlimitedNeighbors()) {
            return 0;
        }

        if ($this->package->max_neighbors == 0) {
            return 100;
        }

        return ($this->current_neighbors_count / $this->package->max_neighbors) * 100;
    }

    /**
     * Get usage percentage for storage.
     */
    public function getStorageUsagePercentage(): float
    {
        if ($this->package->hasUnlimitedStorage()) {
            return 0;
        }

        if ($this->package->storage_limit_gb == 0) {
            return 100;
        }

        $storageUsedGb = $this->storage_used_bytes / (1024 * 1024 * 1024);
        return ($storageUsedGb / $this->package->storage_limit_gb) * 100;
    }

    /**
     * Check if neighbors limit is exceeded.
     */
    public function isNeighborsLimitExceeded(): bool
    {
        if ($this->package->hasUnlimitedNeighbors()) {
            return false;
        }
        return $this->current_neighbors_count > $this->package->max_neighbors;
    }

    /**
     * Check if storage limit is exceeded.
     */
    public function isStorageLimitExceeded(): bool
    {
        if ($this->package->hasUnlimitedStorage()) {
            return false;
        }
        $storageUsedGb = $this->storage_used_bytes / (1024 * 1024 * 1024);
        return $storageUsedGb > $this->package->storage_limit_gb;
    }

    /**
     * Get subscription summary.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'package_name' => $this->package->name,
            'status' => $this->status,
            'billing_cycle' => $this->billing_cycle,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'starts_at' => $this->starts_at->toISOString(),
            'ends_at' => $this->ends_at->toISOString(),
            'days_remaining' => $this->getDaysRemaining(),
            'is_trial' => $this->isInTrial(),
            'trial_days_remaining' => $this->getTrialDaysRemaining(),
            'auto_renew' => $this->auto_renew,
            'usage' => [
                'neighbors' => [
                    'current' => $this->current_neighbors_count,
                    'limit' => $this->package->max_neighbors,
                    'percentage' => $this->getNeighborsUsagePercentage(),
                    'unlimited' => $this->package->hasUnlimitedNeighbors(),
                ],
                'storage' => [
                    'used_bytes' => $this->storage_used_bytes,
                    'used_gb' => round($this->storage_used_bytes / (1024 * 1024 * 1024), 2),
                    'limit_gb' => $this->package->storage_limit_gb,
                    'percentage' => $this->getStorageUsagePercentage(),
                    'unlimited' => $this->package->hasUnlimitedStorage(),
                ],
                'notifications' => $this->notifications_sent_this_month,
                'emails' => $this->emails_sent_this_month,
            ],
        ];
    }
}
