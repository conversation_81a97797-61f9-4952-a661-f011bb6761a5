<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class EmailUsage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'user_id',
        'email_type',
        'recipient_email',
        'subject',
        'status',
        'error_message',
        'sent_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sent_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the building that owns the email usage record.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the user that triggered the email.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get emails sent today for a building.
     */
    public function scopeSentToday($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId)
                    ->where('status', 'sent')
                    ->whereDate('sent_at', Carbon::today());
    }

    /**
     * Scope to get emails sent this month for a building.
     */
    public function scopeSentThisMonth($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId)
                    ->where('status', 'sent')
                    ->whereMonth('sent_at', Carbon::now()->month)
                    ->whereYear('sent_at', Carbon::now()->year);
    }

    /**
     * Scope to get emails by type.
     */
    public function scopeByType($query, string $emailType)
    {
        return $query->where('email_type', $emailType);
    }

    /**
     * Scope to get failed emails.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Mark email as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark email as failed.
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get usage statistics for a building.
     */
    public static function getBuildingStats(int $buildingId, string $period = 'month'): array
    {
        $query = self::where('building_id', $buildingId);

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                break;
            case 'week':
                $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', Carbon::now()->month)
                      ->whereYear('created_at', Carbon::now()->year);
                break;
            case 'year':
                $query->whereYear('created_at', Carbon::now()->year);
                break;
        }

        $total = $query->count();
        $sent = $query->where('status', 'sent')->count();
        $failed = $query->where('status', 'failed')->count();
        $queued = $query->where('status', 'queued')->count();

        return [
            'total' => $total,
            'sent' => $sent,
            'failed' => $failed,
            'queued' => $queued,
            'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get usage by email type for a building.
     */
    public static function getBuildingUsageByType(int $buildingId, string $period = 'month'): array
    {
        $query = self::where('building_id', $buildingId);

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                break;
            case 'month':
                $query->whereMonth('created_at', Carbon::now()->month)
                      ->whereYear('created_at', Carbon::now()->year);
                break;
        }

        return $query->selectRaw('email_type, COUNT(*) as count')
                    ->groupBy('email_type')
                    ->pluck('count', 'email_type')
                    ->toArray();
    }
}
