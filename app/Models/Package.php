<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_en',
        'slug',
        'description',
        'description_en',
        'price',
        'annual_price',
        'max_neighbors',
        'notifications_enabled',
        'email_notifications_enabled',
        'email_limit_per_month',
        'email_limit_per_day',
        'email_quota_warnings_enabled',
        'exports_enabled',
        'exports_per_month',
        'max_records_per_export',
        'export_formats',
        'sms_notifications_enabled',
        'priority_support',
        'file_attachments_enabled',
        'storage_limit_gb',
        'multi_admin_enabled',
        'max_admins',
        'features',
        'limitations',
        'is_active',
        'is_popular',
        'sort_order',
        'billing_cycle',
        'trial_days',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'annual_price' => 'decimal:2',
        'max_neighbors' => 'integer',
        'notifications_enabled' => 'boolean',
        'email_notifications_enabled' => 'boolean',
        'email_limit_per_month' => 'integer',
        'email_limit_per_day' => 'integer',
        'email_quota_warnings_enabled' => 'boolean',
        'exports_enabled' => 'boolean',
        'exports_per_month' => 'integer',
        'max_records_per_export' => 'integer',
        'export_formats' => 'array',
        'sms_notifications_enabled' => 'boolean',
        'priority_support' => 'boolean',
        'file_attachments_enabled' => 'boolean',
        'storage_limit_gb' => 'integer',
        'multi_admin_enabled' => 'boolean',
        'max_admins' => 'integer',
        'features' => 'array',
        'limitations' => 'array',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
        'sort_order' => 'integer',
        'trial_days' => 'integer',
    ];

    /**
     * Package types constants
     */
    const TYPE_FREE = 'free';
    const TYPE_STANDARD = 'standard';
    const TYPE_PREMIUM = 'premium';

    /**
     * Get the subscriptions for this package.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the buildings using this package.
     */
    public function buildings(): HasMany
    {
        return $this->hasMany(Building::class, 'current_package_id');
    }

    /**
     * Scope to get only active packages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get packages ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Check if this is the free package.
     */
    public function isFree(): bool
    {
        return $this->slug === self::TYPE_FREE || $this->price == 0;
    }

    /**
     * Get the effective price based on billing cycle.
     */
    public function getEffectivePrice(string $cycle = 'monthly'): float
    {
        if ($cycle === 'annual' && $this->annual_price) {
            return (float) $this->annual_price;
        }
        return (float) $this->price;
    }

    /**
     * Get monthly equivalent price for annual billing.
     */
    public function getMonthlyEquivalentPrice(): float
    {
        if ($this->annual_price) {
            return (float) $this->annual_price / 12;
        }
        return (float) $this->price;
    }

    /**
     * Get annual savings amount.
     */
    public function getAnnualSavings(): float
    {
        if (!$this->annual_price) {
            return 0;
        }
        $monthlyTotal = (float) $this->price * 12;
        return $monthlyTotal - (float) $this->annual_price;
    }

    /**
     * Get annual savings percentage.
     */
    public function getAnnualSavingsPercentage(): float
    {
        if (!$this->annual_price || $this->price == 0) {
            return 0;
        }
        $monthlyTotal = (float) $this->price * 12;
        return (($monthlyTotal - (float) $this->annual_price) / $monthlyTotal) * 100;
    }

    /**
     * Check if package has unlimited neighbors.
     */
    public function hasUnlimitedNeighbors(): bool
    {
        return $this->max_neighbors === null;
    }

    /**
     * Check if package has unlimited storage.
     */
    public function hasUnlimitedStorage(): bool
    {
        return $this->storage_limit_gb === null;
    }

    /**
     * Check if package supports multiple admins.
     */
    public function hasMultiAdminSupport(): bool
    {
        return $this->multi_admin_enabled === true;
    }

    /**
     * Get maximum number of admins allowed.
     */
    public function getMaxAdmins(): int
    {
        return $this->max_admins ?? 1;
    }



    /**
     * Check if package has unlimited daily emails.
     */
    public function hasUnlimitedDailyEmails(): bool
    {
        return $this->email_limit_per_day === null || $this->email_limit_per_day <= 0;
    }

    /**
     * Check if package has unlimited monthly emails.
     */
    public function hasUnlimitedMonthlyEmails(): bool
    {
        return $this->email_limit_per_month === null || $this->email_limit_per_month <= 0;
    }

    /**
     * Get package features as a formatted array.
     */
    public function getFormattedFeatures(): array
    {
        $features = [];

        // Neighbor limit
        if ($this->hasUnlimitedNeighbors()) {
            $features[] = 'unlimited_neighbors';
        } else {
            $features[] = ['max_neighbors', $this->max_neighbors];
        }

        // Notifications
        if ($this->notifications_enabled) {
            $features[] = 'in_app_notifications';
        }

        if ($this->email_notifications_enabled) {
            $features[] = 'email_notifications';
        }

        if ($this->sms_notifications_enabled) {
            $features[] = 'sms_notifications';
        }

        // File attachments
        if ($this->file_attachments_enabled) {
            $features[] = 'file_attachments';
        }

        // Storage
        if ($this->hasUnlimitedStorage()) {
            $features[] = 'unlimited_storage';
        } else {
            $features[] = ['storage_limit', $this->storage_limit_gb];
        }

        // Additional features
        if ($this->priority_support) {
            $features[] = 'priority_support';
        }

        // Custom features from JSON
        if ($this->features) {
            $features = array_merge($features, $this->features);
        }

        return $features;
    }

    /**
     * Get package limitations as a formatted array.
     */
    public function getFormattedLimitations(): array
    {
        $limitations = [];

        if (!$this->notifications_enabled) {
            $limitations[] = 'no_notifications';
        }

        if (!$this->email_notifications_enabled) {
            $limitations[] = 'no_email_notifications';
        }

        if (!$this->file_attachments_enabled) {
            $limitations[] = 'no_file_attachments';
        }

        if (!$this->priority_support) {
            $limitations[] = 'standard_support';
        }

        // Custom limitations from JSON
        if ($this->limitations) {
            $limitations = array_merge($limitations, $this->limitations);
        }

        return $limitations;
    }

    /**
     * Get the display name with localization support.
     */
    public function getDisplayName(string $locale = 'ar'): string
    {
        if ($locale === 'en' && $this->description_en) {
            return $this->name; // Assuming name is already in English for English locale
        }
        return $this->name;
    }

    /**
     * Get the display description with localization support.
     */
    public function getDisplayDescription(string $locale = 'ar'): string
    {
        if ($locale === 'en' && $this->description_en) {
            return $this->description_en;
        }
        return $this->description;
    }

    /**
     * Get maximum file size in MB from configuration.
     */
    public function getMaxFileSizeMb(): int
    {
        return config('attachments.limits.max_file_size_mb', 10);
    }

    /**
     * Get maximum files per record from configuration.
     */
    public function getMaxFilesPerRecord(): int
    {
        return config('attachments.limits.max_files_per_record', 5);
    }
}
