<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SmsUsageStats extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'date',
        'sms_sent',
        'sms_delivered',
        'sms_failed',
        'total_cost',
        'breakdown_by_type',
        'breakdown_by_provider',
    ];

    protected $casts = [
        'date' => 'date',
        'sms_sent' => 'integer',
        'sms_delivered' => 'integer',
        'sms_failed' => 'integer',
        'total_cost' => 'decimal:4',
        'breakdown_by_type' => 'array',
        'breakdown_by_provider' => 'array',
    ];

    /**
     * Get the building this stats belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Scope to get stats for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get stats within date range.
     */
    public function scopeWithinDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent stats.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('date', '>=', now()->subDays($days));
    }

    /**
     * Scope to get stats for current month.
     */
    public function scopeCurrentMonth($query)
    {
        return $query->whereYear('date', now()->year)
                    ->whereMonth('date', now()->month);
    }

    /**
     * Get success rate for this day.
     */
    public function getSuccessRate(): float
    {
        if ($this->sms_sent == 0) {
            return 0;
        }

        return round(($this->sms_delivered / $this->sms_sent) * 100, 2);
    }

    /**
     * Get failure rate for this day.
     */
    public function getFailureRate(): float
    {
        if ($this->sms_sent == 0) {
            return 0;
        }

        return round(($this->sms_failed / $this->sms_sent) * 100, 2);
    }

    /**
     * Get average cost per SMS for this day.
     */
    public function getAverageCostPerSms(): float
    {
        if ($this->sms_sent == 0) {
            return 0;
        }

        return round($this->total_cost / $this->sms_sent, 4);
    }

    /**
     * Get breakdown by notification type.
     */
    public function getBreakdownByType(): array
    {
        return $this->breakdown_by_type ?? [];
    }

    /**
     * Get breakdown by provider.
     */
    public function getBreakdownByProvider(): array
    {
        return $this->breakdown_by_provider ?? [];
    }

    /**
     * Update stats for a specific date and building.
     */
    public static function updateStatsForDate(int $buildingId, Carbon $date): void
    {
        // Get delivery logs for this date
        $logs = SmsDeliveryLog::forBuilding($buildingId)
            ->whereDate('created_at', $date)
            ->get();

        $sent = $logs->count();
        $delivered = $logs->where('status', SmsDeliveryLog::STATUS_DELIVERED)->count();
        $failed = $logs->whereIn('status', [
            SmsDeliveryLog::STATUS_FAILED,
            SmsDeliveryLog::STATUS_UNDELIVERED
        ])->count();
        $totalCost = $logs->sum('cost');

        // Breakdown by notification type
        $typeBreakdown = $logs->groupBy(function ($log) {
            return $log->notification->type ?? 'unknown';
        })->map(function ($group) {
            return $group->count();
        })->toArray();

        // Breakdown by provider
        $providerBreakdown = $logs->groupBy('provider')
            ->map(function ($group) {
                return $group->count();
            })->toArray();

        // Update or create stats record
        self::updateOrCreate(
            [
                'building_id' => $buildingId,
                'date' => $date->format('Y-m-d'),
            ],
            [
                'sms_sent' => $sent,
                'sms_delivered' => $delivered,
                'sms_failed' => $failed,
                'total_cost' => $totalCost,
                'breakdown_by_type' => $typeBreakdown,
                'breakdown_by_provider' => $providerBreakdown,
            ]
        );
    }

    /**
     * Get aggregated stats for a building within date range.
     */
    public static function getAggregatedStats(int $buildingId, Carbon $startDate, Carbon $endDate): array
    {
        $stats = self::forBuilding($buildingId)
            ->withinDateRange($startDate, $endDate)
            ->selectRaw('
                SUM(sms_sent) as total_sent,
                SUM(sms_delivered) as total_delivered,
                SUM(sms_failed) as total_failed,
                SUM(total_cost) as total_cost,
                AVG(sms_sent) as avg_daily_sent,
                MAX(sms_sent) as max_daily_sent,
                MIN(sms_sent) as min_daily_sent
            ')
            ->first();

        $totalSent = $stats->total_sent ?? 0;
        $totalDelivered = $stats->total_delivered ?? 0;
        $totalFailed = $stats->total_failed ?? 0;

        return [
            'total_sent' => $totalSent,
            'total_delivered' => $totalDelivered,
            'total_failed' => $totalFailed,
            'total_cost' => $stats->total_cost ?? 0,
            'success_rate' => $totalSent > 0 ? round(($totalDelivered / $totalSent) * 100, 2) : 0,
            'failure_rate' => $totalSent > 0 ? round(($totalFailed / $totalSent) * 100, 2) : 0,
            'avg_daily_sent' => round($stats->avg_daily_sent ?? 0, 2),
            'max_daily_sent' => $stats->max_daily_sent ?? 0,
            'min_daily_sent' => $stats->min_daily_sent ?? 0,
            'avg_cost_per_sms' => $totalSent > 0 ? round(($stats->total_cost ?? 0) / $totalSent, 4) : 0,
        ];
    }

    /**
     * Get daily trends for a building.
     */
    public static function getDailyTrends(int $buildingId, int $days = 30): array
    {
        $trends = self::forBuilding($buildingId)
            ->recent($days)
            ->orderBy('date')
            ->get()
            ->keyBy(function ($item) {
                return $item->date->format('Y-m-d');
            });

        // Fill in missing dates with zeros
        $result = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $stat = $trends->get($date);
            
            $result[] = [
                'date' => $date,
                'sms_sent' => $stat?->sms_sent ?? 0,
                'sms_delivered' => $stat?->sms_delivered ?? 0,
                'sms_failed' => $stat?->sms_failed ?? 0,
                'total_cost' => $stat?->total_cost ?? 0,
                'success_rate' => $stat?->getSuccessRate() ?? 0,
            ];
        }

        return $result;
    }

    /**
     * Get notification type breakdown for a building.
     */
    public static function getTypeBreakdown(int $buildingId, int $days = 30): array
    {
        $stats = self::forBuilding($buildingId)
            ->recent($days)
            ->get();

        $breakdown = [];
        foreach ($stats as $stat) {
            $types = $stat->getBreakdownByType();
            foreach ($types as $type => $count) {
                $breakdown[$type] = ($breakdown[$type] ?? 0) + $count;
            }
        }

        return $breakdown;
    }

    /**
     * Get provider breakdown for a building.
     */
    public static function getProviderBreakdown(int $buildingId, int $days = 30): array
    {
        $stats = self::forBuilding($buildingId)
            ->recent($days)
            ->get();

        $breakdown = [];
        foreach ($stats as $stat) {
            $providers = $stat->getBreakdownByProvider();
            foreach ($providers as $provider => $count) {
                $breakdown[$provider] = ($breakdown[$provider] ?? 0) + $count;
            }
        }

        return $breakdown;
    }

    /**
     * Get monthly summary for a building.
     */
    public static function getMonthlySummary(int $buildingId, int $year = null, int $month = null): array
    {
        $year = $year ?? now()->year;
        $month = $month ?? now()->month;

        $stats = self::forBuilding($buildingId)
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->selectRaw('
                SUM(sms_sent) as total_sent,
                SUM(sms_delivered) as total_delivered,
                SUM(sms_failed) as total_failed,
                SUM(total_cost) as total_cost,
                COUNT(*) as active_days
            ')
            ->first();

        $totalSent = $stats->total_sent ?? 0;
        $totalDelivered = $stats->total_delivered ?? 0;
        $totalFailed = $stats->total_failed ?? 0;

        return [
            'year' => $year,
            'month' => $month,
            'total_sent' => $totalSent,
            'total_delivered' => $totalDelivered,
            'total_failed' => $totalFailed,
            'total_cost' => $stats->total_cost ?? 0,
            'active_days' => $stats->active_days ?? 0,
            'success_rate' => $totalSent > 0 ? round(($totalDelivered / $totalSent) * 100, 2) : 0,
            'failure_rate' => $totalSent > 0 ? round(($totalFailed / $totalSent) * 100, 2) : 0,
            'avg_cost_per_sms' => $totalSent > 0 ? round(($stats->total_cost ?? 0) / $totalSent, 4) : 0,
        ];
    }

    /**
     * Clean up old stats.
     */
    public static function cleanupOldStats(int $daysToKeep = 365): int
    {
        return self::where('date', '<', now()->subDays($daysToKeep))->delete();
    }
}
