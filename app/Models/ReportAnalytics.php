<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReportAnalytics extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'building_id',
        'report_id',
        'user_id',
        'action',
        'metadata',
        'created_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Get the building this analytics record belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the report this analytics record is for.
     */
    public function report()
    {
        return $this->belongsTo(CustomReport::class, 'report_id');
    }

    /**
     * Get the user who performed the action.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Log an analytics event.
     */
    public static function logEvent(
        int $buildingId,
        int $reportId,
        ?int $userId,
        string $action,
        ?array $metadata = null
    ): self {
        return self::create([
            'building_id' => $buildingId,
            'report_id' => $reportId,
            'user_id' => $userId,
            'action' => $action,
            'metadata' => $metadata,
            'created_at' => now(),
        ]);
    }

    /**
     * Scope to get analytics for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get analytics for a specific report.
     */
    public function scopeForReport($query, int $reportId)
    {
        return $query->where('report_id', $reportId);
    }

    /**
     * Scope to get analytics for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get analytics for a specific action.
     */
    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to get recent analytics.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get analytics summary for a building.
     */
    public static function getBuildingSummary(int $buildingId, int $days = 30): array
    {
        $analytics = self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->get()
            ->keyBy('action');

        $totalEvents = $analytics->sum('count');
        $uniqueUsers = self::forBuilding($buildingId)
            ->recent($days)
            ->whereNotNull('user_id')
            ->distinct('user_id')
            ->count('user_id');

        $uniqueReports = self::forBuilding($buildingId)
            ->recent($days)
            ->distinct('report_id')
            ->count('report_id');

        return [
            'total_events' => $totalEvents,
            'unique_users' => $uniqueUsers,
            'unique_reports' => $uniqueReports,
            'views' => $analytics['viewed']->count ?? 0,
            'generations' => $analytics['generated']->count ?? 0,
            'downloads' => $analytics['downloaded']->count ?? 0,
            'shares' => $analytics['shared']->count ?? 0,
            'period_days' => $days,
        ];
    }

    /**
     * Get top reports by activity.
     */
    public static function getTopReports(int $buildingId, int $days = 30, int $limit = 10): array
    {
        return self::forBuilding($buildingId)
            ->recent($days)
            ->with(['report:id,name'])
            ->selectRaw('report_id, COUNT(*) as activity_count')
            ->groupBy('report_id')
            ->orderBy('activity_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'report_id' => $item->report_id,
                    'report_name' => $item->report->name ?? 'Unknown Report',
                    'activity_count' => $item->activity_count,
                ];
            })
            ->toArray();
    }

    /**
     * Get most active users.
     */
    public static function getMostActiveUsers(int $buildingId, int $days = 30, int $limit = 10): array
    {
        return self::forBuilding($buildingId)
            ->recent($days)
            ->whereNotNull('user_id')
            ->with(['user:id,name,email'])
            ->selectRaw('user_id, COUNT(*) as activity_count')
            ->groupBy('user_id')
            ->orderBy('activity_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'user_id' => $item->user_id,
                    'user_name' => $item->user->name ?? 'Unknown User',
                    'user_email' => $item->user->email ?? '',
                    'activity_count' => $item->activity_count,
                ];
            })
            ->toArray();
    }

    /**
     * Get activity timeline.
     */
    public static function getActivityTimeline(int $buildingId, int $days = 30): array
    {
        return self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('DATE(created_at) as date, action, COUNT(*) as count')
            ->groupBy('date', 'action')
            ->orderBy('date', 'desc')
            ->get()
            ->groupBy('date')
            ->map(function ($dayActivities, $date) {
                $activities = $dayActivities->keyBy('action');
                return [
                    'date' => $date,
                    'views' => $activities['viewed']->count ?? 0,
                    'generations' => $activities['generated']->count ?? 0,
                    'downloads' => $activities['downloaded']->count ?? 0,
                    'shares' => $activities['shared']->count ?? 0,
                    'total' => $dayActivities->sum('count'),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Clean up old analytics data.
     */
    public static function cleanupOld(int $retentionDays = 365): int
    {
        return self::where('created_at', '<', now()->subDays($retentionDays))->delete();
    }
}
