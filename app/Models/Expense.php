<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Expense extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'expense_type_id',
        'user_id',
        'building_id',
        'amount',
        'currency',
        'original_amount',
        'original_currency',
        'exchange_rate',
        'exchange_rate_date',
        'due_date',
        'month',
        'year',
        'notes',
        'is_automatic',
        'is_archived',
        'archived_at',
        'archived_by',
        'archive_reason',
    ];

    protected $casts = [
        'due_date' => 'date',
        'amount' => 'decimal:2',
        'original_amount' => 'decimal:4',
        'exchange_rate' => 'decimal:6',
        'exchange_rate_date' => 'datetime',
        'is_automatic' => 'boolean',
        'is_archived' => 'boolean',
        'archived_at' => 'datetime',
    ];

    public function template()
    {
        return $this->belongsTo(ExpenseTemplate::class);
    }

    public function expenseType()
    {
        return $this->belongsTo(ExpenseType::class);
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function getTotalPaidAttribute()
    {
        return $this->payments()->where('status', 'completed')->sum('amount');
    }

    public function getRemainingAmountAttribute()
    {
        return $this->amount - $this->total_paid;
    }

    public function fileAttachments()
    {
        return $this->morphMany(FileAttachment::class, 'attachable');
    }

    /**
     * Get the user who archived this expense.
     */
    public function archivedBy()
    {
        return $this->belongsTo(User::class, 'archived_by');
    }

    /**
     * Archive this expense.
     */
    public function archive(User $user, string $reason = null): bool
    {
        return $this->update([
            'is_archived' => true,
            'archived_at' => now(),
            'archived_by' => $user->id,
            'archive_reason' => $reason,
        ]);
    }

    /**
     * Unarchive this expense.
     */
    public function unarchive(): bool
    {
        return $this->update([
            'is_archived' => false,
            'archived_at' => null,
            'archived_by' => null,
            'archive_reason' => null,
        ]);
    }

    /**
     * Scope to get only non-archived expenses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_archived', false);
    }

    /**
     * Scope to get only archived expenses.
     */
    public function scopeArchived($query)
    {
        return $query->where('is_archived', true);
    }

    /**
     * Scope to get expenses older than specified months.
     */
    public function scopeOlderThan($query, int $months)
    {
        return $query->where('created_at', '<', now()->subMonths($months));
    }

    /**
     * Get currency conversions for this expense.
     */
    public function currencyConversions()
    {
        return $this->morphMany(CurrencyConversion::class, 'convertible');
    }

    /**
     * Check if expense was converted from another currency.
     */
    public function wasConverted(): bool
    {
        return !is_null($this->original_currency) && $this->original_currency !== $this->currency;
    }

    /**
     * Get the original amount in original currency.
     */
    public function getOriginalAmount(): float
    {
        return $this->original_amount ?? $this->amount;
    }

    /**
     * Get the original currency.
     */
    public function getOriginalCurrency(): string
    {
        return $this->original_currency ?? $this->currency ?? 'USD';
    }

    /**
     * Get formatted amount with currency symbol.
     */
    public function getFormattedAmount(User $user = null): string
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->formatAmount($this->amount, $this->currency ?? 'USD', $this->building, $user);
    }

    /**
     * Get formatted original amount with currency symbol.
     */
    public function getFormattedOriginalAmount(): string
    {
        if (!$this->wasConverted()) {
            return $this->getFormattedAmount();
        }

        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->formatAmount(
            $this->getOriginalAmount(),
            $this->getOriginalCurrency(),
            $this->building
        );
    }

    /**
     * Convert expense to another currency.
     */
    public function convertToCurrency(string $toCurrency, User $user = null): array
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->convertAndLog($this, $this->currency ?? 'USD', $toCurrency, $this->building, $user);
    }
}
