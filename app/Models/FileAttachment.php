<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Storage;

class FileAttachment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'building_id',
        'folder_id',
        'attachable_type',
        'attachable_id',
        'original_name',
        'filename',
        'file_path',
        'mime_type',
        'file_size',
        'file_extension',
        'description',
        'metadata',
        'is_public',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'file_size' => 'integer',
    ];

    /**
     * File type constants
     */
    const TYPE_IMAGE = 'image';
    const TYPE_DOCUMENT = 'document';
    const TYPE_SPREADSHEET = 'spreadsheet';
    const TYPE_PDF = 'pdf';
    const TYPE_OTHER = 'other';

    /**
     * Get maximum file size in bytes from configuration.
     */
    public static function getMaxFileSize(): int
    {
        return config('attachments.limits.max_file_size_mb', 10) * 1024 * 1024;
    }



    /**
     * Get the user that owns the file attachment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the building that the file attachment belongs to.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the folder that contains this file.
     */
    public function folder(): BelongsTo
    {
        return $this->belongsTo(FileFolder::class);
    }

    /**
     * Get the parent attachable model (expense, income, etc.).
     */
    public function attachable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the file type based on MIME type.
     */
    public function getFileTypeAttribute(): string
    {
        if (str_starts_with($this->mime_type, 'image/')) {
            return self::TYPE_IMAGE;
        }

        if ($this->mime_type === 'application/pdf') {
            return self::TYPE_PDF;
        }

        if (in_array($this->mime_type, [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/rtf'
        ])) {
            return self::TYPE_DOCUMENT;
        }

        if (in_array($this->mime_type, [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/csv'
        ])) {
            return self::TYPE_SPREADSHEET;
        }

        return self::TYPE_OTHER;
    }

    /**
     * Get the human-readable file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the full URL to the file.
     */
    public function getUrlAttribute(): string
    {
        if ($this->is_public) {
            return Storage::url($this->file_path);
        }

        // For private files, return a simple URL path to avoid route() issues
        return "/api/files/{$this->id}/download";
    }

    /**
     * Check if the file is an image.
     */
    public function isImage(): bool
    {
        return $this->file_type === self::TYPE_IMAGE;
    }

    /**
     * Check if the file is a PDF.
     */
    public function isPdf(): bool
    {
        return $this->file_type === self::TYPE_PDF;
    }

    /**
     * Check if the file is a document.
     */
    public function isDocument(): bool
    {
        return $this->file_type === self::TYPE_DOCUMENT;
    }

    /**
     * Check if the file exists in storage.
     */
    public function fileExists(): bool
    {
        return Storage::exists($this->file_path);
    }

    /**
     * Delete the file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->fileExists()) {
            return Storage::delete($this->file_path);
        }

        return true;
    }

    /**
     * Get the file content.
     */
    public function getFileContent(): ?string
    {
        if ($this->fileExists()) {
            return Storage::get($this->file_path);
        }

        return null;
    }

    /**
     * Scope to filter by file type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('attachable_type', $type);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to filter public files.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to filter private files.
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    /**
     * Get all allowed file types.
     */
    public static function getAllowedExtensions(): array
    {
        return config('attachments.allowed_extensions', [
            'jpg', 'jpeg', 'png', 'gif', 'webp', // Images
            'pdf', // PDF
            'doc', 'docx', 'txt', 'rtf', // Documents
            'xls', 'xlsx', 'csv', // Spreadsheets
            'zip', 'rar', // Archives
        ]);
    }

    /**
     * Check if a file extension is allowed.
     */
    public static function isExtensionAllowed(string $extension): bool
    {
        return in_array(strtolower($extension), self::getAllowedExtensions());
    }

    /**
     * Get MIME type from file extension.
     */
    public static function getMimeTypeFromExtension(string $extension): string
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain',
            'rtf' => 'application/rtf',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv' => 'text/csv',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
        ];

        return $mimeTypes[strtolower($extension)] ?? 'application/octet-stream';
    }
}
