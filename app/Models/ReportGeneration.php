<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ReportGeneration extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'report_id',
        'generated_by',
        'schedule_id',
        'format',
        'parameters',
        'status',
        'file_name',
        'file_path',
        'file_size',
        'record_count',
        'error_message',
        'started_at',
        'completed_at',
        'expires_at',
    ];

    protected $casts = [
        'parameters' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the building this generation belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the report this generation is for.
     */
    public function report()
    {
        return $this->belongsTo(CustomReport::class, 'report_id');
    }

    /**
     * Get the user who generated this report.
     */
    public function generatedBy()
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the schedule that triggered this generation (if any).
     */
    public function schedule()
    {
        return $this->belongsTo(ReportSchedule::class, 'schedule_id');
    }

    /**
     * Scope to get generations for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get completed generations.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get failed generations.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get expired generations.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Scope to get non-expired generations.
     */
    public function scopeNotExpired($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Check if generation is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if generation failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if generation is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if file exists.
     */
    public function fileExists(): bool
    {
        return $this->file_path && Storage::exists($this->file_path);
    }

    /**
     * Get file URL for download.
     */
    public function getFileUrl(): ?string
    {
        if (!$this->fileExists()) {
            return null;
        }

        return Storage::url($this->file_path);
    }

    /**
     * Get file contents.
     */
    public function getFileContents(): ?string
    {
        if (!$this->fileExists()) {
            return null;
        }

        return Storage::get($this->file_path);
    }

    /**
     * Delete the generated file.
     */
    public function deleteFile(): bool
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }

        return true;
    }

    /**
     * Mark generation as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark generation as completed.
     */
    public function markAsCompleted(string $filePath, int $fileSize, int $recordCount): void
    {
        $this->update([
            'status' => 'completed',
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'record_count' => $recordCount,
            'completed_at' => now(),
            'expires_at' => now()->addDays(config('reports.file_retention_days', 30)),
        ]);
    }

    /**
     * Mark generation as failed.
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Get generation duration in seconds.
     */
    public function getDurationInSeconds(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * Get human-readable file size.
     */
    public function getHumanReadableFileSize(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'pending' => 'bg-gray-100 text-gray-800',
            'processing' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get parameters used for generation.
     */
    public function getParameters(): array
    {
        return $this->parameters ?? [];
    }

    /**
     * Check if user can download this generation.
     */
    public function canBeDownloadedBy(User $user): bool
    {
        // Must be completed and not expired
        if (!$this->isCompleted() || $this->isExpired()) {
            return false;
        }

        // Super admin can download all
        if ($user->role === 'super_admin') {
            return true;
        }

        // Must be in the same building
        if ($user->building_id !== $this->building_id) {
            return false;
        }

        // Must be admin
        if ($user->role !== 'admin') {
            return false;
        }

        // Check if user can access the report
        return $this->report && $this->report->canBeAccessedBy($user);
    }

    /**
     * Clean up expired generations.
     */
    public static function cleanupExpired(): int
    {
        $expiredGenerations = self::expired()->get();
        $cleanedCount = 0;

        foreach ($expiredGenerations as $generation) {
            $generation->deleteFile();
            $generation->delete();
            $cleanedCount++;
        }

        return $cleanedCount;
    }
}
