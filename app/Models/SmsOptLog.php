<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsOptLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'building_id',
        'action',
        'phone_number',
        'method',
        'reason',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Action constants.
     */
    const ACTION_OPT_IN = 'opt_in';
    const ACTION_OPT_OUT = 'opt_out';

    /**
     * Get the user this log belongs to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the building this log belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Scope to get opt-in logs.
     */
    public function scopeOptIn($query)
    {
        return $query->where('action', self::ACTION_OPT_IN);
    }

    /**
     * Scope to get opt-out logs.
     */
    public function scopeOptOut($query)
    {
        return $query->where('action', self::ACTION_OPT_OUT);
    }

    /**
     * Scope to get logs for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if this is an opt-in action.
     */
    public function isOptIn(): bool
    {
        return $this->action === self::ACTION_OPT_IN;
    }

    /**
     * Check if this is an opt-out action.
     */
    public function isOptOut(): bool
    {
        return $this->action === self::ACTION_OPT_OUT;
    }

    /**
     * Get action display name.
     */
    public function getActionDisplayName(): string
    {
        return $this->isOptIn() ? 'Opted In' : 'Opted Out';
    }

    /**
     * Get method display name.
     */
    public function getMethodDisplayName(): string
    {
        $methods = [
            'web' => 'Website',
            'sms' => 'SMS Reply',
            'admin' => 'Admin Action',
            'api' => 'API',
        ];

        return $methods[$this->method] ?? $this->method;
    }

    /**
     * Get opt-in/opt-out statistics for a building.
     */
    public static function getOptStats(int $buildingId, int $days = 30): array
    {
        $stats = self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('
                COUNT(*) as total_actions,
                SUM(CASE WHEN action = "opt_in" THEN 1 ELSE 0 END) as opt_ins,
                SUM(CASE WHEN action = "opt_out" THEN 1 ELSE 0 END) as opt_outs
            ')
            ->first();

        $optIns = $stats->opt_ins ?? 0;
        $optOuts = $stats->opt_outs ?? 0;
        $total = $stats->total_actions ?? 0;

        return [
            'total_actions' => $total,
            'opt_ins' => $optIns,
            'opt_outs' => $optOuts,
            'net_change' => $optIns - $optOuts,
            'opt_in_rate' => $total > 0 ? round(($optIns / $total) * 100, 2) : 0,
            'opt_out_rate' => $total > 0 ? round(($optOuts / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get opt-out reasons summary.
     */
    public static function getOptOutReasons(int $buildingId, int $days = 30): array
    {
        return self::forBuilding($buildingId)
            ->optOut()
            ->recent($days)
            ->whereNotNull('reason')
            ->selectRaw('reason, COUNT(*) as count')
            ->groupBy('reason')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'reason')
            ->toArray();
    }

    /**
     * Get daily opt-in/opt-out trends.
     */
    public static function getDailyTrends(int $buildingId, int $days = 30): array
    {
        $trends = self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('
                DATE(created_at) as date,
                SUM(CASE WHEN action = "opt_in" THEN 1 ELSE 0 END) as opt_ins,
                SUM(CASE WHEN action = "opt_out" THEN 1 ELSE 0 END) as opt_outs
            ')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();

        // Fill in missing dates with zeros
        $result = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $result[$date] = [
                'date' => $date,
                'opt_ins' => $trends[$date]['opt_ins'] ?? 0,
                'opt_outs' => $trends[$date]['opt_outs'] ?? 0,
                'net_change' => ($trends[$date]['opt_ins'] ?? 0) - ($trends[$date]['opt_outs'] ?? 0),
            ];
        }

        return array_values($result);
    }

    /**
     * Get method breakdown for opt actions.
     */
    public static function getMethodBreakdown(int $buildingId, int $days = 30): array
    {
        return self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('
                method,
                action,
                COUNT(*) as count
            ')
            ->groupBy(['method', 'action'])
            ->get()
            ->groupBy('method')
            ->map(function ($group) {
                return [
                    'opt_ins' => $group->where('action', self::ACTION_OPT_IN)->sum('count'),
                    'opt_outs' => $group->where('action', self::ACTION_OPT_OUT)->sum('count'),
                    'total' => $group->sum('count'),
                ];
            })
            ->toArray();
    }

    /**
     * Clean up old logs.
     */
    public static function cleanupOldLogs(int $daysToKeep = 365): int
    {
        return self::where('created_at', '<', now()->subDays($daysToKeep))->delete();
    }
}
