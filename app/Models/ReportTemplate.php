<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReportTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
        'data_sources',
        'fields',
        'filters',
        'grouping_options',
        'chart_options',
        'default_config',
        'is_system_template',
        'is_active',
    ];

    protected $casts = [
        'data_sources' => 'array',
        'fields' => 'array',
        'filters' => 'array',
        'grouping_options' => 'array',
        'chart_options' => 'array',
        'default_config' => 'array',
        'is_system_template' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get custom reports using this template.
     */
    public function customReports()
    {
        return $this->hasMany(CustomReport::class, 'template_id');
    }

    /**
     * Scope to get only active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get templates by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get system templates.
     */
    public function scopeSystemTemplates($query)
    {
        return $query->where('is_system_template', true);
    }

    /**
     * Scope to get custom templates.
     */
    public function scopeCustomTemplates($query)
    {
        return $query->where('is_system_template', false);
    }

    /**
     * Get available field options for this template.
     */
    public function getAvailableFields(): array
    {
        return $this->fields ?? [];
    }

    /**
     * Get available filter options for this template.
     */
    public function getAvailableFilters(): array
    {
        return $this->filters ?? [];
    }

    /**
     * Get available grouping options for this template.
     */
    public function getAvailableGrouping(): array
    {
        return $this->grouping_options ?? [];
    }

    /**
     * Get available chart options for this template.
     */
    public function getAvailableCharts(): array
    {
        return $this->chart_options ?? [];
    }

    /**
     * Get default configuration for this template.
     */
    public function getDefaultConfig(): array
    {
        return $this->default_config ?? [];
    }

    /**
     * Check if template supports charts.
     */
    public function supportsCharts(): bool
    {
        return !empty($this->chart_options);
    }

    /**
     * Get data source models for this template.
     */
    public function getDataSourceModels(): array
    {
        $modelMap = [
            'expenses' => Expense::class,
            'incomes' => Income::class,
            'payments' => Payment::class,
            'users' => User::class,
            'buildings' => Building::class,
            'notifications' => Notification::class,
        ];

        $models = [];
        foreach ($this->data_sources as $source) {
            if (isset($modelMap[$source])) {
                $models[$source] = $modelMap[$source];
            }
        }

        return $models;
    }

    /**
     * Validate configuration against template.
     */
    public function validateConfiguration(array $config): array
    {
        $errors = [];
        $availableFields = array_column($this->getAvailableFields(), 'key');
        $availableFilters = array_column($this->getAvailableFilters(), 'key');

        // Validate selected fields
        if (isset($config['fields'])) {
            foreach ($config['fields'] as $field) {
                if (!in_array($field, $availableFields)) {
                    $errors[] = "Invalid field: {$field}";
                }
            }
        }

        // Validate filters
        if (isset($config['filters'])) {
            foreach ($config['filters'] as $filterKey => $filterValue) {
                if (!in_array($filterKey, $availableFilters)) {
                    $errors[] = "Invalid filter: {$filterKey}";
                }
            }
        }

        // Validate grouping
        if (isset($config['grouping'])) {
            $availableGrouping = array_column($this->getAvailableGrouping(), 'key');
            foreach ($config['grouping'] as $group) {
                if (!in_array($group, $availableGrouping)) {
                    $errors[] = "Invalid grouping: {$group}";
                }
            }
        }

        return $errors;
    }

    /**
     * Create a new custom report from this template.
     */
    public function createCustomReport(Building $building, User $user, array $config): CustomReport
    {
        return CustomReport::create([
            'building_id' => $building->id,
            'created_by' => $user->id,
            'template_id' => $this->id,
            'name' => $config['name'] ?? "Report from {$this->name}",
            'description' => $config['description'] ?? null,
            'configuration' => $config,
            'chart_config' => $config['chart_config'] ?? null,
            'status' => 'active',
        ]);
    }
}
