<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FileFolder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'parent_id',
        'name',
        'description',
        'color',
        'is_system',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_system' => 'boolean',
    ];

    /**
     * Get the building that owns the folder.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the parent folder.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(FileFolder::class, 'parent_id');
    }

    /**
     * Get the child folders.
     */
    public function children(): Has<PERSON>any
    {
        return $this->hasMany(FileFolder::class, 'parent_id');
    }

    /**
     * Get the user who created the folder.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the files in this folder.
     */
    public function files(): HasMany
    {
        return $this->hasMany(FileAttachment::class, 'folder_id');
    }

    /**
     * Scope to get root folders (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to filter by building.
     */
    public function scopeForBuilding($query, $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get non-system folders.
     */
    public function scopeUserCreated($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Get the full path of the folder.
     */
    public function getFullPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' / ', $path);
    }

    /**
     * Get the total number of files in this folder and subfolders.
     */
    public function getTotalFilesCountAttribute(): int
    {
        $count = $this->files()->count();
        
        foreach ($this->children as $child) {
            $count += $child->total_files_count;
        }

        return $count;
    }

    /**
     * Get the total size of files in this folder and subfolders.
     */
    public function getTotalSizeAttribute(): int
    {
        $size = $this->files()->sum('file_size');
        
        foreach ($this->children as $child) {
            $size += $child->total_size;
        }

        return $size;
    }

    /**
     * Get formatted total size.
     */
    public function getFormattedTotalSizeAttribute(): string
    {
        $bytes = $this->total_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if this folder can be deleted.
     */
    public function canBeDeleted(): bool
    {
        // System folders cannot be deleted
        if ($this->is_system) {
            return false;
        }

        // Folders with files or subfolders cannot be deleted
        return $this->files()->count() === 0 && $this->children()->count() === 0;
    }

    /**
     * Get all descendant folders.
     */
    public function getAllDescendants(): array
    {
        $descendants = [];
        
        foreach ($this->children as $child) {
            $descendants[] = $child;
            $descendants = array_merge($descendants, $child->getAllDescendants());
        }

        return $descendants;
    }

    /**
     * Move folder to a new parent.
     */
    public function moveTo(?FileFolder $newParent): bool
    {
        // Prevent moving to a descendant folder (would create a loop)
        if ($newParent && in_array($newParent->id, array_column($this->getAllDescendants(), 'id'))) {
            return false;
        }

        // Prevent moving to itself
        if ($newParent && $newParent->id === $this->id) {
            return false;
        }

        $this->parent_id = $newParent?->id;
        return $this->save();
    }

    /**
     * Create default folders for a building.
     */
    public static function createDefaultFoldersForBuilding(Building $building, User $creator): array
    {
        $folders = [];

        $defaultFolders = [
            [
                'name' => 'Expenses',
                'description' => 'Expense-related documents',
                'color' => '#ef4444',
                'is_system' => true,
            ],
            [
                'name' => 'Incomes',
                'description' => 'Income-related documents',
                'color' => '#22c55e',
                'is_system' => true,
            ],
            [
                'name' => 'Contracts',
                'description' => 'Building contracts and agreements',
                'color' => '#3b82f6',
                'is_system' => true,
            ],
            [
                'name' => 'Reports',
                'description' => 'Financial and administrative reports',
                'color' => '#8b5cf6',
                'is_system' => true,
            ],
            [
                'name' => 'General',
                'description' => 'General building documents',
                'color' => '#6b7280',
                'is_system' => true,
            ],
        ];

        foreach ($defaultFolders as $folderData) {
            $folders[] = self::create([
                'building_id' => $building->id,
                'created_by' => $creator->id,
                ...$folderData,
            ]);
        }

        return $folders;
    }

    /**
     * Get folder statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_files' => $this->total_files_count,
            'total_size' => $this->total_size,
            'formatted_size' => $this->formatted_total_size,
            'direct_files' => $this->files()->count(),
            'subfolders' => $this->children()->count(),
            'created_at' => $this->created_at,
            'last_updated' => $this->updated_at,
        ];
    }
}
