<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsDeliveryLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'notification_id',
        'user_id',
        'building_id',
        'phone_number',
        'provider',
        'provider_message_id',
        'status',
        'message_content',
        'cost',
        'provider_response',
        'error_code',
        'error_message',
        'sent_at',
        'delivered_at',
    ];

    protected $casts = [
        'cost' => 'decimal:4',
        'provider_response' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];

    /**
     * Status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_FAILED = 'failed';
    const STATUS_UNDELIVERED = 'undelivered';

    /**
     * Get the notification this log belongs to.
     */
    public function notification()
    {
        return $this->belongsTo(Notification::class);
    }

    /**
     * Get the user this log belongs to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the building this log belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Scope to get logs by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get logs by provider.
     */
    public function scopeByProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to get logs for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get logs within date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if SMS was successfully sent.
     */
    public function wasSent(): bool
    {
        return in_array($this->status, [self::STATUS_SENT, self::STATUS_DELIVERED]);
    }

    /**
     * Check if SMS was delivered.
     */
    public function wasDelivered(): bool
    {
        return $this->status === self::STATUS_DELIVERED;
    }

    /**
     * Check if SMS failed.
     */
    public function hasFailed(): bool
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_UNDELIVERED]);
    }

    /**
     * Mark as sent.
     */
    public function markAsSent(string $providerMessageId = null, array $providerResponse = null): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'provider_message_id' => $providerMessageId,
            'provider_response' => $providerResponse,
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => self::STATUS_DELIVERED,
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $errorCode = null, string $errorMessage = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneNumber(): string
    {
        return $this->phone_number;
    }

    /**
     * Get delivery time in seconds (if delivered).
     */
    public function getDeliveryTimeSeconds(): ?int
    {
        if (!$this->sent_at || !$this->delivered_at) {
            return null;
        }

        return $this->delivered_at->diffInSeconds($this->sent_at);
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        $statusNames = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_SENT => 'Sent',
            self::STATUS_DELIVERED => 'Delivered',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_UNDELIVERED => 'Undelivered',
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Get provider display name.
     */
    public function getProviderDisplayName(): string
    {
        $providerNames = [
            'twilio' => 'Twilio',
            'aws_sns' => 'AWS SNS',
        ];

        return $providerNames[$this->provider] ?? $this->provider;
    }

    /**
     * Get message character count.
     */
    public function getMessageCharacterCount(): int
    {
        return mb_strlen($this->message_content);
    }

    /**
     * Check if message is long (requires multiple SMS segments).
     */
    public function isLongMessage(): bool
    {
        return $this->getMessageCharacterCount() > 160;
    }

    /**
     * Get estimated SMS segments count.
     */
    public function getEstimatedSegments(): int
    {
        $length = $this->getMessageCharacterCount();
        
        if ($length <= 160) {
            return 1;
        }
        
        // For messages longer than 160 characters, each segment can contain 153 characters
        return ceil($length / 153);
    }

    /**
     * Get all available statuses.
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_SENT,
            self::STATUS_DELIVERED,
            self::STATUS_FAILED,
            self::STATUS_UNDELIVERED,
        ];
    }

    /**
     * Get delivery statistics for a building.
     */
    public static function getDeliveryStats(int $buildingId, int $days = 30): array
    {
        $stats = self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "sent" OR status = "delivered" THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = "failed" OR status = "undelivered" THEN 1 ELSE 0 END) as failed,
                SUM(cost) as total_cost,
                AVG(cost) as avg_cost
            ')
            ->first();

        $total = $stats->total ?? 0;
        $sent = $stats->sent ?? 0;
        $delivered = $stats->delivered ?? 0;
        $failed = $stats->failed ?? 0;

        return [
            'total' => $total,
            'sent' => $sent,
            'delivered' => $delivered,
            'failed' => $failed,
            'pending' => $total - $sent - $failed,
            'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
            'delivery_rate' => $sent > 0 ? round(($delivered / $sent) * 100, 2) : 0,
            'failure_rate' => $total > 0 ? round(($failed / $total) * 100, 2) : 0,
            'total_cost' => $stats->total_cost ?? 0,
            'avg_cost' => $stats->avg_cost ?? 0,
        ];
    }

    /**
     * Get provider statistics for a building.
     */
    public static function getProviderStats(int $buildingId, int $days = 30): array
    {
        return self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('
                provider,
                COUNT(*) as total,
                SUM(CASE WHEN status = "sent" OR status = "delivered" THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = "failed" OR status = "undelivered" THEN 1 ELSE 0 END) as failed,
                SUM(cost) as total_cost
            ')
            ->groupBy('provider')
            ->get()
            ->keyBy('provider')
            ->toArray();
    }

    /**
     * Clean up old logs.
     */
    public static function cleanupOldLogs(int $daysToKeep = 90): int
    {
        return self::where('created_at', '<', now()->subDays($daysToKeep))->delete();
    }
}
