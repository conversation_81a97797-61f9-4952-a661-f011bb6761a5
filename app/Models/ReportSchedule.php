<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ReportSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'report_id',
        'created_by',
        'name',
        'frequency',
        'frequency_config',
        'format',
        'recipients',
        'is_active',
        'next_run_at',
        'last_run_at',
        'last_run_result',
    ];

    protected $casts = [
        'frequency_config' => 'array',
        'recipients' => 'array',
        'is_active' => 'boolean',
        'next_run_at' => 'datetime',
        'last_run_at' => 'datetime',
        'last_run_result' => 'array',
    ];

    /**
     * Get the building this schedule belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the report this schedule is for.
     */
    public function report()
    {
        return $this->belongsTo(CustomReport::class, 'report_id');
    }

    /**
     * Get the user who created this schedule.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get report generations from this schedule.
     */
    public function generations()
    {
        return $this->hasMany(ReportGeneration::class, 'schedule_id');
    }

    /**
     * Scope to get active schedules.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get schedules for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get schedules that are due to run.
     */
    public function scopeDueToRun($query)
    {
        return $query->where('is_active', true)
            ->where('next_run_at', '<=', now());
    }

    /**
     * Calculate next run time based on frequency and config.
     */
    public function calculateNextRunTime(): Carbon
    {
        $config = $this->frequency_config ?? [];
        $now = now();

        switch ($this->frequency) {
            case 'daily':
                $hour = $config['hour'] ?? 9;
                $minute = $config['minute'] ?? 0;
                $nextRun = $now->copy()->setTime($hour, $minute, 0);
                
                // If time has passed today, schedule for tomorrow
                if ($nextRun <= $now) {
                    $nextRun->addDay();
                }
                return $nextRun;

            case 'weekly':
                $dayOfWeek = $config['day_of_week'] ?? 1; // Monday
                $hour = $config['hour'] ?? 9;
                $minute = $config['minute'] ?? 0;
                
                $nextRun = $now->copy()->next($dayOfWeek)->setTime($hour, $minute, 0);
                return $nextRun;

            case 'monthly':
                $dayOfMonth = $config['day_of_month'] ?? 1;
                $hour = $config['hour'] ?? 9;
                $minute = $config['minute'] ?? 0;
                
                $nextRun = $now->copy()->startOfMonth()->addDays($dayOfMonth - 1)->setTime($hour, $minute, 0);
                
                // If date has passed this month, schedule for next month
                if ($nextRun <= $now) {
                    $nextRun->addMonth();
                }
                return $nextRun;

            case 'quarterly':
                $monthOfQuarter = $config['month_of_quarter'] ?? 1; // First month of quarter
                $dayOfMonth = $config['day_of_month'] ?? 1;
                $hour = $config['hour'] ?? 9;
                $minute = $config['minute'] ?? 0;
                
                $currentQuarter = ceil($now->month / 3);
                $quarterStartMonth = ($currentQuarter - 1) * 3 + 1;
                $targetMonth = $quarterStartMonth + $monthOfQuarter - 1;
                
                $nextRun = $now->copy()->setMonth($targetMonth)->startOfMonth()
                    ->addDays($dayOfMonth - 1)->setTime($hour, $minute, 0);
                
                // If date has passed this quarter, schedule for next quarter
                if ($nextRun <= $now) {
                    $nextRun->addMonths(3);
                }
                return $nextRun;

            default:
                return $now->addDay(); // Default to daily
        }
    }

    /**
     * Update next run time.
     */
    public function updateNextRunTime(): void
    {
        $this->update(['next_run_at' => $this->calculateNextRunTime()]);
    }

    /**
     * Mark schedule as run with result.
     */
    public function markAsRun(bool $success, array $result = []): void
    {
        $this->update([
            'last_run_at' => now(),
            'last_run_result' => array_merge($result, ['success' => $success]),
            'next_run_at' => $this->calculateNextRunTime(),
        ]);
    }

    /**
     * Check if schedule was successful in last run.
     */
    public function wasLastRunSuccessful(): bool
    {
        return ($this->last_run_result['success'] ?? false) === true;
    }

    /**
     * Get human-readable frequency description.
     */
    public function getFrequencyDescription(): string
    {
        $config = $this->frequency_config ?? [];

        switch ($this->frequency) {
            case 'daily':
                $time = sprintf('%02d:%02d', $config['hour'] ?? 9, $config['minute'] ?? 0);
                return "Daily at {$time}";

            case 'weekly':
                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $day = $days[$config['day_of_week'] ?? 1];
                $time = sprintf('%02d:%02d', $config['hour'] ?? 9, $config['minute'] ?? 0);
                return "Weekly on {$day} at {$time}";

            case 'monthly':
                $day = $config['day_of_month'] ?? 1;
                $time = sprintf('%02d:%02d', $config['hour'] ?? 9, $config['minute'] ?? 0);
                $suffix = match($day % 10) {
                    1 => $day === 11 ? 'th' : 'st',
                    2 => $day === 12 ? 'th' : 'nd',
                    3 => $day === 13 ? 'th' : 'rd',
                    default => 'th'
                };
                return "Monthly on the {$day}{$suffix} at {$time}";

            case 'quarterly':
                $months = ['first', 'second', 'third'];
                $month = $months[($config['month_of_quarter'] ?? 1) - 1];
                $day = $config['day_of_month'] ?? 1;
                $time = sprintf('%02d:%02d', $config['hour'] ?? 9, $config['minute'] ?? 0);
                return "Quarterly on the {$day} of the {$month} month at {$time}";

            default:
                return ucfirst($this->frequency);
        }
    }

    /**
     * Get recipients list as string.
     */
    public function getRecipientsString(): string
    {
        return implode(', ', $this->recipients ?? []);
    }

    /**
     * Activate schedule.
     */
    public function activate(): bool
    {
        $this->updateNextRunTime();
        return $this->update(['is_active' => true]);
    }

    /**
     * Deactivate schedule.
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Get recent generations from this schedule.
     */
    public function getRecentGenerations(int $limit = 5)
    {
        return $this->generations()
            ->with(['generatedBy'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
