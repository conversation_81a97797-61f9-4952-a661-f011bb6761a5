<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'building_id',
        'type',
        'title',
        'message',
        'data',
        'read_at',
        'email_sent',
        'email_sent_at',
        'sms_enabled',
        'sms_sent',
        'sms_sent_at',
        'sms_provider',
        'sms_message_id',
        'sms_status',
        'sms_metadata',
        'priority',
        'scheduled_for',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'email_sent' => 'boolean',
        'email_sent_at' => 'datetime',
        'sms_enabled' => 'boolean',
        'sms_sent' => 'boolean',
        'sms_sent_at' => 'datetime',
        'sms_metadata' => 'array',
        'scheduled_for' => 'datetime',
    ];

    /**
     * Notification types constants
     */
    const TYPE_PAYMENT_REMINDER = 'payment_reminder';
    const TYPE_EXPENSE_CREATED = 'expense_created';
    const TYPE_INCOME_RECEIVED = 'income_received';
    const TYPE_GENERAL_ANNOUNCEMENT = 'general_announcement';
    const TYPE_PAYMENT_RECEIVED = 'payment_received';
    const TYPE_OVERDUE_PAYMENT = 'overdue_payment';
    const TYPE_WECLOME_MESSAGE = 'welcome_message';

    /**
     * Priority levels constants
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the building that the notification belongs to.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Scope a query to only include unread notifications.
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope a query to only include read notifications.
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope a query to filter by notification type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by priority.
     */
    public function scopeOfPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to include only scheduled notifications that are due.
     */
    public function scopeDue($query)
    {
        return $query->where('scheduled_for', '<=', now())
                    ->orWhereNull('scheduled_for');
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead()
    {
        $this->update(['read_at' => now()]);
    }

    /**
     * Mark the notification as unread.
     */
    public function markAsUnread()
    {
        $this->update(['read_at' => null]);
    }

    /**
     * Check if the notification is read.
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if the notification is unread.
     */
    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Mark email as sent.
     */
    public function markEmailAsSent()
    {
        $this->update([
            'email_sent' => true,
            'email_sent_at' => now(),
        ]);
    }

    /**
     * Get all available notification types.
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_PAYMENT_REMINDER,
            self::TYPE_EXPENSE_CREATED,
            self::TYPE_INCOME_RECEIVED,
            self::TYPE_GENERAL_ANNOUNCEMENT,
            self::TYPE_PAYMENT_RECEIVED,
            self::TYPE_OVERDUE_PAYMENT,
            self::TYPE_WECLOME_MESSAGE,
        ];
    }

    /**
     * Get all available priority levels.
     */
    public static function getPriorities(): array
    {
        return [
            self::PRIORITY_LOW,
            self::PRIORITY_MEDIUM,
            self::PRIORITY_HIGH,
        ];
    }

    /**
     * Get SMS delivery logs for this notification.
     */
    public function smsDeliveryLogs()
    {
        return $this->hasMany(\App\Models\SmsDeliveryLog::class);
    }

    /**
     * Check if SMS was sent for this notification.
     */
    public function wasSmsEnabled(): bool
    {
        return $this->sms_enabled;
    }

    /**
     * Check if SMS was sent for this notification.
     */
    public function wasSmsSent(): bool
    {
        return $this->sms_sent;
    }

    /**
     * Get SMS status display name.
     */
    public function getSmsStatusDisplayName(): string
    {
        if (!$this->sms_sent) {
            return 'Not Sent';
        }

        $statusNames = [
            'pending' => 'Pending',
            'sent' => 'Sent',
            'delivered' => 'Delivered',
            'failed' => 'Failed',
            'undelivered' => 'Undelivered',
        ];

        return $statusNames[$this->sms_status] ?? $this->sms_status ?? 'Unknown';
    }

    /**
     * Get SMS metadata with defaults.
     */
    public function getSmsMetadata(): array
    {
        return $this->sms_metadata ?? [];
    }

    /**
     * Check if notification should send SMS.
     */
    public function shouldSendSms(): bool
    {
        if (!$this->user || !$this->building) {
            return false;
        }

        // Check if user has SMS enabled
        if (!$this->user->shouldReceiveSmsForType($this->type)) {
            return false;
        }

        // Check building SMS settings
        $smsSettings = \App\Models\BuildingSmsSettings::getForBuilding($this->building);

        return $smsSettings->isSmsEnabled()
            && $smsSettings->isNotificationTypeEnabled($this->type)
            && !$smsSettings->hasReachedSmsLimit();
    }

    /**
     * Send SMS for this notification.
     */
    public function sendSms(): bool
    {
        if ($this->sms_sent) {
            return true; // Already sent
        }

        if (!$this->shouldSendSms()) {
            return false;
        }

        $smsService = app(\App\Services\SmsService::class);
        return $smsService->sendSmsNotification($this);
    }

    /**
     * Mark SMS as enabled for this notification.
     */
    public function enableSms(): void
    {
        $this->update(['sms_enabled' => true]);
    }

    /**
     * Get delivery summary for this notification.
     */
    public function getDeliverySummary(): array
    {
        return [
            'email' => [
                'enabled' => true, // Email is always enabled
                'sent' => $this->email_sent,
                'sent_at' => $this->email_sent_at?->toISOString(),
            ],
            'sms' => [
                'enabled' => $this->sms_enabled,
                'sent' => $this->sms_sent,
                'sent_at' => $this->sms_sent_at?->toISOString(),
                'provider' => $this->sms_provider,
                'status' => $this->sms_status,
                'status_display' => $this->getSmsStatusDisplayName(),
            ],
        ];
    }

    /**
     * Scope to get notifications with SMS enabled.
     */
    public function scopeSmsEnabled($query)
    {
        return $query->where('sms_enabled', true);
    }

    /**
     * Scope to get notifications with SMS sent.
     */
    public function scopeSmsSent($query)
    {
        return $query->where('sms_sent', true);
    }

    /**
     * Scope to get notifications with SMS pending.
     */
    public function scopeSmsPending($query)
    {
        return $query->where('sms_enabled', true)
                    ->where('sms_sent', false);
    }

    /**
     * Get SMS delivery statistics for notifications.
     */
    public static function getSmsDeliveryStats(int $buildingId = null, int $days = 30): array
    {
        $query = self::where('created_at', '>=', now()->subDays($days));

        if ($buildingId) {
            $query->where('building_id', $buildingId);
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_notifications,
            SUM(CASE WHEN sms_enabled = 1 THEN 1 ELSE 0 END) as sms_enabled_count,
            SUM(CASE WHEN sms_sent = 1 THEN 1 ELSE 0 END) as sms_sent_count,
            SUM(CASE WHEN sms_status = "delivered" THEN 1 ELSE 0 END) as sms_delivered_count,
            SUM(CASE WHEN sms_status = "failed" OR sms_status = "undelivered" THEN 1 ELSE 0 END) as sms_failed_count
        ')->first();

        $total = $stats->total_notifications ?? 0;
        $enabled = $stats->sms_enabled_count ?? 0;
        $sent = $stats->sms_sent_count ?? 0;
        $delivered = $stats->sms_delivered_count ?? 0;
        $failed = $stats->sms_failed_count ?? 0;

        return [
            'total_notifications' => $total,
            'sms_enabled' => $enabled,
            'sms_sent' => $sent,
            'sms_delivered' => $delivered,
            'sms_failed' => $failed,
            'sms_pending' => $enabled - $sent,
            'sms_enable_rate' => $total > 0 ? round(($enabled / $total) * 100, 2) : 0,
            'sms_send_rate' => $enabled > 0 ? round(($sent / $enabled) * 100, 2) : 0,
            'sms_delivery_rate' => $sent > 0 ? round(($delivered / $sent) * 100, 2) : 0,
            'sms_failure_rate' => $sent > 0 ? round(($failed / $sent) * 100, 2) : 0,
        ];
    }
}
