<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'created_by',
        'template_id',
        'name',
        'description',
        'configuration',
        'chart_config',
        'status',
        'is_public',
        'last_generated_at',
    ];

    protected $casts = [
        'configuration' => 'array',
        'chart_config' => 'array',
        'is_public' => 'boolean',
        'last_generated_at' => 'datetime',
    ];

    /**
     * Get the building this report belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the user who created this report.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the template this report is based on.
     */
    public function template()
    {
        return $this->belongsTo(ReportTemplate::class, 'template_id');
    }

    /**
     * Get report schedules for this report.
     */
    public function schedules()
    {
        return $this->hasMany(ReportSchedule::class, 'report_id');
    }

    /**
     * Get report generations for this report.
     */
    public function generations()
    {
        return $this->hasMany(ReportGeneration::class, 'report_id');
    }

    /**
     * Get report analytics for this report.
     */
    public function analytics()
    {
        return $this->hasMany(ReportAnalytics::class, 'report_id');
    }

    /**
     * Scope to get reports for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get active reports.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get public reports.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get reports created by a specific user.
     */
    public function scopeCreatedBy($query, int $userId)
    {
        return $query->where('created_by', $userId);
    }

    /**
     * Check if user can access this report.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Super admin can access all reports
        if ($user->role === 'super_admin') {
            return true;
        }

        // Must be in the same building
        if ($user->building_id !== $this->building_id) {
            return false;
        }

        // Creator can always access
        if ($user->id === $this->created_by) {
            return true;
        }

        // Public reports can be accessed by any admin in the building
        if ($this->is_public && $user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Check if user can edit this report.
     */
    public function canBeEditedBy(User $user): bool
    {
        // Super admin can edit all reports
        if ($user->role === 'super_admin') {
            return true;
        }

        // Must be in the same building
        if ($user->building_id !== $this->building_id) {
            return false;
        }

        // Only creator can edit (unless super admin)
        return $user->id === $this->created_by;
    }

    /**
     * Get report configuration with defaults from template.
     */
    public function getFullConfiguration(): array
    {
        $config = $this->configuration ?? [];
        
        if ($this->template) {
            $defaultConfig = $this->template->getDefaultConfig();
            $config = array_merge($defaultConfig, $config);
        }

        return $config;
    }

    /**
     * Update last generated timestamp.
     */
    public function markAsGenerated(): void
    {
        $this->update(['last_generated_at' => now()]);
    }

    /**
     * Get recent generations for this report.
     */
    public function getRecentGenerations(int $limit = 10)
    {
        return $this->generations()
            ->with(['generatedBy', 'schedule'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get analytics summary for this report.
     */
    public function getAnalyticsSummary(int $days = 30): array
    {
        $analytics = $this->analytics()
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->get()
            ->keyBy('action');

        return [
            'views' => $analytics['viewed']->count ?? 0,
            'generations' => $analytics['generated']->count ?? 0,
            'downloads' => $analytics['downloaded']->count ?? 0,
            'shares' => $analytics['shared']->count ?? 0,
            'total_interactions' => $analytics->sum('count'),
            'period_days' => $days,
        ];
    }

    /**
     * Clone this report for another user.
     */
    public function cloneFor(User $user, string $newName = null): self
    {
        $clone = $this->replicate();
        $clone->created_by = $user->id;
        $clone->building_id = $user->building_id;
        $clone->name = $newName ?? "Copy of {$this->name}";
        $clone->is_public = false;
        $clone->last_generated_at = null;
        $clone->save();

        return $clone;
    }

    /**
     * Archive this report.
     */
    public function archive(): bool
    {
        return $this->update(['status' => 'archived']);
    }

    /**
     * Activate this report.
     */
    public function activate(): bool
    {
        return $this->update(['status' => 'active']);
    }

    /**
     * Check if report has charts configured.
     */
    public function hasCharts(): bool
    {
        return !empty($this->chart_config);
    }

    /**
     * Get chart configuration.
     */
    public function getChartConfig(): array
    {
        return $this->chart_config ?? [];
    }

    /**
     * Validate report configuration.
     */
    public function validateConfiguration(): array
    {
        if (!$this->template) {
            return ['Template not found'];
        }

        return $this->template->validateConfiguration($this->configuration ?? []);
    }
}
