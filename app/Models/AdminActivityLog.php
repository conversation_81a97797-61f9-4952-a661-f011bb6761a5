<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'admin_id',
        'building_id',
        'action',
        'resource_type',
        'resource_id',
        'details',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    /**
     * Get the admin who performed the action.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get the building where the action was performed.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the resource that was acted upon (polymorphic).
     */
    public function resource()
    {
        if (!$this->resource_type || !$this->resource_id) {
            return null;
        }

        $modelClass = $this->getModelClass($this->resource_type);
        if (!$modelClass) {
            return null;
        }

        return $modelClass::find($this->resource_id);
    }

    /**
     * Log an admin action.
     */
    public static function logAction(
        User $admin,
        string $action,
        ?string $resourceType = null,
        ?int $resourceId = null,
        ?array $details = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'admin_id' => $admin->id,
            'building_id' => $admin->building_id,
            'action' => $action,
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'details' => $details,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * Scope to get activities for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get activities by a specific admin.
     */
    public function scopeByAdmin($query, int $adminId)
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * Scope to get activities for a specific action.
     */
    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to get activities for a specific resource type.
     */
    public function scopeForResourceType($query, string $resourceType)
    {
        return $query->where('resource_type', $resourceType);
    }

    /**
     * Scope to get recent activities.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get the model class for a resource type.
     */
    private function getModelClass(string $resourceType): ?string
    {
        $modelMap = [
            'expense' => Expense::class,
            'income' => Income::class,
            'payment' => Payment::class,
            'user' => User::class,
            'notification' => Notification::class,
            'building' => Building::class,
        ];

        return $modelMap[$resourceType] ?? null;
    }

    /**
     * Get formatted action description.
     */
    public function getFormattedActionAttribute(): string
    {
        $actionMap = [
            'created' => 'Created',
            'updated' => 'Updated',
            'deleted' => 'Deleted',
            'archived' => 'Archived',
            'unarchived' => 'Unarchived',
            'login' => 'Logged in',
            'logout' => 'Logged out',
            'invited_admin' => 'Invited admin',
            'removed_admin' => 'Removed admin',
            'changed_permissions' => 'Changed permissions',
            'sent_notification' => 'Sent notification',
            'exported_data' => 'Exported data',
            'imported_data' => 'Imported data',
        ];

        return $actionMap[$this->action] ?? ucfirst(str_replace('_', ' ', $this->action));
    }

    /**
     * Get activity summary for dashboard.
     */
    public static function getActivitySummary(int $buildingId, int $days = 30): array
    {
        $activities = self::forBuilding($buildingId)
            ->recent($days)
            ->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->orderBy('count', 'desc')
            ->get();

        $totalActivities = self::forBuilding($buildingId)->recent($days)->count();
        $uniqueAdmins = self::forBuilding($buildingId)
            ->recent($days)
            ->distinct('admin_id')
            ->count('admin_id');

        return [
            'total_activities' => $totalActivities,
            'unique_admins' => $uniqueAdmins,
            'top_actions' => $activities->take(5)->toArray(),
            'period_days' => $days,
        ];
    }
}
