<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BuildingCurrencySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'primary_currency',
        'accepted_currencies',
        'auto_convert',
        'exchange_rate_provider',
        'conversion_markup',
        'regional_settings',
    ];

    protected $casts = [
        'accepted_currencies' => 'array',
        'auto_convert' => 'boolean',
        'conversion_markup' => 'decimal:4',
        'regional_settings' => 'array',
    ];

    /**
     * Get the building this setting belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get accepted currencies including primary currency.
     */
    public function getAllAcceptedCurrencies(): array
    {
        $accepted = $this->accepted_currencies ?? [];
        
        // Always include primary currency
        if (!in_array($this->primary_currency, $accepted)) {
            $accepted[] = $this->primary_currency;
        }

        return array_unique($accepted);
    }

    /**
     * Check if a currency is accepted.
     */
    public function acceptsCurrency(string $currency): bool
    {
        return in_array($currency, $this->getAllAcceptedCurrencies());
    }

    /**
     * Get regional settings with defaults.
     */
    public function getRegionalSettings(): array
    {
        $defaults = [
            'number_format' => [
                'decimal_separator' => '.',
                'thousands_separator' => ',',
                'decimal_places' => 2,
            ],
            'currency_format' => [
                'symbol_position' => 'before', // before, after
                'space_between' => false,
                'show_currency_code' => false,
            ],
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'first_day_of_week' => 1, // Monday
        ];

        return array_merge($defaults, $this->regional_settings ?? []);
    }

    /**
     * Update regional settings.
     */
    public function updateRegionalSettings(array $settings): void
    {
        $currentSettings = $this->getRegionalSettings();
        $newSettings = array_merge($currentSettings, $settings);
        
        $this->update(['regional_settings' => $newSettings]);
    }

    /**
     * Get currency symbol for primary currency.
     */
    public function getPrimaryCurrencySymbol(): string
    {
        return $this->getCurrencySymbol($this->primary_currency);
    }

    /**
     * Get currency symbol for any currency.
     */
    public function getCurrencySymbol(string $currency): string
    {
        $symbols = [
            'USD' => '$',
            'JOD' => 'JD',
            'ILS' => '₪',
        ];

        return $symbols[$currency] ?? $currency;
    }

    /**
     * Get decimal places for currency.
     */
    public function getCurrencyDecimals(string $currency): int
    {
        $decimals = [
            'KWD' => 3,
            'BHD' => 3,
            'OMR' => 3,
            'JOD' => 3,
            'JPY' => 0,
            'KRW' => 0,
        ];

        return $decimals[$currency] ?? 2;
    }

    /**
     * Format amount according to regional settings.
     */
    public function formatAmount(float $amount, string $currency = null): string
    {
        $currency = $currency ?? $this->primary_currency;
        $settings = $this->getRegionalSettings();
        $decimals = $this->getCurrencyDecimals($currency);
        
        $formattedAmount = number_format(
            $amount,
            $decimals,
            $settings['number_format']['decimal_separator'],
            $settings['number_format']['thousands_separator']
        );

        $symbol = $this->getCurrencySymbol($currency);
        $currencyFormat = $settings['currency_format'];

        if ($currencyFormat['symbol_position'] === 'before') {
            $space = $currencyFormat['space_between'] ? ' ' : '';
            $result = $symbol . $space . $formattedAmount;
        } else {
            $space = $currencyFormat['space_between'] ? ' ' : '';
            $result = $formattedAmount . $space . $symbol;
        }

        if ($currencyFormat['show_currency_code']) {
            $result .= ' ' . $currency;
        }

        return $result;
    }

    /**
     * Get or create settings for a building.
     */
    public static function getForBuilding(Building $building): self
    {
        return self::firstOrCreate(
            ['building_id' => $building->id],
            [
                'primary_currency' => $building->currency ?? 'USD',
                'accepted_currencies' => [$building->currency ?? 'USD'],
                'auto_convert' => true,
                'exchange_rate_provider' => 'manual',
                'conversion_markup' => 0.0000,
            ]
        );
    }

    /**
     * Add accepted currency.
     */
    public function addAcceptedCurrency(string $currency): void
    {
        $accepted = $this->accepted_currencies ?? [];
        
        if (!in_array($currency, $accepted)) {
            $accepted[] = $currency;
            $this->update(['accepted_currencies' => $accepted]);
        }
    }

    /**
     * Remove accepted currency.
     */
    public function removeAcceptedCurrency(string $currency): void
    {
        // Cannot remove primary currency
        if ($currency === $this->primary_currency) {
            return;
        }

        $accepted = $this->accepted_currencies ?? [];
        $accepted = array_filter($accepted, fn($c) => $c !== $currency);
        
        $this->update(['accepted_currencies' => array_values($accepted)]);
    }

    /**
     * Change primary currency.
     */
    public function changePrimaryCurrency(string $currency): void
    {
        $this->addAcceptedCurrency($currency);
        $this->update(['primary_currency' => $currency]);
    }

    /**
     * Get conversion markup as decimal.
     */
    public function getConversionMarkupDecimal(): float
    {
        return (float) $this->conversion_markup;
    }

    /**
     * Check if auto-conversion is enabled.
     */
    public function shouldAutoConvert(): bool
    {
        return $this->auto_convert;
    }

    /**
     * Get supported exchange rate providers.
     */
    public static function getSupportedProviders(): array
    {
        return [
            'manual' => 'Manual Entry',
            'fixer' => 'Fixer.io API',
            'exchangerate' => 'ExchangeRate-API',
            'currencylayer' => 'CurrencyLayer API',
        ];
    }
}
