<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CurrencyExchangeRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'provider',
        'effective_date',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'rate' => 'decimal:6',
        'effective_date' => 'datetime',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Scope to get active rates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get rates for a specific currency pair.
     */
    public function scopeForPair($query, string $fromCurrency, string $toCurrency)
    {
        return $query->where('from_currency', $fromCurrency)
                    ->where('to_currency', $toCurrency);
    }

    /**
     * Scope to get rates effective on a specific date.
     */
    public function scopeEffectiveOn($query, Carbon $date)
    {
        return $query->where('effective_date', '<=', $date);
    }

    /**
     * Scope to get latest rates.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('effective_date', 'desc');
    }

    /**
     * Get the latest exchange rate for a currency pair.
     */
    public static function getLatestRate(string $fromCurrency, string $toCurrency, Carbon $date = null): ?float
    {
        $date = $date ?? now();

        $rate = self::active()
            ->forPair($fromCurrency, $toCurrency)
            ->effectiveOn($date)
            ->latest()
            ->first();

        return $rate ? (float) $rate->rate : null;
    }

    /**
     * Get or create a rate for a currency pair.
     */
    public static function getOrCreateRate(
        string $fromCurrency, 
        string $toCurrency, 
        float $rate, 
        string $provider = 'manual',
        Carbon $effectiveDate = null
    ): self {
        $effectiveDate = $effectiveDate ?? now();

        return self::updateOrCreate([
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'effective_date' => $effectiveDate,
            'provider' => $provider,
        ], [
            'rate' => $rate,
            'is_active' => true,
        ]);
    }

    /**
     * Convert amount using this exchange rate.
     */
    public function convert(float $amount): float
    {
        return $amount * $this->rate;
    }

    /**
     * Get inverse rate.
     */
    public function getInverseRate(): float
    {
        return 1 / $this->rate;
    }

    /**
     * Check if rate is stale (older than specified hours).
     */
    public function isStale(int $hours = 24): bool
    {
        return $this->effective_date->addHours($hours)->isPast();
    }

    /**
     * Get formatted rate string.
     */
    public function getFormattedRate(): string
    {
        return "1 {$this->from_currency} = {$this->rate} {$this->to_currency}";
    }

    /**
     * Get rate with markup applied.
     */
    public function getRateWithMarkup(float $markupPercentage = 0): float
    {
        return $this->rate * (1 + ($markupPercentage / 100));
    }

    /**
     * Deactivate old rates for the same currency pair.
     */
    public function deactivateOldRates(): void
    {
        self::where('from_currency', $this->from_currency)
            ->where('to_currency', $this->to_currency)
            ->where('id', '!=', $this->id)
            ->where('effective_date', '<', $this->effective_date)
            ->update(['is_active' => false]);
    }

    /**
     * Get historical rates for a currency pair.
     */
    public static function getHistoricalRates(
        string $fromCurrency, 
        string $toCurrency, 
        Carbon $startDate, 
        Carbon $endDate,
        int $limit = 100
    ) {
        return self::forPair($fromCurrency, $toCurrency)
            ->whereBetween('effective_date', [$startDate, $endDate])
            ->orderBy('effective_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get supported currency pairs.
     */
    public static function getSupportedPairs(): array
    {
        return self::active()
            ->selectRaw('DISTINCT from_currency, to_currency')
            ->get()
            ->map(function ($rate) {
                return [
                    'from' => $rate->from_currency,
                    'to' => $rate->to_currency,
                    'pair' => "{$rate->from_currency}/{$rate->to_currency}"
                ];
            })
            ->toArray();
    }

    /**
     * Bulk update rates from external source.
     */
    public static function bulkUpdateRates(array $rates, string $provider = 'api'): int
    {
        $updated = 0;
        $effectiveDate = now();

        foreach ($rates as $rateData) {
            if (!isset($rateData['from'], $rateData['to'], $rateData['rate'])) {
                continue;
            }

            self::getOrCreateRate(
                $rateData['from'],
                $rateData['to'],
                $rateData['rate'],
                $provider,
                $effectiveDate
            );

            $updated++;
        }

        return $updated;
    }

    /**
     * Clean up old inactive rates.
     */
    public static function cleanupOldRates(int $daysToKeep = 365): int
    {
        return self::where('is_active', false)
            ->where('created_at', '<', now()->subDays($daysToKeep))
            ->delete();
    }
}
