<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'name',
        'notification_type',
        'template',
        'is_active',
        'is_system_template',
        'variables',
        'character_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system_template' => 'boolean',
        'variables' => 'array',
        'character_count' => 'integer',
    ];

    /**
     * Get the building this template belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Scope to get active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get templates by notification type.
     */
    public function scopeForNotificationType($query, string $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Scope to get system templates.
     */
    public function scopeSystemTemplates($query)
    {
        return $query->where('is_system_template', true);
    }

    /**
     * Scope to get custom templates.
     */
    public function scopeCustomTemplates($query)
    {
        return $query->where('is_system_template', false);
    }

    /**
     * Scope to get templates for a building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Render template with variables.
     */
    public function render(array $variables = []): string
    {
        $template = $this->template;
        
        foreach ($variables as $key => $value) {
            $placeholder = '{' . $key . '}';
            $template = str_replace($placeholder, $value, $template);
        }

        return $template;
    }

    /**
     * Update character count based on template content.
     */
    public function updateCharacterCount(): void
    {
        $this->update(['character_count' => mb_strlen($this->template)]);
    }

    /**
     * Get available variables for this template.
     */
    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Check if template is long (requires multiple SMS segments).
     */
    public function isLongTemplate(): bool
    {
        return $this->character_count > 160;
    }

    /**
     * Get estimated SMS segments for this template.
     */
    public function getEstimatedSegments(): int
    {
        if ($this->character_count <= 160) {
            return 1;
        }
        
        return ceil($this->character_count / 153);
    }

    /**
     * Validate template variables.
     */
    public function validateTemplate(): array
    {
        $errors = [];
        $template = $this->template;
        $availableVars = $this->getAvailableVariables();

        // Find all placeholders in template
        preg_match_all('/\{([^}]+)\}/', $template, $matches);
        $usedVars = $matches[1] ?? [];

        // Check for undefined variables
        foreach ($usedVars as $var) {
            if (!in_array($var, $availableVars)) {
                $errors[] = "Undefined variable: {$var}";
            }
        }

        // Check template length
        if ($this->character_count > 1600) { // 10 SMS segments limit
            $errors[] = "Template is too long (max 1600 characters)";
        }

        return $errors;
    }

    /**
     * Get default templates for notification types.
     */
    public static function getDefaultTemplates(): array
    {
        return [
            Notification::TYPE_PAYMENT_REMINDER => [
                'name' => 'Payment Reminder',
                'template' => 'Hi {user_name}, you have a payment due for {expense_type}. Amount: {amount}. Due: {due_date}. Building: {building_name}',
                'variables' => ['user_name', 'expense_type', 'amount', 'due_date', 'building_name'],
            ],
            Notification::TYPE_OVERDUE_PAYMENT => [
                'name' => 'Overdue Payment',
                'template' => 'URGENT: {user_name}, your payment for {expense_type} is {days_overdue} days overdue. Amount: {amount}. Please pay immediately.',
                'variables' => ['user_name', 'expense_type', 'days_overdue', 'amount', 'building_name'],
            ],
            Notification::TYPE_GENERAL_ANNOUNCEMENT => [
                'name' => 'General Announcement',
                'template' => '{building_name}: {title} - {message}',
                'variables' => ['building_name', 'title', 'message'],
            ],
            Notification::TYPE_EXPENSE_CREATED => [
                'name' => 'New Expense',
                'template' => 'New expense created: {expense_type} - {amount}. Due: {due_date}. Building: {building_name}',
                'variables' => ['expense_type', 'amount', 'due_date', 'building_name'],
            ],
            Notification::TYPE_PAYMENT_RECEIVED => [
                'name' => 'Payment Received',
                'template' => 'Payment received: {amount} for {expense_type}. Thank you! Building: {building_name}',
                'variables' => ['amount', 'expense_type', 'building_name'],
            ],
            Notification::TYPE_INCOME_RECEIVED => [
                'name' => 'Income Recorded',
                'template' => 'Income of {amount} recorded for your account. Building: {building_name}',
                'variables' => ['amount', 'building_name'],
            ],
        ];
    }

    /**
     * Create default templates for a building.
     */
    public static function createDefaultTemplatesForBuilding(Building $building): array
    {
        $templates = [];
        $defaultTemplates = self::getDefaultTemplates();

        foreach ($defaultTemplates as $type => $templateData) {
            $template = self::create([
                'building_id' => $building->id,
                'name' => $templateData['name'],
                'notification_type' => $type,
                'template' => $templateData['template'],
                'is_active' => true,
                'is_system_template' => true,
                'variables' => $templateData['variables'],
                'character_count' => mb_strlen($templateData['template']),
            ]);

            $templates[] = $template;
        }

        return $templates;
    }

    /**
     * Get template for notification type and building.
     */
    public static function getForNotification(string $notificationType, int $buildingId): ?self
    {
        return self::active()
            ->forNotificationType($notificationType)
            ->where(function ($query) use ($buildingId) {
                $query->forBuilding($buildingId)
                      ->orWhere('is_system_template', true);
            })
            ->orderBy('is_system_template', 'asc') // Custom templates first
            ->first();
    }

    /**
     * Clone template for customization.
     */
    public function cloneForCustomization(string $newName = null): self
    {
        $newTemplate = $this->replicate();
        $newTemplate->name = $newName ?? ($this->name . ' (Custom)');
        $newTemplate->is_system_template = false;
        $newTemplate->save();

        return $newTemplate;
    }

    /**
     * Get template usage statistics.
     */
    public function getUsageStats(int $days = 30): array
    {
        $notifications = Notification::where('type', $this->notification_type)
            ->where('building_id', $this->building_id)
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        $smsLogs = SmsDeliveryLog::whereHas('notification', function ($query) {
                $query->where('type', $this->notification_type);
            })
            ->where('building_id', $this->building_id)
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        return [
            'total_notifications' => $notifications,
            'sms_sent' => $smsLogs,
            'usage_rate' => $notifications > 0 ? round(($smsLogs / $notifications) * 100, 2) : 0,
        ];
    }

    /**
     * Get character count distribution for templates.
     */
    public static function getCharacterCountDistribution(): array
    {
        return self::selectRaw('
            COUNT(CASE WHEN character_count <= 160 THEN 1 END) as single_sms,
            COUNT(CASE WHEN character_count > 160 AND character_count <= 320 THEN 1 END) as double_sms,
            COUNT(CASE WHEN character_count > 320 THEN 1 END) as multi_sms,
            AVG(character_count) as avg_length,
            MAX(character_count) as max_length,
            MIN(character_count) as min_length
        ')->first()->toArray();
    }

    /**
     * Search templates by content.
     */
    public static function search(string $query): \Illuminate\Database\Eloquent\Builder
    {
        return self::where(function ($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('template', 'LIKE', "%{$query}%");
        });
    }
}
