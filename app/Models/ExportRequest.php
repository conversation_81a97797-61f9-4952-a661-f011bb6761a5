<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ExportRequest extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'user_id',
        'type',
        'format',
        'status',
        'parameters',
        'file_name',
        'file_path',
        'file_size',
        'record_count',
        'error_message',
        'started_at',
        'completed_at',
        'expires_at',
        'download_count',
        'last_downloaded_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parameters' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_downloaded_at' => 'datetime',
        'file_size' => 'integer',
        'record_count' => 'integer',
        'download_count' => 'integer',
    ];

    /**
     * Get the building that owns the export request.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the user that requested the export.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get pending exports.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processing exports.
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope to get completed exports.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get failed exports.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get expired exports.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Scope to get exports for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Mark export as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark export as completed.
     */
    public function markAsCompleted(string $fileName, string $filePath, int $fileSize, int $recordCount = null): void
    {
        $this->update([
            'status' => 'completed',
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'record_count' => $recordCount,
            'completed_at' => now(),
            'expires_at' => now()->addHours(config('export.storage.cleanup_after_hours', 24)),
        ]);
    }

    /**
     * Mark export as failed.
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Record a download.
     */
    public function recordDownload(): void
    {
        $this->increment('download_count');
        $this->update(['last_downloaded_at' => now()]);
    }

    /**
     * Check if export is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if export file exists.
     */
    public function fileExists(): bool
    {
        if (!$this->file_path) {
            return false;
        }

        return \Storage::disk(config('export.storage.disk', 'local'))->exists($this->file_path);
    }

    /**
     * Get file size in human readable format.
     */
    public function getFileSizeHumanAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get processing time in human readable format.
     */
    public function getProcessingTimeAttribute(): ?string
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        $seconds = $this->completed_at->diffInSeconds($this->started_at);
        
        if ($seconds < 60) {
            return $seconds . ' seconds';
        } elseif ($seconds < 3600) {
            return round($seconds / 60, 1) . ' minutes';
        } else {
            return round($seconds / 3600, 1) . ' hours';
        }
    }

    /**
     * Get export type display name.
     */
    public function getTypeDisplayNameAttribute(): string
    {
        $templates = config('export.templates', []);
        
        return $templates[$this->type]['name'] ?? ucfirst(str_replace('_', ' ', $this->type));
    }

    /**
     * Delete the export file.
     */
    public function deleteFile(): bool
    {
        if (!$this->file_path) {
            return true;
        }

        $disk = \Storage::disk(config('export.storage.disk', 'local'));
        
        if ($disk->exists($this->file_path)) {
            return $disk->delete($this->file_path);
        }

        return true;
    }

    /**
     * Get exports that need cleanup.
     */
    public static function getExportsForCleanup(): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('expires_at', '<', now())
                    ->whereNotNull('file_path')
                    ->get();
    }
}
