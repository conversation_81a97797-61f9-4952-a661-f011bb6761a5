<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CurrencyConversion extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'convertible_type',
        'convertible_id',
        'from_currency',
        'to_currency',
        'from_amount',
        'to_amount',
        'exchange_rate',
        'rate_provider',
        'converted_at',
        'converted_by',
        'conversion_metadata',
    ];

    protected $casts = [
        'from_amount' => 'decimal:4',
        'to_amount' => 'decimal:4',
        'exchange_rate' => 'decimal:6',
        'converted_at' => 'datetime',
        'conversion_metadata' => 'array',
    ];

    /**
     * Get the building this conversion belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the user who performed the conversion.
     */
    public function convertedBy()
    {
        return $this->belongsTo(User::class, 'converted_by');
    }

    /**
     * Get the convertible model (expense, income, payment).
     */
    public function convertible()
    {
        return $this->morphTo();
    }

    /**
     * Scope to get conversions for a specific building.
     */
    public function scopeForBuilding($query, int $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Scope to get conversions for a specific currency pair.
     */
    public function scopeForCurrencyPair($query, string $fromCurrency, string $toCurrency)
    {
        return $query->where('from_currency', $fromCurrency)
                    ->where('to_currency', $toCurrency);
    }

    /**
     * Scope to get recent conversions.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('converted_at', '>=', now()->subDays($days));
    }

    /**
     * Log a currency conversion.
     */
    public static function logConversion(
        Building $building,
        $convertible,
        string $fromCurrency,
        string $toCurrency,
        float $fromAmount,
        float $toAmount,
        float $exchangeRate,
        string $rateProvider = 'manual',
        ?User $convertedBy = null,
        ?array $metadata = null
    ): self {
        return self::create([
            'building_id' => $building->id,
            'convertible_type' => get_class($convertible),
            'convertible_id' => $convertible->id,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'from_amount' => $fromAmount,
            'to_amount' => $toAmount,
            'exchange_rate' => $exchangeRate,
            'rate_provider' => $rateProvider,
            'converted_at' => now(),
            'converted_by' => $convertedBy?->id,
            'conversion_metadata' => $metadata,
        ]);
    }

    /**
     * Get conversion summary for building.
     */
    public static function getConversionSummary(Building $building, int $days = 30): array
    {
        $conversions = self::forBuilding($building->id)
            ->recent($days)
            ->get();

        $totalConversions = $conversions->count();
        $uniquePairs = $conversions->groupBy(function ($conversion) {
            return "{$conversion->from_currency}/{$conversion->to_currency}";
        })->count();

        $topPairs = $conversions->groupBy(function ($conversion) {
            return "{$conversion->from_currency}/{$conversion->to_currency}";
        })->map(function ($group) {
            return $group->count();
        })->sortDesc()->take(5);

        $totalVolume = $conversions->groupBy('from_currency')
            ->map(function ($group) {
                return [
                    'currency' => $group->first()->from_currency,
                    'total_amount' => $group->sum('from_amount'),
                ];
            })->values();

        return [
            'total_conversions' => $totalConversions,
            'unique_currency_pairs' => $uniquePairs,
            'top_currency_pairs' => $topPairs->toArray(),
            'total_volume_by_currency' => $totalVolume->toArray(),
            'period_days' => $days,
        ];
    }

    /**
     * Get conversion rate difference from market rate.
     */
    public function getRateDifference(): ?float
    {
        $marketRate = CurrencyExchangeRate::getLatestRate(
            $this->from_currency,
            $this->to_currency,
            $this->converted_at
        );

        if (!$marketRate) {
            return null;
        }

        return $this->exchange_rate - $marketRate;
    }

    /**
     * Get conversion rate difference percentage.
     */
    public function getRateDifferencePercentage(): ?float
    {
        $difference = $this->getRateDifference();
        
        if ($difference === null) {
            return null;
        }

        $marketRate = CurrencyExchangeRate::getLatestRate(
            $this->from_currency,
            $this->to_currency,
            $this->converted_at
        );

        return $marketRate ? ($difference / $marketRate) * 100 : null;
    }

    /**
     * Check if conversion was profitable (considering markup).
     */
    public function wasProfitable(float $expectedMarkup = 0): bool
    {
        $difference = $this->getRateDifferencePercentage();
        
        if ($difference === null) {
            return false;
        }

        return $difference >= $expectedMarkup;
    }

    /**
     * Get formatted conversion string.
     */
    public function getFormattedConversion(): string
    {
        return sprintf(
            '%.4f %s → %.4f %s (Rate: %.6f)',
            $this->from_amount,
            $this->from_currency,
            $this->to_amount,
            $this->to_currency,
            $this->exchange_rate
        );
    }

    /**
     * Get conversion metadata with defaults.
     */
    public function getMetadata(): array
    {
        return array_merge([
            'conversion_method' => 'automatic',
            'markup_applied' => 0,
            'source' => 'system',
        ], $this->conversion_metadata ?? []);
    }

    /**
     * Get conversions by type (expense, income, payment).
     */
    public static function getConversionsByType(Building $building, int $days = 30): array
    {
        $conversions = self::forBuilding($building->id)
            ->recent($days)
            ->selectRaw('convertible_type, COUNT(*) as count, SUM(from_amount) as total_amount')
            ->groupBy('convertible_type')
            ->get();

        return $conversions->mapWithKeys(function ($conversion) {
            $type = class_basename($conversion->convertible_type);
            return [
                strtolower($type) => [
                    'count' => $conversion->count,
                    'total_amount' => $conversion->total_amount,
                    'type' => $type,
                ]
            ];
        })->toArray();
    }

    /**
     * Clean up old conversion logs.
     */
    public static function cleanupOldConversions(int $daysToKeep = 365): int
    {
        return self::where('converted_at', '<', now()->subDays($daysToKeep))->delete();
    }
}
