<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'phone_country_code',
        'sms_notifications_enabled',
        'sms_preferences',
        'password',
        'apartment_number',
        'role',
        'building_id',
        'email_notifications_enabled',
        'email_payment_reminders',
        'email_expense_notifications',
        'email_income_notifications',
        'email_general_announcements',
        'email_overdue_notifications',
        'email_frequency',
        'last_email_sent_at',
        'is_active'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'sms_notifications_enabled' => 'boolean',
            'sms_preferences' => 'array',
            'email_notifications_enabled' => 'boolean',
            'email_payment_reminders' => 'boolean',
            'email_expense_notifications' => 'boolean',
            'email_income_notifications' => 'boolean',
            'email_general_announcements' => 'boolean',
            'email_overdue_notifications' => 'boolean',
            'last_email_sent_at' => 'datetime',
            'is_active' => 'boolean',
            'admin_permissions' => 'array',
            'last_activity_at' => 'datetime',
        ];
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function incomes()
    {
        return $this->hasMany(Income::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function unreadNotifications()
    {
        return $this->hasMany(Notification::class)->unread();
    }

    /**
     * Check if user wants to receive email notifications for a specific type.
     */
    public function wantsEmailNotification(string $notificationType): bool
    {
        if (!$this->email_notifications_enabled) {
            return false;
        }

        return match($notificationType) {
            'payment_reminder' => $this->email_payment_reminders,
            'expense_created' => $this->email_expense_notifications,
            'income_received' => $this->email_income_notifications,
            'general_announcement' => $this->email_general_announcements,
            'payment_received' => $this->email_income_notifications,
            'overdue_payment' => $this->email_overdue_notifications,
            default => $this->email_notifications_enabled,
        };
    }

    /**
     * Check if user can receive email based on frequency preference.
     */
    public function canReceiveEmailNow(): bool
    {
        if (!$this->email_notifications_enabled) {
            return false;
        }

        if ($this->email_frequency === 'immediate') {
            return true;
        }

        if (!$this->last_email_sent_at) {
            return true;
        }

        $hoursSinceLastEmail = $this->last_email_sent_at->diffInHours(now());

        return match($this->email_frequency) {
            'daily' => $hoursSinceLastEmail >= 24,
            'weekly' => $hoursSinceLastEmail >= (24 * 7),
            default => true,
        };
    }

    /**
     * Update the last email sent timestamp.
     */
    public function markEmailSent(): void
    {
        $this->update(['last_email_sent_at' => now()]);
    }

    /**
     * Get email preference summary.
     */
    public function getEmailPreferences(): array
    {
        return [
            'enabled' => $this->email_notifications_enabled,
            'payment_reminders' => $this->email_payment_reminders,
            'expense_notifications' => $this->email_expense_notifications,
            'income_notifications' => $this->email_income_notifications,
            'general_announcements' => $this->email_general_announcements,
            'overdue_notifications' => $this->email_overdue_notifications,
            'frequency' => $this->email_frequency,
            'last_sent' => $this->last_email_sent_at?->toISOString(),
        ];
    }

    public function fileAttachments()
    {
        return $this->hasMany(FileAttachment::class);
    }

    /**
     * Get SMS delivery logs for this user.
     */
    public function smsDeliveryLogs()
    {
        return $this->hasMany(\App\Models\SmsDeliveryLog::class);
    }

    /**
     * Get SMS opt logs for this user.
     */
    public function smsOptLogs()
    {
        return $this->hasMany(\App\Models\SmsOptLog::class);
    }

    /**
     * Check if user has SMS notifications enabled.
     */
    public function hasSmsEnabled(): bool
    {
        return $this->sms_notifications_enabled && !empty($this->phone_number);
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneNumber(): string
    {
        if (!$this->phone_number) {
            return '';
        }

        $smsService = app(\App\Services\SmsService::class);
        return $smsService->formatPhoneNumber($this->phone_number, $this->phone_country_code ?? '+1');
    }

    /**
     * Get SMS preferences with defaults.
     */
    public function getSmsPreferences(): array
    {
        $defaults = [
            'payment_reminders' => true,
            'expense_notifications' => false,
            'income_notifications' => false,
            'general_announcements' => true,
            'overdue_notifications' => true,
        ];

        return array_merge($defaults, $this->sms_preferences ?? []);
    }

    /**
     * Update SMS preferences.
     */
    public function updateSmsPreferences(array $preferences): void
    {
        $currentPreferences = $this->getSmsPreferences();
        $newPreferences = array_merge($currentPreferences, $preferences);

        $this->update(['sms_preferences' => $newPreferences]);
    }

    /**
     * Enable SMS notifications.
     */
    public function enableSmsNotifications(string $phoneNumber = null, string $countryCode = null): void
    {
        $updateData = ['sms_notifications_enabled' => true];

        if ($phoneNumber) {
            $updateData['phone_number'] = $phoneNumber;
        }

        if ($countryCode) {
            $updateData['phone_country_code'] = $countryCode;
        }

        $this->update($updateData);

        // Log opt-in
        if ($this->building_id) {
            \App\Models\SmsOptLog::create([
                'user_id' => $this->id,
                'building_id' => $this->building_id,
                'action' => 'opt_in',
                'phone_number' => $this->getFormattedPhoneNumber(),
                'method' => 'web',
            ]);
        }
    }

    /**
     * Disable SMS notifications.
     */
    public function disableSmsNotifications(string $reason = null): void
    {
        $this->update(['sms_notifications_enabled' => false]);

        // Log opt-out
        if ($this->building_id) {
            \App\Models\SmsOptLog::create([
                'user_id' => $this->id,
                'building_id' => $this->building_id,
                'action' => 'opt_out',
                'phone_number' => $this->getFormattedPhoneNumber(),
                'method' => 'web',
                'reason' => $reason,
            ]);
        }
    }

    /**
     * Check if user should receive SMS for notification type.
     */
    public function shouldReceiveSmsForType(string $notificationType): bool
    {
        if (!$this->hasSmsEnabled()) {
            return false;
        }

        $preferences = $this->getSmsPreferences();

        switch ($notificationType) {
            case \App\Models\Notification::TYPE_PAYMENT_REMINDER:
                return $preferences['payment_reminders'] ?? true;
            case \App\Models\Notification::TYPE_OVERDUE_PAYMENT:
                return $preferences['overdue_notifications'] ?? true;
            case \App\Models\Notification::TYPE_EXPENSE_CREATED:
                return $preferences['expense_notifications'] ?? false;
            case \App\Models\Notification::TYPE_INCOME_RECEIVED:
                return $preferences['income_notifications'] ?? false;
            case \App\Models\Notification::TYPE_GENERAL_ANNOUNCEMENT:
                return $preferences['general_announcements'] ?? true;
            default:
                return false;
        }
    }

    /**
     * Get SMS statistics for this user.
     */
    public function getSmsStats(int $days = 30): array
    {
        $logs = $this->smsDeliveryLogs()
            ->where('created_at', '>=', now()->subDays($days))
            ->get();

        $total = $logs->count();
        $sent = $logs->where('status', \App\Models\SmsDeliveryLog::STATUS_SENT)->count() +
                $logs->where('status', \App\Models\SmsDeliveryLog::STATUS_DELIVERED)->count();
        $delivered = $logs->where('status', \App\Models\SmsDeliveryLog::STATUS_DELIVERED)->count();
        $failed = $logs->where('status', \App\Models\SmsDeliveryLog::STATUS_FAILED)->count() +
                  $logs->where('status', \App\Models\SmsDeliveryLog::STATUS_UNDELIVERED)->count();

        return [
            'total' => $total,
            'sent' => $sent,
            'delivered' => $delivered,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
            'delivery_rate' => $sent > 0 ? round(($delivered / $sent) * 100, 2) : 0,
        ];
    }
    /**
     * Generate a new email verification token.
     */
    public function generateEmailVerificationToken(): string
    {
        $token = \Illuminate\Support\Str::random(64);
        $this->email_verification_token = $token;
        $this->email_verification_token_expires_at = now()->addHours(24);
        $this->save();

        return $token;
    }

    /**
     * Check if the email verification token is valid.
     */
    public function isEmailVerificationTokenValid(string $token): bool
    {
        return $this->email_verification_token === $token
            && $this->email_verification_token_expires_at
            && $this->email_verification_token_expires_at->isFuture();
    }

    /**
     * Mark email as verified and clear verification token.
     */
    public function markEmailAsVerified(): void
    {
        $this->email_verified_at = now();
        $this->email_verification_token = null;
        $this->email_verification_token_expires_at = null;
        $this->save();
    }

    /**
     * Check if email verification token has expired.
     */
    public function isEmailVerificationTokenExpired(): bool
    {
        return $this->email_verification_token_expires_at
            && $this->email_verification_token_expires_at->isPast();
    }

    /**
     * Manually verify email (for admin use).
     */
    public function manuallyVerifyEmail(): void
    {
        $this->markEmailAsVerified();
    }

    /**
     * Check if user is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if user is active.
     */
    public function isActive(): bool
    {
        return $this->is_active ?? true; // Default to true for backward compatibility
    }

    /**
     * Check if user is inactive.
     */
    public function isInactive(): bool
    {
        return !$this->isActive();
    }

    /**
     * Activate the user.
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the user.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Scope to get only active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only inactive users.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Update last activity timestamp.
     */
    public function updateLastActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }
}
