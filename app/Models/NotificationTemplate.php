<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'name',
        'description',
        'type',
        'title_template',
        'message_template',
        'variables',
        'priority',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the building that owns the template.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get only active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by notification type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by building.
     */
    public function scopeForBuilding($query, $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Render the template with provided variables.
     */
    public function render(array $variables = []): array
    {
        $title = $this->renderTemplate($this->title_template, $variables);
        $message = $this->renderTemplate($this->message_template, $variables);

        return [
            'title' => $title,
            'message' => $message,
            'type' => $this->type,
            'priority' => $this->priority,
        ];
    }

    /**
     * Render a template string with variables.
     */
    private function renderTemplate(string $template, array $variables): string
    {
        $rendered = $template;

        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $rendered = str_replace($placeholder, $value, $rendered);
        }

        return $rendered;
    }

    /**
     * Get available variables for this template.
     */
    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Validate that all required variables are provided.
     */
    public function validateVariables(array $variables): array
    {
        $errors = [];
        $requiredVariables = $this->getAvailableVariables();

        foreach ($requiredVariables as $variable) {
            if ($variable['required'] && !isset($variables[$variable['name']])) {
                $errors[] = "Required variable '{$variable['name']}' is missing";
            }
        }

        return $errors;
    }

    /**
     * Create a notification from this template.
     */
    public function createNotification(User $user, array $variables = []): Notification
    {
        $errors = $this->validateVariables($variables);
        if (!empty($errors)) {
            throw new \InvalidArgumentException('Template validation failed: ' . implode(', ', $errors));
        }

        $rendered = $this->render($variables);

        return Notification::create([
            'user_id' => $user->id,
            'building_id' => $this->building_id,
            'type' => $rendered['type'],
            'title' => $rendered['title'],
            'message' => $rendered['message'],
            'priority' => $rendered['priority'],
            'data' => [
                'template_id' => $this->id,
                'template_variables' => $variables,
            ],
        ]);
    }

    /**
     * Get default templates for common notification types.
     */
    public static function getDefaultTemplates(): array
    {
        return [
            [
                'name' => 'Payment Reminder',
                'description' => 'Remind users about upcoming payments',
                'type' => Notification::TYPE_PAYMENT_REMINDER,
                'title_template' => 'Payment Reminder: {{expense_type}}',
                'message_template' => 'Dear {{user_name}}, you have a payment due for {{expense_type}}. Amount: {{amount}}. Due date: {{due_date}}.',
                'variables' => [
                    ['name' => 'user_name', 'required' => true, 'description' => 'User\'s name'],
                    ['name' => 'expense_type', 'required' => true, 'description' => 'Type of expense'],
                    ['name' => 'amount', 'required' => true, 'description' => 'Payment amount'],
                    ['name' => 'due_date', 'required' => true, 'description' => 'Payment due date'],
                ],
                'priority' => Notification::PRIORITY_HIGH,
            ],
            [
                'name' => 'General Announcement',
                'description' => 'General building announcements',
                'type' => Notification::TYPE_GENERAL_ANNOUNCEMENT,
                'title_template' => '{{title}}',
                'message_template' => '{{message}}',
                'variables' => [
                    ['name' => 'title', 'required' => true, 'description' => 'Announcement title'],
                    ['name' => 'message', 'required' => true, 'description' => 'Announcement message'],
                ],
                'priority' => Notification::PRIORITY_MEDIUM,
            ],
            [
                'name' => 'Overdue Payment',
                'description' => 'Notify users about overdue payments',
                'type' => Notification::TYPE_OVERDUE_PAYMENT,
                'title_template' => 'Overdue Payment: {{expense_type}}',
                'message_template' => 'Dear {{user_name}}, your payment for {{expense_type}} is {{days_overdue}} days overdue. Amount: {{amount}}. Please pay as soon as possible.',
                'variables' => [
                    ['name' => 'user_name', 'required' => true, 'description' => 'User\'s name'],
                    ['name' => 'expense_type', 'required' => true, 'description' => 'Type of expense'],
                    ['name' => 'amount', 'required' => true, 'description' => 'Payment amount'],
                    ['name' => 'days_overdue', 'required' => true, 'description' => 'Number of days overdue'],
                ],
                'priority' => Notification::PRIORITY_HIGH,
            ],
        ];
    }

    /**
     * Create default templates for a building.
     */
    public static function createDefaultTemplatesForBuilding(Building $building, User $creator): array
    {
        $templates = [];
        $defaultTemplates = self::getDefaultTemplates();

        foreach ($defaultTemplates as $templateData) {
            $templates[] = self::create([
                'building_id' => $building->id,
                'created_by' => $creator->id,
                'is_active' => true,
                ...$templateData,
            ]);
        }

        return $templates;
    }
}
