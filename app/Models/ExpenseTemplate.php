<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class ExpenseTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'building_id',
        'expense_type_id',
        'name',
        'description',
        'amount',
        'currency',
        'recurrence_type',
        'recurrence_day',
        'recurrence_month',
        'due_days_after',
        'notes_template',
        'is_active',
        'auto_generate',
        'last_generated_at',
        'next_generation_date',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'is_active' => 'boolean',
        'auto_generate' => 'boolean',
        'last_generated_at' => 'datetime',
        'next_generation_date' => 'date',
    ];

    /**
     * Recurrence type constants
     */
    const RECURRENCE_MONTHLY = 'monthly';
    const RECURRENCE_QUARTERLY = 'quarterly';
    const RECURRENCE_YEARLY = 'yearly';
    const RECURRENCE_CUSTOM = 'custom';

    /**
     * Get the building that owns the template.
     */
    public function building(): BelongsTo
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get the expense type for this template.
     */
    public function expenseType(): BelongsTo
    {
        return $this->belongsTo(ExpenseType::class);
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the expenses generated from this template.
     */
    public function generatedExpenses(): HasMany
    {
        return $this->hasMany(Expense::class, 'template_id');
    }

    /**
     * Scope to get only active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get templates that should auto-generate.
     */
    public function scopeAutoGenerate($query)
    {
        return $query->where('auto_generate', true);
    }

    /**
     * Scope to get templates due for generation.
     */
    public function scopeDueForGeneration($query)
    {
        return $query->where('next_generation_date', '<=', now()->toDateString())
                    ->orWhereNull('next_generation_date');
    }

    /**
     * Scope to filter by building.
     */
    public function scopeForBuilding($query, $buildingId)
    {
        return $query->where('building_id', $buildingId);
    }

    /**
     * Get available recurrence types.
     */
    public static function getRecurrenceTypes(): array
    {
        return [
            self::RECURRENCE_MONTHLY,
            self::RECURRENCE_QUARTERLY,
            self::RECURRENCE_YEARLY,
            self::RECURRENCE_CUSTOM,
        ];
    }

    /**
     * Calculate the next generation date based on recurrence settings.
     */
    public function calculateNextGenerationDate(?Carbon $fromDate = null): Carbon
    {
        $fromDate = $fromDate ?? now();

        switch ($this->recurrence_type) {
            case self::RECURRENCE_MONTHLY:
                $nextDate = $fromDate->copy()->addMonth();
                if ($this->recurrence_day) {
                    $nextDate->day($this->recurrence_day);
                }
                break;

            case self::RECURRENCE_QUARTERLY:
                $nextDate = $fromDate->copy()->addMonths(3);
                if ($this->recurrence_day) {
                    $nextDate->day($this->recurrence_day);
                }
                break;

            case self::RECURRENCE_YEARLY:
                $nextDate = $fromDate->copy()->addYear();
                if ($this->recurrence_month) {
                    $nextDate->month($this->recurrence_month);
                }
                if ($this->recurrence_day) {
                    $nextDate->day($this->recurrence_day);
                }
                break;

            default:
                $nextDate = $fromDate->copy()->addMonth(); // Default to monthly
                break;
        }

        return $nextDate;
    }

    /**
     * Generate expenses for all users in the building.
     */
    public function generateExpenses(?Carbon $forDate = null): array
    {
        $forDate = $forDate ?? now();
        $dueDate = $forDate->copy()->addDays($this->due_days_after ?? 30);

        // Get all active neighbors in the building
        $neighbors = User::where('building_id', $this->building_id)
            ->where('role', 'neighbor')
            ->where('is_active', true) // Only include active users
            ->get();

        $generatedExpenses = [];

        foreach ($neighbors as $neighbor) {
            // Check if expense already exists for this period
            $existingExpense = Expense::where('template_id', $this->id)
                ->where('user_id', $neighbor->id)
                ->where('month', $forDate->format('m'))
                ->where('year', $forDate->format('Y'))
                ->first();

            if ($existingExpense) {
                continue; // Skip if already generated
            }

            // Render notes template
            $notes = $this->renderNotesTemplate([
                'user_name' => $neighbor->name,
                'apartment_number' => $neighbor->apartment_number,
                'month' => $forDate->format('F'),
                'year' => $forDate->format('Y'),
                'amount' => $this->amount,
            ]);

            $expense = Expense::create([
                'template_id' => $this->id,
                'expense_type_id' => $this->expense_type_id,
                'user_id' => $neighbor->id,
                'building_id' => $this->building_id,
                'amount' => $this->amount,
                'currency' => $this->currency ?? 'USD',
                'due_date' => $dueDate,
                'month' => $forDate->format('m'),
                'year' => $forDate->format('Y'),
                'notes' => $notes,
                'is_automatic' => true,
            ]);

            $generatedExpenses[] = $expense;
        }

        // Update template's last generation date and calculate next
        $this->last_generated_at = now();
        $this->next_generation_date = $this->calculateNextGenerationDate($forDate);
        $this->save();

        return $generatedExpenses;
    }

    /**
     * Render notes template with variables.
     */
    private function renderNotesTemplate(array $variables): string
    {
        $notes = $this->notes_template ?? 'Monthly expense for {{user_name}} - Apartment {{apartment_number}}';

        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $notes = str_replace($placeholder, $value, $notes);
        }

        return $notes;
    }

    /**
     * Get template statistics.
     */
    public function getStatistics(): array
    {
        $totalGenerated = $this->generatedExpenses()->count();
        $totalAmount = $this->generatedExpenses()->sum('amount');
        $lastGenerated = $this->generatedExpenses()->latest()->first();

        return [
            'total_expenses_generated' => $totalGenerated,
            'total_amount_generated' => $totalAmount,
            'last_expense_generated_at' => $lastGenerated?->created_at,
            'next_generation_date' => $this->next_generation_date,
            'is_due_for_generation' => $this->next_generation_date <= now()->toDateString(),
        ];
    }

    /**
     * Create default templates for a building.
     */
    public static function createDefaultTemplatesForBuilding(Building $building, User $creator): array
    {
        $templates = [];

        // Get or create expense types
        $maintenanceType = ExpenseType::firstOrCreate(
            ['name' => 'Monthly Maintenance'],
            ['description' => 'Monthly maintenance fee for all neighbors']
        );

        $subscriptionType = ExpenseType::firstOrCreate(
            ['name' => 'Monthly Subscription'],
            ['description' => 'Monthly subscription fee per building']
        );

        $defaultTemplates = [
            [
                'expense_type_id' => $maintenanceType->id,
                'name' => 'Monthly Maintenance Fee',
                'description' => 'Automatic monthly maintenance fee for all neighbors',
                'amount' => $building->monthly_fee ?? 50.00, // Updated default monthly fee
                'currency' => $building->currency ?? 'USD',
                'recurrence_type' => self::RECURRENCE_MONTHLY,
                'recurrence_day' => 1,
                'due_days_after' => 30,
                'notes_template' => 'Monthly maintenance fee for {{user_name}} - Apartment {{apartment_number}} for {{month}} {{year}}',
                'is_active' => true,
                'auto_generate' => true,
            ],
        ];

        foreach ($defaultTemplates as $templateData) {
            $template = self::create([
                'building_id' => $building->id,
                'created_by' => $creator->id,
                'next_generation_date' => now()->addMonth()->startOfMonth(),
                ...$templateData,
            ]);

            $templates[] = $template;
        }

        return $templates;
    }
}
