<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Building extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'city',
        'country',
        'postal_code',
        'description',
        'monthly_fee',
        'current_package_id',
        'package_updated_at',
        'currency',
    ];

    protected $casts = [
        'monthly_fee' => 'decimal:2',
        'package_updated_at' => 'datetime',
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function incomes()
    {
        return $this->hasMany(Income::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function fileAttachments()
    {
        return $this->hasMany(FileAttachment::class);
    }

    public function emailUsage()
    {
        return $this->hasMany(EmailUsage::class);
    }

    public function exportRequests()
    {
        return $this->hasMany(ExportRequest::class);
    }

    public function currentPackage()
    {
        return $this->belongsTo(Package::class, 'current_package_id');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class)->where('status', 'active');
    }

    /**
     * Get the current active subscription or create a default one.
     */
    public function getCurrentSubscription(): ?Subscription
    {
        return $this->activeSubscription()->first();
    }

    /**
     * Check if building has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->getCurrentSubscription() !== null;
    }

    /**
     * Get the effective package (from subscription or default).
     */
    public function getEffectivePackage(): ?Package
    {
        $subscription = $this->getCurrentSubscription();
        if ($subscription) {
            // If subscription is in grace period, still return the original package
            // but with some limitations (handled in middleware)
            if ($subscription->isInGracePeriod()) {
                return $subscription->package;
            }

            // If subscription is active or in trial, return the package
            if ($subscription->isActive() || $subscription->isInTrial()) {
                return $subscription->package;
            }
        }

        // Fallback to current package or free package
        if ($this->currentPackage) {
            return $this->currentPackage;
        }

        // Last resort: assign free package
        $freePackage = Package::where('slug', 'free')->first();
        if ($freePackage) {
            $this->update(['current_package_id' => $freePackage->id]);
            return $freePackage;
        }

        return null;
    }

    /**
     * Check if building can add more neighbors.
     */
    public function canAddNeighbors(int $count = 1): bool
    {
        $package = $this->getEffectivePackage();
        if (!$package || $package->hasUnlimitedNeighbors()) {
            return true;
        }

        $currentCount = $this->users()->count();
        return ($currentCount + $count) <= $package->max_neighbors;
    }

    /**
     * Check if building can use notifications.
     */
    public function canUseNotifications(): bool
    {
        $package = $this->getEffectivePackage();
        return $package && $package->notifications_enabled;
    }

    /**
     * Check if building can use email notifications.
     */
    public function canUseEmailNotifications(): bool
    {
        $package = $this->getEffectivePackage();
        return $package && $package->email_notifications_enabled;
    }

    /**
     * Check if building can use file attachments.
     */
    public function canUseFileAttachments(): bool
    {
        $package = $this->getEffectivePackage();
        return $package && $package->file_attachments_enabled;
    }

    /**
     * Get maximum file size allowed.
     */
    public function getMaxFileSize(): int
    {
        $package = $this->getEffectivePackage();
        return $package ? $package->getMaxFileSizeMb() : 0;
    }

    /**
     * Get maximum files per record allowed.
     */
    public function getMaxFilesPerRecord(): int
    {
        $package = $this->getEffectivePackage();
        return $package ? $package->getMaxFilesPerRecord() : 0;
    }

    /**
     * Update package and create/update subscription.
     */
    public function updatePackage(Package $package, string $billingCycle = 'monthly'): bool
    {
        $subscription = $this->getCurrentSubscription();

        if ($subscription) {
            // Update existing subscription
            $subscription->upgradeTo($package, $billingCycle);
        } else {
            // Create new subscription
            $this->subscriptions()->create([
                'package_id' => $package->id,
                'user_id' => auth()->id(),
                'status' => $package->isFree() ? 'active' : 'trial',
                'billing_cycle' => $billingCycle,
                'amount' => $package->getEffectivePrice($billingCycle),
                'starts_at' => now(),
                'ends_at' => now()->addDays($package->trial_days ?: 30),
                'trial_ends_at' => $package->trial_days ? now()->addDays($package->trial_days) : null,
            ]);
        }

        return $this->update([
            'current_package_id' => $package->id,
            'package_updated_at' => now(),
        ]);
    }

    /**
     * Get the currency settings for this building.
     */
    public function currencySettings()
    {
        return $this->hasOne(\App\Models\BuildingCurrencySetting::class);
    }

    /**
     * Get currency conversions for this building.
     */
    public function currencyConversions()
    {
        return $this->hasMany(\App\Models\CurrencyConversion::class);
    }

    /**
     * Get the primary currency for this building.
     */
    public function getPrimaryCurrency(): string
    {
        return $this->currencySettings?->primary_currency ?? $this->currency ?? 'USD';
    }

    /**
     * Get accepted currencies for this building.
     */
    public function getAcceptedCurrencies(): array
    {
        if ($this->currencySettings) {
            return $this->currencySettings->getAllAcceptedCurrencies();
        }

        return [$this->currency ?? 'USD'];
    }

    /**
     * Check if building accepts a specific currency.
     */
    public function acceptsCurrency(string $currency): bool
    {
        return in_array($currency, $this->getAcceptedCurrencies());
    }

    /**
     * Format amount according to building's settings.
     */
    public function formatAmount(float $amount, string $currency = null): string
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->formatAmount($amount, $currency ?? $this->getPrimaryCurrency(), $this);
    }

    public function packageChangeRequests()
    {
        return $this->hasMany(PackageChangeRequest::class);
    }

    public function pendingPackageChangeRequests()
    {
        return $this->packageChangeRequests()->where('status', 'pending');
    }

    public function hasPendingPackageChangeRequest(): bool
    {
        return $this->pendingPackageChangeRequests()->exists();
    }

    /**
     * Get SMS delivery logs for this building.
     */
    public function smsDeliveryLogs(): HasMany
    {
        return $this->hasMany(\App\Models\SmsDeliveryLog::class);
    }

    /**
     * Get SMS settings for this building.
     */
    public function smsSettings()
    {
        return $this->hasOne(\App\Models\BuildingSmsSettings::class);
    }
}
