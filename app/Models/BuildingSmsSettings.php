<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BuildingSmsSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'building_id',
        'sms_enabled',
        'sms_provider',
        'provider_config',
        'monthly_sms_limit',
        'sms_sent_this_month',
        'cost_per_sms',
        'notification_types',
        'require_user_opt_in',
        'default_country_code',
    ];

    protected $casts = [
        'sms_enabled' => 'boolean',
        'provider_config' => 'array',
        'monthly_sms_limit' => 'integer',
        'sms_sent_this_month' => 'integer',
        'cost_per_sms' => 'decimal:4',
        'notification_types' => 'array',
        'require_user_opt_in' => 'boolean',
    ];

    /**
     * Get the building this SMS settings belongs to.
     */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    /**
     * Get SMS delivery logs for this building.
     */
    public function deliveryLogs()
    {
        return $this->hasMany(SmsDeliveryLog::class, 'building_id', 'building_id');
    }

    /**
     * Get SMS usage statistics for this building.
     */
    public function usageStats()
    {
        return $this->hasMany(SmsUsageStats::class, 'building_id', 'building_id');
    }

    /**
     * Check if SMS is enabled for this building.
     */
    public function isSmsEnabled(): bool
    {
        return $this->sms_enabled;
    }

    /**
     * Check if building has reached SMS limit for this month.
     */
    public function hasReachedSmsLimit(): bool
    {
        return $this->sms_sent_this_month >= $this->monthly_sms_limit;
    }

    /**
     * Get remaining SMS count for this month.
     */
    public function getRemainingSmsCount(): int
    {
        return max(0, $this->monthly_sms_limit - $this->sms_sent_this_month);
    }

    /**
     * Check if a notification type is enabled for SMS.
     */
    public function isNotificationTypeEnabled(string $type): bool
    {
        $enabledTypes = $this->notification_types ?? [];
        return in_array($type, $enabledTypes);
    }

    /**
     * Increment SMS sent count.
     */
    public function incrementSmsSent(int $count = 1): void
    {
        $this->increment('sms_sent_this_month', $count);
    }

    /**
     * Reset monthly SMS count (called at the beginning of each month).
     */
    public function resetMonthlySmsCount(): void
    {
        $this->update(['sms_sent_this_month' => 0]);
    }

    /**
     * Get provider configuration.
     */
    public function getProviderConfig(): array
    {
        return $this->provider_config ?? [];
    }

    /**
     * Update provider configuration.
     */
    public function updateProviderConfig(array $config): void
    {
        $currentConfig = $this->getProviderConfig();
        $newConfig = array_merge($currentConfig, $config);
        $this->update(['provider_config' => $newConfig]);
    }

    /**
     * Get supported SMS providers.
     */
    public static function getSupportedProviders(): array
    {
        return [
            'twilio' => [
                'name' => 'Twilio',
                'description' => 'Twilio SMS service',
                'config_fields' => ['account_sid', 'auth_token', 'from_number'],
            ],
            'aws_sns' => [
                'name' => 'AWS SNS',
                'description' => 'Amazon Simple Notification Service',
                'config_fields' => ['access_key', 'secret_key', 'region'],
            ],
        ];
    }

    /**
     * Get default notification types for SMS.
     */
    public static function getDefaultNotificationTypes(): array
    {
        return [
            Notification::TYPE_PAYMENT_REMINDER,
            Notification::TYPE_OVERDUE_PAYMENT,
            Notification::TYPE_GENERAL_ANNOUNCEMENT,
        ];
    }

    /**
     * Get or create SMS settings for a building.
     */
    public static function getForBuilding(Building $building): self
    {
        return self::firstOrCreate(
            ['building_id' => $building->id],
            [
                'sms_enabled' => false,
                'sms_provider' => 'twilio',
                'monthly_sms_limit' => 100,
                'sms_sent_this_month' => 0,
                'cost_per_sms' => 0.0075,
                'notification_types' => self::getDefaultNotificationTypes(),
                'require_user_opt_in' => true,
                'default_country_code' => '+1',
            ]
        );
    }

    /**
     * Get SMS usage summary for the current month.
     */
    public function getCurrentMonthUsage(): array
    {
        $currentMonth = now()->format('Y-m');
        
        $stats = $this->usageStats()
            ->whereRaw('DATE_FORMAT(date, "%Y-%m") = ?', [$currentMonth])
            ->selectRaw('
                SUM(sms_sent) as total_sent,
                SUM(sms_delivered) as total_delivered,
                SUM(sms_failed) as total_failed,
                SUM(total_cost) as total_cost
            ')
            ->first();

        return [
            'sent' => $stats->total_sent ?? 0,
            'delivered' => $stats->total_delivered ?? 0,
            'failed' => $stats->total_failed ?? 0,
            'cost' => $stats->total_cost ?? 0,
            'limit' => $this->monthly_sms_limit,
            'remaining' => $this->getRemainingSmsCount(),
            'percentage_used' => $this->monthly_sms_limit > 0 
                ? round(($this->sms_sent_this_month / $this->monthly_sms_limit) * 100, 2) 
                : 0,
        ];
    }

    /**
     * Check if provider is properly configured.
     */
    public function isProviderConfigured(): bool
    {
        $config = $this->getProviderConfig();
        $providers = self::getSupportedProviders();
        
        if (!isset($providers[$this->sms_provider])) {
            return false;
        }

        $requiredFields = $providers[$this->sms_provider]['config_fields'];
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get estimated cost for sending SMS count.
     */
    public function getEstimatedCost(int $smsCount): float
    {
        return $smsCount * $this->cost_per_sms;
    }

    /**
     * Check if building can send SMS (enabled, configured, within limits).
     */
    public function canSendSms(int $count = 1): bool
    {
        return $this->isSmsEnabled() 
            && $this->isProviderConfigured() 
            && ($this->sms_sent_this_month + $count) <= $this->monthly_sms_limit;
    }

    /**
     * Get SMS delivery rate for the current month.
     */
    public function getDeliveryRate(): float
    {
        $usage = $this->getCurrentMonthUsage();
        
        if ($usage['sent'] == 0) {
            return 0;
        }

        return round(($usage['delivered'] / $usage['sent']) * 100, 2);
    }

    /**
     * Enable SMS for specific notification types.
     */
    public function enableNotificationTypes(array $types): void
    {
        $currentTypes = $this->notification_types ?? [];
        $newTypes = array_unique(array_merge($currentTypes, $types));
        $this->update(['notification_types' => $newTypes]);
    }

    /**
     * Disable SMS for specific notification types.
     */
    public function disableNotificationTypes(array $types): void
    {
        $currentTypes = $this->notification_types ?? [];
        $newTypes = array_diff($currentTypes, $types);
        $this->update(['notification_types' => array_values($newTypes)]);
    }
}
