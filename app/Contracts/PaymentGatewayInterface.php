<?php

namespace App\Contracts;

interface PaymentGatewayInterface
{
    /**
     * Create a payment for the given amount
     *
     * @param float $amount
     * @param string $currency
     * @param string $description
     * @param array $metadata
     * @return array Payment data with approval_url
     */
    public function createPayment(float $amount, string $currency, string $description, array $metadata = []): array;

    /**
     * Execute/complete a payment
     *
     * @param string $paymentId
     * @param string $payerId
     * @return array Payment result
     */
    public function executePayment(string $paymentId, string $payerId): array;

    /**
     * Get payment details
     *
     * @param string $paymentId
     * @return array Payment details
     */
    public function getPayment(string $paymentId): array;

    /**
     * Cancel a payment
     *
     * @param string $paymentId
     * @return array Cancellation result
     */
    public function cancelPayment(string $paymentId): array;

    /**
     * Get the gateway name
     *
     * @return string
     */
    public function getGatewayName(): string;

    /**
     * Get supported currencies
     *
     * @return array
     */
    public function getSupportedCurrencies(): array;

    /**
     * Check if gateway is available in the given country
     *
     * @param string $countryCode
     * @return bool
     */
    public function isAvailableInCountry(string $countryCode): bool;

    /**
     * Format amount according to gateway requirements
     *
     * @param float $amount
     * @return string
     */
    public function formatAmount(float $amount): string;
}
