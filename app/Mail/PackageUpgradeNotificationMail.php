<?php

namespace App\Mail;

use App\Models\PackageChangeRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PackageUpgradeNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public PackageChangeRequest $packageRequest;
    public string $status;

    public function __construct(PackageChangeRequest $packageRequest, string $status)
    {
        $this->packageRequest = $packageRequest;
        $this->status = $status;
    }

    public function build()
    {
        $subject = $this->status === 'approved' 
            ? 'Package Upgrade Approved' 
            : 'Package Upgrade Request Update';

        // Determine change type based on package prices
        $oldPrice = $this->packageRequest->currentPackage?->price ?? 0;
        $newPrice = $this->packageRequest->requestedPackage->price;
        
        if ($newPrice > $oldPrice) {
            $changeType = 'upgrade';
        } elseif ($newPrice < $oldPrice) {
            $changeType = 'downgrade';
        } else {
            $changeType = 'change';
        }

        return $this->subject($subject)
                    ->view('emails.package-upgrade-notification')
                    ->with([
                        'changeType' => $changeType,
                        'admin' => $this->packageRequest->requestedBy,
                        'building' => $this->packageRequest->building,
                        'oldPackage' => $this->packageRequest->currentPackage ?? $this->packageRequest->requestedPackage,
                        'newPackage' => $this->packageRequest->requestedPackage,
                        'subscription' => $this->packageRequest->building->getCurrentSubscription(),
                        'status' => $this->status,
                    ]);
    }
}
