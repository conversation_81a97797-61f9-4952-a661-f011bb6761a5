<?php

namespace App\Mail;

use App\Models\Building;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

class MonthlyFinancialSummaryMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Building $building;
    public User $admin;
    public array $summaryData;
    public string $month;
    public string $year;

    /**
     * Create a new message instance.
     */
    public function __construct(Building $building, User $admin, array $summaryData, string $month, string $year)
    {
        $this->building = $building;
        $this->admin = $admin;
        $this->summaryData = $summaryData;
        $this->month = $month;
        $this->year = $year;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $monthName = Carbon::createFromDate($this->year, $this->month, 1)->format('F Y');
        
        return new Envelope(
            subject: "Monthly Financial Summary - {$monthName} - Amara Building Management",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.monthly-financial-summary',
            with: [
                'building' => $this->building,
                'admin' => $this->admin,
                'summaryData' => $this->summaryData,
                'month' => $this->month,
                'year' => $this->year,
                'monthName' => Carbon::createFromDate($this->year, $this->month, 1)->format('F Y'),
                'monthNameAr' => Carbon::createFromDate($this->year, $this->month, 1)->locale('ar')->format('F Y'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
