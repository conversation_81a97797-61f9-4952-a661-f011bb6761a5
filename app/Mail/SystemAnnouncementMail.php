<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SystemAnnouncementMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public User $user;
    public string $title;
    public string $message;
    public string $announcementType;
    public array $additionalData;

    /**
     * Create a new message instance.
     */
    public function __construct(
        User $user, 
        string $title, 
        string $message, 
        string $announcementType = 'general',
        array $additionalData = []
    ) {
        $this->user = $user;
        $this->title = $title;
        $this->message = $message;
        $this->announcementType = $announcementType;
        $this->additionalData = $additionalData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->title . ' - Amara Building Management',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.system-announcement',
            with: [
                'user' => $this->user,
                'title' => $this->title,
                'message' => $this->message,
                'announcementType' => $this->announcementType,
                'additionalData' => $this->additionalData,
                'building' => $this->user->building,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
