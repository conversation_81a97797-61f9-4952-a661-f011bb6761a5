#!/usr/bin/env node

/**
 * Debug Service Worker Communication
 * This script helps identify and resolve service worker message port issues
 */

import { sendMessageToServiceWorker, getServiceWorkerVersion, clearServiceWorkerCaches } from './resources/js/utils/serviceWorker.js';

// Simple test functions for browser console
const debugFunctions = `
// Service Worker Debug Functions
// Copy and paste these into your browser console to test service worker communication

// Test basic service worker communication
async function testServiceWorkerCommunication() {
    console.log('🧪 Testing Service Worker Communication...');
    
    try {
        // Check if service worker is registered
        const registration = await navigator.serviceWorker.getRegistration();
        if (!registration) {
            console.error('❌ No service worker registered');
            return false;
        }
        
        console.log('✅ Service worker registered:', registration);
        
        // Test message sending
        const messageChannel = new MessageChannel();
        const testMessage = { type: 'GET_VERSION' };
        
        const messagePromise = new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Message timeout'));
            }, 5000);
            
            messageChannel.port1.onmessage = (event) => {
                clearTimeout(timeout);
                console.log('✅ Received response:', event.data);
                resolve(event.data);
            };
            
            messageChannel.port1.onerror = (error) => {
                clearTimeout(timeout);
                console.error('❌ Message error:', error);
                reject(error);
            };
        });
        
        if (registration.active) {
            registration.active.postMessage(testMessage, [messageChannel.port2]);
            const response = await messagePromise;
            console.log('✅ Service worker communication test passed');
            return true;
        } else {
            console.error('❌ No active service worker');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Service worker communication test failed:', error);
        return false;
    }
}

// Test service worker update mechanism
async function testServiceWorkerUpdate() {
    console.log('🔄 Testing Service Worker Update...');
    
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (!registration) {
            console.error('❌ No service worker registered');
            return;
        }
        
        // Force update check
        await registration.update();
        console.log('✅ Update check completed');
        
        if (registration.waiting) {
            console.log('🔄 New service worker waiting, testing skip waiting...');
            
            // Test skip waiting message
            try {
                registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                console.log('✅ Skip waiting message sent');
            } catch (error) {
                console.error('❌ Failed to send skip waiting message:', error);
            }
        } else {
            console.log('ℹ️  No service worker waiting');
        }
        
    } catch (error) {
        console.error('❌ Service worker update test failed:', error);
    }
}

// Clear all service worker caches
async function clearAllCaches() {
    console.log('🗑️  Clearing all caches...');
    
    try {
        const cacheNames = await caches.keys();
        console.log('📦 Found caches:', cacheNames);
        
        await Promise.all(
            cacheNames.map(cacheName => {
                console.log('🗑️  Deleting cache:', cacheName);
                return caches.delete(cacheName);
            })
        );
        
        console.log('✅ All caches cleared');
        
        // Also try to clear via service worker
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration && registration.active) {
            const messageChannel = new MessageChannel();
            
            const clearPromise = new Promise((resolve) => {
                messageChannel.port1.onmessage = (event) => {
                    console.log('✅ Service worker cache clear response:', event.data);
                    resolve(event.data);
                };
                
                setTimeout(() => {
                    console.log('⏰ Service worker cache clear timeout');
                    resolve(null);
                }, 3000);
            });
            
            registration.active.postMessage({ type: 'CLEAR_CACHE' }, [messageChannel.port2]);
            await clearPromise;
        }
        
    } catch (error) {
        console.error('❌ Failed to clear caches:', error);
    }
}

// Unregister service worker completely
async function unregisterServiceWorker() {
    console.log('🚫 Unregistering service worker...');
    
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
            const result = await registration.unregister();
            console.log('✅ Service worker unregistered:', result);
            
            // Clear all caches after unregistering
            await clearAllCaches();
            
            console.log('✅ Complete cleanup finished. Refresh the page to start fresh.');
        } else {
            console.log('ℹ️  No service worker to unregister');
        }
    } catch (error) {
        console.error('❌ Failed to unregister service worker:', error);
    }
}

// Get service worker status
async function getServiceWorkerStatus() {
    console.log('📊 Service Worker Status:');
    
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (!registration) {
            console.log('❌ No service worker registered');
            return;
        }
        
        console.log('📋 Registration:', registration);
        console.log('🔄 Installing:', registration.installing);
        console.log('⏳ Waiting:', registration.waiting);
        console.log('✅ Active:', registration.active);
        console.log('🔄 Update found:', registration.updatefound);
        
        if (registration.active) {
            console.log('📦 Active SW script URL:', registration.active.scriptURL);
            console.log('🔄 Active SW state:', registration.active.state);
        }
        
        // Check caches
        const cacheNames = await caches.keys();
        console.log('📦 Available caches:', cacheNames);
        
    } catch (error) {
        console.error('❌ Failed to get service worker status:', error);
    }
}

// Run all tests
async function runAllTests() {
    console.log('🧪 Running all Service Worker tests...');
    
    await getServiceWorkerStatus();
    await testServiceWorkerCommunication();
    await testServiceWorkerUpdate();
    
    console.log('✅ All tests completed');
}

// Export functions for console use
window.swDebug = {
    testCommunication: testServiceWorkerCommunication,
    testUpdate: testServiceWorkerUpdate,
    clearCaches: clearAllCaches,
    unregister: unregisterServiceWorker,
    getStatus: getServiceWorkerStatus,
    runAllTests: runAllTests
};

console.log('🛠️  Service Worker debug functions loaded!');
console.log('Use: swDebug.runAllTests() to run all tests');
console.log('Available functions:', Object.keys(window.swDebug));
`;

console.log('🛠️  Service Worker Debug Utility');
console.log('================================');
console.log('');
console.log('Copy and paste the following code into your browser console to debug service worker issues:');
console.log('');
console.log(debugFunctions);
console.log('');
console.log('📋 Quick Commands:');
console.log('- swDebug.runAllTests()     - Run all diagnostic tests');
console.log('- swDebug.testCommunication() - Test message port communication');
console.log('- swDebug.getStatus()       - Get current service worker status');
console.log('- swDebug.clearCaches()     - Clear all caches');
console.log('- swDebug.unregister()      - Completely remove service worker');
