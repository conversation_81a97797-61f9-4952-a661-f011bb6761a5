<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    |
    | This option controls the default payment gateway that will be used
    | when no specific gateway is requested. You can change this to any
    | of the supported gateways.
    |
    */
    'default_gateway' => env('PAYMENT_DEFAULT_GATEWAY', 'paypal'),

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the settings for each payment gateway.
    | Each gateway can have different settings based on their requirements.
    |
    */
    'gateways' => [
        'paypal' => [
            'enabled' => env('PAYPAL_ENABLED', true),
            'mode' => env('PAYPAL_MODE', 'sandbox'),
            'sandbox' => [
                'client_id' => env('PAYPAL_SANDBOX_CLIENT_ID', ''),
                'client_secret' => env('PAYPAL_SANDBOX_CLIENT_SECRET', ''),
            ],
            'live' => [
                'client_id' => env('PAYPAL_LIVE_CLIENT_ID', ''),
                'client_secret' => env('PAYPAL_LIVE_CLIENT_SECRET', ''),
            ],
            'currency' => env('PAYPAL_CURRENCY', 'USD'),
        ],

        'amazon' => [
            'enabled' => env('AMAZON_PAYMENTS_ENABLED', false),
            'sandbox_mode' => env('AMAZON_PAYMENTS_SANDBOX', true),
            'merchant_identifier' => env('AMAZON_PAYMENTS_MERCHANT_ID', ''),
            'access_code' => env('AMAZON_PAYMENTS_ACCESS_CODE', ''),
            'sha_request_phrase' => env('AMAZON_PAYMENTS_SHA_REQUEST', ''),
            'sha_response_phrase' => env('AMAZON_PAYMENTS_SHA_RESPONSE', ''),
            'currency' => env('AMAZON_PAYMENTS_CURRENCY', 'JOD'),
        ],

        // Add more gateways here as needed
        'stripe' => [
            'enabled' => env('STRIPE_ENABLED', false),
            'public_key' => env('STRIPE_PUBLIC_KEY', ''),
            'secret_key' => env('STRIPE_SECRET_KEY', ''),
            'webhook_secret' => env('STRIPE_WEBHOOK_SECRET', ''),
            'currency' => env('STRIPE_CURRENCY', 'USD'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Country-Specific Gateway Preferences
    |--------------------------------------------------------------------------
    |
    | Define which payment gateways should be prioritized for specific countries.
    | This helps optimize the payment experience for users in different regions.
    |
    */
    'country_preferences' => [
        // MENA region - prioritize Amazon Payment Services
        'JO' => ['amazon', 'paypal'],
        'AE' => ['amazon', 'paypal'],
        'SA' => ['amazon', 'paypal'],
        'KW' => ['amazon', 'paypal'],
        'BH' => ['amazon', 'paypal'],
        'OM' => ['amazon', 'paypal'],
        'QA' => ['amazon', 'paypal'],
        'EG' => ['amazon', 'paypal'],
        'LB' => ['amazon', 'paypal'],
        
        // North America and Europe - prioritize PayPal/Stripe
        'US' => ['paypal', 'stripe'],
        'CA' => ['paypal', 'stripe'],
        'GB' => ['paypal', 'stripe'],
        'DE' => ['paypal', 'stripe'],
        'FR' => ['paypal', 'stripe'],
        
        // Default fallback
        'default' => ['paypal', 'amazon', 'stripe']
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Settings
    |--------------------------------------------------------------------------
    |
    | Configure currency preferences and conversion settings.
    |
    */
    'currencies' => [
        'default' => env('PAYMENT_DEFAULT_CURRENCY', 'USD'),
        'supported' => [
            'USD', 'JOD'
        ],
        'country_defaults' => [
            'JO' => 'JOD',
            'US' => 'USD',
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    |
    | General payment configuration settings.
    |
    */
    'monthly_subscription' => [
        'amount' => env('PAYMENT_MONTHLY_FEE', null), // Amount determined by selected package
        'currency' => env('PAYMENT_MONTHLY_CURRENCY', 'USD'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Settings
    |--------------------------------------------------------------------------
    |
    | Configure webhook endpoints for payment notifications.
    |
    */
    'webhooks' => [
        'paypal' => [
            'url' => env('PAYPAL_WEBHOOK_URL', '/webhooks/paypal'),
            'events' => ['PAYMENT.SALE.COMPLETED', 'PAYMENT.SALE.DENIED']
        ],
        'amazon' => [
            'url' => env('AMAZON_WEBHOOK_URL', '/webhooks/amazon'),
        ],
        'stripe' => [
            'url' => env('STRIPE_WEBHOOK_URL', '/webhooks/stripe'),
            'events' => ['payment_intent.succeeded', 'payment_intent.payment_failed']
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Configure payment gateway logging settings.
    |
    */
    'logging' => [
        'enabled' => env('PAYMENT_LOGGING_ENABLED', true),
        'level' => env('PAYMENT_LOG_LEVEL', 'info'),
        'channel' => env('PAYMENT_LOG_CHANNEL', 'single'),
    ]
];
