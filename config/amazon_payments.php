<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Amazon Payment Services Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Amazon Payment Services (formerly PayFort)
    | This is the recommended payment gateway for MENA region including Jordan.
    |
    */

    'merchant_identifier' => env('AMAZON_PAYMENTS_MERCHANT_ID', ''),
    'access_code' => env('AMAZON_PAYMENTS_ACCESS_CODE', ''),
    'sha_request_phrase' => env('AMAZON_PAYMENTS_SHA_REQUEST', ''),
    'sha_response_phrase' => env('AMAZON_PAYMENTS_SHA_RESPONSE', ''),
    'sandbox_mode' => env('AMAZON_PAYMENTS_SANDBOX', true),
    'currency' => env('AMAZON_PAYMENTS_CURRENCY', 'JOD'),

    /*
    |--------------------------------------------------------------------------
    | API Endpoints
    |--------------------------------------------------------------------------
    */
    'endpoints' => [
        'sandbox' => [
            'payment_api' => 'https://sbcheckout.payfort.com/FortAPI/paymentApi',
            'payment_page' => 'https://sbcheckout.payfort.com/FortAPI/paymentPage',
        ],
        'live' => [
            'payment_api' => 'https://checkout.payfort.com/FortAPI/paymentApi',
            'payment_page' => 'https://checkout.payfort.com/FortAPI/paymentPage',
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    */
    'supported_currencies' => [
        'JOD', 'USD'
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Decimal Places
    |--------------------------------------------------------------------------
    | Some currencies use 3 decimal places instead of 2
    */
    'currency_decimals' => [
        'KWD' => 3,
        'BHD' => 3,
        'OMR' => 3,
        'JOD' => 3,
        // All others default to 2
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Commands
    |--------------------------------------------------------------------------
    */
    'commands' => [
        'authorization' => 'AUTHORIZATION',
        'purchase' => 'PURCHASE',
        'capture' => 'CAPTURE',
        'refund' => 'REFUND',
        'void' => 'VOID_AUTHORIZATION',
        'check_status' => 'PAYMENT_STATUS'
    ],

    /*
    |--------------------------------------------------------------------------
    | Response Codes
    |--------------------------------------------------------------------------
    */
    'response_codes' => [
        'success' => ['00000', '02000', '14000'],
        'pending' => ['20064'],
        'declined' => ['05000', '10000', '11000', '12000', '13000'],
    ]
];
