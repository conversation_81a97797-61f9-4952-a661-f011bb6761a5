<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Subscription Grace Period
    |--------------------------------------------------------------------------
    |
    | Number of days after subscription expiration before downgrading to free package.
    | This allows for payment processing delays and gives users time to renew.
    |
    */
    'grace_period_days' => env('SUBSCRIPTION_GRACE_PERIOD_DAYS', 7),

    /*
    |--------------------------------------------------------------------------
    | Expiration Warning Days
    |--------------------------------------------------------------------------
    |
    | Days before expiration to send warning notifications.
    | Multiple warnings will be sent at these intervals.
    |
    */
    'warning_days' => [
        30, // 30 days before
        14, // 2 weeks before
        7,  // 1 week before
        3,  // 3 days before
        1,  // 1 day before
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configure how and when to send subscription-related notifications.
    |
    */
    'notifications' => [
        'enabled' => env('SUBSCRIPTION_NOTIFICATIONS_ENABLED', true),
        'channels' => [
            'email' => true,
            'database' => true,
            'sms' => false, // Enable if SMS service is configured
        ],
        'send_time' => '09:00', // Time to send daily notifications
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-Renewal Settings
    |--------------------------------------------------------------------------
    |
    | Settings for automatic subscription renewal attempts.
    |
    */
    'auto_renewal' => [
        'enabled' => env('SUBSCRIPTION_AUTO_RENEWAL_ENABLED', true),
        'retry_attempts' => 3,
        'retry_interval_days' => 3,
        'failure_grace_days' => 7,
    ],

    /*
    |--------------------------------------------------------------------------
    | Package Downgrade Settings
    |--------------------------------------------------------------------------
    |
    | Configure what happens when a subscription expires.
    |
    */
    'downgrade' => [
        'to_free_package' => true,
        'preserve_data' => true,
        'notify_admin' => true,
        'log_transitions' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Status Transitions
    |--------------------------------------------------------------------------
    |
    | Define valid status transitions and their rules.
    |
    */
    'status_transitions' => [
        'trial' => ['active', 'expired', 'cancelled'],
        'active' => ['expired', 'cancelled'],
        'expired' => ['active', 'cancelled'],
        'cancelled' => ['active'], // Allow reactivation
        'inactive' => ['active', 'cancelled'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    |
    | Settings for cleaning up old subscription data.
    |
    */
    'cleanup' => [
        'old_expired_days' => 365, // Keep expired subscriptions for 1 year
        'cancelled_retention_days' => 90, // Keep cancelled subscriptions for 3 months
        'log_retention_days' => 180, // Keep subscription logs for 6 months
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Limits During Grace Period
    |--------------------------------------------------------------------------
    |
    | Define which features remain available during grace period.
    |
    */
    'grace_period_features' => [
        'basic_access' => true,
        'data_export' => true,
        'new_uploads' => false,
        'new_users' => false,
        'premium_features' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Alerts
    |--------------------------------------------------------------------------
    |
    | Settings for monitoring subscription health and sending alerts.
    |
    */
    'monitoring' => [
        'enabled' => env('SUBSCRIPTION_MONITORING_ENABLED', true),
        'alert_thresholds' => [
            'high_expiration_rate' => 20, // Alert if >20% expire in a month
            'low_renewal_rate' => 70,     // Alert if <70% renewal rate
            'payment_failure_rate' => 10, // Alert if >10% payment failures
        ],
        'admin_emails' => [
            env('ADMIN_EMAIL', '<EMAIL>'),
        ],
    ],
];