<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Export Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for PDF and Excel export functionality optimized for
    | shared hosting environments with memory and resource constraints.
    |
    */

    'pdf' => [
        /*
        |--------------------------------------------------------------------------
        | PDF Engine
        |--------------------------------------------------------------------------
        |
        | The PDF engine to use. DomPDF is recommended for shared hosting
        | as it's lightweight and doesn't require external dependencies.
        |
        */
        'engine' => env('PDF_ENGINE', 'dompdf'),

        /*
        |--------------------------------------------------------------------------
        | DomPDF Configuration
        |--------------------------------------------------------------------------
        |
        | Configuration options for DomPDF optimized for shared hosting.
        |
        */
        'dompdf' => [
            'paper' => env('PDF_PAPER_SIZE', 'a4'),
            'orientation' => env('PDF_ORIENTATION', 'portrait'),
            'memory_limit' => env('PDF_MEMORY_LIMIT', '128M'),
            'time_limit' => env('PDF_TIME_LIMIT', 60),
            'enable_remote' => env('PDF_ENABLE_REMOTE', false),
            'enable_php' => env('PDF_ENABLE_PHP', false),
            'enable_javascript' => env('PDF_ENABLE_JAVASCRIPT', false),
            'enable_html5_parser' => env('PDF_ENABLE_HTML5_PARSER', true),
            'font_cache' => storage_path('app/dompdf/fonts/'),
            'temp_dir' => storage_path('app/dompdf/temp/'),
        ],
    ],

    'excel' => [
        /*
        |--------------------------------------------------------------------------
        | Excel Engine
        |--------------------------------------------------------------------------
        |
        | PhpSpreadsheet configuration optimized for shared hosting.
        |
        */
        'memory_limit' => env('EXCEL_MEMORY_LIMIT', '256M'),
        'time_limit' => env('EXCEL_TIME_LIMIT', 120),
        'chunk_size' => env('EXCEL_CHUNK_SIZE', 1000), // Process records in chunks
        'cache_method' => env('EXCEL_CACHE_METHOD', 'memory'), // memory, disk
        'temp_dir' => storage_path('app/excel/temp/'),
        
        /*
        |--------------------------------------------------------------------------
        | Excel Writer Options
        |--------------------------------------------------------------------------
        |
        | Options for Excel file generation to optimize memory usage.
        |
        */
        'writer_options' => [
            'pre_calculate_formulas' => false,
            'office_2003_compatibility' => false,
            'zip_class' => \PhpOffice\PhpSpreadsheet\Shared\File::class,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Export Limits
    |--------------------------------------------------------------------------
    |
    | Limits to prevent resource exhaustion on shared hosting.
    |
    */
    'limits' => [
        'max_records_per_export' => env('EXPORT_MAX_RECORDS', 10000),
        'max_file_size_mb' => env('EXPORT_MAX_FILE_SIZE', 50),
        'max_concurrent_exports' => env('EXPORT_MAX_CONCURRENT', 3),
        'export_timeout_minutes' => env('EXPORT_TIMEOUT_MINUTES', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Management
    |--------------------------------------------------------------------------
    |
    | Configuration for export file storage and cleanup.
    |
    */
    'storage' => [
        'disk' => env('EXPORT_STORAGE_DISK', 'local'),
        'path' => env('EXPORT_STORAGE_PATH', 'exports'),
        'cleanup_after_hours' => env('EXPORT_CLEANUP_HOURS', 24),
        'max_storage_mb' => env('EXPORT_MAX_STORAGE', 500),
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Queue settings for background export processing.
    |
    */
    'queue' => [
        'enabled' => env('EXPORT_QUEUE_ENABLED', true),
        'connection' => env('EXPORT_QUEUE_CONNECTION', 'database'),
        'queue_name' => env('EXPORT_QUEUE_NAME', 'exports'),
        'max_tries' => env('EXPORT_MAX_TRIES', 3),
        'retry_after' => env('EXPORT_RETRY_AFTER', 300), // 5 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Package-Based Restrictions
    |--------------------------------------------------------------------------
    |
    | Default export restrictions by package type.
    | These can be overridden in the packages table.
    |
    */
    'package_restrictions' => [
        'free' => [
            'exports_per_month' => 5,
            'max_records_per_export' => 100,
            'formats' => ['pdf'],
        ],
        'standard' => [
            'exports_per_month' => 50,
            'max_records_per_export' => 5000,
            'formats' => ['pdf', 'excel'],
        ],
        'premium' => [
            'exports_per_month' => null, // unlimited
            'max_records_per_export' => 10000,
            'formats' => ['pdf', 'excel'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Report Templates
    |--------------------------------------------------------------------------
    |
    | Available report templates and their configurations.
    |
    */
    'templates' => [
        'financial_summary' => [
            'name' => 'Financial Summary Report',
            'description' => 'Monthly financial summary with income and expenses',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => false,
        ],
        'expense_report' => [
            'name' => 'Expense Report',
            'description' => 'Detailed expense report with attachments info',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => true,
        ],
        'income_report' => [
            'name' => 'Income Collection Report',
            'description' => 'Income collection and payment tracking report',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => false,
        ],
        'building_expense_report' => [
            'name' => 'Building Expense Report',
            'description' => 'Detailed building expense report with types and dates',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => false,
        ],
        'neighbor_payments' => [
            'name' => 'Neighbor Payment History',
            'description' => 'Payment history for all neighbors',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => true,
        ],
        'building_summary' => [
            'name' => 'Building Financial Summary',
            'description' => 'Complete building financial overview',
            'formats' => ['pdf', 'excel'],
            'memory_intensive' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for export completion notifications.
    |
    */
    'notifications' => [
        'enabled' => env('EXPORT_NOTIFICATIONS_ENABLED', true),
        'email_on_completion' => env('EXPORT_EMAIL_ON_COMPLETION', true),
        'email_on_failure' => env('EXPORT_EMAIL_ON_FAILURE', true),
        'in_app_notifications' => env('EXPORT_IN_APP_NOTIFICATIONS', true),
    ],
];
