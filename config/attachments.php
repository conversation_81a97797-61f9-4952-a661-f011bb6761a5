<?php

return [

    /*
    |--------------------------------------------------------------------------
    | File Attachment Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for file attachments in the
    | application. These settings control file upload limits, allowed types,
    | and storage options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | File Upload Limits
    |--------------------------------------------------------------------------
    |
    | These settings control the maximum file size and number of files that
    | can be uploaded per record. Values are read from environment variables
    | to allow easy configuration across different environments.
    |
    */

    'limits' => [
        // Maximum file size in MB
        'max_file_size_mb' => env('FILE_MAX_SIZE_MB', 10),
        
        // Maximum number of files per record (expense/income)
        'max_files_per_record' => env('FILE_MAX_FILES_PER_RECORD', 5),
        
        // Maximum total storage per building in GB (null = unlimited)
        'max_storage_gb' => env('FILE_MAX_STORAGE_GB', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed File Types
    |--------------------------------------------------------------------------
    |
    | Define the allowed file extensions and their corresponding MIME types.
    | This helps ensure only safe file types can be uploaded.
    |
    */

    'allowed_extensions' => [
        'jpg', 'jpeg', 'png', 'gif', 'webp', // Images
        'pdf', // PDF
        'doc', 'docx', 'txt', 'rtf', // Documents
        'xls', 'xlsx', 'csv', // Spreadsheets
        'zip', 'rar', // Archives
    ],

    'mime_types' => [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'txt' => 'text/plain',
        'rtf' => 'application/rtf',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'csv' => 'text/csv',
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Configuration
    |--------------------------------------------------------------------------
    |
    | Configure where and how files are stored.
    |
    */

    'storage' => [
        // Default storage disk
        'disk' => env('FILE_STORAGE_DISK', 'local'),
        
        // Base directory for attachments
        'path' => env('FILE_STORAGE_PATH', 'attachments'),
        
        // Whether to organize files by date (Y/m structure)
        'organize_by_date' => env('FILE_ORGANIZE_BY_DATE', true),
        
        // File cleanup settings
        'cleanup_orphaned_files' => env('FILE_CLEANUP_ORPHANED', true),
        'cleanup_after_days' => env('FILE_CLEANUP_AFTER_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related configuration for file uploads.
    |
    */

    'security' => [
        // Whether to scan files for viruses (requires ClamAV or similar)
        'virus_scan' => env('FILE_VIRUS_SCAN', false),
        
        // Whether to validate file contents match extension
        'validate_file_contents' => env('FILE_VALIDATE_CONTENTS', true),
        
        // Maximum filename length
        'max_filename_length' => env('FILE_MAX_FILENAME_LENGTH', 255),
        
        // Whether to generate unique filenames
        'use_unique_filenames' => env('FILE_USE_UNIQUE_FILENAMES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize file handling performance.
    |
    */

    'performance' => [
        // Whether to generate thumbnails for images
        'generate_thumbnails' => env('FILE_GENERATE_THUMBNAILS', true),
        
        // Thumbnail sizes (width x height)
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600],
        ],
        
        // Image quality for thumbnails (1-100)
        'thumbnail_quality' => env('FILE_THUMBNAIL_QUALITY', 80),
        
        // Whether to compress uploaded images
        'compress_images' => env('FILE_COMPRESS_IMAGES', true),
        
        // Image compression quality (1-100)
        'image_quality' => env('FILE_IMAGE_QUALITY', 85),
    ],

];
