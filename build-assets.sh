#!/bin/bash

# Build frontend assets and fix manifest location
echo "Building frontend assets..."
npm run build

# Copy manifest to expected location
if [ -f "public/build/.vite/manifest.json" ]; then
    echo "Copying manifest to correct location..."
    cp public/build/.vite/manifest.json public/build/manifest.json
    echo "✅ Build complete! Manifest file is now available at public/build/manifest.json"
else
    echo "❌ Error: Manifest file not found at public/build/.vite/manifest.json"
    exit 1
fi
