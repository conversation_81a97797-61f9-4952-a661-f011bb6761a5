#!/bin/bash

echo "Testing Advanced Reporting API endpoints..."

# Get token
TOKEN=$(php artisan tinker --execute="echo App\Models\User::where('role', 'admin')->first()->createToken('test')->plainTextToken;")

echo "Token: ${TOKEN:0:20}..."

# Test stats endpoint
echo "Testing /api/advanced-reporting/stats..."
curl -s -X GET "http://localhost:8000/api/advanced-reporting/stats" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq .

echo -e "\nTesting /api/advanced-reporting/reports..."
curl -s -X GET "http://localhost:8000/api/advanced-reporting/reports" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq .

echo -e "\nTesting report generation..."
REPORT_ID=$(curl -s -X GET "http://localhost:8000/api/advanced-reporting/reports" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq -r '.data[0].id')

if [ "$REPORT_ID" != "null" ] && [ "$REPORT_ID" != "" ]; then
  echo "Testing report generation for ID: $REPORT_ID"
  curl -s -X POST "http://localhost:8000/api/advanced-reporting/reports/$REPORT_ID/generate" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" | jq .
else
  echo "No reports found to test generation"
fi
