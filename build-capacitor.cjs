#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Create dist directory if it doesn't exist
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Create a basic index.html for Capacitor
const indexHtml = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <title>لجنة العمارة</title>
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="لجنة العمارة">
    <meta name="description" content="Complete building committee management system for expenses, incomes, and neighbor management">
    <meta name="theme-color" content="#2563eb">
    <meta name="background-color" content="#ffffff">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="لجنة العمارة">
    
    <!-- Capacitor -->
    <script type="module" src="https://unpkg.com/@capacitor/core@latest/dist/capacitor.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .container {
            max-width: 400px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>لجنة العمارة</h1>
        <p>نظام إدارة شامل للجان العمارات لإدارة المصروفات والإيرادات والجيران</p>
        <div id="loading" style="display: none;">
            <div class="loading"></div>
            جاري التحميل...
        </div>
        <a href="#" id="launch-btn" class="btn" onclick="launchApp()">تشغيل التطبيق</a>
    </div>

    <script>
        function launchApp() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('launch-btn').style.display = 'none';
            
            // Redirect to the actual Laravel app
            setTimeout(() => {
                window.location.href = 'https://amaretna.com';
            }, 1000);
        }
        
        // Auto-launch after 2 seconds
        setTimeout(() => {
            launchApp();
        }, 2000);
        
        // Capacitor initialization
        if (window.Capacitor) {
            console.log('Running in Capacitor');
            
            // Handle back button
            document.addEventListener('ionBackButton', (ev) => {
                ev.detail.register(-1, () => {
                    if (window.location.pathname === '/') {
                        // Exit app
                        navigator.app.exitApp();
                    } else {
                        window.history.back();
                    }
                });
            });
        }
    </script>
</body>
</html>`;

// Write the index.html file
fs.writeFileSync(path.join(distDir, 'index.html'), indexHtml);

console.log('✅ Created dist/index.html for Capacitor');

// Copy manifest.json if it exists
const manifestSrc = path.join(__dirname, 'public', 'manifest.json');
const manifestDest = path.join(distDir, 'manifest.json');

if (fs.existsSync(manifestSrc)) {
    fs.copyFileSync(manifestSrc, manifestDest);
    console.log('✅ Copied manifest.json to dist/');
}

console.log('🚀 Ready for Capacitor build!');
