# عمارتنا (Amaretna) - Building Management System

<div align="center">
  <img src="public/images/logo.png" alt="Amaretna Logo" width="200"/>
  
  [![Laravel](https://img.shields.io/badge/Laravel-10.x-red.svg)](https://laravel.com)
  [![Vue.js](https://img.shields.io/badge/Vue.js-3.x-green.svg)](https://vuejs.org)
  [![PHP](https://img.shields.io/badge/PHP-8.1+-blue.svg)](https://php.net)
  [![License](https://img.shields.io/badge/License-Proprietary-yellow.svg)]()
</div>

## 📋 Table of Contents
- [About](#about)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [Contributing](#contributing)
- [Support](#support)
- [License](#license)

## 🏢 About

**عمارتنا (Amaretna)** is a comprehensive building management system designed to streamline residential building administration in Jordan and the Middle East. The platform provides tools for expense tracking, resident management, financial reporting, and communication between building administrators and residents.

### Key Benefits
- 🏠 **Centralized Management** - All building operations in one platform
- 💰 **Financial Transparency** - Clear expense and income tracking
- 📱 **Mobile-First Design** - Responsive design with PWA capabilities
- 🌐 **Bilingual Support** - Full Arabic and English localization
- 🔒 **Secure & Reliable** - Role-based access control and data security
- 📊 **Advanced Reporting** - Comprehensive financial analytics

## ✨ Features

### 🔐 User Management
- **Multi-Role System**: Super Admin, Admin, and Neighbor roles
- **Secure Authentication**: JWT-based authentication with role-based access
- **Profile Management**: Complete user profile and settings management
- **Building Assignment**: Automatic user-building association

### 💼 Administrative Features
- **Dashboard Analytics**: Real-time financial and operational insights
- **Expense Management**: Track and categorize all building expenses
- **Income Tracking**: Monitor payments and revenue streams
- **User Administration**: Manage residents and building staff
- **Advanced Reporting**: Generate detailed financial reports
- **Export Capabilities**: Export data in multiple formats

### 🏠 Resident Features
- **Personal Dashboard**: View personal expenses and payments
- **Balance Tracking**: Real-time outstanding balance monitoring
- **Payment History**: Complete transaction history
- **Profile Management**: Update personal information and preferences

### 📱 Communication & Notifications
- **Real-time Notifications**: Instant updates and alerts
- **SMS Integration**: Bulk SMS notifications to residents
- **Email Notifications**: Automated email communications
- **Notification Templates**: Customizable message templates

### 🌍 Localization & Accessibility
- **Bilingual Interface**: Complete Arabic and English support
- **RTL/LTR Layouts**: Proper right-to-left and left-to-right layouts
- **Cultural Localization**: Region-specific formatting and conventions
- **Mobile Optimization**: Touch-friendly mobile interface

## 🛠 Technology Stack

### Backend
- **Framework**: Laravel 10.x
- **Language**: PHP 8.1+
- **Database**: MySQL 8.0+
- **Authentication**: JWT (JSON Web Tokens)
- **API**: RESTful API architecture

### Frontend
- **Framework**: Vue.js 3.x
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Vuex/Pinia
- **HTTP Client**: Axios
- **Routing**: Vue Router

### Additional Technologies
- **Charts**: Chart.js for data visualization
- **Icons**: Heroicons and custom SVG icons
- **Notifications**: Real-time notification system
- **PWA**: Progressive Web App capabilities
- **SMS**: SMS gateway integration
- **Email**: Laravel Mail with queue support

## 🚀 Installation

### Prerequisites
- PHP 8.1 or higher
- Composer
- Node.js 16+ and npm/yarn
- MySQL 8.0+
- Web server (Apache/Nginx)

### Step 1: Clone the Repository
```bash
git clone https://github.com/your-username/lajnet-amara.git
cd lajnet-amara
```

### Step 2: Install PHP Dependencies
```bash
composer install
```

### Step 3: Install Node.js Dependencies
```bash
npm install
# or
yarn install
```

### Step 4: Environment Configuration
```bash
cp .env.example .env
php artisan key:generate
```

### Step 5: Database Setup
```bash
php artisan migrate
php artisan db:seed
```

### Step 6: Build Frontend Assets
```bash
npm run build
# or for development
npm run dev
```

### Step 7: Start the Application
```bash
php artisan serve
```

## ⚙️ Configuration

### Environment Variables
Configure the following in your `.env` file:

```env
# Application
APP_NAME="عمارتنا"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=amaretna
DB_USERNAME=your_username
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_jwt_secret

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>

# SMS Configuration (Optional)
SMS_GATEWAY_URL=your_sms_gateway
SMS_API_KEY=your_sms_api_key

# Payment Gateway (Optional)
PAYMENT_GATEWAY_KEY=your_payment_key
PAYMENT_GATEWAY_SECRET=your_payment_secret
```

### File Permissions
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

## 📖 Usage

### Default Accounts
After seeding, you can use these default accounts:

**Super Admin:**
- Email: `<EMAIL>`
- Password: `password`

**Admin:**
- Email: `<EMAIL>`
- Password: `password`

**Neighbor:**
- Email: `<EMAIL>`
- Password: `password`

### Creating Your First Building
1. Log in as Super Admin
2. Navigate to Building Management
3. Create a new building with admin user
4. Configure building settings and monthly fees
5. Add residents to the building

### Managing Expenses
1. Log in as Admin
2. Go to Expense Management
3. Create expense categories and templates
4. Generate monthly expenses for all residents
5. Track payments and outstanding balances

## 📚 API Documentation

The application provides a comprehensive RESTful API. Key endpoints include:

### Authentication
- `POST /api/login` - User authentication
- `POST /api/register` - User registration
- `POST /api/logout` - User logout

### Buildings
- `GET /api/buildings` - List buildings
- `POST /api/buildings` - Create building
- `PUT /api/buildings/{id}` - Update building
- `DELETE /api/buildings/{id}` - Delete building

### Expenses
- `GET /api/expenses` - List expenses
- `POST /api/expenses` - Create expense
- `PUT /api/expenses/{id}` - Update expense
- `DELETE /api/expenses/{id}` - Delete expense

### Users
- `GET /api/admin/users` - List users
- `POST /api/admin/users` - Create user
- `PUT /api/admin/users/{id}` - Update user
- `DELETE /api/admin/users/{id}` - Delete user

For complete API documentation, visit `/api/documentation` after installation.

## 🤝 Contributing

We welcome contributions to improve عمارتنا! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PSR-12 coding standards for PHP
- Use ESLint and Prettier for JavaScript/Vue.js
- Write tests for new features
- Update documentation as needed
- Ensure mobile responsiveness

## 📞 Support

### Contact Information
- **Email**: <EMAIL>
- **Phone**: +962 78 291 2391
- **Support Hours**: 24/7 availability
- **Website**: [عمارتنا Platform](https://amaretna.com)

### Getting Help
1. Check the [Frontend Features Documentation](FRONTEND_FEATURES_DOCUMENTATION.md)
2. Search existing issues on GitHub
3. Contact our support team
4. Join our community discussions

### Reporting Issues
Please report bugs and feature requests through GitHub Issues with:
- Clear description of the problem
- Steps to reproduce
- Expected vs actual behavior
- Screenshots if applicable
- Environment details

## 📄 License

This project is proprietary software. All rights reserved.

**© 2025 عمارتنا (Amaretna). All rights reserved.**

Unauthorized copying, modification, distribution, or use of this software is strictly prohibited without explicit written permission from the copyright holder.

---

## 🙏 Acknowledgments

- Laravel community for the excellent framework
- Vue.js team for the reactive frontend framework
- Tailwind CSS for the utility-first CSS framework
- All contributors and beta testers

---

<div align="center">
  <p>Made with ❤️ for building management in Jordan and the Middle East</p>
  <p>
    <a href="https://amaretna.com">Website</a> •
    <a href="mailto:<EMAIL>">Support</a> •
    <a href="tel:+962782912391">Phone</a>
  </p>
</div>
