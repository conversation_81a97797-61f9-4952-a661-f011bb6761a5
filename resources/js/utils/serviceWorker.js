// Service Worker registration and management

/**
 * Register the service worker
 */
export async function registerServiceWorker() {
    if (!('serviceWorker' in navigator)) {
        console.log('Service Worker not supported');
        return false;
    }

    try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
            scope: '/'
        });

        console.log('Service Worker registered successfully:', registration);

        // Handle updates
        registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            
            if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // New service worker is available
                        showUpdateAvailableNotification();
                    }
                });
            }
        });

        // Listen for messages from service worker with error handling
        navigator.serviceWorker.addEventListener('message', (event) => {
            try {
                handleServiceWorkerMessage(event.data);
            } catch (error) {
                console.error('Error handling service worker message:', error);
            }
        });

        // Handle service worker errors
        navigator.serviceWorker.addEventListener('error', (event) => {
            console.error('Service worker error:', event);
        });

        // Handle controller changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
            console.log('Service worker controller changed');
            // Optionally reload the page when a new service worker takes control
            // window.location.reload();
        });

        return registration;
    } catch (error) {
        console.error('Service Worker registration failed:', error);
        return false;
    }
}

/**
 * Unregister the service worker
 */
export async function unregisterServiceWorker() {
    if (!('serviceWorker' in navigator)) {
        return false;
    }

    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
            const result = await registration.unregister();
            console.log('Service Worker unregistered:', result);
            return result;
        }
        return false;
    } catch (error) {
        console.error('Service Worker unregistration failed:', error);
        return false;
    }
}

/**
 * Check if the app is running in standalone mode (PWA)
 */
export function isStandalone() {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
}

/**
 * Check if the device is online
 */
export function isOnline() {
    return navigator.onLine;
}

/**
 * Show notification when app update is available
 */
function showUpdateAvailableNotification() {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50';
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <div>
                <p class="font-semibold">App Update Available</p>
                <p class="text-sm">A new version is ready to install.</p>
            </div>
            <button id="update-app-btn" class="ml-4 bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium">
                Update
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Handle update button click
    document.getElementById('update-app-btn').addEventListener('click', () => {
        updateServiceWorker();
        notification.remove();
    });

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 10000);
}

/**
 * Update the service worker
 */
async function updateServiceWorker() {
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration && registration.waiting) {
            // Tell the waiting service worker to skip waiting
            try {
                registration.waiting.postMessage({ type: 'SKIP_WAITING' });

                // Wait a bit for the service worker to process the message
                await new Promise(resolve => setTimeout(resolve, 100));

                // Reload the page to use the new service worker
                window.location.reload();
            } catch (messageError) {
                console.warn('Failed to send message to service worker:', messageError);
                // Fallback: just reload the page
                window.location.reload();
            }
        }
    } catch (error) {
        console.error('Failed to update service worker:', error);
    }
}

/**
 * Safely send message to service worker
 */
async function sendMessageToServiceWorker(message, timeout = 5000) {
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (!registration || !registration.active) {
            throw new Error('No active service worker found');
        }

        return new Promise((resolve, reject) => {
            const messageChannel = new MessageChannel();
            const timeoutId = setTimeout(() => {
                reject(new Error('Service worker message timeout'));
            }, timeout);

            messageChannel.port1.onmessage = (event) => {
                clearTimeout(timeoutId);
                resolve(event.data);
            };

            messageChannel.port1.onerror = (error) => {
                clearTimeout(timeoutId);
                reject(error);
            };

            try {
                registration.active.postMessage(message, [messageChannel.port2]);
            } catch (postError) {
                clearTimeout(timeoutId);
                reject(postError);
            }
        });
    } catch (error) {
        console.warn('Failed to send message to service worker:', error);
        throw error;
    }
}

/**
 * Handle messages from service worker
 */
function handleServiceWorkerMessage(data) {
    switch (data.type) {
        case 'CACHE_UPDATED':
            console.log('Cache updated:', data.payload);
            break;
        case 'OFFLINE_READY':
            console.log('App is ready to work offline');
            showOfflineReadyNotification();
            break;
        case 'MESSAGE_RECEIVED':
            console.log('Service worker acknowledged message:', data.payload);
            break;
        default:
            console.log('Service worker message:', data);
    }
}

/**
 * Show notification when app is ready to work offline
 */
function showOfflineReadyNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <p class="text-sm font-medium">App is ready to work offline</p>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Add network status listeners
 */
export function addNetworkListeners() {
    window.addEventListener('online', () => {
        console.log('App is online');
        showNetworkStatusNotification('You are back online', 'green');
    });

    window.addEventListener('offline', () => {
        console.log('App is offline');
        showNetworkStatusNotification('You are offline. Some features may be limited.', 'yellow');
    });
}

/**
 * Show network status notification
 */
function showNetworkStatusNotification(message, color) {
    const colorClasses = {
        green: 'bg-green-600',
        yellow: 'bg-yellow-600',
        red: 'bg-red-600'
    };

    const notification = document.createElement('div');
    notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 ${colorClasses[color]} text-white px-4 py-2 rounded-lg shadow-lg z-50`;
    notification.innerHTML = `
        <p class="text-sm font-medium">${message}</p>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

/**
 * Request persistent storage (for PWA)
 */
export async function requestPersistentStorage() {
    if ('storage' in navigator && 'persist' in navigator.storage) {
        try {
            const persistent = await navigator.storage.persist();
            console.log('Persistent storage:', persistent);
            return persistent;
        } catch (error) {
            console.error('Failed to request persistent storage:', error);
            return false;
        }
    }
    return false;
}

/**
 * Get storage usage estimate
 */
export async function getStorageEstimate() {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
        try {
            const estimate = await navigator.storage.estimate();
            console.log('Storage estimate:', estimate);
            return estimate;
        } catch (error) {
            console.error('Failed to get storage estimate:', error);
            return null;
        }
    }
    return null;
}

/**
 * Clear all service worker caches
 */
export async function clearServiceWorkerCaches() {
    try {
        const response = await sendMessageToServiceWorker({ type: 'CLEAR_CACHE' });
        if (response.type === 'CACHE_CLEARED' && response.payload.success) {
            console.log('Service worker caches cleared successfully');
            return true;
        } else {
            console.error('Failed to clear caches:', response.payload.error);
            return false;
        }
    } catch (error) {
        console.error('Failed to communicate with service worker for cache clearing:', error);
        return false;
    }
}

/**
 * Get service worker version
 */
export async function getServiceWorkerVersion() {
    try {
        const response = await sendMessageToServiceWorker({ type: 'GET_VERSION' });
        if (response.type === 'VERSION_RESPONSE') {
            return response.payload.version;
        }
        return null;
    } catch (error) {
        console.error('Failed to get service worker version:', error);
        return null;
    }
}
