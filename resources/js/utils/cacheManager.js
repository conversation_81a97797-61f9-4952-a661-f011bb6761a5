/**
 * Cache Manager Utility
 * Provides client-side cache management functions for development and debugging
 */

/**
 * Clear all service worker caches
 */
export async function clearAllCaches() {
    try {
        const cacheNames = await caches.keys();
        console.log('🗑️  Clearing caches:', cacheNames);
        
        await Promise.all(
            cacheNames.map(cacheName => {
                console.log(`Deleting cache: ${cacheName}`);
                return caches.delete(cacheName);
            })
        );
        
        console.log('✅ All caches cleared successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to clear caches:', error);
        return false;
    }
}

/**
 * Get cache status and contents
 */
export async function getCacheStatus() {
    try {
        const cacheNames = await caches.keys();
        const cacheDetails = await Promise.all(
            cacheNames.map(async (name) => {
                const cache = await caches.open(name);
                const keys = await cache.keys();
                return {
                    name,
                    count: keys.length,
                    urls: keys.slice(0, 10).map(req => req.url),
                    size: await estimateCacheSize(cache, keys)
                };
            })
        );
        
        console.log('📦 Cache Status:', cacheDetails);
        return cacheDetails;
    } catch (error) {
        console.error('❌ Failed to get cache status:', error);
        return [];
    }
}

/**
 * Estimate cache size (approximate)
 */
async function estimateCacheSize(cache, keys) {
    try {
        let totalSize = 0;
        const sampleSize = Math.min(keys.length, 5); // Sample first 5 items
        
        for (let i = 0; i < sampleSize; i++) {
            const response = await cache.match(keys[i]);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
        
        // Estimate total size based on sample
        const estimatedTotal = (totalSize / sampleSize) * keys.length;
        return formatBytes(estimatedTotal);
    } catch (error) {
        return 'Unknown';
    }
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Force refresh without cache
 */
export function forceRefresh() {
    // Add cache bypass parameter
    const url = new URL(window.location);
    url.searchParams.set('sw-bypass', '1');
    url.searchParams.set('_t', Date.now()); // Cache buster
    
    console.log('🔄 Force refreshing with cache bypass...');
    window.location.href = url.toString();
}

/**
 * Check if we're in development mode
 */
export function isDevelopmentMode() {
    return window.location.hostname === 'localhost' || 
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.includes('dev');
}

/**
 * Get service worker status
 */
export async function getServiceWorkerStatus() {
    if (!('serviceWorker' in navigator)) {
        return { supported: false };
    }
    
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        
        if (!registration) {
            return { supported: true, registered: false };
        }
        
        return {
            supported: true,
            registered: true,
            installing: !!registration.installing,
            waiting: !!registration.waiting,
            active: !!registration.active,
            scope: registration.scope,
            updateViaCache: registration.updateViaCache,
            scriptURL: registration.active?.scriptURL
        };
    } catch (error) {
        console.error('Failed to get service worker status:', error);
        return { supported: true, error: error.message };
    }
}

/**
 * Update service worker if available
 */
export async function updateServiceWorker() {
    try {
        const registration = await navigator.serviceWorker.getRegistration();
        
        if (!registration) {
            console.log('No service worker registered');
            return false;
        }
        
        console.log('🔄 Checking for service worker updates...');
        await registration.update();
        
        if (registration.waiting) {
            console.log('🔄 New service worker available, activating...');
            registration.waiting.postMessage({ type: 'SKIP_WAITING' });
            return true;
        }
        
        console.log('✅ Service worker is up to date');
        return false;
    } catch (error) {
        console.error('Failed to update service worker:', error);
        return false;
    }
}

/**
 * Completely reset service worker and caches
 */
export async function resetServiceWorker() {
    try {
        console.log('🧹 Resetting service worker and caches...');
        
        // Clear all caches
        await clearAllCaches();
        
        // Unregister service worker
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(reg => reg.unregister()));
        
        // Clear storage
        if (isDevelopmentMode()) {
            localStorage.clear();
            sessionStorage.clear();
        }
        
        console.log('✅ Service worker reset complete');
        return true;
    } catch (error) {
        console.error('❌ Failed to reset service worker:', error);
        return false;
    }
}

/**
 * Initialize cache manager for development
 */
export function initCacheManager() {
    if (!isDevelopmentMode()) {
        return;
    }
    
    // Add global cache management functions for development
    window.cacheManager = {
        clear: clearAllCaches,
        status: getCacheStatus,
        forceRefresh,
        swStatus: getServiceWorkerStatus,
        update: updateServiceWorker,
        reset: resetServiceWorker
    };
    
    console.log('🛠️  Cache Manager loaded for development');
    console.log('Available commands:');
    console.log('- cacheManager.clear()      - Clear all caches');
    console.log('- cacheManager.status()     - Get cache status');
    console.log('- cacheManager.forceRefresh() - Force refresh without cache');
    console.log('- cacheManager.swStatus()   - Get service worker status');
    console.log('- cacheManager.update()     - Update service worker');
    console.log('- cacheManager.reset()      - Complete reset');
    
    // Auto-clear caches on page load if requested
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('clear-cache')) {
        console.log('🗑️  Auto-clearing caches as requested...');
        clearAllCaches().then(() => {
            // Remove the parameter and reload
            urlParams.delete('clear-cache');
            const newUrl = window.location.pathname + 
                          (urlParams.toString() ? '?' + urlParams.toString() : '');
            window.history.replaceState({}, '', newUrl);
        });
    }
}
