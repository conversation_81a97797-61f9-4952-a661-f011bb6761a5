// Lazy loading utilities for Vue components and assets

import { defineAsyncComponent } from 'vue';

/**
 * Create a lazy-loaded Vue component with loading and error states
 */
export function createLazyComponent(importFn, options = {}) {
    const defaultOptions = {
        loadingComponent: {
            template: `
                <div class="flex items-center justify-center p-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Loading...</span>
                </div>
            `
        },
        errorComponent: {
            template: `
                <div class="flex items-center justify-center p-8 text-red-600">
                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z">
                        </path>
                    </svg>
                    <span>Failed to load component</span>
                </div>
            `
        },
        delay: 200,
        timeout: 30000,
        suspensible: false,
        ...options
    };

    return defineAsyncComponent({
        loader: importFn,
        ...defaultOptions
    });
}

/**
 * Preload a component for better performance
 */
export function preloadComponent(importFn) {
    // Start loading the component but don't wait for it
    importFn().catch(error => {
        console.warn('Failed to preload component:', error);
    });
}

/**
 * Create route-based lazy components with automatic preloading
 */
export function createRouteComponent(importFn, preload = false) {
    if (preload) {
        // Preload on route hover or focus
        const component = createLazyComponent(importFn);
        component.preload = () => preloadComponent(importFn);
        return component;
    }
    
    return createLazyComponent(importFn);
}

/**
 * Lazy load images with intersection observer
 */
export class LazyImageLoader {
    constructor(options = {}) {
        this.options = {
            rootMargin: '50px',
            threshold: 0.1,
            ...options
        };
        
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            this.options
        );
        
        this.loadedImages = new Set();
    }

    observe(img) {
        if (this.loadedImages.has(img.src)) {
            this.loadImage(img);
            return;
        }
        
        this.observer.observe(img);
    }

    unobserve(img) {
        this.observer.unobserve(img);
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                this.loadImage(img);
                this.observer.unobserve(img);
            }
        });
    }

    loadImage(img) {
        const src = img.dataset.src || img.src;
        
        if (src && !this.loadedImages.has(src)) {
            const imageLoader = new Image();
            
            imageLoader.onload = () => {
                img.src = src;
                img.classList.add('loaded');
                img.classList.remove('loading');
                this.loadedImages.add(src);
            };
            
            imageLoader.onerror = () => {
                img.classList.add('error');
                img.classList.remove('loading');
            };
            
            img.classList.add('loading');
            imageLoader.src = src;
        }
    }

    disconnect() {
        this.observer.disconnect();
    }
}

/**
 * Vue directive for lazy loading images
 */
export const lazyImageDirective = {
    mounted(el, binding) {
        const loader = new LazyImageLoader(binding.value || {});
        
        // Store loader instance on element for cleanup
        el._lazyLoader = loader;
        
        // Set up the image
        if (el.tagName === 'IMG') {
            if (binding.arg) {
                el.dataset.src = binding.arg;
            }
            loader.observe(el);
        }
    },
    
    unmounted(el) {
        if (el._lazyLoader) {
            el._lazyLoader.disconnect();
            delete el._lazyLoader;
        }
    }
};

/**
 * Lazy load CSS files
 */
export function loadCSS(href) {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        
        link.onload = () => resolve(link);
        link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
        
        document.head.appendChild(link);
    });
}

/**
 * Lazy load JavaScript files
 */
export function loadScript(src, options = {}) {
    return new Promise((resolve, reject) => {
        // Check if script is already loaded
        const existingScript = document.querySelector(`script[src="${src}"]`);
        if (existingScript) {
            resolve(existingScript);
            return;
        }
        
        const script = document.createElement('script');
        script.src = src;
        script.async = options.async !== false;
        script.defer = options.defer === true;
        
        if (options.type) {
            script.type = options.type;
        }
        
        script.onload = () => resolve(script);
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        
        document.head.appendChild(script);
    });
}

/**
 * Preload resources using link prefetch/preload
 */
export function preloadResource(href, as, type = 'prefetch') {
    const link = document.createElement('link');
    link.rel = type; // 'preload' or 'prefetch'
    link.href = href;
    
    if (as) {
        link.as = as; // 'script', 'style', 'image', etc.
    }
    
    document.head.appendChild(link);
    
    return link;
}

/**
 * Intersection Observer utility for general lazy loading
 */
export class LazyLoader {
    constructor(callback, options = {}) {
        this.callback = callback;
        this.options = {
            rootMargin: '50px',
            threshold: 0.1,
            ...options
        };
        
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            this.options
        );
    }

    observe(element) {
        this.observer.observe(element);
    }

    unobserve(element) {
        this.observer.unobserve(element);
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.callback(entry.target, entry);
                this.observer.unobserve(entry.target);
            }
        });
    }

    disconnect() {
        this.observer.disconnect();
    }
}

/**
 * Debounce function for performance optimization
 */
export function debounce(func, wait, immediate = false) {
    let timeout;
    
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) func.apply(this, args);
    };
}

/**
 * Throttle function for performance optimization
 */
export function throttle(func, limit) {
    let inThrottle;
    
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
