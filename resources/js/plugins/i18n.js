import { reactive } from 'vue';
import i18n from '../i18n/index.js';

// Create a reactive state for the locale
const reactiveState = reactive({
  locale: i18n.getLocale()
});

// Set the reactive state in the i18n instance
i18n.reactiveLocale = reactiveState;

export default {
  install(app) {
    // Make i18n available globally
    app.config.globalProperties.$i18n = i18n;
    
    // Make $t reactive by accessing the reactive locale
    app.config.globalProperties.$t = (key, params) => {
      // Access the reactive locale to trigger re-renders
      reactiveState.locale;
      return i18n.t(key, params);
    };
    
    app.config.globalProperties.$locale = () => {
      // Access the reactive locale to trigger re-renders
      return reactiveState.locale;
    };
    
    app.config.globalProperties.$isRTL = () => {
      // Access the reactive locale to trigger re-renders
      reactiveState.locale;
      return i18n.isRTL();
    };
    
    app.config.globalProperties.$setLocale = (locale) => {
      i18n.setLocale(locale);
      // Update the reactive state to trigger re-renders
      reactiveState.locale = locale;
    };
    
    // Provide for composition API
    app.provide('i18n', i18n);
    app.provide('reactiveLocale', reactiveState);
  }
};
