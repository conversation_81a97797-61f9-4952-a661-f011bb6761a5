// Internationalization system for the Building Committee app
class I18n {
  constructor() {
    this.currentLocale = localStorage.getItem('locale') || 'ar';
    this.translations = {};
    this.reactiveLocale = null; // Will be set by Vue plugin
    this.loadTranslations();
  }

  loadTranslations() {
    // Arabic translations
    this.translations.ar = {
      // Navigation & General
      app_name: 'عمارتنا',
      welcome: 'مرحباً بكم في عمارتنا',
      login: 'تسجيل الدخول',
      register: 'التسجيل',
      logout: 'تسجيل الخروج',
      dashboard: 'لوحة التحكم',
      settings: 'الإعدادات',
      loading: 'جاري التحميل...',
      save: 'حفظ',
      cancel: 'إلغاء',
      edit: 'تعديل',

      // Common namespace for shared translations
      common: {
        cancel: 'إلغاء',
        save: 'حفظ',
        edit: 'تعديل',
        delete: 'حذف',
        create: 'إنشاء',
        loading: 'جاري التحميل...',
        error: 'خطأ',
        success: 'نجح',
        try_again: 'حاول مرة أخرى'
      },
      delete: 'حذف',
      create: 'إنشاء',
      add: 'إضافة',
      back: 'رجوع',
      next: 'التالي',
      previous: 'السابق',
      search: 'بحث',
      filter: 'تصفية',
      actions: 'الإجراءات',
      view: 'عرض',
      status: 'الحالة',
      date: 'التاريخ',
      amount: 'المبلغ',
      total: 'المجموع',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      password: 'كلمة المرور',
      confirm_password: 'تأكيد كلمة المرور',
      yes: 'نعم',
      no: 'لا',

      // Dashboard titles
      admin_dashboard: 'لوحة تحكم الإدارة',
      neighbor_dashboard: 'لوحة تحكم الجار',
      super_admin_dashboard: 'لوحة تحكم المدير العام',
      admin_dashboard_description: 'إدارة مبناك ومتابعة الإيرادات والمصروفات',
      super_admin_dashboard_description: 'إدارة جميع المباني والمديرين في النظام',

      // Menu items
      expenses: 'المصروفات',
      incomes: 'الإيرادات',
      users: 'المستخدمين',
      buildings: 'المباني',
      my_building: 'مبناي',

      // User Management
      user_management: 'إدارة المستخدمين',
      create_user: 'إنشاء مستخدم جديد',
      edit_user: 'تعديل المستخدم',
      apartment_number: 'رقم الشقة',
      role: 'الدور',
      building: 'المبنى',
      admin: 'مدير',
      neighbor: 'جار',
      super_admin: 'مدير عام',
      select_role: 'اختر الدور',
      admin_role_limited: 'دور المدير محدود',
      admin_limit_reached_for_package: 'تم الوصول لحد المديرين ({current}/{limit}) للباقة الحالية',
      package_does_not_support_multiple_admins: 'الباقة الحالية لا تدعم عدة مديرين',
      cannot_create_admin_check_package: 'لا يمكن إنشاء مدير، تحقق من الباقة',

      // Expense Management
      expense_management: 'إدارة المصروفات',
      create_expense: 'إنشاء مصروف جديد',
      edit_expense: 'تعديل المصروف',
      edit_expense_description: 'تحديث تفاصيل المصروف الموجود',
      edit_expense_details_description: 'تحديث تفاصيل المصروف ومعلومات الدفع',
      loading_expense: 'جاري تحميل المصروف...',
      expense_not_found: 'المصروف غير موجود',
      expense_not_found_description: 'لم يتم العثور على المصروف المطلوب أو تم حذفه',
      expense_type: 'نوع المصروف',
      month: 'الشهر',
      year: 'السنة',
      automatic: 'تلقائي',
      manual: 'يدوي',
      notes: 'ملاحظات',
      expenses: 'المصروفات',
      neighbor: 'الجار',
      select_neighbor: 'اختر الجار',
      amount: 'المبلغ',
      enter_amount: 'أدخل المبلغ',
      select_month: 'اختر الشهر',
      select_year: 'اختر السنة',
      enter_notes: 'أدخل ملاحظات',
      automatic_expense: 'مصروف تلقائي',
      attachments: 'المرفقات',
      processing: 'جاري المعالجة...',
      update_expense: 'تحديث المصروف',
      total_expenses: 'إجمالي المصروفات',
      this_month: 'هذا الشهر',
      filters: 'المرشحات',
      all_types: 'جميع الأنواع',
      all_months: 'جميع الأشهر',
      all_years: 'جميع السنوات',
      clear_filters: 'مسح المرشحات',
      no_expenses: 'لا توجد مصروفات',
      get_started_by_creating_expense: 'ابدأ بإنشاء مصروف جديد',
      create_first_expense: 'إنشاء أول مصروف',
      type: 'النوع',
      created_at: 'تاريخ الإنشاء',
      actions: 'الإجراءات',
      confirm_delete_expense: 'هل أنت متأكد من حذف هذا المصروف؟',
      deleted: 'تم الحذف',
      expense_deleted_successfully: 'تم حذف المصروف بنجاح',
      deletion_failed: 'فشل الحذف',
      failed_to_delete_expense: 'فشل في حذف المصروف',
      failed_to_load_expenses: 'فشل في تحميل المصروفات',
      select_expense_type: 'اختر نوع المصروف',
      back_to_expenses: 'العودة إلى المصروفات',
      expense_details: 'تفاصيل المصروف',
      expense_details_description: 'أدخل تفاصيل المصروف ومعلومات الدفع',
      create_expense_description: 'إنشاء مصروف جديد للمبنى',
      expenses_description: 'قائمة بجميع مصروفات المبنى',

      // Building Expense Management
      building_expense_management: 'إدارة مصروفات المبنى',
      building_expenses: 'مصروفات المبنى',
      create_building_expense: 'إنشاء مصروف مبنى جديد',
      edit_building_expense: 'تعديل مصروف المبنى',
      edit_building_expense_description: 'تحديث تفاصيل مصروف المبنى الموجود',
      loading_building_expense: 'جاري تحميل مصروف المبنى...',
      building_expense_not_found: 'مصروف المبنى غير موجود',
      building_expense_not_found_description: 'مصروف المبنى المطلوب غير موجود أو تم حذفه',
      back_to_building_expenses: 'العودة إلى مصروفات المبنى',
      building_expense_details: 'تفاصيل مصروف المبنى',
      building_expense_details_description: 'أدخل تفاصيل مصروف المبنى',
      create_building_expense_description: 'إنشاء مصروف جديد للمبنى بالكامل',
      manage_building_expenses_description: 'إدارة وتتبع جميع مصروفات المبنى العامة',
      building_expenses_description: 'قائمة بجميع مصروفات المبنى العامة',
      building_expense_type: 'نوع مصروف المبنى',
      select_building_expense_type: 'اختر نوع مصروف المبنى',
      update_building_expense: 'تحديث مصروف المبنى',
      total_building_expenses: 'إجمالي مصروفات المبنى',
      no_building_expenses: 'لا توجد مصروفات مبنى',
      get_started_by_creating_building_expense: 'ابدأ بإنشاء مصروف مبنى جديد',
      create_first_building_expense: 'إنشاء أول مصروف مبنى',
      confirm_delete_building_expense: 'هل أنت متأكد من حذف مصروف المبنى هذا؟',
      building_expense_deleted_successfully: 'تم حذف مصروف المبنى بنجاح',
      failed_to_delete_building_expense: 'فشل في حذف مصروف المبنى',
      failed_to_load_building_expenses: 'فشل في تحميل مصروفات المبنى',

      // View Pages
      view_expense: 'عرض المصروف',
      view_expense_description: 'عرض تفاصيل المصروف والمرفقات',
      view_income: 'عرض الإيراد',
      view_income_description: 'عرض تفاصيل الإيراد والمرفقات',
      view_building_expense: 'عرض مصروف المبنى',
      view_building_expense_description: 'عرض تفاصيل مصروف المبنى والمرفقات',
      basic_information: 'المعلومات الأساسية',
      month_year: 'الشهر/السنة',
      due_date: 'تاريخ الاستحقاق',
      currency_information: 'معلومات العملة',
      original_amount: 'المبلغ الأصلي',
      converted_amount: 'المبلغ المحول',
      exchange_rate: 'سعر الصرف',
      exchange_rate_date: 'تاريخ سعر الصرف',
      loading_income: 'جاري تحميل الإيراد...',
      income_details: 'تفاصيل الإيراد',
      income_not_found: 'الإيراد غير موجود',
      income_not_found_description: 'الإيراد المطلوب غير موجود أو تم حذفه',
      back_to_incomes: 'العودة إلى الإيرادات',
      incomes: 'الإيرادات',
      apartment: 'الشقة',
      payment_date: 'تاريخ الدفع',
      payment_method: 'طريقة الدفع',
      cash: 'نقدي',
      bank_transfer: 'تحويل بنكي',
      check: 'شيك',
      received: 'مستلم',
      pending: 'معلق',
      cancelled: 'ملغي',
      confirm_delete_income: 'هل أنت متأكد من حذف هذا الإيراد؟',
      income_deleted: 'تم حذف الإيراد بنجاح',
      delete_failed: 'فشل الحذف',
      failed_delete_income: 'فشل في حذف الإيراد',

      // Income Management
      income_management: 'إدارة الإيرادات',
      create_income: 'إنشاء إيراد جديد',
      edit_income: 'تعديل الإيراد',
      edit_income_description: 'تحديث تفاصيل الإيراد ومعلومات الدفع',
      payment_date: 'تاريخ الدفع',
      payment_method: 'طريقة الدفع',
      cash: 'نقداً',
      bank_transfer: 'تحويل بنكي',
      check: 'شيك',

      // Building Management
      building_management: 'إدارة المباني',
      create_building: 'إنشاء مبنى جديد',
      edit_building: 'تعديل المبنى',
      building_name: 'اسم المبنى',
      address: 'العنوان',
      city: 'المدينة',
      country: 'البلد',
      postal_code: 'الرمز البريدي',
      monthly_fee: 'الرسوم الشهرية',
      description: 'الوصف',

      // Status values
      pending: 'معلق',
      paid: 'مدفوع',
      received: 'مستلم',
      cancelled: 'ملغي',

      // Messages
      success: 'نجح',
      error: 'خطأ',
      warning: 'تحذير',
      info: 'معلومات',
      no_data: 'لا توجد بيانات',
      loading_menu: 'جاري تحميل القائمة...',

      // Months
      january: 'يناير',
      february: 'فبراير',
      march: 'مارس',
      april: 'أبريل',
      may: 'مايو',
      june: 'يونيو',
      july: 'يوليو',
      august: 'أغسطس',
      september: 'سبتمبر',
      october: 'أكتوبر',
      november: 'نوفمبر',
      december: 'ديسمبر',

      // Form validation
      required_field: 'هذا الحقل مطلوب',
      invalid_email: 'البريد الإلكتروني غير صحيح',
      password_mismatch: 'كلمات المرور غير متطابقة',

      // Email Verification
      verify_your_email: 'تحقق من بريدك الإلكتروني',
      email_verified: 'تم التحقق من البريد الإلكتروني',
      verification_failed: 'فشل التحقق',
      verifying_email: 'جاري التحقق من البريد الإلكتروني',
      please_wait_verifying: 'يرجى الانتظار أثناء التحقق من بريدك الإلكتروني',
      email_verified_successfully: 'تم التحقق من البريد الإلكتروني بنجاح',
      email_verified_message: 'تم التحقق من بريدك الإلكتروني بنجاح. يمكنك الآن الوصول إلى حسابك.',
      verification_error_message: 'حدث خطأ أثناء التحقق من بريدك الإلكتروني.',
      verify_email_message: 'يرجى النقر على الرابط في البريد الإلكتروني للتحقق من حسابك.',
      invalid_verification_link: 'رابط التحقق غير صالح',
      resend_verification_email: 'إعادة إرسال بريد التحقق',
      sending: 'جاري الإرسال...',
      continue_to_dashboard: 'المتابعة إلى لوحة التحكم',
      go_to_login: 'الذهاب لتسجيل الدخول',
      back_to_login: 'العودة لتسجيل الدخول',
      click_verification_link_message: 'يرجى النقر على رابط التحقق في البريد الإلكتروني المرسل إليك.',
      need_help: 'تحتاج مساعدة؟',
      contact_support: 'تواصل مع الدعم',
      verification_email_sent_successfully: 'تم إرسال بريد التحقق بنجاح',
      failed_to_send_verification_email: 'فشل في إرسال بريد التحقق',

      // Admin Invitations
      admin_invitation: 'دعوة إدارية',
      invitation_accepted: 'تم قبول الدعوة',
      invitation_error: 'خطأ في الدعوة',
      loading_invitation: 'جاري تحميل الدعوة',
      please_wait_loading: 'يرجى الانتظار أثناء تحميل تفاصيل الدعوة',
      invitation_accepted_successfully: 'تم قبول الدعوة بنجاح',
      invitation_accepted_message: 'تم قبول دعوتك الإدارية بنجاح. يمكنك الآن تسجيل الدخول.',
      invitation_error_message: 'حدث خطأ في معالجة الدعوة.',
      accept_invitation_message: 'يرجى إنشاء كلمة مرور لقبول الدعوة الإدارية.',
      invalid_invitation_token: 'رمز الدعوة غير صالح',
      invitation_expired: 'انتهت صلاحية الدعوة',
      invitation_no_longer_valid: 'الدعوة لم تعد صالحة',
      failed_to_load_invitation: 'فشل في تحميل تفاصيل الدعوة',
      failed_to_accept_invitation: 'فشل في قبول الدعوة',
      invitation_details: 'تفاصيل الدعوة',
      building: 'المبنى',
      admin: 'مدير',
      invited_by: 'دعا من قبل',
      expires_on: 'تنتهي في',
      enter_password: 'أدخل كلمة المرور',
      confirm_password: 'تأكيد كلمة المرور',
      accepting: 'جاري القبول...',
      accept_invitation: 'قبول الدعوة',

      // Admin Management
      admin_management: 'إدارة المديرين',
      admin_management_description: 'إدارة مديري المبنى والدعوات المعلقة',
      invite_admin: 'دعوة مدير',
      current_admins: 'المديرون الحاليون',
      pending_invitations: 'الدعوات المعلقة',
      current_admins_description: 'قائمة بجميع مديري المبنى الحاليين',
      pending_invitations_description: 'الدعوات التي تم إرسالها ولم يتم قبولها بعد',
      no_admins_found: 'لم يتم العثور على مديرين',
      no_pending_invitations: 'لا توجد دعوات معلقة',
      joined_date: 'تاريخ الانضمام',
      invitee: 'المدعو',
      expires: 'تنتهي',
      invite_new_admin: 'دعوة مدير جديد',
      full_name: 'الاسم الكامل',
      enter_full_name: 'أدخل الاسم الكامل',
      email_address: 'عنوان البريد الإلكتروني',
      enter_email_address: 'أدخل عنوان البريد الإلكتروني',
      select_building: 'اختر المبنى',
      send_invitation: 'إرسال الدعوة',
      invitation_sent_successfully: 'تم إرسال الدعوة بنجاح',
      failed_to_send_invitation: 'فشل في إرسال الدعوة',
      invitation_resent_successfully: 'تم إعادة إرسال الدعوة بنجاح',
      failed_to_resend_invitation: 'فشل في إعادة إرسال الدعوة',
      invitation_cancelled_successfully: 'تم إلغاء الدعوة بنجاح',
      failed_to_cancel_invitation: 'فشل في إلغاء الدعوة',
      admin_removed_successfully: 'تم إزالة المدير بنجاح',
      failed_to_remove_admin: 'فشل في إزالة المدير',
      confirm_cancel_invitation: 'هل أنت متأكد من إلغاء هذه الدعوة؟',
      confirm_remove_admin: 'هل أنت متأكد من إزالة هذا المدير؟',
      resend: 'إعادة إرسال',
      cancel: 'إلغاء',
      remove: 'إزالة',
      pending: 'معلق',
      accepted: 'مقبول',
      expired: 'منتهي الصلاحية',
      cancelled: 'ملغي',

      // Subscription Management
      subscription_management: 'إدارة الاشتراكات',
      subscription_management_description: 'إدارة اشتراكات المباني وحزم الخدمات',
      create_subscription: 'إنشاء اشتراك',
      active_subscriptions: 'الاشتراكات النشطة',
      trial_subscriptions: 'الاشتراكات التجريبية',
      total_revenue: 'إجمالي الإيرادات',
      cancelled_subscriptions: 'الاشتراكات الملغية',
      all_statuses: 'جميع الحالات',
      all_packages: 'جميع الحزم',
      packages: ' الحزم',
      search_buildings: 'البحث في المباني',
      clear_filters: 'مسح المرشحات',
      subscriptions: 'الاشتراكات',
      manage_all_building_subscriptions: 'إدارة جميع اشتراكات المباني',
      expense_types: 'أنواع المصروفات',
      building_expense_types: 'أنواع مصروفات المبنى',
      expense_types: 'أنواع المصروفات',
      building_expense_types: 'أنواع مصروفات المبنى',
      no_subscriptions_found: 'لم يتم العثور على اشتراكات',
      billing_cycle: 'دورة الفوترة',
      expires_at: 'تنتهي في',
      create_new_subscription: 'إنشاء اشتراك جديد',
      select_building: 'اختر المبنى',
      select_package: 'اختر الحزمة',
      monthly: 'شهري',
      annual: 'سنوي',
      trial_days: 'أيام التجربة',
      optional_trial_days: 'أيام التجربة (اختياري)',
      notes: 'ملاحظات',
      optional_notes: 'ملاحظات (اختياري)',
      creating: 'جاري الإنشاء',
      edit: 'تعديل',
      renew: 'تجديد',
      showing: 'عرض',
      to: 'إلى',
      of: 'من',
      results: 'نتيجة',
      previous: 'السابق',
      next: 'التالي',
      month: 'شهر',
      subscription_created_successfully: 'تم إنشاء الاشتراك بنجاح',
      failed_to_create_subscription: 'فشل في إنشاء الاشتراك',
      failed_to_load_subscriptions: 'فشل في تحميل الاشتراكات',
      confirm_renew_subscription: 'هل أنت متأكد من تجديد هذا الاشتراك؟',
      subscription_renewed_successfully: 'تم تجديد الاشتراك بنجاح',
      failed_to_renew_subscription: 'فشل في تجديد الاشتراك',
      confirm_cancel_subscription: 'هل أنت متأكد من إلغاء هذا الاشتراك؟',
      subscription_cancelled_successfully: 'تم إلغاء الاشتراك بنجاح',
      failed_to_cancel_subscription: 'فشل في إلغاء الاشتراك',
      edit_subscription: 'تعديل الاشتراك',
      update_subscription: 'تحديث الاشتراك',
      subscription_updated_successfully: 'تم تحديث الاشتراك بنجاح',
      failed_to_update_subscription: 'فشل في تحديث الاشتراك',
      auto_renew: 'التجديد التلقائي',
      updating: 'جاري التحديث',
      active: 'نشط',
      inactive: 'غير نشط',
      trial: 'تجربة',
      cancelled: 'ملغي',
      expired: 'منتهي الصلاحية',
      unsaved_changes_warning: 'لديك تغييرات غير محفوظة. يرجى حفظ التغييرات أو إلغاؤها قبل الإغلاق.',
      confirm_discard_changes: 'لديك تغييرات غير محفوظة. هل أنت متأكد من أنك تريد تجاهلها؟',
      trial: 'تجريبي',
      active: 'نشط',
      expired: 'منتهي الصلاحية',
      inactive: 'غير نشط',

      // Package Management
      package_management: 'إدارة الحزم',
      package_management_description: 'إدارة حزم الخدمات والأسعار والميزات',
      create_package: 'إنشاء حزمة',
      search_packages: 'البحث في الحزم',
      active_only: 'النشطة فقط',
      inactive_only: 'غير النشطة فقط',
      no_packages_found: 'لم يتم العثور على حزم',
      create_first_package: 'ابدأ بإنشاء أول حزمة خدمة',
      popular: 'شائع',
      year: 'سنة',
      max_neighbors: 'الحد الأقصى للجيران',
      storage_limit: 'حد التخزين',
      email_notifications: 'إشعارات البريد الإلكتروني',
      priority_support: 'دعم أولوية',
      advanced_reporting: 'تقارير متقدمة',
      duplicate: 'نسخ',
      deactivate: 'إلغاء التفعيل',
      activate: 'تفعيل',

      // Package Change Requests
      package_change_requests: 'طلبات تغيير الباقة',
      package_change_requests_description: 'إدارة طلبات تغيير الباقة من المباني',
      total_requests: 'إجمالي الطلبات',
      search_requests: 'البحث في الطلبات',
      all_statuses: 'جميع الحالات',
      all_cycles: 'جميع الدورات',
      no_requests_found: 'لم يتم العثور على طلبات',
      no_requests_found_description: 'لا توجد طلبات تغيير باقة حالياً',
      building: 'المبنى',
      requested_by: 'طلب بواسطة',
      current_package: 'الباقة الحالية',
      requested_package: 'الباقة المطلوبة',
      billing_cycle: 'دورة الفوترة',
      status: 'الحالة',
      date: 'التاريخ',
      actions: 'الإجراءات',
      view_details: 'عرض التفاصيل',
      approve_request: 'الموافقة على الطلب',
      reject_request: 'رفض الطلب',
      pending: 'معلق',
      approved: 'تمت الموافقة',
      rejected: 'مرفوض',
      monthly: 'شهري',
      annual: 'سنوي',
      request_details: 'تفاصيل الطلب',
      building_information: 'معلومات المبنى',
      building_name: 'اسم المبنى',
      building_address: 'عنوان المبنى',
      request_information: 'معلومات الطلب',
      request_date: 'تاريخ الطلب',
      payment_method: 'طريقة الدفع',
      package_information: 'معلومات الباقة',
      current_price: 'السعر الحالي',
      requested_price: 'السعر المطلوب',
      reason: 'السبب',
      admin_notes: 'ملاحظات المدير',
      status_information: 'معلومات الحالة',
      approved_by: 'تمت الموافقة بواسطة',
      approved_at: 'تاريخ الموافقة',
      rejected_at: 'تاريخ الرفض',
      approve: 'موافقة',
      reject: 'رفض',
      rejection_reason: 'سبب الرفض',
      rejection_reason_placeholder: 'أدخل سبب رفض الطلب (اختياري)',
      request_approved_successfully: 'تمت الموافقة على الطلب بنجاح',
      request_rejected_successfully: 'تم رفض الطلب بنجاح',
      error_loading_requests: 'خطأ في تحميل الطلبات',
      error_approving_request: 'خطأ في الموافقة على الطلب',
      error_rejecting_request: 'خطأ في رفض الطلب',
      showing: 'عرض',
      to: 'إلى',
      of: 'من',
      results: 'نتيجة',
      previous: 'السابق',
      next: 'التالي',
      no_package: 'لا توجد باقة',
      free: 'مجاني',

      // Export Management
      export_management: 'إدارة التصدير',
      manage_exports_and_reports: 'إدارة عمليات التصدير والتقارير',
      create_export: 'إنشاء تصدير',
      total_exports: 'إجمالي التصديرات',
      completed_exports: 'التصديرات المكتملة',
      pending_exports: 'التصديرات المعلقة',
      monthly_quota: 'الحصة الشهرية',
      export_history: 'سجل التصدير',
      all_types: 'جميع الأنواع',
      all_statuses: 'جميع الحالات',
      financial_summary: 'الملخص المالي',
      building_expense_report: 'تقرير مصروفات المبنى',
      expense_report: 'تقرير المصروفات',
      income_report: 'تقرير الإيرادات',
      neighbor_report: 'تقرير الجيران',
      pending: 'معلق',
      processing: 'قيد المعالجة',
      completed: 'مكتمل',
      failed: 'فشل',
      created_by: 'أنشأ بواسطة',
      created_at: 'تاريخ الإنشاء',
      download: 'تحميل',
      no_exports: 'لا توجد تصديرات',
      no_exports_description: 'لم يتم إنشاء أي تصديرات بعد',
      export_type: 'نوع التصدير',
      select_export_type: 'اختر نوع التصدير',
      export_format: 'تنسيق التصدير',
      from_date: 'من تاريخ',
      to_date: 'إلى تاريخ',
      include_attachments: 'تضمين المرفقات',
      description: 'الوصف',
      optional: 'اختياري',
      export_description_placeholder: 'وصف التصدير (اختياري)',
      export_quota_info: 'معلومات حصة التصدير',
      monthly_exports_used: 'التصديرات الشهرية المستخدمة',
      remaining_today: 'المتبقي اليوم',
      creating: 'جاري الإنشاء',
      export_created_successfully: 'تم إنشاء التصدير بنجاح',
      export_creation_failed: 'فشل في إنشاء التصدير',
      download_failed: 'فشل في التحميل',
      confirm_delete_export: 'هل أنت متأكد من حذف هذا التصدير؟',
      export_deleted_successfully: 'تم حذف التصدير بنجاح',
      export_downloaded_successfully: 'تم تحميل التصدير بنجاح',
      error_loading_exports: 'خطأ في تحميل التصديرات',
      error_loading_export_stats: 'خطأ في تحميل إحصائيات التصدير',
      error_loading_export_types: 'خطأ في تحميل أنواع التصدير',
      please_select_export_type: 'يرجى اختيار نوع التصدير',
      please_select_export_format: 'يرجى اختيار تنسيق التصدير',
      date_range_info: 'معلومات نطاق التاريخ',
      default_date_range_help: 'النطاق الافتراضي يغطي الفترة من يونيو إلى أغسطس 2025 حيث توجد البيانات الفعلية',
      format: 'التنسيق',
      type: 'النوع',

      // Expense Template Management
      expense_template_management: 'إدارة قوالب المصروفات',
      manage_automated_expense_templates: 'إدارة قوالب المصروفات التلقائية',
      create_template: 'إنشاء قالب',
      total_templates: 'إجمالي القوالب',
      active_templates: 'القوالب النشطة',
      auto_generate_templates: 'قوالب التوليد التلقائي',
      generated_this_month: 'تم توليدها هذا الشهر',
      expense_templates: 'قوالب المصروفات',
      all_templates: 'جميع القوالب',
      inactive_only: 'غير النشطة فقط',
      run_auto_generation: 'تشغيل التوليد التلقائي',
      generating: 'جاري التوليد',
      expense_type: 'نوع المصروف',
      frequency: 'التكرار',
      next_generation: 'التوليد التالي',
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      yearly: 'سنوي',
      no_templates: 'لا توجد قوالب',
      no_templates_description: 'لم يتم إنشاء أي قوالب بعد',
      confirm_run_auto_generation: 'هل أنت متأكد من تشغيل التوليد التلقائي؟',
      auto_generation_completed: 'تم إكمال التوليد التلقائي',
      auto_generation_failed: 'فشل التوليد التلقائي',
      template_updated_successfully: 'تم تحديث القالب بنجاح',
      update_failed: 'فشل التحديث',
      confirm_delete_template: 'هل أنت متأكد من حذف هذا القالب؟',
      template_deleted_successfully: 'تم حذف القالب بنجاح',
      delete_failed: 'فشل الحذف',
      edit_template: 'تعديل القالب',
      template_name: 'اسم القالب',
      template_name_placeholder: 'أدخل اسم القالب',
      template_description_placeholder: 'أدخل وصف القالب (اختياري)',
      title_template: 'قالب العنوان',
      title_template_placeholder: 'أدخل قالب العنوان (مثل: تذكير دفع: {{expense_type}})',
      message_template: 'قالب الرسالة',
      message_template_placeholder: 'أدخل قالب الرسالة (مثل: عزيزي {{user_name}}، لديك دفعة مستحقة...)',
      notification_template_info: 'قوالب الإشعارات',
      notification_template_description: 'هذه القوالب تُستخدم للإشعارات العامة (البريد الإلكتروني، الإشعارات داخل التطبيق). للرسائل النصية، يوجد نظام منفصل.',
      expense_created: 'تم إنشاء مصروف',
      income_received: 'تم استلام دخل',
      payment_received: 'تم استلام دفعة',
      overdue_payment: 'دفعة متأخرة',
      expense_type_name: 'اسم نوع المصروف',
      template_usage_note: 'يتم استخدام هذه القوالب تلقائياً عند إرسال الإشعارات من النوع المحدد.',

      // SMS Management
      sms_management: 'إدارة الرسائل النصية',
      sms_settings: 'إعدادات الرسائل النصية',
      sms_templates: 'قوالب الرسائل النصية',
      sms_delivery_logs: 'سجلات التسليم',
      sms_analytics: 'تحليلات الرسائل النصية',
      send_bulk_sms: 'إرسال رسائل جماعية',
      send_test_sms: 'إرسال رسالة تجريبية',
      configure_sms_provider_and_settings: 'تكوين مزود الخدمة والإعدادات',
      sms_notifications: 'إشعارات الرسائل النصية',
      enable_disable_sms_notifications: 'تفعيل أو إلغاء تفعيل إشعارات الرسائل النصية',
      sms_provider_configuration: 'تكوين مزود الرسائل النصية',
      sms_provider: 'مزود الرسائل النصية',
      select_provider: 'اختر المزود',
      twilio_account_sid: 'معرف حساب Twilio',
      twilio_auth_token: 'رمز المصادقة Twilio',
      twilio_phone_number: 'رقم هاتف Twilio',
      aws_access_key_id: 'معرف مفتاح الوصول AWS',
      aws_secret_access_key: 'مفتاح الوصول السري AWS',
      aws_region: 'منطقة AWS',
      select_aws_region: 'اختر منطقة AWS',
      monthly_sms_limit: 'الحد الشهري للرسائل',
      cost_per_sms: 'تكلفة الرسالة الواحدة',
      default_country_code: 'رمز البلد الافتراضي',
      enabled_notification_types: 'أنواع الإشعارات المفعلة',
      require_user_opt_in: 'يتطلب موافقة المستخدم',
      require_user_opt_in_description: 'يجب على المستخدمين الموافقة على تلقي الرسائل النصية',
      usage_statistics: 'إحصائيات الاستخدام',
      sms_sent_this_month: 'الرسائل المرسلة هذا الشهر',
      monthly_limit: 'الحد الشهري',
      remaining_messages: 'الرسائل المتبقية',
      save_settings: 'حفظ الإعدادات',

      // SMS Templates
      manage_sms_templates_description: 'إنشاء وإدارة قوالب الرسائل النصية',
      create_sms_template: 'إنشاء قالب رسالة نصية',
      edit_sms_template: 'تعديل قالب الرسالة النصية',
      sms_template: 'قالب الرسالة النصية',
      sms_template_placeholder: 'أدخل نص الرسالة (مثل: مرحباً {user_name}، لديك دفعة مستحقة...)',
      characters: 'حرف',
      sms_segment: 'جزء رسالة',
      sms_segments: 'أجزاء رسالة',
      multiple_sms_warning: 'سيتم إرسال عدة رسائل',
      template_active: 'القالب نشط',
      sms_limits: 'حدود الرسائل النصية',
      sms_limit_160: 'الرسالة الواحدة تحتوي على 160 حرف كحد أقصى',
      sms_limit_segments: 'الرسائل الأطول سيتم تقسيمها إلى عدة أجزاء',
      sms_limit_cost: 'كل جزء يحسب كرسالة منفصلة',
      template_preview_empty: 'لا يوجد محتوى للمعاينة',
      preview_note: 'هذه معاينة مع بيانات تجريبية',
      no_sms_templates: 'لا توجد قوالب رسائل نصية',
      no_sms_templates_description: 'ابدأ بإنشاء قالب رسالة نصية جديد',
      create_first_sms_template: 'إنشاء أول قالب رسالة نصية',
      all_template_types: 'جميع أنواع القوالب',
      system_templates: 'قوالب النظام',
      custom_templates: 'قوالب مخصصة',
      system: 'نظام',
      duplicate: 'نسخ',
      template_duplicated_successfully: 'تم نسخ القالب بنجاح',
      duplicate_failed: 'فشل في نسخ القالب',
      confirm_delete_sms_template: 'هل أنت متأكد من حذف هذا القالب؟',
      template_deleted_successfully: 'تم حذف القالب بنجاح',
      delete_failed: 'فشل في حذف القالب',
      select_expense_type: 'اختر نوع المصروف',
      currency: 'العملة',
      auto_generate_expenses: 'توليد المصروفات تلقائياً',
      auto_generate_description: 'سيتم توليد المصروفات تلقائياً حسب التكرار المحدد',
      due_days_after: 'أيام الاستحقاق بعد',
      due_days_after_description: 'عدد الأيام بعد التوليد لاستحقاق الدفع',
      template_description_placeholder: 'وصف القالب (اختياري)',
      template_active: 'القالب نشط',
      saving: 'جاري الحفظ',
      update_template: 'تحديث القالب',
      template_created_successfully: 'تم إنشاء القالب بنجاح',
      save_failed: 'فشل الحفظ',

      // Notification Template Management
      notification_template_management: 'إدارة قوالب الإشعارات',
      manage_notification_templates: 'إدارة قوالب الإشعارات والرسائل',
      notification_templates: 'قوالب الإشعارات',
      email_templates: 'قوالب البريد الإلكتروني',
      sms_templates: 'قوالب الرسائل النصية',
      payment_reminder: 'تذكير بالدفع',
      expense_notification: 'إشعار المصروف',
      general_announcement: 'إعلان عام',
      overdue_notification: 'إشعار التأخير',
      welcome_message: 'رسالة ترحيب',
      system_maintenance: 'صيانة النظام',
      priority: 'الأولوية',
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      delivery_method: 'طريقة التسليم',
      email: 'بريد إلكتروني',
      sms: 'رسالة نصية',
      push: 'إشعار فوري',
      preview: 'معاينة',
      no_notification_templates_description: 'لم يتم إنشاء أي قوالب إشعارات بعد',
      create_notification_template: 'إنشاء قالب إشعار',
      edit_notification_template: 'تعديل قالب الإشعار',
      select_notification_type: 'اختر نوع الإشعار',
      email_template: 'قالب البريد الإلكتروني',
      email_subject: 'موضوع البريد الإلكتروني',
      email_subject_placeholder: 'أدخل موضوع البريد الإلكتروني',
      email_body: 'نص البريد الإلكتروني',
      email_body_placeholder: 'أدخل نص البريد الإلكتروني',
      sms_template: 'قالب الرسالة النصية',
      sms_message: 'نص الرسالة',
      sms_message_placeholder: 'أدخل نص الرسالة النصية',
      characters: 'حرف',
      push_notification_template: 'قالب الإشعار الفوري',
      push_title: 'عنوان الإشعار',
      push_title_placeholder: 'أدخل عنوان الإشعار',
      push_body: 'نص الإشعار',
      push_body_placeholder: 'أدخل نص الإشعار',
      available_variables: 'المتغيرات المتاحة',
      user_full_name: 'اسم المستخدم الكامل',
      building_name: 'اسم المبنى',
      payment_amount: 'مبلغ الدفع',
      payment_due_date: 'تاريخ استحقاق الدفع',
      apartment_number: 'رقم الشقة',
      preview_template: 'معاينة القالب',
      type: 'النوع',
      delivery_methods: 'طرق التسليم',
      email_preview: 'معاينة البريد الإلكتروني',
      sms_preview: 'معاينة الرسالة النصية',
      push_preview: 'معاينة الإشعار الفوري',
      app_name: 'عمارتنا',
      send_test: 'إرسال تجريبي',
      sending: 'جاري الإرسال',
      close: 'إغلاق',
      test_notification_sent: 'تم إرسال الإشعار التجريبي',
      test_send_failed: 'فشل إرسال الإشعار التجريبي',
      preview_note: 'ملاحظة المعاينة',
      preview_note_description: 'هذه معاينة بالبيانات التجريبية. البيانات الفعلية ستختلف حسب المستخدم.',

      // Payment Gateway Settings
      payment_gateway_settings: 'إعدادات بوابة الدفع',
      configure_payment_gateways: 'تكوين بوابات الدفع',
      test_connection: 'اختبار الاتصال',
      testing: 'جاري الاختبار',
      stripe_description: 'بوابة دفع عالمية تدعم البطاقات الائتمانية',
      publishable_key: 'المفتاح العام',
      stripe_publishable_key_placeholder: 'أدخل المفتاح العام لـ Stripe',
      secret_key: 'المفتاح السري',
      stripe_secret_key_placeholder: 'أدخل المفتاح السري لـ Stripe',
      webhook_secret: 'سر الـ Webhook',
      stripe_webhook_secret_placeholder: 'أدخل سر الـ Webhook لـ Stripe',
      test_mode: 'وضع الاختبار',
      webhook_url: 'رابط الـ Webhook',
      paypal_description: 'بوابة دفع تدعم PayPal والبطاقات الائتمانية',
      client_id: 'معرف العميل',
      paypal_client_id_placeholder: 'أدخل معرف العميل لـ PayPal',
      client_secret: 'سر العميل',
      paypal_client_secret_placeholder: 'أدخل سر العميل لـ PayPal',
      sandbox_mode: 'وضع الاختبار',
      bank_transfer: 'تحويل بنكي',
      bank_transfer_description: 'تحويل بنكي مباشر للمدفوعات',
      bank_name: 'اسم البنك',
      bank_name_placeholder: 'أدخل اسم البنك',
      account_number: 'رقم الحساب',
      account_number_placeholder: 'أدخل رقم الحساب',
      account_holder_name: 'اسم صاحب الحساب',
      account_holder_placeholder: 'أدخل اسم صاحب الحساب',
      iban: 'رقم الآيبان',
      iban_placeholder: 'أدخل رقم الآيبان',
      swift_code: 'رمز السويفت',
      swift_code_placeholder: 'أدخل رمز السويفت',
      instructions: 'التعليمات',
      bank_transfer_instructions_placeholder: 'أدخل تعليمات التحويل البنكي',
      banking_information: 'معلومات البنك',
      account_holder: 'صاحب الحساب',
      bank_address: 'عنوان البنك',
      payment_instructions: 'تعليمات الدفع',
      bank_transfer_instructions: 'يرجى تحويل المبلغ إلى الحساب المذكور أعلاه مع كتابة رقم الفاتورة في التفاصيل.',
      general_settings: 'الإعدادات العامة',
      general_payment_settings: 'إعدادات الدفع العامة',
      default_currency: 'العملة الافتراضية',
      payment_timeout: 'مهلة الدفع',
      minutes: 'دقيقة',
      auto_confirm_payments: 'تأكيد المدفوعات تلقائياً',
      send_payment_receipts: 'إرسال إيصالات الدفع',
      connection_test_successful: 'نجح اختبار الاتصال',
      connection_test_failed: 'فشل اختبار الاتصال',

      // Management Menu
      management: 'الإدارة',
      create_new_package: 'إنشاء حزمة جديدة',
      edit_package: 'تعديل الحزمة',
      package_name: 'اسم الحزمة',
      enter_package_name: 'أدخل اسم الحزمة',
      slug: 'الرابط المختصر',
      auto_generated: 'يتم إنشاؤه تلقائياً',
      enter_package_description: 'أدخل وصف الحزمة',
      monthly_price: 'السعر الشهري',
      enter_monthly_price: 'أدخل السعر الشهري',
      annual_price: 'السعر السنوي',
      optional_annual_price: 'السعر السنوي (اختياري)',
      storage_limit_gb: 'حد التخزين (جيجابايت)',
      unlimited: 'غير محدود',
      no_trial: 'بدون تجربة',
      sms_notifications: 'إشعارات الرسائل النصية',
      updating: 'جاري التحديث',
      update_package: 'تحديث الحزمة',
      enter_new_package_name: 'أدخل اسم الحزمة الجديد',
      package_created_successfully: 'تم إنشاء الحزمة بنجاح',
      failed_to_create_package: 'فشل في إنشاء الحزمة',
      failed_to_load_packages: 'فشل في تحميل الحزم',
      max_admins: 'الحد الأقصى للمديرين',
      max_admins_placeholder: 'عدد المديرين المسموح',
      multi_admin_support: 'دعم المديرين المتعددين',
      package_updated_successfully: 'تم تحديث الحزمة بنجاح',
      failed_to_update_package: 'فشل في تحديث الحزمة',
      package_duplicated_successfully: 'تم نسخ الحزمة بنجاح',
      failed_to_duplicate_package: 'فشل في نسخ الحزمة',
      confirm_activate_package: 'هل أنت متأكد من تفعيل هذه الحزمة؟',
      confirm_deactivate_package: 'هل أنت متأكد من إلغاء تفعيل هذه الحزمة؟',
      package_status_updated_successfully: 'تم تحديث حالة الحزمة بنجاح',
      failed_to_update_package_status: 'فشل في تحديث حالة الحزمة',
      confirm_delete_package: 'هل أنت متأكد من حذف هذه الحزمة؟ لا يمكن التراجع عن هذا الإجراء.',
      package_deleted_successfully: 'تم حذف الحزمة بنجاح',
      failed_to_delete_package: 'فشل في حذف الحزمة',

      // Admin Transfer
      transfer: 'نقل',
      no_other_buildings_available: 'لا توجد مباني أخرى متاحة للنقل',
      select_building_for_transfer: 'اختر المبنى للنقل إليه',
      enter_building_id: 'أدخل رقم المبنى',
      invalid_building_id: 'رقم المبنى غير صحيح',
      confirm_transfer_admin: 'هل أنت متأكد من نقل المدير {admin} إلى مبنى {building}؟',
      admin_transferred_successfully: 'تم نقل المدير بنجاح',
      failed_to_transfer_admin: 'فشل في نقل المدير',

      // Notifications
      user_created: 'تم إنشاء المستخدم بنجاح',
      user_updated: 'تم تحديث المستخدم بنجاح',
      user_deleted: 'تم حذف المستخدم بنجاح',
      expense_created: 'تم إنشاء المصروف بنجاح',
      expense_updated: 'تم تحديث المصروف بنجاح',
      expense_deleted: 'تم حذف المصروف بنجاح',
      income_created: 'تم إنشاء الإيراد بنجاح',
      income_updated: 'تم تحديث الإيراد بنجاح',
      income_deleted: 'تم حذف الإيراد بنجاح',
      building_created: 'تم إنشاء المبنى بنجاح',
      building_updated: 'تم تحديث المبنى بنجاح',
      building_deleted: 'تم حذف المبنى بنجاح',

      // Notification Center
      notifications: 'الإشعارات',
      mark_all_read: 'تحديد الكل كمقروء',
      mark_read: 'تحديد كمقروء',
      mark_unread: 'تحديد كغير مقروء',
      unread: 'غير مقروء',
      no_notifications: 'لا توجد إشعارات',
      no_notifications_description: 'ستظهر الإشعارات هنا عند توفرها',
      view_all_notifications: 'عرض جميع الإشعارات',
      confirm_delete_notification: 'هل أنت متأكد من حذف هذا الإشعار؟',
      manage_your_notifications: 'إدارة الإشعارات الخاصة بك',
      all_types: 'جميع الأنواع',
      payment_reminders: 'تذكيرات الدفع',
      expense_notifications: 'إشعارات المصروفات',
      income_notifications: 'إشعارات الإيرادات',
      announcements: 'الإعلانات',
      payment_confirmations: 'تأكيدات الدفع',
      overdue_notifications: 'إشعارات التأخير',
      refresh: 'تحديث',
      details: 'التفاصيل',
      due_date: 'تاريخ الاستحقاق',
      payment_date: 'تاريخ الدفع',
      payment_method: 'طريقة الدفع',
      days_overdue: 'أيام التأخير',
      cash: 'نقداً',
      bank_transfer: 'تحويل بنكي',
      check: 'شيك',
      showing: 'عرض',
      to: 'إلى',
      of: 'من',
      results: 'نتيجة',
      just_now: 'الآن',
      minutes_ago: 'دقائق مضت',
      hours_ago: 'ساعات مضت',
      days_ago: 'أيام مضت',
      high: 'عالي',
      medium: 'متوسط',
      low: 'منخفض',

      // File Upload
      drop_files_here: 'اسحب الملفات هنا',
      drop_files_now: 'أفلت الملفات الآن',
      or: 'أو',
      browse_files: 'تصفح الملفات',
      supported_formats: 'الصيغ المدعومة',
      max_file_size: 'الحد الأقصى لحجم الملف',
      selected_files: 'الملفات المحددة',
      files_selected: 'ملف محدد',
      clear_all: 'مسح الكل',
      upload_files: 'رفع الملفات',
      uploading: 'جاري الرفع',
      upload_errors: 'أخطاء الرفع',
      optional: 'اختياري',
      file_description_placeholder: 'وصف الملف (اختياري)',
      make_files_public: 'جعل الملفات عامة',
      public_files_description: 'الملفات العامة يمكن رؤيتها من قبل جميع سكان العمارة',
      attachments: 'المرفقات',
      no_attachments: 'لا توجد مرفقات',
      add_more_files: 'إضافة ملفات أخرى',
      add_new_attachments: 'إضافة مرفقات جديدة',
      loading_files: 'جاري تحميل الملفات...',
      error_loading_files: 'خطأ في تحميل الملفات',
      retry: 'إعادة المحاولة',
      download: 'تحميل',
      preview: 'معاينة',
      close: 'إغلاق',
      public: 'عام',
      private: 'خاص',
      confirm_delete_file: 'هل أنت متأكد من حذف هذا الملف؟',
      file_deleted_successfully: 'تم حذف الملف بنجاح',
      error_deleting_file: 'خطأ في حذف الملف',
      max_files_exceeded: 'تم تجاوز الحد الأقصى للملفات ({max})',
      file_size_exceeded: '{filename}: حجم الملف يتجاوز {maxSize}MB',
      file_type_not_allowed: '{filename}: نوع الملف غير مسموح. الأنواع المسموحة: {allowedTypes}',
      file_already_selected: '{filename}: الملف محدد مسبقاً',

      // Search
      search_placeholder: 'البحث في المصروفات والإيرادات والمستخدمين...',
      searching: 'جاري البحث',
      search_results: 'نتائج البحث',
      results_for: 'النتائج لـ',
      view_all_results: 'عرض جميع النتائج',
      no_results_found: 'لم يتم العثور على نتائج',
      try_different_search: 'جرب كلمات بحث مختلفة أو قم بتعديل التصفيات',
      found: 'تم العثور على',
      results: 'نتيجة',
      with_filters: 'مع تصفيات',
      clear_filters: 'مسح التصفيات',
      amount_range: 'نطاق المبلغ',
      min: 'الحد الأدنى',
      max: 'الحد الأقصى',
      date_filter: 'تصفيه حسب التاريخ',
      amount_filter: 'تصفيه حسب المبلغ',
      view_expenses: 'عرض المصروفات',
      view_details: 'عرض التفاصيل',
      income_payment: 'دفعة إيراد',
      apartment: 'شقة',
      user: 'مستخدم',
      expense: 'مصروف',
      income: 'إيراد',
      payment: 'دفعة',
      expense_type: 'نوع المصروف',

      // Additional translations
      more: 'المزيد',
      advance_report: 'التقارير المتقدمة',
      export_report: 'تصدير التقارير',
      create_custom_reports_and_analytics: 'إنشاء تقارير مخصصة وتحليلات',
      create_custom_report: 'إنشاء تقرير مخصص',
      advanced_reporting_unavailable: 'التقارير المتقدمة غير متاحة',
      advanced_reporting_standard_plus_only: 'التقارير المتقدمة متاحة فقط للباقات القياسية والاحترافية',
      custom_reports: 'التقارير المخصصة',
      total_generations: 'إجمالي الإنشاءات',
      recent_generations: 'الإنشاءات الحديثة',
      last_30_days: 'آخر 30 يوم',
      chart_types: 'أنواع المخططات',
      available: 'متاح',
      report_templates: 'قوالب التقارير',
      analytics: 'التحليلات',

      inactive_users_excluded_from_monthly_expenses: 'يتم استبعاد المستخدمين غير النشطين من المصروفات الشهرية',

      // Reports & Advanced Reporting
      reports: {
        title: 'التقارير',
        subtitle: 'إنشاء وإدارة التقارير المالية',
        loading: 'جاري تحميل التقارير...',
        create_report: 'إنشاء تقرير',
        create_new_report: 'إنشاء تقرير جديد',
        create_report_description: 'إنشاء تقرير مخصص لتتبع بياناتك المالية.',
        report_name: 'اسم التقرير',
        report_name_placeholder: 'مثال: ملخص المصروفات الشهرية',
        description: 'الوصف',
        description_placeholder: 'وصف اختياري لما يتضمنه هذا التقرير...',
        report_type: 'نوع التقرير',
        financial_summary: 'الملخص المالي',
        building_expense_report: 'تقرير مصروفات المبنى',
        expenses_only: 'المصروفات فقط',
        income_only: 'الإيرادات فقط',
        creating: 'جاري الإنشاء...',
        quick_reports: 'التقارير السريعة',
        financial_summary_desc: 'نظرة شاملة على الإيرادات والمصروفات',
        expenses_report: 'تقرير المصروفات',
        expenses_report_desc: 'تفصيل مفصل لجميع المصروفات',
        income_report: 'تقرير الإيرادات',
        income_report_desc: 'ملخص لجميع مصادر الإيرادات',
        custom_reports: 'التقارير المخصصة',
        of: 'من',
        reports: 'تقارير',
        no_custom_reports: 'لا توجد تقارير مخصصة',
        no_custom_reports_desc: 'ابدأ بإنشاء أول تقرير مخصص لك.',
        this_month: 'هذا الشهر',
        generated: 'تم إنشاؤها',
        custom_reports_count: 'التقارير المخصصة',
        run: 'تشغيل',
        running: 'جاري التشغيل...',
        no_description_provided: 'لا يوجد وصف',
        created: 'تم الإنشاء',
        generations: 'مرة إنشاء',
        errors: {
          upgrade_required: 'مطلوب ترقية الباقة',
          upgrade_required_desc: 'ميزات التقارير المتقدمة تتطلب باقة قياسية أو احترافية.',
          authentication_required: 'مطلوب تسجيل الدخول',
          authentication_required_desc: 'يرجى تسجيل الدخول للوصول إلى ميزات التقارير.',
          something_wrong: 'حدث خطأ ما',
          something_wrong_desc: 'واجهنا خطأ في تحميل تقاريرك. يرجى المحاولة مرة أخرى.',
          view_packages: 'عرض الباقات',
          go_to_login: 'الذهاب لتسجيل الدخول',
          try_again: 'حاول مرة أخرى'
        }
      },

      error_loading_reporting_stats: 'خطأ في تحميل إحصائيات التقارير',
      exports_enabled: 'تفعيل التصدير',
      export_settings: 'إعدادات التصدير',
      exports_per_month: 'عدد التصديرات شهرياً',
      max_records_per_export: 'الحد الأقصى للسجلات لكل تصدير',
      export_formats: 'تنسيقات التصدير',
      leave_empty_for_unlimited: 'اتركه فارغاً للحصول على عدد غير محدود',
      default_1000: 'افتراضي: 1000',
      configure_export_limitations_for_package: 'تكوين قيود التصدير لهذه الباقة',
      select_allowed_export_formats: 'اختر تنسيقات التصدير المسموحة',
      portable_document_format: 'تنسيق المستند المحمول',
      spreadsheet_format: 'تنسيق جدول البيانات',

      // Advanced Reporting - Templates
      choose_from_predefined_templates: 'اختر من القوالب المحددة مسبقاً',
      financial_summary_report: 'تقرير الملخص المالي',
      comprehensive_financial_overview: 'نظرة شاملة على الوضع المالي',
      user_activity_report: 'تقرير نشاط المستخدمين',
      track_user_engagement_patterns: 'تتبع أنماط تفاعل المستخدمين',
      maintenance_tracking_report: 'تقرير تتبع الصيانة',
      monitor_building_maintenance_activities: 'مراقبة أنشطة صيانة المبنى',
      income_expense_analysis: 'تحليل الدخل والمصروفات',
      monthly_trends: 'الاتجاهات الشهرية',
      category_breakdown: 'تفصيل الفئات',
      login_patterns: 'أنماط تسجيل الدخول',
      feature_usage: 'استخدام الميزات',
      user_demographics: 'التركيبة السكانية للمستخدمين',
      maintenance_schedule: 'جدول الصيانة',
      cost_analysis: 'تحليل التكاليف',
      completion_rates: 'معدلات الإنجاز',
      template_preview: 'معاينة القالب',
      included_features: 'الميزات المتضمنة',
      available_chart_types: 'أنواع المخططات المتاحة',
      data_fields: 'حقول البيانات',
      available_filters: 'المرشحات المتاحة',
      sample_preview: 'معاينة العينة',
      sample_chart_will_appear_here: 'سيظهر المخطط النموذجي هنا',
      preview_available_after_data_selection: 'المعاينة متاحة بعد اختيار البيانات',
      estimated_creation_time: 'الوقت المقدر للإنشاء',
      use_this_template: 'استخدم هذا القالب',
      no_templates_available: 'لا توجد قوالب متاحة',
      templates_will_be_available_soon: 'ستكون القوالب متاحة قريباً',

      // Advanced Reporting - Custom Reports
      manage_your_custom_reports: 'إدارة تقاريرك المخصصة',
      create_new_report: 'إنشاء تقرير جديد',
      search_reports: 'البحث في التقارير',
      all_categories: 'جميع الفئات',
      all_statuses: 'جميع الحالات',
      active: 'نشط',
      draft: 'مسودة',
      archived: 'مؤرشف',
      no_chart: 'بدون مخطط',
      run_report: 'تشغيل التقرير',
      edit: 'تعديل',
      duplicate: 'نسخ',
      share: 'مشاركة',
      delete: 'حذف',
      no_custom_reports: 'لا توجد تقارير مخصصة',
      get_started_by_creating_first_report: 'ابدأ بإنشاء تقريرك الأول',
      create_first_report: 'إنشاء التقرير الأول',
      running_report: 'تشغيل التقرير: {name}',
      edit_feature_coming_soon: 'ميزة التعديل قادمة قريباً',
      duplicate_feature_coming_soon: 'ميزة النسخ قادمة قريباً',
      share_feature_coming_soon: 'ميزة المشاركة قادمة قريباً',
      confirm_delete_report: 'هل أنت متأكد من حذف التقرير "{name}"؟',
      report_deleted_successfully: 'تم حذف التقرير بنجاح',
      error_loading_reports: 'خطأ في تحميل التقارير',
      package_upgrade_required: 'مطلوب ترقية الباقة',
      custom_reports_require_upgrade: 'التقارير المخصصة تتطلب ترقية إلى باقة أعلى',
      please_login_to_access_reports: 'يرجى تسجيل الدخول للوصول إلى التقارير',
      go_to_login: 'الذهاب لتسجيل الدخول',
      feature_not_available_for_your_package: 'هذه الميزة غير متاحة في باقتك الحالية',
      something_went_wrong_try_again: 'حدث خطأ ما، يرجى المحاولة مرة أخرى',
      try_again: 'حاول مرة أخرى',
      upgrade_required: 'مطلوب ترقية',
      custom_reports_require_standard_or_pro_package: 'التقارير المخصصة تتطلب باقة قياسية أو احترافية',
      available_packages: 'الباقات المتاحة',
      standard_package_features: 'الباقة القياسية: تقارير مخصصة، تصدير البيانات، دعم متعدد المديرين',
      pro_package_features: 'الباقة الاحترافية: جميع الميزات مع الدعم الهاتفي والأرشفة',
      close: 'إغلاق',

      // Advanced Reporting - Report Builder
      build_your_custom_report_step_by_step: 'اصنع تقريرك المخصص خطوة بخطوة',
      basic_info: 'المعلومات الأساسية',
      data_source: 'مصدر البيانات',
      fields: 'الحقول',
      visualization: 'التصور',
      report_name: 'اسم التقرير',
      enter_report_name: 'أدخل اسم التقرير',
      describe_your_report: 'صف تقريرك',
      select_category: 'اختر الفئة',
      select_data_source: 'اختر مصدر البيانات',
      expenses_data: 'بيانات المصروفات',
      building_expenses_and_costs: 'مصروفات وتكاليف المبنى',
      users_data: 'بيانات المستخدمين',
      user_activity_and_engagement: 'نشاط وتفاعل المستخدمين',
      select_fields_to_include: 'اختر الحقول المراد تضمينها',
      available_fields: 'الحقول المتاحة',
      selected_fields: 'الحقول المختارة',
      no_fields_selected: 'لم يتم اختيار أي حقول',
      choose_visualization: 'اختر التصور',
      bar_chart: 'مخطط أعمدة',
      compare_values_across_categories: 'مقارنة القيم عبر الفئات',
      line_chart: 'مخطط خطي',
      show_trends_over_time: 'إظهار الاتجاهات عبر الزمن',
      pie_chart: 'مخطط دائري',
      show_proportions_and_percentages: 'إظهار النسب والمئويات',
      table_view: 'عرض جدولي',
      display_data_in_rows_and_columns: 'عرض البيانات في صفوف وأعمدة',
      previous: 'السابق',
      next: 'التالي',
      create_report: 'إنشاء التقرير',
      report_created_successfully: 'تم إنشاء التقرير بنجاح',
      error_creating_report: 'خطأ في إنشاء التقرير',

      // Advanced Reporting - Analytics
      reporting_analytics: 'تحليلات التقارير',
      insights_and_trends_from_your_reports: 'رؤى واتجاهات من تقاريرك',
      last_7_days: 'آخر 7 أيام',
      last_30_days: 'آخر 30 يوم',
      last_90_days: 'آخر 90 يوم',
      last_year: 'العام الماضي',
      refresh: 'تحديث',
      reports_generated: 'التقارير المُنشأة',
      avg_generation_time: 'متوسط وقت الإنشاء',
      most_used_chart: 'المخطط الأكثر استخداماً',
      exports_this_period: 'التصديرات في هذه الفترة',
      report_generation_trend: 'اتجاه إنشاء التقارير',
      daily_reports: 'التقارير اليومية',
      chart_type_distribution: 'توزيع أنواع المخططات',
      usage_by_type: 'الاستخدام حسب النوع',
      chart_will_appear_here: 'سيظهر المخطط هنا',
      pie_chart_will_appear_here: 'سيظهر المخطط الدائري هنا',
      chart_library_integration_needed: 'مطلوب تكامل مكتبة المخططات',
      most_popular_reports: 'التقارير الأكثر شعبية',
      reports_generated_most_frequently: 'التقارير المُنشأة بشكل أكثر تكراراً',
      times_generated: 'مرة إنشاء',
      no_reports_generated_yet: 'لم يتم إنشاء تقارير بعد',
      start_creating_reports_to_see_analytics: 'ابدأ بإنشاء التقارير لرؤية التحليلات',
      insights_and_recommendations: 'الرؤى والتوصيات',
      no_insights_available_yet: 'لا توجد رؤى متاحة بعد',
      bar_charts_most_popular_this_month: 'المخططات العمودية هي الأكثر شعبية هذا الشهر',
      report_generation_increased_15_percent: 'زاد إنشاء التقارير بنسبة 15%',
      financial_reports_generated_most_frequently: 'التقارير المالية هي الأكثر إنشاءاً',
      analytics_refreshed: 'تم تحديث التحليلات',
      error_loading_analytics: 'خطأ في تحميل التحليلات',
      manage_building_expenses: 'إدارة مصروفات المبنى',
      filters: 'التصفيات',
      filter_expenses_description: 'استخدم التصفية أدناه لتصفية المصروفات حسب النوع والشهر والسنة',
      active_filters: 'التصفيات النشطة',
      clear_all_filters: 'مسح جميع التصفيات',
      expense_records_description: 'عرض وإدارة جميع مصروفات المبنى',
      total_records: 'إجمالي السجلات',
      showing: 'عرض',
      no_expenses_found: 'لم يتم العثور على مصروفات',
      no_expenses_description: 'لا توجد مصروفات مسجلة حتى الآن. ابدأ بإنشاء أول مصروف.',
      create_first_expense: 'إنشاء أول مصروف',
      create_expense_description: 'إضافة مصروف جديد لمبناك',
      back_to_expenses: 'العودة للمصروفات',
      expense_details: 'تفاصيل المصروف',
      expense_details_description: 'أدخل تفاصيل المصروف الجديد أدناه',
      select_expense_type: 'اختر نوع المصروف',
      select_neighbor: 'اختر الجار',
      select_month: 'اختر الشهر',
      automatic_expense: 'مصروف تلقائي',
      automatic_expense_description: 'سيتم إنشاؤه تلقائياً كل شهر',
      notes_placeholder: 'أضف أي ملاحظات إضافية هنا...',
      saving: 'جاري الحفظ...',
      update_expense: 'تحديث المصروف',
      expense_attachments_description: 'أرفق الإيصالات أو المستندات المتعلقة بهذا المصروف',
      super_admin_dashboard: 'لوحة تحكم المدير العام',
      super_admin_dashboard_description: 'إدارة جميع المباني والمديرين في النظام',
      admin_dashboard_description: 'إدارة مبناك ومتابعة الإيرادات والمصروفات',
      buildings_description: 'إدارة جميع المباني في النظام',
      admins_description: 'إدارة مديري المباني',
      neighbor_financial_summary_description: 'ملخص مالي لجميع الجيران في مبناك',
      select_role: 'اختر الدور',
      creating_neighbor_user: 'إنشاء مستخدم جار',
      neighbor_user_description: 'سيتم إنشاء هذا المستخدم كجار في مبناك',
      creating_admin_user: 'إنشاء مستخدم مدير',
      admin_user_description: 'سيتم إنشاء هذا المستخدم كمدير في مبناك',
      create_user_description: 'إضافة مستخدم جديد للنظام',
      back_to_users: 'العودة للمستخدمين',
      user_details: 'تفاصيل المستخدم',
      user_details_description: 'أدخل تفاصيل المستخدم الجديد أدناه',
      user_created_successfully: 'تم إنشاء المستخدم بنجاح',
      failed_to_save_user: 'فشل في حفظ المستخدم',
      leave_blank_keep_current: '(اتركه فارغاً للاحتفاظ بالحالي)',
      outstanding_balance: 'الرصيد المستحق',
      total_expenses: 'إجمالي المصروفات',
      total_income: 'إجمالي الإيرادات',
      building_expense_report: 'تقرير مصروفات المبنى',
      financial_summary: 'الملخص المالي',
      building_summary: 'ملخص البنايه',
      neighbor_payments: 'ملخص مدفوعات الجيران',
      financial_overview: 'النظرة المالية العامة',
      financial_overview_description: 'تحليل شامل للوضع المالي للمبنى',
      expenses_vs_income_trend: 'اتجاه المصروفات مقابل الإيرادات',
      monthly_comparison: 'المقارنة الشهرية',
      net_balance: 'الرصيد الصافي',
      avg_monthly_expense: 'متوسط المصروفات الشهرية',
      current_period: 'الفترة الحالية',
      recent_expenses: 'المصروفات الأخيرة',
      recent_income: 'الإيرادات الأخيرة',
      view_all: 'عرض الكل',
      add_expense: 'إضافة مصروف',
      add_income: 'إضافة إيراد',
      add_user: 'إضافة مستخدم',
      add_building: 'إضافة مبنى',
      select: 'اختر',
      all: 'الكل',
      type: 'النوع',
      user: 'المستخدم',

      // Building Information
      building_information: 'معلومات المبنى',
      not_specified: 'غير محدد',
      monthly_expense_generation: 'إنشاء المصروفات الشهرية',
      generate_monthly_expenses_description: 'إنشاء المصروفات الشهرية لجميع الجيران في مبناك.',
      each_neighbor_charged: 'سيتم تحصيل',
      generate_monthly_expenses: 'إنشاء المصروفات الشهرية',
      generating: 'جاري الإنشاء...',
      no_building_assigned: 'لا يوجد مبنى مخصص لحسابك.',

      // Income page
      record_new_income: 'تسجيل إيراد جديد',
      apply_filters: 'تطبيق التصفيات',
      from_date: 'من تاريخ',
      to_date: 'إلى تاريخ',
      comparison_period: 'فترة المقارنة',
      no_comparison: 'بدون مقارنة',
      previous_month: 'الشهر السابق',
      same_month_last_year: 'نفس الشهر من العام الماضي',
      previous_period: 'الفترة السابقة',

      last_30_days: 'آخر 30 يوم',
      last_3_months: 'آخر 3 أشهر',
      last_6_months: 'آخر 6 أشهر',
      last_year: 'آخر سنة',

      // Financial Overview
      financial_overview: 'النظرة المالية العامة',
      financial_overview_description: 'تحليل شامل للوضع المالي للمبنى',
      expenses_vs_income_trend: 'اتجاه المصروفات مقابل الإيرادات',
      monthly_comparison: 'المقارنة الشهرية',
      net_balance: 'الرصيد الصافي',
      avg_monthly_expense: 'متوسط المصروفات الشهرية',
      this_month: 'هذا الشهر',
      total_income: 'إجمالي الإيرادات',
      income_records: 'سجلات الإيرادات',
      method: 'الطريقة',
      payment_date: 'تاريخ الدفع',
      apartment: 'الشقة',
      neighbor: 'الجار',
      showing_results: 'عرض النتائج',
      to: 'إلى',
      of: 'من',
      results: 'نتيجة',
      next: 'التالي',
      previous: 'السابق',

      // Expense types and filters
      all_types: 'جميع الأنواع',
      all_months: 'جميع الشهور',
      building_services: 'خدمات المبنى',
      building_electricity: 'كهرباء المبنى',
      personal_electricity: 'كهرباء شخصية',
      water: 'مياه',
      other: 'أخرى',
      overdue: 'متأخر',
      auto: 'تلقائي',
      expense_records: 'سجلات المصروفات',

      // Additional messages and confirmations
      confirm_delete_income: 'هل أنت متأكد من حذف سجل الإيراد هذا؟',
      deleted: 'تم الحذف',
      delete_failed: 'فشل الحذف',
      failed_delete_income: 'فشل في حذف سجل الإيراد',
      updated: 'تم التحديث',
      update_failed: 'فشل التحديث',
      error_loading_incomes: 'خطأ في تحميل الإيرادات',

      // Neighbor dashboard
      my_financial_summary: 'ملخصي المالي',
      total_payments: 'إجمالي المدفوعات',
      my_expenses: 'مصروفاتي',
      my_income_records: 'سجلات إيراداتي',
      authentication_error: 'خطأ في المصادقة',
      user_not_found: 'المستخدم غير موجود. يرجى تسجيل الدخول مرة أخرى.',
      data_loaded: 'تم تحميل البيانات',
      no_financial_records: 'لم يتم العثور على سجلات مصروفات أو إيرادات لحسابك.',
      error_loading_dashboard: 'خطأ في تحميل بيانات لوحة التحكم',
      failed_load_financial_data: 'فشل في تحميل بياناتك المالية',

      // Admin dashboard
      total_buildings: 'إجمالي المباني',
      total_admins: 'إجمالي المديرين',
      total_neighbors: 'إجمالي الجيران',
      total_users: 'إجمالي المستخدمين',
      active_buildings: 'المباني النشطة',
      admins: 'المديرين',
      neighbors: 'الجيران',
      add_admin: 'إضافة مدير',
      neighbor_financial_summary: 'ملخص الجيران المالي',
      neighbor_financial_summary_description: 'ملخص مالي لجميع الجيران في مبناك',
      loading_dashboard_data: 'جاري تحميل بيانات لوحة التحكم...',
      created: 'تاريخ الإنشاء',
      confirm_delete_building: 'هل أنت متأكد من حذف المبنى "{name}"؟',
      failed_delete_building: 'فشل في حذف المبنى',
      confirm_delete_admin: 'هل أنت متأكد من حذف المدير "{name}"؟',
      failed_delete_admin: 'فشل في حذف المدير',

      // SMS Management
      sms_management: 'إدارة الرسائل النصية',
      manage_sms_notifications_and_settings: 'إدارة إعدادات وإشعارات الرسائل النصية',
      sms_settings: 'إعدادات الرسائل النصية',
      sms_templates: 'قوالب الرسائل النصية',
      delivery_logs: 'سجلات التسليم',
      analytics: 'التحليلات',
      sms_provider: 'مزود الرسائل النصية',
      sms_notifications_disabled: 'إشعارات الرسائل النصية معطلة',
      enable_sms_in_settings_to_start_sending: 'فعل الرسائل النصية في الإعدادات لبدء الإرسال',
      sms_sent_this_month: 'الرسائل المرسلة هذا الشهر',
      delivery_rate: 'معدل التسليم',
      monthly_cost: 'التكلفة الشهرية',
      send_bulk_sms: 'إرسال رسائل جماعية',
      test_sms: 'رسالة تجريبية',
      bulk_sms_sent_successfully: 'تم إرسال الرسائل الجماعية بنجاح',
      test_sms_sent_successfully: 'تم إرسال الرسالة التجريبية بنجاح',
      sms_template_created_successfully: 'تم إنشاء قالب الرسالة بنجاح',
      sms_settings_updated: 'تم تحديث إعدادات الرسائل النصية',
      error_loading_sms_settings: 'خطأ في تحميل إعدادات الرسائل النصية',
      error_loading_sms_templates: 'خطأ في تحميل قوالب الرسائل النصية',
      error_loading_delivery_logs: 'خطأ في تحميل سجلات التسليم',
      error_loading_sms_statistics: 'خطأ في تحميل إحصائيات الرسائل النصية',
      error_updating_sms_settings: 'خطأ في تحديث إعدادات الرسائل النصية',

      // Building form and management
      loading_building: 'جاري تحميل المبنى...',
      building_not_found: 'المبنى غير موجود.',
      failed_load_building: 'فشل في تحميل المبنى',
      building_updated: 'تم تحديث المبنى بنجاح',
      building_created: 'تم إنشاء المبنى بنجاح',
      monthly_fee_description: 'سيتم تحصيل هذا المبلغ من كل جار شهرياً',
      saving: 'جاري الحفظ...',
      update_building: 'تحديث المبنى',
      create_building: 'إنشاء المبنى',
      failed_save_building: 'فشل في حفظ المبنى',

      // Building management page
      average_monthly_fee: 'متوسط الرسوم الشهرية',
      no_permission: 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
      failed_load_buildings: 'فشل في تحميل المباني',
      building_deleted: 'تم حذف المبنى بنجاح',
      no_buildings_found: 'لم يتم العثور على مبانٍ.',

      // My building page
      not_specified: 'غير محدد',
      monthly_expense_generation: 'توليد المصروفات الشهرية',
      generate_monthly_expenses_description: 'انقر على الزر أدناه لتوليد المصروفات الشهرية لجميع الجيران في مبناك.',
      each_neighbor_charged: 'سيتم تحصيل',
      generating: 'جاري التوليد...',
      generate_monthly_expenses: 'توليد المصروفات الشهرية',
      no_building_assigned: 'لم يتم تعيين مبنى لحسابك.',
      failed_load_building_info: 'فشل في تحميل معلومات المبنى',
      confirm_generate_monthly_expenses: 'توليد المصروفات الشهرية ({fee}) لجميع الجيران في مبناك لشهر {month}/{year}؟',
      generated: 'تم التوليد',
      monthly_expenses_generated: 'تم توليد المصروفات الشهرية بنجاح',
      generation_failed: 'فشل التوليد',
      failed_generate_monthly_expenses: 'فشل في توليد المصروفات الشهرية',

      // Profile management
      my_profile: 'ملفي الشخصي',
      update_profile: 'تحديث الملف الشخصي',
      profile_information: 'معلومات الملف الشخصي',
      new_password: 'كلمة المرور الجديدة',
      confirm_new_password: 'تأكيد كلمة المرور الجديدة',
      leave_blank_to_keep_current: 'اتركه فارغاً للاحتفاظ بالحالي',
      profile_updated_successfully: 'تم تحديث الملف الشخصي بنجاح',
      failed_to_update_profile: 'فشل في تحديث الملف الشخصي',
      failed_to_load_profile: 'فشل في تحميل الملف الشخصي',
      password_confirmation_mismatch: 'تأكيد كلمة المرور غير متطابق',
      updating: 'جاري التحديث...',

      // Email Preferences
      email_preferences: 'تفضيلات البريد الإلكتروني',
      email_preferences_description: 'إدارة إعدادات الإشعارات عبر البريد الإلكتروني',
      email_notifications: 'إشعارات البريد الإلكتروني',
      master_email_toggle_description: 'تفعيل أو إلغاء جميع إشعارات البريد الإلكتروني',
      notification_types: 'أنواع الإشعارات',
      payment_reminders: 'تذكيرات الدفع',
      payment_reminders_description: 'تذكيرات بالمدفوعات المستحقة',
      expense_notifications: 'إشعارات المصروفات',
      expense_notifications_description: 'إشعارات عند إضافة مصروفات جديدة',
      income_notifications: 'إشعارات الإيرادات',
      income_notifications_description: 'إشعارات عند تسجيل دفعات جديدة',
      general_announcements: 'الإعلانات العامة',
      general_announcements_description: 'إعلانات مهمة من إدارة العمارة',
      overdue_notifications: 'إشعارات التأخير',
      overdue_notifications_description: 'إشعارات عند تأخر المدفوعات',
      email_frequency: 'تكرار الإشعارات',
      email_frequency_description: 'كم مرة تريد تلقي الإشعارات',
      immediate: 'فوري',
      daily: 'يومي',
      weekly: 'أسبوعي',
      save_preferences: 'حفظ التفضيلات',
      send_test_email: 'إرسال بريد تجريبي',
      sending_test_email: 'جاري الإرسال...',
      reset_to_defaults: 'إعادة تعيين افتراضي',
      preferences_saved_successfully: 'تم حفظ التفضيلات بنجاح',
      test_email_sent: 'تم إرسال البريد التجريبي بنجاح',
      preferences_reset_successfully: 'تم إعادة تعيين التفضيلات بنجاح',
      error_loading_preferences: 'خطأ في تحميل التفضيلات',
      error_saving_preferences: 'خطأ في حفظ التفضيلات',
      error_sending_test_email: 'خطأ في إرسال البريد التجريبي',
      error_resetting_preferences: 'خطأ في إعادة تعيين التفضيلات',
      confirm_reset_preferences: 'هل أنت متأكد من إعادة تعيين جميع التفضيلات إلى القيم الافتراضية؟',

      // Package Selection
      choose_your_package: 'اختر باقتك',
      package_selection_description: 'اختر الباقة التي تناسب احتياجات عمارتك وميزانيتك',
      most_popular: 'الأكثر شعبية',
      current_package: 'الباقة الحالية',
      unlimited_neighbors: 'عدد لا محدود من السكان',
      max_neighbors_count: 'حتى {count} ساكن',
      unlimited_storage: 'مساحة تخزين لا محدودة',
      storage_limit_gb: '{gb} جيجابايت تخزين',
      in_app_notifications: 'إشعارات داخل التطبيق',
      file_attachments_enabled: 'مرفقات الملفات متاحة',
      file_attachments_disabled: 'مرفقات الملفات غير متاحة',
      standard_support: 'دعم قياسي',
      get_started: 'ابدأ الآن',
      select_package: 'اختر الباقة',
      selected: 'محدد',
      save_up_to: 'وفر حتى',
      loading_packages: 'جاري تحميل الباقات...',
      error_loading_packages: 'خطأ في تحميل الباقات',
      selected_package_summary: 'ملخص الباقة المحددة',
      per_year: 'سنوياً',
      per_month: 'شهرياً',
      includes_free_trial: 'يتضمن تجربة مجانية لمدة {days} أيام',
      processing: 'جاري المعالجة...',
      upgrade_package: 'ترقية الباقة',
      start_trial: 'بدء التجربة',
      current_subscription: 'الاشتراك الحالي',
      package: 'الباقة',
      status: 'الحالة',
      expires_on: 'تنتهي في',
      subscription_status_active: 'نشط',
      subscription_status_trial: 'تجربة مجانية',
      subscription_status_expired: 'منتهي الصلاحية',
      subscription_status_cancelled: 'ملغي',
      subscription_expiring_soon: 'الاشتراك ينتهي قريباً',
      days_remaining: '{days} أيام متبقية',
      error_subscribing_package: 'خطأ في الاشتراك في الباقة',
      free_trial_days: 'تجربة مجانية لمدة {days} أيام',

      // New Package Features
      core_saas_admin_tool: 'أداة إدارة الأساسية',
      reports_export: 'تصدير التقارير',
      multi_admin_support: 'دعم متعدد المشرفين',
      phone_support: 'الدعم الهاتفي',
      archive_feature: 'ميزة الأرشفة',
      small_sms_package: 'حزمة SMS صغيرة',
      data_archiving: 'أرشفة البيانات',
      custom_integrations: 'تكاملات مخصصة',
      additional_sms_packages: 'حزم SMS إضافية',
      premium_support: 'دعم مميز',
      custom_reports: 'تقارير مخصصة',
      api_access: 'الوصول للـ API',
      white_labeling: 'العلامة التجارية البيضاء',
      requires_separate_booking: 'يتطلب حجز منفصل',
      variable_pricing: 'تسعير متغير',
      annual_discount_20: 'خصم 20% للدفع السنوي',

      // Pricing page translations
      monthly: 'شهري',
      annual: 'سنوي',
      per_month: 'في الشهر',
      per_year: 'في السنة',
      save: 'وفر',
      most_popular: 'الأكثر شعبية',
      loading_packages: 'جاري تحميل الباقات...',
      error_loading_packages: 'خطأ في تحميل الباقات',
      max_neighbors_count: 'حد أقصى {count} جار',
      storage_limit_gb: 'مساحة تخزين {gb} جيجابايت',
      contact_sales: 'اتصل بالمبيعات',
      get_started: 'ابدأ الآن',

      // Package Usage Dashboard
      package_usage: 'استخدام الباقة',
      package_usage_description: 'تتبع استخدامك الحالي وحدود الباقة',
      loading_usage_data: 'جاري تحميل بيانات الاستخدام...',
      package_name: 'اسم الباقة',
      upgrade: 'ترقية',
      neighbors_usage: 'استخدام السكان',
      storage_usage: 'استخدام التخزين',
      notifications_usage: 'استخدام الإشعارات',
      email_usage: 'استخدام البريد الإلكتروني',
      available_features: 'الميزات المتاحة',
      enabled: 'مفعل',
      disabled: 'معطل',
      notifications_sent_this_month: 'الإشعارات المرسلة هذا الشهر',
      emails_sent_this_month: 'الرسائل المرسلة هذا الشهر',
      feature_not_available: 'الميزة غير متاحة',
      priority_support_available: 'دعم أولوية متاح',
      standard_support_only: 'دعم قياسي فقط',
      advanced_reports_available: 'تقارير متقدمة متاحة',

      // Package Management
      package_management: 'إدارة الباقة',
      package_management_description: 'عرض وإدارة باقة العمارة الحالية',
      upgrade_package: 'ترقية الباقة',
      super_admin_unlimited_access: 'وصول غير محدود للمدير العام',
      super_admin_no_package_restrictions: 'المدير العام لا يخضع لقيود الباقات',
      trial: 'تجريبي',
      month: 'شهر',
      year: 'سنة',
      annual_billing: 'فوترة سنوية',
      monthly_billing: 'فوترة شهرية',
      expires_on: 'تنتهي في',
      days_remaining: 'يوم متبقي',
      used: 'مستخدم',
      current_features: 'الميزات الحالية',
      error_loading_package: 'خطأ في تحميل معلومات الباقة',
      no_package_found_for_building: 'لم يتم العثور على باقة للمبنى',

      // Storage Usage
      storage_usage_description: 'عرض تفاصيل استخدام مساحة التخزين',
      refresh: 'تحديث',
      super_admin_unlimited_storage: 'المدير العام لديه مساحة تخزين غير محدودة',
      overall_usage: 'الاستخدام الإجمالي',
      unlimited: 'غير محدود',
      remaining: 'متبقي',
      storage_warning: 'تحذير التخزين',
      storage_near_limit_message: 'أنت تقترب من حد مساحة التخزين المسموحة',
      storage_limit_exceeded: 'تم تجاوز حد التخزين',
      storage_over_limit_message: 'لقد تجاوزت حد مساحة التخزين المسموحة لباقتك',
      file_type_breakdown: 'تفصيل أنواع الملفات',
      monthly_usage_trend: 'اتجاه الاستخدام الشهري',
      files: 'ملفات',
      total_files: 'إجمالي الملفات',
      total_storage_used: 'إجمالي التخزين المستخدم',
      storage_remaining: 'التخزين المتبقي',
      error_loading_storage: 'خطأ في تحميل معلومات التخزين',

      // File Types
      file_type_images: 'الصور',
      file_type_pdf: 'ملفات PDF',
      file_type_documents: 'المستندات',
      file_type_spreadsheets: 'جداول البيانات',
      file_type_archives: 'الأرشيف',
      file_type_other: 'أخرى',

      // Package Upgrade Modal
      popular: 'شائع',
      current: 'حالي',
      storage: 'تخزين',
      file_attachments: 'مرفقات الملفات',
      advanced_reporting: 'تقارير متقدمة',
      downgrade: 'تخفيض',
      billing_cycle: 'دورة الفوترة',
      off: 'خصم',
      payment_method: 'طريقة الدفع',
      credit_card: 'بطاقة ائتمان',
      bank_transfer: 'تحويل بنكي',
      cash: 'نقدي',
      processing: 'جاري المعالجة',
      confirm_change: 'تأكيد التغيير',
      error_loading_packages: 'خطأ في تحميل الباقات',
      package_change_requested: 'تم طلب تغيير الباقة بنجاح',
      package_change_failed: 'فشل في تغيير الباقة',

      // Package Features
      feature_core_saas_admin_tool: 'أداة الإدارة الأساسية',
      feature_expense_tracking: 'تتبع المصروفات',
      feature_income_tracking: 'تتبع الإيرادات',
      feature_neighbor_management: 'إدارة السكان',
      feature_basic_notifications: 'الإشعارات الأساسية',
      feature_file_attachments: 'مرفقات الملفات',
      feature_reports_export: 'تصدير التقارير',
      feature_multi_admin_support: 'دعم متعدد المشرفين',
      feature_unlimited_neighbors: 'عدد لا محدود من السكان',
      feature_advanced_reports: 'تقارير متقدمة',
      feature_priority_support: 'دعم أولوية',
      feature_phone_support: 'الدعم الهاتفي',
      feature_archive_feature: 'ميزة الأرشفة',
      feature_small_sms_package: 'حزمة SMS صغيرة',
      feature_data_archiving: 'أرشفة البيانات',
      feature_in_app_notifications: 'الإشعارات داخل التطبيق',
      feature_email_notifications: 'إشعارات البريد الإلكتروني',
      feature_sms_notifications: 'إشعارات الرسائل النصية',
      feature_unlimited_storage: 'مساحة تخزين غير محدودة',
      feature_advanced_reporting: 'التقارير المتقدمة',
      feature_large_file_support: 'دعم الملفات الكبيرة',
      feature_user_management: 'إدارة المستخدمين',
      feature_payment_reminders: 'تذكيرات الدفع',
      basic_reports_only: 'تقارير أساسية فقط',
      max_file_size_mb: 'حد أقصى {size} ميجابايت',
      upgrade_recommended: 'ترقية مُوصى بها',
      upgrade_recommendation_message: 'أنت تقترب من حدود باقتك الحالية. فكر في الترقية للحصول على المزيد من الميزات.',
      view_upgrade_options: 'عرض خيارات الترقية',
      limits_exceeded: 'تم تجاوز الحدود',
      limits_exceeded_message: 'لقد تجاوزت حدود باقتك الحالية. يرجى الترقية للمتابعة.',
      neighbor_limit_exceeded: 'تم تجاوز حد عدد السكان',
      storage_limit_exceeded: 'تم تجاوز حد التخزين',
      notifications_not_available: 'الإشعارات غير متاحة',
      email_notifications_not_available: 'إشعارات البريد الإلكتروني غير متاحة',
      file_attachments_not_available: 'مرفقات الملفات غير متاحة',
      upgrade_now: 'ترقية الآن',
      unlimited_neighbors_available: 'عدد لا محدود من السكان متاح',
      neighbors_limit_description: '{current} من {limit} سكان',
      unlimited_storage_available: 'مساحة تخزين لا محدودة متاحة',
      storage_limit_description: '{used} من {limit} جيجابايت مستخدمة',
      error_loading_usage_data: 'خطأ في تحميل بيانات الاستخدام',

      // Package names and descriptions from screenshot
      free_package: 'الباقة المجانية',
      basic_package: 'الباقة الأساسية',
      standard_package: 'الباقة القياسية',
      free_package_description: 'باقة مجانية أساسية للمباني الصغيرة مع ميزات محدودة',
      basic_package_description: 'أداة إدارة SaaS الأساسية للمباني الصغيرة',
      standard_package_description: 'باقة قياسية مع تصدير التقارير ودعم متعدد المشرفين',

      // Feature translations from screenshot
      expense_tracking: 'تتبع المصروفات',
      income_tracking: 'تتبع الإيرادات',
      basic_expense_tracking: 'تتبع المصروفات الأساسي',
      basic_income_tracking: 'تتبع الإيرادات الأساسي',
      basic_user_management: 'إدارة المستخدمين الأساسية',
      basic_notifications: 'الإشعارات الأساسية',
      basic_reports: 'التقارير الأساسية',

      // Limits from screenshot
      max_10_neighbors: 'حد أقصى 10 جار',
      max_50_neighbors: 'حد أقصى 50 جار',
      storage_1gb: 'مساحة تخزين 1 جيجابايت',
      storage_5gb: 'مساحة تخزين 5 جيجابايت',
      storage_25gb: 'مساحة تخزين 25 جيجابايت',
      trial_protection_14_days: 'تجربة مجانية لمدة 14 أيام',

      // Additional pricing page text
      recommended: 'الأكثر شعبية',
      per_month_short: 'في الشهر',
      free: 'مجاني',
      choose_plan: 'اختر الخطة',
      current_plan: 'الخطة الحالية',
      upgrade_now: 'ترقية الآن',
      downgrade: 'تخفيض الدرجة',
      contact_us: 'اتصل بنا',

      // Language labels
      arabic: 'العربية',
      english: 'الإنجليزية',
      enter_package_name_english: 'أدخل اسم الباقة بالإنجليزية',
      enter_package_description_english: 'أدخل وصف الباقة بالإنجليزية',

      // User Management
      loading: 'جاري التحميل...',
      role: 'الدور',
      all_roles: 'جميع الأدوار',
      admin: 'مدير',
      neighbor: 'ساكن',
      building: 'العمارة',
      all_buildings: 'جميع العمارات',
      apply_filters: 'تطبيق التصفيات',
      add_new_user: 'إضافة مستخدم جديد',
      total_users: 'إجمالي المستخدمين',
      admins: 'المديرين',
      neighbors: 'السكان',
      buildings: 'العمارات',
      active_users: 'المستخدمين النشطين',
      users: 'المستخدمين',
      edit: 'تعديل',
      delete: 'حذف',
      edit_user: 'تعديل المستخدم',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      apartment: 'الشقة',
      apartment_number: 'رقم الشقة',
      success: 'نجح',
      error: 'خطأ',
      user_updated_successfully: 'تم تحديث المستخدم بنجاح',
      user_deleted_successfully: 'تم حذف المستخدم بنجاح',
      failed_to_delete_user: 'فشل في حذف المستخدم',
      failed_to_load_users: 'فشل في تحميل المستخدمين',
      confirm_delete_user: 'هل أنت متأكد من حذف المستخدم "{name}"؟',
      no_permission_access_page: 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
      create_user: 'إنشاء مستخدم',
      all_users: 'جميع المستخدمين',
      expenses: 'المصروفات',
      incomes: 'الإيرادات',
      user_created_successfully: 'تم إنشاء المستخدم بنجاح',
      leave_blank_keep_current: '(اتركه فارغاً للاحتفاظ بالحالي)',
      failed_to_save_user: 'فشل في حفظ المستخدم',
      select_building: 'اختر العمارة',

      // Income Management
      income_management: 'إدارة الإيرادات',
      manage_building_incomes: 'إدارة إيرادات العمارة',
      filter_incomes_description: 'تصفية سجلات الإيرادات حسب التاريخ والسنة',
      average_monthly: 'المتوسط الشهري',
      from: 'من',
      clear_all: 'مسح الكل',
      create_income_description: 'أضف دخل جديد للعمارة مع تفاصيل الدفع',
      back_to_incomes: 'العودة للإيرادات',
      income_details: 'تفاصيل الدخل',
      income_details_description: 'أدخل تفاصيل الدخل والمبلغ المدفوع',
      income_recorded: 'تم تسجيل الدخل',
      income_recorded_successfully: 'تم تسجيل الدخل بنجاح',
      recording_failed: 'فشل التسجيل',
      loading_income: 'جاري تحميل الإيراد...',
      income_not_found: 'الإيراد غير موجود',
      failed_load_income: 'فشل في تحميل الإيراد',
      edit_income_details_description: 'تحديث تفاصيل الإيراد ومعلومات الدفع',

      // User Management Updates
      user_management: 'إدارة المستخدمين',
      manage_building_users: 'إدارة مستخدمي العمارة والسكان',
      filter_users_description: 'تصفية المستخدمين حسب العمارة',
      recent_users: 'المستخدمين الجدد',
      create_user_description: 'أضف مستخدم جديد للعمارة مع تفاصيل الحساب',
      back_to_users: 'العودة للمستخدمين',
      user_details: 'تفاصيل المستخدم',
      user_details_description: 'أدخل تفاصيل المستخدم ومعلومات الحساب',

      // Building Management Updates
      manage_buildings_description: 'إدارة العمارات والرسوم الشهرية',
      filter_buildings_description: 'تصفية العمارات حسب الحالة والرسوم',
      total_residents: 'إجمالي السكان',
      status: 'الحالة',
      all_statuses: 'جميع الحالات',
      active: 'نشط',
      inactive: 'غير نشط',
      monthly_fee_range: 'نطاق الرسوم الشهرية',
      all_ranges: 'جميع النطاقات',
      fee_range: 'نطاق الرسوم',

      // Building Create/Edit Updates
      create_building_description: 'أضف عمارة جديدة مع تفاصيل الإدارة والرسوم',
      back_to_buildings: 'العودة للعمارات',
      building_details: 'تفاصيل العمارة',
      building_details_description: 'أدخل تفاصيل العمارة ومعلومات الإدارة',
      edit_building_description: 'تحديث تفاصيل العمارة والرسوم الشهرية',
      edit_building_details_description: 'تحديث تفاصيل العمارة ومعلومات الإدارة',


      // Home Page
      building_management: 'لجنة العمارة',
      building_management_short: 'لجنة البناء هي مجموعة مختارة من السكان، مسؤولة عن إدارة المسؤوليات المشتركة داخل المبنى. يشمل ذلك تنظيم أعمال الصيانة، ومتابعة الإيرادات والمصروفات، وتحصيل الرسوم، وضمان الشفافية والإنصاف في جميع الأمور المالية المتعلقة بصيانة المبنى والخدمات المشتركة. تعمل اللجنة كحلقة وصل بين الجيران ومقدمي الخدمات لضمان عيش كريم وتعاوني.',
      home: 'الرئيسية',
      about: 'معلومات عنا',
      services: 'خدماتنا',
      contact: 'تواصل معنا',
      get_started_login: 'ابدأ الان',
      generate_monthly_subscriptions: 'توليد الاشتراكات الشهرية',
      generating: 'جاري التوليد...',

      // About Us Page
      about_us_title: 'حول لجنة العمارة',
      about_us_subtitle: 'نحن نقدم حلول شاملة لإدارة المباني السكنية',
      about_mission_title: 'مهمتنا',
      about_mission_text: 'نهدف إلى تبسيط إدارة المباني السكنية من خلال توفير منصة رقمية شاملة تساعد إدارات المباني على تتبع المصروفات والإيرادات وإدارة شؤون السكان بكفاءة وشفافية.',
      about_vision_title: 'رؤيتنا',
      about_vision_text: 'أن نكون المنصة الرائدة في المنطقة لإدارة المباني السكنية، مما يساهم في تحسين جودة الحياة للسكان وتعزيز الشفافية في إدارة الأموال.',
      about_values_title: 'قيمنا',
      about_transparency: 'الشفافية',
      about_transparency_desc: 'نؤمن بأهمية الشفافية الكاملة في جميع المعاملات المالية',
      about_efficiency: 'الكفاءة',
      about_efficiency_desc: 'نسعى لتوفير أدوات تزيد من كفاءة إدارة المباني',
      about_innovation: 'الابتكار',
      about_innovation_desc: 'نطور حلول تقنية مبتكرة لتلبية احتياجات العصر الحديث',

      // Services Page
      services_title: 'خدماتنا',
      services_subtitle: 'حلول شاملة لإدارة المباني السكنية',
      service_expense_title: 'إدارة المصروفات',
      service_expense_desc: 'تتبع وإدارة جميع مصروفات المبنى بطريقة منظمة وشفافة مع إمكانية توليد التقارير المفصلة',
      service_income_title: 'إدارة الإيرادات',
      service_income_desc: 'تسجيل ومتابعة جميع المدفوعات والإيرادات من السكان مع نظام تنبيهات للمدفوعات المتأخرة',
      service_residents_title: 'إدارة السكان',
      service_residents_desc: 'قاعدة بيانات شاملة للسكان مع إمكانية تتبع المدفوعات والمستحقات لكل ساكن',
      service_reports_title: 'التقارير والإحصائيات',
      service_reports_desc: 'تقارير مالية مفصلة وإحصائيات تساعد في اتخاذ القرارات الإدارية السليمة',
      service_notifications_title: 'نظام التنبيهات',
      service_notifications_desc: 'تنبيهات تلقائية للمدفوعات المستحقة والمواعيد المهمة',
      service_security_title: 'الأمان والحماية',
      service_security_desc: 'حماية عالية للبيانات مع نظام صلاحيات متقدم لضمان الخصوصية',

      // Contact Page
      contact_title: 'اتصل بنا',
      contact_subtitle: 'نحن هنا لمساعدتك',
      contact_info_title: 'معلومات الاتصال',
      contact_info_description: 'تواصل معنا عبر أي من الطرق التالية وسنكون سعداء لمساعدتك',
      contact_form_title: 'أرسل لنا رسالة',
      contact_name: 'الاسم',
      contact_email: 'البريد الإلكتروني',
      contact_subject: 'الموضوع',
      contact_message: 'الرسالة',
      contact_send: 'إرسال الرسالة',
      contact_address: 'العنوان',
      contact_phone: 'الهاتف',
      contact_email_label: 'البريد الإلكتروني',
      contact_hours: 'ساعات العمل',
      contact_hours_value: 'الأحد - الخميس: 9:00 ص - 6:00 م',

      // Pricing and Sign Up
      pricing: 'الأسعار',
      get_started: 'ابدأ الآن',
      sign_up: 'إنشاء حساب',
      create_your_account: 'إنشاء حسابك',
      signup_subtitle: 'انضم إلينا لإدارة مبناك بكفاءة',
      personal_information: 'المعلومات الشخصية',
      full_name: 'الاسم الكامل',
      enter_full_name: 'أدخل الاسم الكامل',
      email_address: 'عنوان البريد الإلكتروني',
      enter_email: 'أدخل البريد الإلكتروني',
      enter_phone_number: 'أدخل رقم الهاتف',
      enter_apartment_number: 'أدخل رقم الشقة',
      enter_password: 'أدخل كلمة المرور',
      building_information: 'معلومات المبنى',
      building_name: 'اسم المبنى',
      enter_building_name: 'أدخل اسم المبنى',
      building_address: 'عنوان المبنى',
      enter_building_address: 'أدخل عنوان المبنى',
      city: 'المدينة',
      enter_city: 'أدخل المدينة',
      postal_code: 'الرمز البريدي',
      enter_postal_code: 'أدخل الرمز البريدي',
      monthly_fee_per_neighbor: 'الرسوم الشهرية لكل جار',
      monthly_fee_help: 'الرسوم الشهرية التي يدفعها كل جار للمصروفات المشتركة',
      pricing_summary: 'ملخص الأسعار',
      setup_fee: 'رسوم الإعداد',
      monthly_subscription: 'الاشتراك الشهري',
      first_month_total: 'إجمالي الشهر الأول',
      one_time: 'مرة واحدة',
      per_building_per_month: 'لكل مبنى شهرياً',
      agree_to: 'أوافق على',
      terms_and_conditions: 'الشروط والأحكام',
      registration_failed: 'فشل التسجيل',
      creating_account: 'جاري إنشاء الحساب...',
      create_account: 'إنشاء الحساب',
      already_have_account: 'لديك حساب بالفعل؟',
      sign_in: 'تسجيل الدخول',
      registration_error: 'حدث خطأ أثناء التسجيل',

      // Pricing page
      simple_transparent_pricing: 'أسعار بسيطة وشفافة',
      pricing_subtitle: 'ادفع فقط مقابل ما تحتاجه، بدون رسوم خفية',
      setup_fee_description: 'رسوم إعداد لمرة واحدة لتسجيل المبنى وإعداد النظام',
      monthly_subscription_description: 'اشتراك شهري لكل مبنى للوصول إلى جميع الميزات',
      building_registration: 'تسجيل المبنى',
      admin_account_setup: 'إعداد حساب المدير',
      initial_configuration: 'التكوين الأولي',
      onboarding_support: 'دعم التأهيل',
      recommended: 'موصى به',
      unlimited_neighbors: 'عدد غير محدود من الجيران',
      expense_management: 'إدارة المصروفات',
      payment_tracking: 'تتبع المدفوعات',
      automated_billing: 'الفوترة التلقائية',
      priority_support: 'دعم أولوية',
      ready_to_get_started: 'مستعد للبدء؟',
      start_free_trial: 'ابدأ تجربة مجانية',
      contact_sales: 'تواصل مع المبيعات',
      frequently_asked_questions: 'الأسئلة الشائعة',
      faq_subtitle: 'إجابات على الأسئلة الأكثر شيوعاً',
      faq_setup_fee_question: 'ما هي رسوم الإعداد؟',
      faq_setup_fee_answer: 'رسوم الإعداد هي رسوم لمرة واحدة لتسجيل المبنى وإعداد النظام وحساب المدير.',
      faq_monthly_fee_question: 'كيف يعمل الاشتراك الشهري؟',
      faq_monthly_fee_answer: 'الاشتراك الشهري يتم تحديده حسب الباقة المختارة لكل مبنى شهرياً، بغض النظر عن عدد الجيران.',
      faq_neighbor_fees_question: 'ماذا عن رسوم الجيران؟',
      faq_neighbor_fees_answer: 'يمكنك تحديد الرسوم الشهرية لكل جار للمصروفات المشتركة للمبنى.',
      view_pricing: 'عرض الأسعار',

      // Sign up success
      account_created_successfully: 'تم إنشاء الحساب بنجاح',
      signup_success_message: 'تم إنشاء حسابك ومبناك بنجاح. يمكنك الآن البدء في إدارة مبناك.',
      welcome_aboard: 'مرحباً بك معنا',
      registration_successful_message: 'تم إنشاء حسابك بنجاح',
      welcome_message: 'مرحباً بك',
      account_details: 'تفاصيل الحساب',
      building_admin: 'مدير المبنى',
      neighbor: 'جار',
      whats_next: 'ما التالي؟',
      access_dashboard_now: 'يمكنك الآن الوصول إلى لوحة التحكم',
      manage_building_expenses: 'إدارة مصروفات المبنى وعرض التقارير',
      view_expenses_make_payments: 'عرض المصروفات وإجراء المدفوعات',
      need_help: 'تحتاج مساعدة؟',
      next_steps: 'الخطوات التالية',
      step_1_verify_email: 'تحقق من بريدك الإلكتروني (إذا لزم الأمر)',
      step_2_setup_fee: 'ادفع رسوم الإعداد',
      step_3_add_neighbors: 'أضف الجيران إلى النظام',
      step_4_start_managing: 'ابدأ في إدارة مصروفات وإيرادات المبنى',
      pricing_reminder: 'تذكير بالأسعار',
      billing_info: 'ستتم فوترتك شهرياً بدءاً من الشهر التالي',
      contact_for_pricing: 'اتصل للحصول على السعر',
      based_on_selected_package: 'حسب الباقة المختارة',
      sign_in_to_dashboard: 'تسجيل الدخول إلى لوحة التحكم',
      need_help: 'تحتاج مساعدة؟',
      questions_or_issues: 'أسئلة أو مشاكل؟',
      phone: 'الهاتف',
      phone_number: 'رقم الهاتف',
      helpful_resources: 'موارد مفيدة',
      user_guide: 'دليل المستخدم',
      user_guide_description: 'دليل شامل لاستخدام النظام',
      video_tutorials: 'دروس فيديو',
      video_tutorials_description: 'دروس فيديو خطوة بخطوة',
      community_forum: 'منتدى المجتمع',
      community_forum_description: 'تواصل مع مستخدمين آخرين',
      registration_successful: 'تم التسجيل بنجاح',
      today: 'اليوم',
      starts_next_month: 'يبدأ الشهر القادم',
      total_due_today: 'المجموع المستحق اليوم',
      subscription_billing_note: 'سيتم فوترة الاشتراك الشهري بدءاً من الشهر القادم',
      create_account_and_pay: 'إنشاء الحساب والدفع',
      payment_after_account_creation: 'سيتم توجيهك لدفع رسوم الإعداد بعد إنشاء الحساب',
      processing_payment: 'جاري معالجة الدفع...',
      payment_successful: 'تم الدفع بنجاح',
      setup_fee_paid_successfully: 'تم دفع رسوم الإعداد بنجاح',
      continue_to_dashboard: 'المتابعة إلى لوحة التحكم',
      payment_failed: 'فشل الدفع',
      payment_processing_error: 'حدث خطأ أثناء معالجة الدفع',
      retry_payment: 'إعادة المحاولة',
      contact_support: 'تواصل مع الدعم',
      payment_processing: 'معالجة الدفع',
      building_settings: 'إعدادات المبنى',
      building_settings_description: 'إدارة معلومات المبنى والعملة المفضلة',
      currency: 'العملة',
      currency_display_note: 'هذه العملة للعرض فقط. المدفوعات تتم بالدولار الأمريكي.',
      description: 'الوصف',
      building_description_placeholder: 'أدخل وصفاً للمبنى (اختياري)',
      payment_information: 'معلومات الدفع',
      payment_currency_note: 'جميع المدفوعات تتم بالدولار الأمريكي بغض النظر عن عملة العرض المختارة.',
      save_changes: 'حفظ التغييرات',
      saving: 'جاري الحفظ...',
      building_updated_successfully: 'تم تحديث معلومات المبنى بنجاح',
      error_loading_building_data: 'خطأ في تحميل بيانات المبنى',
      error_updating_building: 'خطأ في تحديث المبنى',

      // Terms and Conditions
      terms_and_conditions: 'الشروط والأحكام',
      terms_subtitle: 'يرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام خدماتنا',
      last_updated: 'آخر تحديث',
      terms_last_updated_date: '15 يناير 2025',
      all_rights_reserved: 'جميع الحقوق محفوظة',
      privacy_policy: 'سياسة الخصوصية',
      contact_us: 'اتصل بنا',
      back_to_home: 'العودة للرئيسية',
      back_to_top: 'العودة للأعلى',

      // Terms sections
      terms_introduction_title: '1. مقدمة وتعريفات',
      terms_introduction_text: 'مرحباً بك في منصة عمارتنا ("المنصة" أو "الخدمة"). هذه الاتفاقية ("الشروط") تشكل عقداً ملزماً قانونياً بينك ("المستخدم" أو "العميل") وبين شركة عمارتنا المحدودة ("الشركة" أو "نحن"). تحكم هذه الشروط استخدامك لمنصتنا الرقمية لإدارة المباني السكنية والخدمات ذات الصلة.',

      terms_acceptance_title: '2. قبول الشروط والأهلية',
      terms_acceptance_text: 'بالوصول إلى المنصة أو استخدامها، فإنك تقر وتوافق على: (أ) أنك قرأت وفهمت هذه الشروط بالكامل؛ (ب) أنك تتمتع بالأهلية القانونية الكاملة لإبرام هذه الاتفاقية؛ (ج) أنك ستلتزم بجميع الشروط والأحكام المنصوص عليها هنا. إذا كنت لا توافق على أي من هذه الشروط، فيجب عليك التوقف فوراً عن استخدام المنصة.',

      terms_service_title: '3. وصف الخدمات والميزات',
      terms_service_text: 'تقدم منصة عمارتنا حلولاً تقنية شاملة لإدارة المباني السكنية، وتشمل على سبيل المثال لا الحصر:',
      terms_service_feature_1: 'نظام إدارة المصروفات والإيرادات مع التتبع التلقائي والتصنيف',
      terms_service_feature_2: 'منصة تتبع المدفوعات والمستحقات مع التنبيهات الذكية',
      terms_service_feature_3: 'قاعدة بيانات شاملة لإدارة معلومات السكان والوحدات السكنية',
      terms_service_feature_4: 'تقارير مالية تفصيلية وتحليلات بيانات متقدمة مع إمكانية التصدير',

      terms_responsibilities_title: '4. التزامات ومسؤوليات المستخدم',
      terms_responsibilities_text: 'بصفتك مستخدماً للمنصة، فإنك تتعهد والتزم بما يلي:',
      terms_responsibility_1: 'تقديم معلومات دقيقة ومحدثة وكاملة في جميع الأوقات، والإبلاغ فوراً عن أي تغييرات',
      terms_responsibility_2: 'الحفاظ على سرية وأمان بيانات الدخول الخاصة بك وعدم مشاركتها مع أطراف ثالثة',
      terms_responsibility_3: 'استخدام المنصة وفقاً للقوانين المعمول بها وبطريقة لا تضر بالآخرين أو بالمنصة',
      terms_responsibility_4: 'سداد جميع الرسوم والمستحقات في المواعيد المحددة دون تأخير',

      terms_payment_title: '5. الشروط المالية والدفع',
      terms_payment_text: 'تخضع الخدمات المقدمة لهيكل الرسوم التالي، والذي يعتبر جزءاً لا يتجزأ من هذه الاتفاقية:',
      terms_payment_fees_title: 'هيكل الرسوم والتسعير',
      terms_setup_fee_text: 'رسوم الإعداد والتهيئة الأولية (دفعة واحدة غير قابلة للاسترداد)',
      terms_monthly_fee_text: 'رسوم الاشتراك الشهري حسب الباقة المختارة لكل مبنى (تُحصل مقدماً)',

      terms_privacy_title: '6. الخصوصية وحماية البيانات',
      terms_privacy_text: 'نحن ملتزمون التزاماً كاملاً بحماية خصوصيتك وأمان بياناتك وفقاً لأعلى المعايير الدولية. تحكم سياسة الخصوصية المنفصلة الخاصة بنا عمليات جمع ومعالجة واستخدام وحماية معلوماتك الشخصية. بالموافقة على هذه الشروط، فإنك تقر بأنك قرأت وفهمت سياسة الخصوصية وتوافق على ممارساتنا في التعامل مع البيانات.',

      terms_liability_title: '7. إخلاء المسؤولية وتحديد الضمانات',
      terms_liability_text: 'رغم سعينا المستمر لتقديم خدمة عالية الجودة، فإن المنصة تُقدم "كما هي" دون أي ضمانات صريحة أو ضمنية. لا نضمن عدم انقطاع الخدمة أو خلوها من الأخطاء أو الفيروسات. مسؤوليتنا الإجمالية تجاهك محدودة بإجمالي المبالغ المدفوعة لنا خلال الاثني عشر شهراً السابقة للحادثة المعنية، ولا تتجاوز في أي حال 1000 دولار أمريكي.',

      terms_termination_title: '8. إنهاء الخدمة والحساب',
      terms_termination_text: 'يحق لأي من الطرفين إنهاء هذه الاتفاقية بإشعار كتابي مسبق مدته ثلاثون (30) يوماً. كما يحق لنا إنهاء حسابك فوراً في حالة انتهاك هذه الشروط. عند الإنهاء، ستفقد فوراً جميع حقوق الوصول إلى المنصة والبيانات المخزنة، وسنحتفظ بالبيانات لمدة تسعين (90) يوماً فقط لأغراض الانتقال.',

      terms_changes_title: '9. تعديل الشروط والإشعارات',
      terms_changes_text: 'نحتفظ بالحق المطلق في تعديل أو تحديث هذه الشروط في أي وقت وفقاً لتقديرنا المنفرد. سيتم إشعارك بأي تغييرات جوهرية قبل ثلاثين (30) يوماً من سريانها عبر البريد الإلكتروني المسجل أو من خلال إشعار بارز على المنصة. استمرارك في استخدام المنصة بعد سريان التعديلات يشكل موافقة منك على الشروط المعدلة.',

      terms_contact_title: '10. معلومات الاتصال والدعم',
      terms_contact_text: 'لأي استفسارات أو مخاوف تتعلق بهذه الشروط والأحكام أو لطلب الدعم الفني، يرجى التواصل معنا عبر القنوات التالية:',

      terms_law_title: '11. القانون الحاكم وتسوية النزاعات',
      terms_law_text: 'تخضع هذه الاتفاقية وتُفسر وفقاً لقوانين المملكة الأردنية الهاشمية دون الإخلال بقواعد تنازع القوانين. أي نزاع أو خلاف ينشأ عن هذه الاتفاقية أو يتعلق بها سيخضع للاختصاص الحصري للمحاكم المختصة في عمان، الأردن. تتنازل الأطراف عن أي اعتراض على هذا الاختصاص.',
      agree_to: 'أوافق على',

      // New Professional Guest Mode Translations
      // Navigation
      get_started: 'ابدأ الآن',
      sign_up_here: 'سجل هنا',
      dont_have_account: 'ليس لديك حساب؟',
      already_have_account: 'لديك حساب بالفعل؟',
      sign_in: 'تسجيل الدخول',
      welcome_back: 'مرحباً بعودتك',
      sign_in_to_your_account: 'سجل دخولك إلى حسابك',

      // Home Page
      building_management: 'إدارة المباني',
      made_simple: 'بكل بساطة',
      building_management_description: 'منصة شاملة لإدارة المباني السكنية بكفاءة وشفافية. تتبع المصروفات والإيرادات وإدارة السكان بسهولة.',
      trusted_by_buildings: 'موثوق من قبل المباني',
      no_setup_fees: 'بدون رسوم إعداد',
      free_trial: 'تجربة مجانية',
      '24_7_support': 'دعم 24/7',
      building_dashboard: 'لوحة تحكم المبنى',
      neighbors: 'الجيران',
      monthly_income: 'الدخل الشهري',
      collection_rate: 'معدل التحصيل',
      new_payment: 'دفعة جديدة',
      apartment_3a: 'شقة 3أ',
      maintenance_due: 'صيانة مستحقة',

      // Features
      why_choose_us: 'لماذا تختارنا؟',
      features_description: 'نقدم حلولاً متكاملة لإدارة المباني السكنية بأحدث التقنيات',
      expense_management: 'إدارة المصروفات',
      expense_management_desc: 'تتبع وإدارة جميع مصروفات المبنى بطريقة منظمة وشفافة',
      payment_tracking: 'تتبع المدفوعات',
      payment_tracking_desc: 'متابعة دقيقة لجميع المدفوعات والمستحقات من السكان',
      notifications: 'الإشعارات',
      notifications_desc: 'نظام إشعارات ذكي لتنبيه السكان والإدارة بالمواعيد المهمة',

      // Testimonials
      what_customers_say: 'ماذا يقول عملاؤنا',
      testimonials_description: 'آراء حقيقية من مديري المباني الذين يستخدمون منصتنا',
      testimonial_1_text: 'منصة رائعة سهلت علينا إدارة المبنى كثيراً. الآن كل شيء منظم ومرتب.',
      testimonial_1_name: 'أحمد حسن',
      testimonial_1_role: 'مدير مبنى',
      testimonial_2_text: 'التقارير المالية أصبحت واضحة وشفافة. السكان راضي عن مستوى الشفافية.',
      testimonial_2_name: 'مريم خالد',
      testimonial_2_role: 'أمينة صندوق',
      testimonial_3_text: 'خدمة العملاء ممتازة والدعم الفني سريع ومفيد. أنصح بها بشدة.',
      testimonial_3_name: 'سامر علي',
      testimonial_3_role: 'رئيس لجنة',

      // CTA Sections
      ready_to_get_started: 'مستعد للبدء؟',
      cta_description: 'انضم إلى مئات المباني التي تستخدم منصتنا لإدارة أفضل وأكثر شفافية',
      start_free_trial: 'ابدأ تجربة مجانية',
      contact_sales: 'تواصل مع المبيعات',
      no_credit_card_required: 'لا حاجة لبطاقة ائتمان',

      // About Page
      established_2025: 'تأسست في 2025',
      buildings_managed: 'مبنى مُدار',
      happy_residents: 'ساكن راضٍ',
      satisfaction_rate: 'معدل الرضا',
      our_mission_vision: 'رؤيتنا ورسالتنا',
      mission_vision_description: 'نسعى لتحويل إدارة المباني السكنية إلى تجربة سهلة وشفافة',
      our_mission: 'رسالتنا',
      mission_point_1: 'تبسيط إدارة المباني السكنية',
      mission_point_2: 'زيادة الشفافية المالية',
      mission_point_3: 'تحسين التواصل بين السكان',
      our_vision: 'رؤيتنا',
      vision_point_1: 'أن نكون المنصة الرائدة في المنطقة',
      vision_point_2: 'تمكين المجتمعات السكنية',
      vision_point_3: 'الابتكار المستمر في الحلول',

      // Values
      our_values: 'قيمنا',
      values_description: 'القيم التي نؤمن بها وتوجه عملنا اليومي',
      transparency: 'الشفافية',
      about_transparency_desc: 'نؤمن بالشفافية الكاملة في جميع العمليات المالية والإدارية',
      efficiency: 'الكفاءة',
      about_efficiency_desc: 'نسعى لتقديم حلول فعالة توفر الوقت والجهد',
      innovation: 'الابتكار',
      about_innovation_desc: 'نطور باستمرار حلولاً مبتكرة لتلبية احتياجات عملائنا',
      reliability: 'الموثوقية',
      about_reliability_desc: 'نضمن خدمة موثوقة ومستقرة على مدار الساعة',

      // Team
      meet_our_team: 'تعرف على فريقنا',
      team_description: 'فريق من الخبراء المتخصصين في تطوير حلول إدارة المباني',
      team_member_1_name: 'أحمد حسن',
      team_member_1_role: 'المدير التنفيذي',
      team_member_1_bio: 'خبرة 15 عاماً في إدارة المباني والتطوير العقاري',
      team_member_2_name: 'مريم خالد',
      team_member_2_role: 'مديرة التطوير',
      team_member_2_bio: 'متخصصة في تطوير البرمجيات وتجربة المستخدم',
      team_member_3_name: 'سامر علي',
      team_member_3_role: 'مدير المبيعات',
      team_member_3_bio: 'خبير في خدمة العملاء وتطوير الأعمال',

      ready_to_join_us: 'مستعد للانضمام إلينا؟',
      about_cta_description: 'ابدأ رحلتك معنا اليوم واكتشف كيف يمكننا تحسين إدارة مبناك',
      get_started_today: 'ابدأ اليوم',

      // Services Page
      comprehensive_solutions: 'حلول شاملة',
      core_services: 'خدمات أساسية',
      support_available: 'دعم متاح',
      cloud_based: 'قائم على السحابة',
      our_core_services: 'خدماتنا الأساسية',
      core_services_description: 'نقدم مجموعة شاملة من الخدمات لإدارة المباني السكنية بكفاءة',
      track_all_expenses: 'تتبع جميع المصروفات',
      categorize_expenses: 'تصنيف المصروفات',
      generate_reports: 'إنشاء التقارير',
      income_management: 'إدارة الإيرادات',
      monthly_fee_collection: 'تحصيل الرسوم الشهرية',
      automated_reminders: 'تذكيرات تلقائية',
      resident_management: 'إدارة السكان',
      resident_profiles: 'ملفات السكان',
      contact_management: 'إدارة جهات الاتصال',
      apartment_assignments: 'تخصيص الشقق',
      additional_features: 'ميزات إضافية',
      additional_features_description: 'المزيد من الميزات المتقدمة لتحسين تجربة إدارة المبنى',
      reports_analytics: 'التقارير والتحليلات',
      smart_notifications: 'الإشعارات الذكية',
      security_privacy: 'الأمان والخصوصية',
      how_it_works: 'كيف يعمل',
      how_it_works_description: 'ثلاث خطوات بسيطة للبدء في إدارة مبناك بكفاءة',
      step_1_title: 'إنشاء الحساب',
      step_1_description: 'سجل حسابك وأضف معلومات المبنى الأساسية',
      step_2_title: 'إعداد النظام',
      step_2_description: 'أضف السكان والشقق وحدد الرسوم الشهرية',
      step_3_title: 'ابدأ الإدارة',
      step_3_description: 'تتبع المصروفات والإيرادات وأرسل الإشعارات للسكان',
      simple_transparent_pricing: 'تسعير بسيط وشفاف',
      pricing_integration_description: 'خطة واحدة تناسب جميع احتياجاتك بسعر عادل',
      month: 'شهر',
      per_building: 'لكل مبنى',
      setup_fee_70: 'رسوم إعداد لمرة واحدة',
      view_full_pricing: 'عرض التسعير الكامل',
      ready_to_transform_building: 'مستعد لتحويل إدارة مبناك؟',
      services_cta_description: 'انضم إلى مئات المباني التي تثق بنا لإدارة أفضل وأكثر كفاءة',

      // Contact Page
      get_in_touch: 'تواصل معنا',
      call_us: 'اتصل بنا',
      email_us: 'راسلنا',
      business_hours: 'ساعات العمل',
      contact_hours_value: 'الأحد - الخميس: 9:00 ص - 6:00 م',
      our_office: 'مكتبنا',
      middle_east: 'الشرق الأوسط',
      phone_support: 'الدعم الهاتفي',
      available_24_7: 'متاح 24/7',
      email_support: 'الدعم عبر البريد',
      response_within_24h: 'رد خلال 24 ساعة',
      jordan_timezone: 'توقيت الأردن',
      send_us_message: 'أرسل لنا رسالة',
      contact_form_description: 'املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن',
      enter_your_name: 'أدخل اسمك',
      enter_your_email: 'أدخل بريدك الإلكتروني',
      select_subject: 'اختر الموضوع',
      general_inquiry: 'استفسار عام',
      technical_support: 'دعم فني',
      sales_inquiry: 'استفسار مبيعات',
      partnership: 'شراكة',
      other: 'أخرى',
      enter_your_message: 'أدخل رسالتك',
      privacy_notice: 'نحن نحترم خصوصيتك. لن نشارك معلوماتك مع أطراف ثالثة.',
      send_message: 'إرسال الرسالة',
      sending: 'جاري الإرسال',
      message_sent_successfully: 'تم إرسال الرسالة بنجاح',
      we_will_respond_soon: 'سنرد عليك في أقرب وقت ممكن',
      message_send_failed: 'فشل في إرسال الرسالة',
      please_try_again: 'يرجى المحاولة مرة أخرى',
      frequently_asked_questions: 'الأسئلة الشائعة',
      faq_description: 'إجابات على الأسئلة الأكثر شيوعاً حول خدماتنا',
      faq_1_question: 'كم يكلف استخدام المنصة؟',
      faq_1_answer: 'نقدم باقات متنوعة تناسب جميع أحجام المباني مع رسوم إعداد لمرة واحدة.',
      faq_2_question: 'هل يمكنني تجربة المنصة مجاناً؟',
      faq_2_answer: 'نعم، نقدم تجربة مجانية لمدة 30 يوماً لجميع الميزات.',
      faq_3_question: 'هل البيانات آمنة؟',
      faq_3_answer: 'نعم، نستخدم أحدث تقنيات التشفير لحماية بياناتك.',
      faq_4_question: 'هل يوجد دعم فني؟',
      faq_4_answer: 'نعم، نقدم دعم فني 24/7 عبر الهاتف والبريد الإلكتروني.',
      still_have_questions: 'لا تزال لديك أسئلة؟',
      contact_cta_description: 'ابدأ رحلتك معنا اليوم واكتشف كيف يمكننا تحسين إدارة مبناك',

      // Login/Register
      email_address: 'عنوان البريد الإلكتروني',
      enter_your_password: 'أدخل كلمة المرور',
      remember_me: 'تذكرني',
      forgot_password: 'نسيت كلمة المرور؟',
      signing_in: 'جاري تسجيل الدخول',
      login_failed: 'فشل تسجيل الدخول',
      create_your_account: 'إنشاء حسابك',
      signup_subtitle: 'انضم إلى آلاف المديرين الذين يثقون بنا لإدارة مبانيهم',
      account_info: 'معلومات الحساب',
      building_info: 'معلومات المبنى',
      complete: 'اكتمال',
      personal_information: 'المعلومات الشخصية',
      full_name: 'الاسم الكامل',
      enter_full_name: 'أدخل الاسم الكامل',
      enter_email: 'أدخل البريد الإلكتروني',
      password_requirements: 'يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل',
      confirm_password: 'تأكيد كلمة المرور',

      // Footer
      footer_description: 'منصة شاملة لإدارة المباني السكنية بكفاءة وشفافية في الشرق الأوسط',
      quick_links: 'روابط سريعة',
      support: 'الدعم',
      made_with_love: 'صُنع بحب',
      back_to_home: 'العودة للرئيسية',

      // Pricing Page
      transparent_pricing: 'تسعير شفاف',
      no_hidden_fees: 'بدون رسوم خفية',
      what_you_see_is_what_you_pay: 'ما تراه هو ما تدفعه',
      instant_setup: 'إعداد فوري',
      get_started_in_minutes: 'ابدأ في دقائق',
      money_back_guarantee: 'ضمان استرداد الأموال',
      '30_day_guarantee': 'ضمان 30 يوم',
      choose_your_plan: 'اختر خطتك',
      pricing_plans_description: 'خطط بسيطة وشفافة تناسب جميع أحجام المباني',
      one_time_payment: 'دفعة واحدة',
      data_migration: 'نقل البيانات',
      advanced_reports: 'تقارير متقدمة',
      mobile_app_access: 'الوصول للتطبيق المحمول',
      ready_to_start_saving: 'مستعد لبدء التوفير؟',
      pricing_cta_description: 'انضم إلى مئات المباني التي توفر الوقت والمال مع منصتنا',
      cancel_anytime: 'إلغاء في أي وقت',
      pricing_faqs: 'الأسئلة الشائعة حول التسعير',
      pricing_faqs_description: 'إجابات على الأسئلة الأكثر شيوعاً حول خطط التسعير',
      pricing_faq_1_question: 'هل يمكنني تغيير خطتي لاحقاً؟',
      pricing_faq_1_answer: 'نعم، يمكنك ترقية أو تخفيض خطتك في أي وقت. التغييرات تسري من الفاتورة التالية.',
      pricing_faq_2_question: 'ماذا يحدث إذا تجاوزت حد الشقق؟',
      pricing_faq_2_answer: 'لا توجد حدود على عدد الشقق. يمكنك إضافة عدد غير محدود من الشقق والسكان.',
      pricing_faq_3_question: 'هل هناك عقود طويلة المدى؟',
      pricing_faq_3_answer: 'لا، جميع خططنا شهرية ويمكنك الإلغاء في أي وقت بدون رسوم إضافية.',
      pricing_faq_4_question: 'هل تقدمون خصومات للمباني الكبيرة؟',
      pricing_faq_4_answer: 'نعم، نقدم خصومات خاصة للمباني الكبيرة والشركات. تواصل معنا للحصول على عرض مخصص.',
      more_questions: 'لديك المزيد من الأسئلة؟',
      transform_building_management: 'حوّل إدارة مبناك اليوم',
      pricing_final_cta_description: 'انضم إلى آلاف المديرين الذين يثقون بنا لإدارة مبانيهم بكفاءة أكبر',
      get_started_now: 'ابدأ الآن',
      schedule_demo: 'احجز عرض تجريبي',
      join_satisfied_customers: 'انضم إلى عملائنا الراضين'

    };

    // English translations
    this.translations.en = {
      // Navigation & General
      app_name: 'Amaretna',
      welcome: 'Welcome to Amaretna',
      login: 'Login',
      register: 'Register',
      logout: 'Logout',
      dashboard: 'Dashboard',
      loading: 'Loading...',
      save: 'Save',
      cancel: 'Cancel',
      edit: 'Edit',

      // Common namespace for shared translations
      common: {
        cancel: 'Cancel',
        save: 'Save',
        edit: 'Edit',
        delete: 'Delete',
        create: 'Create',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        try_again: 'Try Again'
      },
      delete: 'Delete',
      create: 'Create',
      add: 'Add',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      search: 'Search',
      filter: 'Filter',
      actions: 'Actions',
      view: 'View',
      status: 'Status',
      date: 'Date',
      amount: 'Amount',
      total: 'Total',
      name: 'Name',
      email: 'Email',
      password: 'Password',
      confirm_password: 'Confirm Password',
      yes: 'Yes',
      no: 'No',

      // Dashboard titles
      admin_dashboard: 'Admin Dashboard',
      neighbor_dashboard: 'Neighbor Dashboard',

      // Menu items
      expenses: 'Expenses',
      incomes: 'Incomes',
      users: 'Users',
      buildings: 'Buildings',
      my_building: 'My Building',

      // User Management
      user_management: 'User Management',
      create_user: 'Create New User',
      edit_user: 'Edit User',
      apartment_number: 'Apartment Number',
      role: 'Role',
      building: 'Building',
      admin: 'Admin',
      neighbor: 'Neighbor',
      super_admin: 'Super Admin',
      select_role: 'Select Role',
      admin_role_limited: 'Admin Role Limited',
      admin_limit_reached_for_package: 'Admin limit reached ({current}/{limit}) for current package',
      package_does_not_support_multiple_admins: 'Current package does not support multiple admins',
      cannot_create_admin_check_package: 'Cannot create admin, check package',

      // Expense Management
      expense_management: 'Expense Management',
      create_expense: 'Create New Expense',
      edit_expense: 'Edit Expense',
      edit_expense_description: 'Update existing expense details',
      edit_expense_details_description: 'Update expense details and payment information',
      loading_expense: 'Loading expense...',
      expense_not_found: 'Expense not found',
      expense_not_found_description: 'The requested expense was not found or has been deleted',
      expense_type: 'Expense Type',
      month: 'Month',
      year: 'Year',
      automatic: 'Automatic',
      manual: 'Manual',
      notes: 'Notes',
      expenses: 'Expenses',
      neighbor: 'Neighbor',
      select_neighbor: 'Select Neighbor',
      amount: 'Amount',
      enter_amount: 'Enter Amount',
      select_month: 'Select Month',
      select_year: 'Select Year',
      enter_notes: 'Enter Notes',
      automatic_expense: 'Automatic Expense',
      attachments: 'Attachments',
      processing: 'Processing...',
      update_expense: 'Update Expense',
      total_expenses: 'Total Expenses',
      this_month: 'This Month',
      filters: 'Filters',
      all_types: 'All Types',
      all_months: 'All Months',
      all_years: 'All Years',
      clear_filters: 'Clear Filters',
      no_expenses: 'No Expenses',
      get_started_by_creating_expense: 'Get started by creating a new expense',
      create_first_expense: 'Create First Expense',
      type: 'Type',
      created_at: 'Created At',
      actions: 'Actions',
      confirm_delete_expense: 'Are you sure you want to delete this expense?',
      deleted: 'Deleted',
      expense_deleted_successfully: 'Expense deleted successfully',
      deletion_failed: 'Deletion Failed',
      failed_to_delete_expense: 'Failed to delete expense',
      failed_to_load_expenses: 'Failed to load expenses',
      select_expense_type: 'Select Expense Type',
      back_to_expenses: 'Back to Expenses',
      expense_details: 'Expense Details',
      expense_details_description: 'Enter expense details and payment information',
      create_expense_description: 'Create a new expense for the building',
      expenses_description: 'List of all building expenses',

      // Building Expense Management
      building_expense_management: 'Building Expense Management',
      building_expenses: 'Building Expenses',
      create_building_expense: 'Create New Building Expense',
      edit_building_expense: 'Edit Building Expense',
      edit_building_expense_description: 'Update existing building expense details',
      loading_building_expense: 'Loading building expense...',
      building_expense_not_found: 'Building expense not found',
      building_expense_not_found_description: 'The requested building expense was not found or has been deleted',
      back_to_building_expenses: 'Back to Building Expenses',
      building_expense_details: 'Building Expense Details',
      building_expense_details_description: 'Enter building expense details',
      create_building_expense_description: 'Create a new expense for the entire building',
      manage_building_expenses_description: 'Manage and track all general building expenses',
      building_expenses_description: 'List of all general building expenses',
      building_expense_type: 'Building Expense Type',
      select_building_expense_type: 'Select Building Expense Type',
      update_building_expense: 'Update Building Expense',
      total_building_expenses: 'Total Building Expenses',
      no_building_expenses: 'No Building Expenses',
      get_started_by_creating_building_expense: 'Get started by creating a new building expense',
      create_first_building_expense: 'Create First Building Expense',
      confirm_delete_building_expense: 'Are you sure you want to delete this building expense?',
      building_expense_deleted_successfully: 'Building expense deleted successfully',
      failed_to_delete_building_expense: 'Failed to delete building expense',
      failed_to_load_building_expenses: 'Failed to load building expenses',

      // View Pages
      view_expense: 'View Expense',
      view_expense_description: 'View expense details and attachments',
      view_income: 'View Income',
      view_income_description: 'View income details and attachments',
      view_building_expense: 'View Building Expense',
      view_building_expense_description: 'View building expense details and attachments',
      basic_information: 'Basic Information',
      month_year: 'Month/Year',
      due_date: 'Due Date',
      currency_information: 'Currency Information',
      original_amount: 'Original Amount',
      converted_amount: 'Converted Amount',
      exchange_rate: 'Exchange Rate',
      exchange_rate_date: 'Exchange Rate Date',
      loading_income: 'Loading income...',
      income_details: 'Income Details',
      income_not_found: 'Income not found',
      income_not_found_description: 'The requested income was not found or has been deleted',
      back_to_incomes: 'Back to Incomes',
      incomes: 'Incomes',
      apartment: 'Apartment',
      payment_date: 'Payment Date',
      payment_method: 'Payment Method',
      cash: 'Cash',
      bank_transfer: 'Bank Transfer',
      check: 'Check',
      received: 'Received',
      pending: 'Pending',
      cancelled: 'Cancelled',
      confirm_delete_income: 'Are you sure you want to delete this income?',
      income_deleted: 'Income deleted successfully',
      delete_failed: 'Delete Failed',
      failed_delete_income: 'Failed to delete income',

      // Income Management
      income_management: 'Income Management',
      create_income: 'Create New Income',
      edit_income: 'Edit Income',
      edit_income_description: 'Update income details and payment information',
      payment_date: 'Payment Date',
      payment_method: 'Payment Method',
      cash: 'Cash',
      bank_transfer: 'Bank Transfer',
      check: 'Check',

      // Building Management
      building_management: 'Building Management',
      create_building: 'Create New Building',
      edit_building: 'Edit Building',
      building_name: 'Building Name',
      address: 'Address',
      city: 'City',
      country: 'Country',
      postal_code: 'Postal Code',
      monthly_fee: 'Monthly Fee',
      description: 'Description',

      // Status values
      pending: 'Pending',
      paid: 'Paid',
      received: 'Received',
      cancelled: 'Cancelled',

      // Messages
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Info',
      no_data: 'No data available',
      loading_menu: 'Loading menu...',

      // Months
      january: 'January',
      february: 'February',
      march: 'March',
      april: 'April',
      may: 'May',
      june: 'June',
      july: 'July',
      august: 'August',
      september: 'September',
      october: 'October',
      november: 'November',
      december: 'December',

      // Form validation
      required_field: 'This field is required',
      invalid_email: 'Invalid email address',
      password_mismatch: 'Passwords do not match',

      // Email Verification
      verify_your_email: 'Verify Your Email',
      email_verified: 'Email Verified',
      verification_failed: 'Verification Failed',
      verifying_email: 'Verifying Email',
      please_wait_verifying: 'Please wait while we verify your email address',
      email_verified_successfully: 'Email verified successfully',
      email_verified_message: 'Your email has been verified successfully. You can now access your account.',
      verification_error_message: 'An error occurred while verifying your email address.',
      verify_email_message: 'Please click the link in the email to verify your account.',
      invalid_verification_link: 'Invalid verification link',
      resend_verification_email: 'Resend Verification Email',
      sending: 'Sending...',
      continue_to_dashboard: 'Continue to Dashboard',
      go_to_login: 'Go to Login',
      back_to_login: 'Back to Login',
      click_verification_link_message: 'Please click the verification link in the email sent to you.',
      need_help: 'Need help?',
      contact_support: 'Contact Support',
      verification_email_sent_successfully: 'Verification email sent successfully',
      failed_to_send_verification_email: 'Failed to send verification email',

      // Admin Invitations
      admin_invitation: 'Admin Invitation',
      invitation_accepted: 'Invitation Accepted',
      invitation_error: 'Invitation Error',
      loading_invitation: 'Loading Invitation',
      please_wait_loading: 'Please wait while we load the invitation details',
      invitation_accepted_successfully: 'Invitation accepted successfully',
      invitation_accepted_message: 'Your admin invitation has been accepted successfully. You can now log in.',
      invitation_error_message: 'An error occurred while processing the invitation.',
      accept_invitation_message: 'Please create a password to accept the admin invitation.',
      invalid_invitation_token: 'Invalid invitation token',
      invitation_expired: 'Invitation has expired',
      invitation_no_longer_valid: 'Invitation is no longer valid',
      failed_to_load_invitation: 'Failed to load invitation details',
      failed_to_accept_invitation: 'Failed to accept invitation',
      invitation_details: 'Invitation Details',
      building: 'Building',
      admin: 'Admin',
      invited_by: 'Invited by',
      expires_on: 'Expires on',
      enter_password: 'Enter password',
      confirm_password: 'Confirm password',
      accepting: 'Accepting...',
      accept_invitation: 'Accept Invitation',

      // Admin Management
      admin_management: 'Admin Management',
      admin_management_description: 'Manage building admins and pending invitations',
      invite_admin: 'Invite Admin',
      current_admins: 'Current Admins',
      pending_invitations: 'Pending Invitations',
      current_admins_description: 'List of all current building administrators',
      pending_invitations_description: 'Invitations that have been sent but not yet accepted',
      no_admins_found: 'No admins found',
      no_pending_invitations: 'No pending invitations',
      joined_date: 'Joined Date',
      invitee: 'Invitee',
      expires: 'Expires',
      invite_new_admin: 'Invite New Admin',
      full_name: 'Full Name',
      enter_full_name: 'Enter full name',
      email_address: 'Email Address',
      enter_email_address: 'Enter email address',
      select_building: 'Select Building',
      send_invitation: 'Send Invitation',
      invitation_sent_successfully: 'Invitation sent successfully',
      failed_to_send_invitation: 'Failed to send invitation',
      invitation_resent_successfully: 'Invitation resent successfully',
      failed_to_resend_invitation: 'Failed to resend invitation',
      invitation_cancelled_successfully: 'Invitation cancelled successfully',
      failed_to_cancel_invitation: 'Failed to cancel invitation',
      admin_removed_successfully: 'Admin removed successfully',
      failed_to_remove_admin: 'Failed to remove admin',
      confirm_cancel_invitation: 'Are you sure you want to cancel this invitation?',
      confirm_remove_admin: 'Are you sure you want to remove this admin?',
      resend: 'Resend',
      cancel: 'Cancel',
      remove: 'Remove',
      pending: 'Pending',
      accepted: 'Accepted',
      expired: 'Expired',
      cancelled: 'Cancelled',

      // Subscription Management
      subscription_management: 'Subscription Management',
      subscription_management_description: 'Manage building subscriptions and service packages',
      create_subscription: 'Create Subscription',
      active_subscriptions: 'Active Subscriptions',
      trial_subscriptions: 'Trial Subscriptions',
      total_revenue: 'Total Revenue',
      cancelled_subscriptions: 'Cancelled Subscriptions',
      all_statuses: 'All Statuses',
      all_packages: 'All Packages',
      packages: 'Packages',
      search_buildings: 'Search buildings',
      clear_filters: 'Clear Filters',
      subscriptions: 'Subscriptions',
      manage_all_building_subscriptions: 'Manage all building subscriptions',
      expense_types: 'Expense Types',
      building_expense_types: 'Building Expense Types',
      no_subscriptions_found: 'No subscriptions found',
      billing_cycle: 'Billing Cycle',
      expires_at: 'Expires At',
      create_new_subscription: 'Create New Subscription',
      select_building: 'Select Building',
      select_package: 'Select Package',
      monthly: 'Monthly',
      annual: 'Annual',
      trial_days: 'Trial Days',
      optional_trial_days: 'Trial days (optional)',
      notes: 'Notes',
      optional_notes: 'Notes (optional)',
      creating: 'Creating',
      edit: 'Edit',
      renew: 'Renew',
      showing: 'Showing',
      to: 'to',
      of: 'of',
      results: 'results',
      previous: 'Previous',
      next: 'Next',
      month: 'month',
      subscription_created_successfully: 'Subscription created successfully',
      failed_to_create_subscription: 'Failed to create subscription',
      failed_to_load_subscriptions: 'Failed to load subscriptions',
      confirm_renew_subscription: 'Are you sure you want to renew this subscription?',
      subscription_renewed_successfully: 'Subscription renewed successfully',
      failed_to_renew_subscription: 'Failed to renew subscription',
      confirm_cancel_subscription: 'Are you sure you want to cancel this subscription?',
      subscription_cancelled_successfully: 'Subscription cancelled successfully',
      failed_to_cancel_subscription: 'Failed to cancel subscription',
      edit_subscription: 'Edit Subscription',
      update_subscription: 'Update Subscription',
      subscription_updated_successfully: 'Subscription updated successfully',
      failed_to_update_subscription: 'Failed to update subscription',
      auto_renew: 'Auto Renew',
      updating: 'Updating',
      active: 'Active',
      inactive: 'Inactive',
      trial: 'Trial',
      cancelled: 'Cancelled',
      expired: 'Expired',
      unsaved_changes_warning: 'You have unsaved changes. Please save or cancel your changes before closing.',
      confirm_discard_changes: 'You have unsaved changes. Are you sure you want to discard them?',
      trial: 'Trial',
      active: 'Active',
      expired: 'Expired',
      inactive: 'Inactive',

      // Package Management
      package_management: 'Package Management',
      package_management_description: 'Manage service packages, pricing, and features',
      create_package: 'Create Package',
      search_packages: 'Search packages',
      active_only: 'Active Only',
      inactive_only: 'Inactive Only',
      no_packages_found: 'No packages found',
      create_first_package: 'Get started by creating your first service package',
      popular: 'Popular',
      year: 'year',
      max_neighbors: 'Max Neighbors',
      storage_limit: 'Storage Limit',
      email_notifications: 'Email Notifications',
      priority_support: 'Priority Support',
      advanced_reporting: 'Advanced Reporting',
      duplicate: 'Duplicate',
      deactivate: 'Deactivate',
      activate: 'Activate',

      // Package Change Requests
      package_change_requests: 'Package Change Requests',
      package_change_requests_description: 'Manage package change requests from buildings',
      total_requests: 'Total Requests',
      search_requests: 'Search requests',
      all_statuses: 'All Statuses',
      all_cycles: 'All Cycles',
      no_requests_found: 'No requests found',
      no_requests_found_description: 'No package change requests at the moment',
      building: 'Building',
      requested_by: 'Requested By',
      current_package: 'Current Package',
      requested_package: 'Requested Package',
      billing_cycle: 'Billing Cycle',
      status: 'Status',
      date: 'Date',
      actions: 'Actions',
      view_details: 'View Details',
      approve_request: 'Approve Request',
      reject_request: 'Reject Request',
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      monthly: 'Monthly',
      annual: 'Annual',
      request_details: 'Request Details',
      building_information: 'Building Information',
      building_name: 'Building Name',
      building_address: 'Building Address',
      request_information: 'Request Information',
      request_date: 'Request Date',
      payment_method: 'Payment Method',
      package_information: 'Package Information',
      current_price: 'Current Price',
      requested_price: 'Requested Price',
      reason: 'Reason',
      admin_notes: 'Admin Notes',
      status_information: 'Status Information',
      approved_by: 'Approved By',
      approved_at: 'Approved At',
      rejected_at: 'Rejected At',
      approve: 'Approve',
      reject: 'Reject',
      rejection_reason: 'Rejection Reason',
      rejection_reason_placeholder: 'Enter rejection reason (optional)',
      request_approved_successfully: 'Request approved successfully',
      request_rejected_successfully: 'Request rejected successfully',
      error_loading_requests: 'Error loading requests',
      error_approving_request: 'Error approving request',
      error_rejecting_request: 'Error rejecting request',
      showing: 'Showing',
      to: 'to',
      of: 'of',
      results: 'results',
      previous: 'Previous',
      next: 'Next',
      no_package: 'No Package',
      free: 'Free',
      create_new_package: 'Create New Package',
      edit_package: 'Edit Package',
      package_name: 'Package Name',
      enter_package_name: 'Enter package name',
      slug: 'Slug',
      auto_generated: 'Auto-generated',
      enter_package_description: 'Enter package description',
      monthly_price: 'Monthly Price',
      enter_monthly_price: 'Enter monthly price',
      annual_price: 'Annual Price',
      optional_annual_price: 'Annual price (optional)',
      storage_limit_gb: 'Storage Limit (GB)',
      unlimited: 'Unlimited',
      no_trial: 'No trial',
      sms_notifications: 'SMS Notifications',
      updating: 'Updating',
      update_package: 'Update Package',
      enter_new_package_name: 'Enter new package name',
      package_created_successfully: 'Package created successfully',
      failed_to_create_package: 'Failed to create package',
      failed_to_load_packages: 'Failed to load packages',
      max_admins: 'Max Admins',
      max_admins_placeholder: 'Number of admins allowed',
      multi_admin_support: 'Multi-Admin Support',
      package_updated_successfully: 'Package updated successfully',
      failed_to_update_package: 'Failed to update package',
      package_duplicated_successfully: 'Package duplicated successfully',
      failed_to_duplicate_package: 'Failed to duplicate package',
      confirm_activate_package: 'Are you sure you want to activate this package?',
      confirm_deactivate_package: 'Are you sure you want to deactivate this package?',
      package_status_updated_successfully: 'Package status updated successfully',
      failed_to_update_package_status: 'Failed to update package status',
      confirm_delete_package: 'Are you sure you want to delete this package? This action cannot be undone.',
      package_deleted_successfully: 'Package deleted successfully',
      failed_to_delete_package: 'Failed to delete package',

      // Email Settings
      email_settings: 'Email Settings',
      configure_email_limitations_for_package: 'Configure email limitations for package',
      email_limit_per_month: 'Monthly Email Limit',
      email_limit_per_day: 'Daily Email Limit',
      email_quota_warnings: 'Email Quota Warnings',
      
      // Advanced Reporting Settings
      advanced_reporting_settings: 'Advanced Reporting Settings',
      configure_advanced_reporting_features: 'Configure advanced reporting features',
      max_custom_reports: 'Max Custom Reports',
      max_scheduled_reports: 'Max Scheduled Reports',
      available_chart_types: 'Available Chart Types',
      select_available_chart_types: 'Select available chart types',
      custom_reports_enabled: 'Custom Reports Enabled',
      report_scheduling_enabled: 'Report Scheduling Enabled',
      advanced_charts_enabled: 'Advanced Charts Enabled',

      // Export Management
      export_management: 'Export Management',
      manage_exports_and_reports: 'Manage exports and reports',
      create_export: 'Create Export',
      total_exports: 'Total Exports',
      completed_exports: 'Completed Exports',
      pending_exports: 'Pending Exports',
      monthly_quota: 'Monthly Quota',
      export_history: 'Export History',
      all_types: 'All Types',
      all_statuses: 'All Statuses',
      financial_summary: 'Financial Summary',
      building_summary: 'Building Summary',
      neighbor_payments: 'Neighbor Payments',
      expense_report: 'Expense Report',
      income_report: 'Income Report',
      neighbor_report: 'Neighbor Report',
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      failed: 'Failed',
      created_by: 'Created By',
      created_at: 'Created At',
      download: 'Download',
      no_exports: 'No Exports',
      no_exports_description: 'No exports have been created yet',
      export_type: 'Export Type',
      select_export_type: 'Select Export Type',
      export_format: 'Export Format',
      from_date: 'From Date',
      to_date: 'To Date',
      include_attachments: 'Include Attachments',
      description: 'Description',
      optional: 'Optional',
      export_description_placeholder: 'Export description (optional)',
      export_quota_info: 'Export Quota Information',
      monthly_exports_used: 'Monthly Exports Used',
      remaining_today: 'Remaining Today',
      creating: 'Creating',
      export_created_successfully: 'Export created successfully',
      export_creation_failed: 'Export creation failed',
      download_failed: 'Download failed',
      confirm_delete_export: 'Are you sure you want to delete this export?',
      export_deleted_successfully: 'Export deleted successfully',

      // Expense Template Management
      expense_template_management: 'Expense Template Management',
      manage_automated_expense_templates: 'Manage automated expense templates',
      create_template: 'Create Template',
      total_templates: 'Total Templates',
      active_templates: 'Active Templates',
      auto_generate_templates: 'Auto Generate Templates',
      generated_this_month: 'Generated This Month',
      expense_templates: 'Expense Templates',
      all_templates: 'All Templates',
      inactive_only: 'Inactive Only',
      run_auto_generation: 'Run Auto Generation',
      generating: 'Generating',
      expense_type: 'Expense Type',
      frequency: 'Frequency',
      next_generation: 'Next Generation',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly',
      no_templates: 'No Templates',
      no_templates_description: 'No templates have been created yet',
      confirm_run_auto_generation: 'Are you sure you want to run auto generation?',
      auto_generation_completed: 'Auto generation completed',
      auto_generation_failed: 'Auto generation failed',
      template_updated_successfully: 'Template updated successfully',
      update_failed: 'Update failed',
      confirm_delete_template: 'Are you sure you want to delete this template?',
      template_deleted_successfully: 'Template deleted successfully',
      delete_failed: 'Delete failed',
      edit_template: 'Edit Template',
      template_name: 'Template Name',
      template_name_placeholder: 'Enter template name',
      template_description_placeholder: 'Enter template description (optional)',
      title_template: 'Title Template',
      title_template_placeholder: 'Enter title template (e.g., Payment Reminder: {{expense_type}})',
      message_template: 'Message Template',
      message_template_placeholder: 'Enter message template (e.g., Dear {{user_name}}, you have a payment due...)',
      notification_template_info: 'Notification Templates',
      notification_template_description: 'These templates are used for general notifications (email, in-app notifications). For SMS messages, there is a separate system.',
      expense_created: 'Expense Created',
      income_received: 'Income Received',
      payment_received: 'Payment Received',
      overdue_payment: 'Overdue Payment',
      expense_type_name: 'Expense Type Name',
      template_usage_note: 'These templates are automatically used when sending notifications of the specified type.',

      // SMS Management
      sms_management: 'SMS Management',
      sms_settings: 'SMS Settings',
      sms_templates: 'SMS Templates',
      sms_delivery_logs: 'Delivery Logs',
      sms_analytics: 'SMS Analytics',
      send_bulk_sms: 'Send Bulk SMS',
      send_test_sms: 'Send Test SMS',
      configure_sms_provider_and_settings: 'Configure SMS provider and settings',
      sms_notifications: 'SMS Notifications',
      enable_disable_sms_notifications: 'Enable or disable SMS notifications',
      sms_provider_configuration: 'SMS Provider Configuration',
      sms_provider: 'SMS Provider',
      select_provider: 'Select Provider',
      twilio_account_sid: 'Twilio Account SID',
      twilio_auth_token: 'Twilio Auth Token',
      twilio_phone_number: 'Twilio Phone Number',
      aws_access_key_id: 'AWS Access Key ID',
      aws_secret_access_key: 'AWS Secret Access Key',
      aws_region: 'AWS Region',
      select_aws_region: 'Select AWS Region',
      monthly_sms_limit: 'Monthly SMS Limit',
      cost_per_sms: 'Cost per SMS',
      default_country_code: 'Default Country Code',
      enabled_notification_types: 'Enabled Notification Types',
      require_user_opt_in: 'Require User Opt-in',
      require_user_opt_in_description: 'Users must opt-in to receive SMS notifications',
      usage_statistics: 'Usage Statistics',
      sms_sent_this_month: 'SMS Sent This Month',
      monthly_limit: 'Monthly Limit',
      remaining_messages: 'Remaining Messages',
      save_settings: 'Save Settings',

      // SMS Templates
      manage_sms_templates_description: 'Create and manage SMS templates',
      create_sms_template: 'Create SMS Template',
      edit_sms_template: 'Edit SMS Template',
      sms_template: 'SMS Template',
      sms_template_placeholder: 'Enter message text (e.g., Hello {user_name}, you have a payment due...)',
      characters: 'characters',
      sms_segment: 'SMS segment',
      sms_segments: 'SMS segments',
      multiple_sms_warning: 'Will send multiple messages',
      template_active: 'Template Active',
      sms_limits: 'SMS Limits',
      sms_limit_160: 'Single message contains maximum 160 characters',
      sms_limit_segments: 'Longer messages will be split into multiple segments',
      sms_limit_cost: 'Each segment counts as a separate message',
      template_preview_empty: 'No content to preview',
      preview_note: 'This is a preview with sample data',
      no_sms_templates: 'No SMS Templates',
      no_sms_templates_description: 'Start by creating a new SMS template',
      create_first_sms_template: 'Create First SMS Template',
      all_template_types: 'All Template Types',
      system_templates: 'System Templates',
      custom_templates: 'Custom Templates',
      system: 'System',
      duplicate: 'Duplicate',
      template_duplicated_successfully: 'Template duplicated successfully',
      duplicate_failed: 'Failed to duplicate template',
      confirm_delete_sms_template: 'Are you sure you want to delete this template?',
      template_deleted_successfully: 'Template deleted successfully',
      delete_failed: 'Failed to delete template',

      // SMS Delivery & Analytics
      track_sms_delivery_status: 'Track SMS delivery status and history',
      sms_usage_statistics_and_insights: 'SMS usage statistics and insights',
      all_providers: 'All Providers',
      date_from: 'Date From',
      date_to: 'Date To',
      recipient: 'Recipient',
      message: 'Message',
      provider: 'Provider',
      cost: 'Cost',
      sent_at: 'Sent At',
      actions: 'Actions',
      unknown_user: 'Unknown User',
      details: 'Details',
      retry: 'Retry',
      no_delivery_logs: 'No Delivery Logs',
      no_delivery_logs_description: 'No SMS delivery logs found',
      sms_retry_initiated: 'SMS retry initiated',
      retry_failed: 'Retry failed',
      showing_results: 'Showing {from} to {to} of {total} results',
      previous: 'Previous',
      next: 'Next',
      select_expense_type: 'Select Expense Type',
      currency: 'Currency',
      auto_generate_expenses: 'Auto Generate Expenses',
      auto_generate_description: 'Expenses will be generated automatically based on the specified frequency',
      due_days_after: 'Due Days After',
      due_days_after_description: 'Number of days after generation for payment due',
      template_description_placeholder: 'Template description (optional)',
      template_active: 'Template Active',
      saving: 'Saving',
      update_template: 'Update Template',
      template_created_successfully: 'Template created successfully',
      save_failed: 'Save failed',

      // Notification Template Management
      notification_template_management: 'Notification Template Management',
      manage_notification_templates: 'Manage notification templates and messages',
      notification_templates: 'Notification Templates',
      email_templates: 'Email Templates',
      sms_templates: 'SMS Templates',
      payment_reminder: 'Payment Reminder',
      expense_notification: 'Expense Notification',
      general_announcement: 'General Announcement',
      overdue_notification: 'Overdue Notification',
      welcome_message: 'Welcome Message',
      system_maintenance: 'System Maintenance',
      priority: 'Priority',
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      delivery_method: 'Delivery Method',
      email: 'Email',
      sms: 'SMS',
      push: 'Push',
      preview: 'Preview',
      no_notification_templates_description: 'No notification templates have been created yet',
      create_notification_template: 'Create Notification Template',
      edit_notification_template: 'Edit Notification Template',
      select_notification_type: 'Select Notification Type',
      email_template: 'Email Template',
      email_subject: 'Email Subject',
      email_subject_placeholder: 'Enter email subject',
      email_body: 'Email Body',
      email_body_placeholder: 'Enter email body',
      sms_template: 'SMS Template',
      sms_message: 'SMS Message',
      sms_message_placeholder: 'Enter SMS message',
      characters: 'characters',
      push_notification_template: 'Push Notification Template',
      push_title: 'Push Title',
      push_title_placeholder: 'Enter push title',
      push_body: 'Push Body',
      push_body_placeholder: 'Enter push body',
      available_variables: 'Available Variables',
      user_full_name: 'User Full Name',
      building_name: 'Building Name',
      payment_amount: 'Payment Amount',
      payment_due_date: 'Payment Due Date',
      apartment_number: 'Apartment Number',
      preview_template: 'Preview Template',
      type: 'Type',
      delivery_methods: 'Delivery Methods',
      email_preview: 'Email Preview',
      sms_preview: 'SMS Preview',
      push_preview: 'Push Preview',
      app_name: 'Amaretna',
      send_test: 'Send Test',
      sending: 'Sending',
      close: 'Close',
      test_notification_sent: 'Test notification sent',
      test_send_failed: 'Test send failed',
      preview_note: 'Preview Note',
      preview_note_description: 'This is a preview with sample data. Actual data will vary by user.',

      // Payment Gateway Settings
      payment_gateway_settings: 'Payment Gateway Settings',
      configure_payment_gateways: 'Configure payment gateways',
      test_connection: 'Test Connection',
      testing: 'Testing',
      stripe_description: 'Global payment gateway supporting credit cards',
      publishable_key: 'Publishable Key',
      stripe_publishable_key_placeholder: 'Enter Stripe publishable key',
      secret_key: 'Secret Key',
      stripe_secret_key_placeholder: 'Enter Stripe secret key',
      webhook_secret: 'Webhook Secret',
      stripe_webhook_secret_placeholder: 'Enter Stripe webhook secret',
      test_mode: 'Test Mode',
      webhook_url: 'Webhook URL',
      paypal_description: 'Payment gateway supporting PayPal and credit cards',
      client_id: 'Client ID',
      paypal_client_id_placeholder: 'Enter PayPal client ID',
      client_secret: 'Client Secret',
      paypal_client_secret_placeholder: 'Enter PayPal client secret',
      sandbox_mode: 'Sandbox Mode',
      bank_transfer: 'Bank Transfer',
      bank_transfer_description: 'Direct bank transfer for payments',
      bank_name: 'Bank Name',
      bank_name_placeholder: 'Enter bank name',
      account_number: 'Account Number',
      account_number_placeholder: 'Enter account number',
      account_holder_name: 'Account Holder Name',
      account_holder_placeholder: 'Enter account holder name',
      iban: 'IBAN',
      iban_placeholder: 'Enter IBAN',
      swift_code: 'SWIFT Code',
      swift_code_placeholder: 'Enter SWIFT code',
      instructions: 'Instructions',
      bank_transfer_instructions_placeholder: 'Enter bank transfer instructions',
      banking_information: 'Banking Information',
      account_holder: 'Account Holder',
      bank_address: 'Bank Address',
      payment_instructions: 'Payment Instructions',
      bank_transfer_instructions: 'Please transfer the amount to the account mentioned above with the invoice number in the details.',
      general_settings: 'General Settings',
      general_payment_settings: 'General payment settings',
      default_currency: 'Default Currency',
      payment_timeout: 'Payment Timeout',
      minutes: 'minutes',
      auto_confirm_payments: 'Auto Confirm Payments',
      send_payment_receipts: 'Send Payment Receipts',
      connection_test_successful: 'Connection test successful',
      connection_test_failed: 'Connection test failed',

      // Management Menu
      management: 'Management',

      // Admin Transfer
      transfer: 'Transfer',
      no_other_buildings_available: 'No other buildings available for transfer',
      select_building_for_transfer: 'Select building to transfer to',
      enter_building_id: 'Enter building ID',
      invalid_building_id: 'Invalid building ID',
      confirm_transfer_admin: 'Are you sure you want to transfer admin {admin} to building {building}?',
      admin_transferred_successfully: 'Admin transferred successfully',
      failed_to_transfer_admin: 'Failed to transfer admin',

      // Notifications
      user_created: 'User created successfully',
      user_updated: 'User updated successfully',
      user_deleted: 'User deleted successfully',
      expense_created: 'Expense created successfully',
      expense_updated: 'Expense updated successfully',
      expense_deleted: 'Expense deleted successfully',
      income_created: 'Income created successfully',
      income_updated: 'Income updated successfully',
      income_deleted: 'Income deleted successfully',
      building_created: 'Building created successfully',
      building_updated: 'Building updated successfully',
      building_deleted: 'Building deleted successfully',

      // Notification Center
      notifications: 'Notifications',
      mark_all_read: 'Mark All Read',
      mark_read: 'Mark Read',
      mark_unread: 'Mark Unread',
      unread: 'Unread',
      no_notifications: 'No notifications',
      no_notifications_description: 'Notifications will appear here when available',
      view_all_notifications: 'View All Notifications',
      confirm_delete_notification: 'Are you sure you want to delete this notification?',
      manage_your_notifications: 'Manage your notifications',
      all_types: 'All Types',
      payment_reminders: 'Payment Reminders',
      expense_notifications: 'Expense Notifications',
      income_notifications: 'Income Notifications',
      announcements: 'Announcements',
      payment_confirmations: 'Payment Confirmations',
      overdue_notifications: 'Overdue Notifications',
      refresh: 'Refresh',
      details: 'Details',
      due_date: 'Due Date',
      payment_date: 'Payment Date',
      payment_method: 'Payment Method',
      days_overdue: 'Days Overdue',
      cash: 'Cash',
      bank_transfer: 'Bank Transfer',
      check: 'Check',
      showing: 'Showing',
      to: 'to',
      of: 'of',
      results: 'results',
      just_now: 'Just now',
      minutes_ago: 'minutes ago',
      hours_ago: 'hours ago',
      days_ago: 'days ago',
      high: 'High',
      medium: 'Medium',
      low: 'Low',

      // File Upload
      drop_files_here: 'Drop files here',
      drop_files_now: 'Drop files now',
      or: 'or',
      browse_files: 'browse files',
      supported_formats: 'Supported formats',
      max_file_size: 'Max file size',
      selected_files: 'Selected files',
      files_selected: 'files selected',
      clear_all: 'Clear All',
      upload_files: 'Upload Files',
      uploading: 'Uploading',
      upload_errors: 'Upload Errors',
      optional: 'optional',
      file_description_placeholder: 'File description (optional)',
      make_files_public: 'Make files public',
      public_files_description: 'Public files can be viewed by all building residents',
      attachments: 'Attachments',
      no_attachments: 'No attachments',
      add_more_files: 'Add more files',
      add_new_attachments: 'Add new attachments',
      loading_files: 'Loading files...',
      error_loading_files: 'Error loading files',
      retry: 'Retry',
      download: 'Download',
      preview: 'Preview',
      close: 'Close',
      public: 'Public',
      private: 'Private',
      confirm_delete_file: 'Are you sure you want to delete this file?',
      file_deleted_successfully: 'File deleted successfully',
      error_deleting_file: 'Error deleting file',
      max_files_exceeded: 'Maximum {max} files allowed',
      file_size_exceeded: '{filename}: File size exceeds {maxSize}MB',
      file_type_not_allowed: '{filename}: File type not allowed. Allowed types: {allowedTypes}',
      file_already_selected: '{filename}: File already selected',

      // Search
      search_placeholder: 'Search expenses, incomes, users...',
      searching: 'Searching',
      search_results: 'Search Results',
      results_for: 'Results for',
      view_all_results: 'View all results',
      no_results_found: 'No results found',
      try_different_search: 'Try different search terms or adjust filters',
      found: 'Found',
      results: 'results',
      with_filters: 'with filters',
      clear_filters: 'Clear filters',
      amount_range: 'Amount range',
      min: 'Min',
      max: 'Max',
      date_filter: 'Date filter',
      amount_filter: 'Amount filter',
      view_expenses: 'View expenses',
      view_details: 'View details',
      income_payment: 'Income payment',
      apartment: 'Apartment',
      user: 'User',
      expense: 'Expense',
      income: 'Income',
      payment: 'Payment',
      expense_type: 'Expense type',

      // Additional translations
      more: 'More',
      advance_report: 'Advanced Reports',
      export_report: 'Export Reports',
      create_custom_reports_and_analytics: 'Create custom reports and analytics',
      create_custom_report: 'Create Custom Report',
      advanced_reporting_unavailable: 'Advanced Reporting Unavailable',
      advanced_reporting_standard_plus_only: 'Advanced reporting is available only for Standard and Pro packages',
      custom_reports: 'Custom Reports',
      total_generations: 'Total Generations',
      recent_generations: 'Recent Generations',
      last_30_days: 'Last 30 days',
      chart_types: 'Chart Types',
      available: 'Available',
      report_templates: 'Report Templates',
      analytics: 'Analytics',

      // Reports & Advanced Reporting
      reports: {
        title: 'Reports',
        subtitle: 'Generate and manage your financial reports',
        loading: 'Loading reports...',
        create_report: 'Create Report',
        create_new_report: 'Create New Report',
        create_report_description: 'Create a custom report to track your financial data.',
        report_name: 'Report Name',
        report_name_placeholder: 'e.g., Monthly Expense Summary',
        description: 'Description',
        description_placeholder: 'Optional description of what this report includes...',
        report_type: 'Report Type',
        financial_summary: 'Financial Summary',
        expenses_only: 'Expenses Only',
        income_only: 'Income Only',
        creating: 'Creating...',
        quick_reports: 'Quick Reports',
        financial_summary_desc: 'Complete overview of income and expenses',
        expenses_report: 'Expenses Report',
        expenses_report_desc: 'Detailed breakdown of all expenses',
        income_report: 'Income Report',
        income_report_desc: 'Summary of all income sources',
        custom_reports: 'Custom Reports',
        of: 'of',
        reports: 'reports',
        no_custom_reports: 'No custom reports',
        no_custom_reports_desc: 'Get started by creating your first custom report.',
        this_month: 'This Month',
        generated: 'Generated',
        custom_reports_count: 'Custom Reports',
        run: 'Run',
        running: 'Running...',
        no_description_provided: 'No description provided',
        created: 'Created',
        generations: 'generations',
        errors: {
          upgrade_required: 'Upgrade Required',
          upgrade_required_desc: 'Advanced reporting features require a Standard or Pro package.',
          authentication_required: 'Authentication Required',
          authentication_required_desc: 'Please log in to access reporting features.',
          something_wrong: 'Something went wrong',
          something_wrong_desc: 'We encountered an error loading your reports. Please try again.',
          view_packages: 'View Packages',
          go_to_login: 'Go to Login',
          try_again: 'Try Again'
        }
      },

      error_loading_reporting_stats: 'Error loading reporting stats',
      exports_enabled: 'Exports Enabled',
      export_settings: 'Export Settings',
      exports_per_month: 'Exports per Month',
      max_records_per_export: 'Max Records per Export',
      export_formats: 'Export Formats',
      leave_empty_for_unlimited: 'Leave empty for unlimited',
      default_1000: 'Default: 1000',
      configure_export_limitations_for_package: 'Configure export limitations for this package',
      select_allowed_export_formats: 'Select allowed export formats',
      portable_document_format: 'Portable Document Format',
      spreadsheet_format: 'Spreadsheet Format',

      // Advanced Reporting - Templates
      choose_from_predefined_templates: 'Choose from predefined templates',
      financial_summary_report: 'Financial Summary Report',
      comprehensive_financial_overview: 'Comprehensive financial overview',
      user_activity_report: 'User Activity Report',
      track_user_engagement_patterns: 'Track user engagement patterns',
      maintenance_tracking_report: 'Maintenance Tracking Report',
      monitor_building_maintenance_activities: 'Monitor building maintenance activities',
      income_expense_analysis: 'Income & expense analysis',
      monthly_trends: 'Monthly trends',
      category_breakdown: 'Category breakdown',
      login_patterns: 'Login patterns',
      feature_usage: 'Feature usage',
      user_demographics: 'User demographics',
      maintenance_schedule: 'Maintenance schedule',
      cost_analysis: 'Cost analysis',
      completion_rates: 'Completion rates',
      template_preview: 'Template Preview',
      included_features: 'Included Features',
      available_chart_types: 'Available Chart Types',
      data_fields: 'Data Fields',
      available_filters: 'Available Filters',
      sample_preview: 'Sample Preview',
      sample_chart_will_appear_here: 'Sample chart will appear here',
      preview_available_after_data_selection: 'Preview available after data selection',
      estimated_creation_time: 'Estimated creation time',
      use_this_template: 'Use This Template',
      no_templates_available: 'No templates available',
      templates_will_be_available_soon: 'Templates will be available soon',

      // Advanced Reporting - Custom Reports
      manage_your_custom_reports: 'Manage your custom reports',
      create_new_report: 'Create New Report',
      search_reports: 'Search reports',
      all_categories: 'All Categories',
      all_statuses: 'All Statuses',
      active: 'Active',
      draft: 'Draft',
      archived: 'Archived',
      no_chart: 'No chart',
      run_report: 'Run Report',
      edit: 'Edit',
      duplicate: 'Duplicate',
      share: 'Share',
      delete: 'Delete',
      no_custom_reports: 'No custom reports',
      get_started_by_creating_first_report: 'Get started by creating your first report',
      create_first_report: 'Create First Report',
      running_report: 'Running report: {name}',
      edit_feature_coming_soon: 'Edit feature coming soon',
      duplicate_feature_coming_soon: 'Duplicate feature coming soon',
      share_feature_coming_soon: 'Share feature coming soon',
      confirm_delete_report: 'Are you sure you want to delete the report "{name}"?',
      report_deleted_successfully: 'Report deleted successfully',
      error_loading_reports: 'Error loading reports',
      package_upgrade_required: 'Package Upgrade Required',
      custom_reports_require_upgrade: 'Custom reports require an upgrade to a higher package',
      please_login_to_access_reports: 'Please log in to access reports',
      go_to_login: 'Go to Login',
      feature_not_available_for_your_package: 'This feature is not available in your current package',
      something_went_wrong_try_again: 'Something went wrong, please try again',
      try_again: 'Try Again',
      upgrade_required: 'Upgrade Required',
      custom_reports_require_standard_or_pro_package: 'Custom reports require a Standard or Pro package',
      available_packages: 'Available Packages',
      standard_package_features: 'Standard Package: Custom reports, data export, multi-admin support',
      pro_package_features: 'Pro Package: All features with phone support and archiving',
      close: 'Close',

      // Advanced Reporting - Report Builder
      build_your_custom_report_step_by_step: 'Build your custom report step by step',
      basic_info: 'Basic Info',
      data_source: 'Data Source',
      fields: 'Fields',
      visualization: 'Visualization',
      report_name: 'Report Name',
      enter_report_name: 'Enter report name',
      describe_your_report: 'Describe your report',
      select_category: 'Select category',
      select_data_source: 'Select data source',
      expenses_data: 'Expenses Data',
      building_expenses_and_costs: 'Building expenses and costs',
      users_data: 'Users Data',
      user_activity_and_engagement: 'User activity and engagement',
      select_fields_to_include: 'Select fields to include',
      available_fields: 'Available Fields',
      selected_fields: 'Selected Fields',
      no_fields_selected: 'No fields selected',
      choose_visualization: 'Choose visualization',
      bar_chart: 'Bar Chart',
      compare_values_across_categories: 'Compare values across categories',
      line_chart: 'Line Chart',
      show_trends_over_time: 'Show trends over time',
      pie_chart: 'Pie Chart',
      show_proportions_and_percentages: 'Show proportions and percentages',
      table_view: 'Table View',
      display_data_in_rows_and_columns: 'Display data in rows and columns',
      previous: 'Previous',
      next: 'Next',
      create_report: 'Create Report',
      report_created_successfully: 'Report created successfully',
      error_creating_report: 'Error creating report',

      // Advanced Reporting - Analytics
      reporting_analytics: 'Reporting Analytics',
      insights_and_trends_from_your_reports: 'Insights and trends from your reports',
      last_7_days: 'Last 7 days',
      last_30_days: 'Last 30 days',
      last_90_days: 'Last 90 days',
      last_year: 'Last year',
      refresh: 'Refresh',
      reports_generated: 'Reports Generated',
      avg_generation_time: 'Avg Generation Time',
      most_used_chart: 'Most Used Chart',
      exports_this_period: 'Exports This Period',
      report_generation_trend: 'Report Generation Trend',
      daily_reports: 'Daily reports',
      chart_type_distribution: 'Chart Type Distribution',
      usage_by_type: 'Usage by type',
      chart_will_appear_here: 'Chart will appear here',
      pie_chart_will_appear_here: 'Pie chart will appear here',
      chart_library_integration_needed: 'Chart library integration needed',
      most_popular_reports: 'Most Popular Reports',
      reports_generated_most_frequently: 'Reports generated most frequently',
      times_generated: 'times generated',
      no_reports_generated_yet: 'No reports generated yet',
      start_creating_reports_to_see_analytics: 'Start creating reports to see analytics',
      insights_and_recommendations: 'Insights & Recommendations',
      no_insights_available_yet: 'No insights available yet',
      bar_charts_most_popular_this_month: 'Bar charts are most popular this month',
      report_generation_increased_15_percent: 'Report generation increased by 15%',
      financial_reports_generated_most_frequently: 'Financial reports are generated most frequently',
      analytics_refreshed: 'Analytics refreshed',
      error_loading_analytics: 'Error loading analytics',
      manage_building_expenses: 'Manage Building Expenses',
      filters: 'Filters',
      filter_expenses_description: 'Use the filters below to filter expenses by type, month, and year',
      active_filters: 'Active Filters',
      clear_all_filters: 'Clear All Filters',
      expense_records_description: 'View and manage all building expenses',
      total_records: 'Total Records',
      showing: 'Showing',
      no_expenses_found: 'No expenses found',
      no_expenses_description: 'No expenses have been recorded yet. Start by creating your first expense.',
      create_first_expense: 'Create First Expense',
      create_expense_description: 'Add a new expense to your building',
      back_to_expenses: 'Back to Expenses',
      expense_details: 'Expense Details',
      expense_details_description: 'Enter the details for the new expense below',
      select_expense_type: 'Select expense type',
      select_neighbor: 'Select neighbor',
      select_month: 'Select month',
      automatic_expense: 'Automatic expense',
      automatic_expense_description: 'Will be generated automatically each month',
      notes_placeholder: 'Add any additional notes here...',
      saving: 'Saving...',
      update_expense: 'Update Expense',
      expense_attachments_description: 'Attach receipts or documents related to this expense',
      super_admin_dashboard: 'Super Admin Dashboard',
      super_admin_dashboard_description: 'Manage all buildings and admins in the system',
      admin_dashboard_description: 'Manage your building and track income and expenses',
      buildings_description: 'Manage all buildings in the system',
      admins_description: 'Manage building administrators',
      neighbor_financial_summary_description: 'Financial summary for all neighbors in your building',
      select_role: 'Select role',
      creating_neighbor_user: 'Creating neighbor user',
      neighbor_user_description: 'This user will be created as a neighbor in your building',
      creating_admin_user: 'Creating admin user',
      admin_user_description: 'This user will be created as an admin in your building',
      create_user_description: 'Add a new user to the system',
      back_to_users: 'Back to Users',
      user_details: 'User Details',
      user_details_description: 'Enter the details for the new user below',
      user_created_successfully: 'User created successfully',
      failed_to_save_user: 'Failed to save user',
      outstanding_balance: 'Outstanding Balance',
      total_expenses: 'Total Expenses',
      total_income: 'Total Income',
      financial_summary: 'Financial Summary',
      recent_expenses: 'Recent Expenses',
      recent_income: 'Recent Income',
      view_all: 'View All',
      add_expense: 'Add Expense',
      add_income: 'Add Income',
      add_user: 'Add User',
      add_building: 'Add Building',
      select: 'Select',
      all: 'All',
      type: 'Type',
      user: 'User',

      // Building Information
      building_information: 'Building Information',
      not_specified: 'Not specified',
      monthly_expense_generation: 'Monthly Expense Generation',
      generate_monthly_expenses_description: 'Generate monthly expenses for all neighbors in your building.',
      each_neighbor_charged: 'Each neighbor will be charged',
      generate_monthly_expenses: 'Generate Monthly Expenses',
      generating: 'Generating...',
      no_building_assigned: 'No building assigned to your account.',

      // Income page
      record_new_income: 'Record New Income',
      apply_filters: 'Apply Filters',
      from_date: 'From Date',
      to_date: 'To Date',

      last_30_days: 'Last 30 Days',
      last_3_months: 'Last 3 Months',
      last_6_months: 'Last 6 Months',
      last_year: 'Last Year',

      // Financial Overview
      financial_overview: 'Financial Overview',
      financial_overview_description: 'Comprehensive analysis of the building\'s financial status',
      expenses_vs_income_trend: 'Expenses vs Income Trend',
      monthly_comparison: 'Monthly Comparison',
      net_balance: 'Net Balance',
      avg_monthly_expense: 'Average Monthly Expense',
      current_period: 'Current Period',
      comparison_period: 'Comparison Period',

      this_month: 'This Month',
      total_income: 'Total Income',
      income_records: 'Income Records',
      method: 'Method',
      payment_date: 'Payment Date',
      apartment: 'Apartment',
      neighbor: 'Neighbor',
      showing_results: 'Showing',
      to: 'to',
      of: 'of',
      results: 'results',
      next: 'Next',
      previous: 'Previous',

      // Expense types and filters
      all_types: 'All Types',
      all_months: 'All Months',
      building_services: 'Building Services',
      building_electricity: 'Building Electricity',
      personal_electricity: 'Personal Electricity',
      water: 'Water',
      other: 'Other',
      overdue: 'Overdue',
      auto: 'Auto',
      expense_records: 'Expense Records',

      // Additional messages and confirmations
      confirm_delete_income: 'Are you sure you want to delete this income record?',
      deleted: 'Deleted',
      delete_failed: 'Delete Failed',
      failed_delete_income: 'Failed to delete income record',
      updated: 'Updated',
      update_failed: 'Update Failed',
      error_loading_incomes: 'Error loading incomes',

      // Neighbor dashboard
      my_financial_summary: 'My Financial Summary',
      total_payments: 'Total Payments',
      my_expenses: 'My Expenses',
      my_income_records: 'My Income Records',
      authentication_error: 'Authentication Error',
      user_not_found: 'User not found. Please log in again.',
      data_loaded: 'Data Loaded',
      no_financial_records: 'No expenses or income records found for your account.',
      error_loading_dashboard: 'Error loading dashboard data',
      failed_load_financial_data: 'Failed to load your financial data',

      // Admin dashboard
      total_buildings: 'Total Buildings',
      total_admins: 'Total Admins',
      total_neighbors: 'Total Neighbors',
      total_users: 'Total Users',
      active_buildings: 'Active Buildings',
      admins: 'Admins',
      neighbors: 'Neighbors',
      add_admin: 'Add Admin',
      neighbor_financial_summary: 'Neighbor Financial Summary',
      loading_dashboard_data: 'Loading dashboard data...',
      created: 'Created',
      confirm_delete_building: 'Are you sure you want to delete building "{name}"?',
      failed_delete_building: 'Failed to delete building',
      confirm_delete_admin: 'Are you sure you want to delete admin "{name}"?',
      failed_delete_admin: 'Failed to delete admin',

      // Building form and management
      loading_building: 'Loading building...',
      building_not_found: 'Building not found.',
      failed_load_building: 'Failed to load building',
      building_updated: 'Building updated successfully',
      building_created: 'Building created successfully',
      monthly_fee_description: 'This amount will be charged to each neighbor monthly',
      saving: 'Saving...',
      update_building: 'Update Building',
      create_building: 'Create Building',
      failed_save_building: 'Failed to save building',

      // Building management page
      average_monthly_fee: 'Average Monthly Fee',
      no_permission: 'You do not have permission to access this page.',
      failed_load_buildings: 'Failed to load buildings',
      building_deleted: 'Building deleted successfully',
      no_buildings_found: 'No buildings found.',

      // My building page
      not_specified: 'Not specified',
      monthly_expense_generation: 'Monthly Expense Generation',
      generate_monthly_expenses_description: 'Click the button below to generate monthly expenses for all neighbors in your building.',
      each_neighbor_charged: 'Each neighbor will be charged',
      generating: 'Generating...',
      generate_monthly_expenses: 'Generate Monthly Expenses',
      no_building_assigned: 'No building assigned to your account.',
      failed_load_building_info: 'Failed to load building information',
      confirm_generate_monthly_expenses: 'Generate monthly expenses ({fee}) for all neighbors in your building for {month}/{year}?',
      generated: 'Generated',
      monthly_expenses_generated: 'Monthly expenses generated successfully',
      generation_failed: 'Generation Failed',
      failed_generate_monthly_expenses: 'Failed to generate monthly expenses',

      // Profile management
      my_profile: 'My Profile',
      update_profile: 'Update Profile',
      profile_information: 'Profile Information',
      new_password: 'New Password',
      confirm_new_password: 'Confirm New Password',
      leave_blank_to_keep_current: 'leave blank to keep current',
      profile_updated_successfully: 'Profile updated successfully',
      failed_to_update_profile: 'Failed to update profile',
      failed_to_load_profile: 'Failed to load profile',
      password_confirmation_mismatch: 'Password confirmation does not match',
      updating: 'Updating...',

      // Email Preferences
      email_preferences: 'Email Preferences',
      email_preferences_description: 'Manage your email notification settings',
      email_notifications: 'Email Notifications',
      master_email_toggle_description: 'Enable or disable all email notifications',
      notification_types: 'Notification Types',
      payment_reminders: 'Payment Reminders',
      payment_reminders_description: 'Reminders for due payments',
      expense_notifications: 'Expense Notifications',
      expense_notifications_description: 'Notifications when new expenses are added',
      income_notifications: 'Income Notifications',
      income_notifications_description: 'Notifications when new payments are recorded',
      general_announcements: 'General Announcements',
      general_announcements_description: 'Important announcements from building management',
      overdue_notifications: 'Overdue Notifications',
      overdue_notifications_description: 'Notifications when payments are overdue',
      email_frequency: 'Email Frequency',
      email_frequency_description: 'How often you want to receive notifications',
      immediate: 'Immediate',
      daily: 'Daily',
      weekly: 'Weekly',
      save_preferences: 'Save Preferences',
      send_test_email: 'Send Test Email',
      sending_test_email: 'Sending...',
      reset_to_defaults: 'Reset to Defaults',
      preferences_saved_successfully: 'Preferences saved successfully',
      test_email_sent: 'Test email sent successfully',
      preferences_reset_successfully: 'Preferences reset successfully',
      error_loading_preferences: 'Error loading preferences',
      error_saving_preferences: 'Error saving preferences',
      error_sending_test_email: 'Error sending test email',
      error_resetting_preferences: 'Error resetting preferences',
      confirm_reset_preferences: 'Are you sure you want to reset all preferences to default values?',

      // Package Selection
      choose_your_package: 'Choose Your Package',
      package_selection_description: 'Select the package that fits your building needs and budget',
      most_popular: 'Most Popular',
      current_package: 'Current Package',
      unlimited_neighbors: 'Unlimited Neighbors',
      max_neighbors_count: 'Up to {count} neighbors',
      unlimited_storage: 'Unlimited Storage',
      storage_limit_gb: '{gb}GB Storage',
      in_app_notifications: 'In-App Notifications',
      file_attachments_enabled: 'File Attachments Available',
      file_attachments_disabled: 'File Attachments Not Available',
      standard_support: 'Standard Support',
      get_started: 'Get Started',
      select_package: 'Select Package',
      selected: 'Selected',
      save_up_to: 'Save up to',
      loading_packages: 'Loading packages...',
      error_loading_packages: 'Error loading packages',
      selected_package_summary: 'Selected Package Summary',
      per_year: 'per year',
      per_month: 'per month',
      includes_free_trial: 'Includes {days}-day free trial',
      processing: 'Processing...',
      upgrade_package: 'Upgrade Package',
      start_trial: 'Start Trial',
      current_subscription: 'Current Subscription',
      package: 'Package',
      status: 'Status',
      expires_on: 'Expires On',
      subscription_status_active: 'Active',
      subscription_status_trial: 'Free Trial',
      subscription_status_expired: 'Expired',
      subscription_status_cancelled: 'Cancelled',
      subscription_expiring_soon: 'Subscription expiring soon',
      days_remaining: '{days} days remaining',
      error_subscribing_package: 'Error subscribing to package',
      free_trial_days: '{days}-day free trial',

      // New Package Features
      core_saas_admin_tool: 'Core Admin Tool',
      reports_export: 'Reports Export',
      multi_admin_support: 'Multi-Admin Support',
      phone_support: 'Phone Support',
      archive_feature: 'Archive Feature',
      small_sms_package: 'Small SMS Package',
      data_archiving: 'Data Archiving',
      custom_integrations: 'Custom Integrations',
      additional_sms_packages: 'Additional SMS Packages',
      premium_support: 'Premium Support',
      custom_reports: 'Custom Reports',
      api_access: 'API Access',
      white_labeling: 'White Labeling',
      requires_separate_booking: 'Requires Separate Booking',
      variable_pricing: 'Variable Pricing',
      annual_discount_20: '20% Discount for Annual Payment',

      // Pricing page translations
      monthly: 'Monthly',
      annual: 'Annual',
      per_month: 'per month',
      per_year: 'per year',
      save: 'Save',
      most_popular: 'Most Popular',
      loading_packages: 'Loading packages...',
      error_loading_packages: 'Error loading packages',
      max_neighbors_count: 'Up to {count} neighbors',
      storage_limit_gb: '{gb}GB storage',
      contact_sales: 'Contact Sales',
      get_started: 'Get Started',

      // Package Usage Dashboard
      package_usage: 'Package Usage',
      package_usage_description: 'Track your current usage and package limits',
      loading_usage_data: 'Loading usage data...',
      package_name: 'Package Name',
      upgrade: 'Upgrade',
      neighbors_usage: 'Neighbors Usage',
      storage_usage: 'Storage Usage',
      notifications_usage: 'Notifications Usage',
      email_usage: 'Email Usage',
      available_features: 'Available Features',
      enabled: 'Enabled',
      disabled: 'Disabled',
      notifications_sent_this_month: 'Notifications sent this month',
      emails_sent_this_month: 'Emails sent this month',
      feature_not_available: 'Feature not available',
      priority_support_available: 'Priority support available',
      standard_support_only: 'Standard support only',

      // Package Management
      package_management: 'Package Management',
      package_management_description: 'View and manage your current building package',
      upgrade_package: 'Upgrade Package',
      super_admin_unlimited_access: 'Super Admin Unlimited Access',
      super_admin_no_package_restrictions: 'Super admin has no package restrictions',
      trial: 'Trial',
      month: 'month',
      year: 'year',
      annual_billing: 'Annual Billing',
      monthly_billing: 'Monthly Billing',
      expires_on: 'Expires on',
      days_remaining: 'days remaining',
      used: 'used',
      current_features: 'Current Features',
      error_loading_package: 'Error loading package information',
      no_package_found_for_building: 'No package found for building',

      // Storage Usage
      storage_usage_description: 'View detailed storage usage statistics',
      refresh: 'Refresh',
      super_admin_unlimited_storage: 'Super admin has unlimited storage',
      overall_usage: 'Overall Usage',
      unlimited: 'Unlimited',
      remaining: 'remaining',
      storage_warning: 'Storage Warning',
      storage_near_limit_message: 'You are approaching your storage limit',
      storage_limit_exceeded: 'Storage Limit Exceeded',
      storage_over_limit_message: 'You have exceeded your package storage limit',
      file_type_breakdown: 'File Type Breakdown',
      monthly_usage_trend: 'Monthly Usage Trend',
      files: 'files',
      total_files: 'Total Files',
      total_storage_used: 'Total Storage Used',
      storage_remaining: 'Storage Remaining',
      error_loading_storage: 'Error loading storage information',

      // File Types
      file_type_images: 'Images',
      file_type_pdf: 'PDF Files',
      file_type_documents: 'Documents',
      file_type_spreadsheets: 'Spreadsheets',
      file_type_archives: 'Archives',
      file_type_other: 'Other',
      inactive_users_excluded_from_monthly_expenses: 'Inactive users are excluded from monthly expenses',

      // Package Upgrade Modal
      popular: 'Popular',
      current: 'Current',
      storage: 'storage',
      file_attachments: 'File Attachments',
      advanced_reporting: 'Advanced Reporting',
      downgrade: 'Downgrade',
      billing_cycle: 'Billing Cycle',
      off: 'off',
      payment_method: 'Payment Method',
      credit_card: 'Credit Card',
      bank_transfer: 'Bank Transfer',
      cash: 'Cash',
      processing: 'Processing',
      confirm_change: 'Confirm Change',
      error_loading_packages: 'Error loading packages',
      package_change_requested: 'Package change requested successfully',
      package_change_failed: 'Failed to change package',

      // Package Features
      feature_core_saas_admin_tool: 'Core Admin Tool',
      feature_expense_tracking: 'Expense Tracking',
      feature_income_tracking: 'Income Tracking',
      feature_neighbor_management: 'Neighbor Management',
      feature_basic_notifications: 'Basic Notifications',
      feature_file_attachments: 'File Attachments',
      feature_reports_export: 'Reports Export',
      feature_multi_admin_support: 'Multi-Admin Support',
      feature_unlimited_neighbors: 'Unlimited Neighbors',
      feature_advanced_reports: 'Advanced Reports',
      feature_priority_support: 'Priority Support',
      feature_phone_support: 'Phone Support',
      feature_archive_feature: 'Archive Feature',
      feature_small_sms_package: 'Small SMS Package',
      feature_data_archiving: 'Data Archiving',
      feature_in_app_notifications: 'In-App Notifications',
      feature_email_notifications: 'Email Notifications',
      feature_sms_notifications: 'SMS Notifications',
      feature_unlimited_storage: 'Unlimited Storage',
      feature_advanced_reporting: 'Advanced Reporting',
      feature_large_file_support: 'Large File Support',
      feature_user_management: 'User Management',
      feature_payment_reminders: 'Payment Reminders',
      advanced_reports_available: 'Advanced reports available',
      basic_reports_only: 'Basic reports only',
      max_file_size_mb: 'Max {size}MB file size',
      upgrade_recommended: 'Upgrade Recommended',
      upgrade_recommendation_message: 'You are approaching your current package limits. Consider upgrading for more features.',
      view_upgrade_options: 'View Upgrade Options',
      limits_exceeded: 'Limits Exceeded',
      limits_exceeded_message: 'You have exceeded your current package limits. Please upgrade to continue.',
      neighbor_limit_exceeded: 'Neighbor limit exceeded',
      storage_limit_exceeded: 'Storage limit exceeded',
      notifications_not_available: 'Notifications not available',
      email_notifications_not_available: 'Email notifications not available',
      file_attachments_not_available: 'File attachments not available',
      upgrade_now: 'Upgrade Now',
      unlimited_neighbors_available: 'Unlimited neighbors available',
      neighbors_limit_description: '{current} of {limit} neighbors',
      unlimited_storage_available: 'Unlimited storage available',
      storage_limit_description: '{used} of {limit}GB used',
      error_loading_usage_data: 'Error loading usage data',

      // Package names and descriptions from screenshot
      free_package: 'Free Package',
      basic_package: 'Basic Package',
      standard_package: 'Standard Package',
      free_package_description: 'Free basic package for small buildings with limited features',
      basic_package_description: 'Core SaaS admin tool for small buildings',
      standard_package_description: 'Standard package with reports export and multi-admin support',

      // Feature translations from screenshot
      expense_tracking: 'Expense Tracking',
      income_tracking: 'Income Tracking',
      basic_expense_tracking: 'Basic Expense Tracking',
      basic_income_tracking: 'Basic Income Tracking',
      basic_user_management: 'Basic User Management',
      basic_notifications: 'Basic Notifications',
      basic_reports: 'Basic Reports',

      // Limits from screenshot
      max_10_neighbors: 'Max 10 neighbors',
      max_50_neighbors: 'Max 50 neighbors',
      storage_1gb: '1GB storage',
      storage_5gb: '5GB storage',
      storage_25gb: '25GB storage',
      trial_protection_14_days: '14-day free trial',

      // Additional pricing page text
      recommended: 'Most Popular',
      per_month_short: 'per month',
      free: 'Free',
      choose_plan: 'Choose Plan',
      current_plan: 'Current Plan',
      upgrade_now: 'Upgrade Now',
      downgrade: 'Downgrade',
      contact_us: 'Contact Us',

      // Language labels
      arabic: 'Arabic',
      english: 'English',
      enter_package_name_english: 'Enter package name in English',
      enter_package_description_english: 'Enter package description in English',

      // User Management
      loading: 'Loading...',
      role: 'Role',
      all_roles: 'All Roles',
      admin: 'Admin',
      neighbor: 'Neighbor',
      building: 'Building',
      all_buildings: 'All Buildings',
      apply_filters: 'Apply Filters',
      add_new_user: 'Add New User',
      total_users: 'Total Users',
      admins: 'Admins',
      neighbors: 'Neighbors',
      buildings: 'Buildings',
      active_users: 'Active Users',
      users: 'Users',
      edit: 'Edit',
      delete: 'Delete',
      edit_user: 'Edit User',
      name: 'Name',
      email: 'Email',
      apartment: 'Apartment',
      apartment_number: 'Apartment Number',
      success: 'Success',
      error: 'Error',
      user_updated_successfully: 'User updated successfully',
      user_deleted_successfully: 'User deleted successfully',
      failed_to_delete_user: 'Failed to delete user',
      failed_to_load_users: 'Failed to load users',
      confirm_delete_user: 'Are you sure you want to delete user "{name}"?',
      no_permission_access_page: 'You do not have permission to access this page.',
      create_user: 'Create User',
      all_users: 'All Users',
      expenses: 'Expenses',
      incomes: 'Incomes',
      user_created_successfully: 'User created successfully',
      leave_blank_keep_current: '(leave blank to keep current)',
      failed_to_save_user: 'Failed to save user',
      select_building: 'Select Building',

      // Income Management
      income_management: 'Income Management',
      manage_building_incomes: 'Manage building incomes and payments',
      filter_incomes_description: 'Filter income records by date and year',
      average_monthly: 'Average Monthly',
      from: 'From',
      clear_all: 'Clear All',
      create_income_description: 'Add a new income record for the building with payment details',
      back_to_incomes: 'Back to Incomes',
      income_details: 'Income Details',
      income_details_description: 'Enter the income details and payment amount',
      income_recorded: 'Income Recorded',
      income_recorded_successfully: 'The income has been recorded successfully',
      recording_failed: 'Recording Failed',
      loading_income: 'Loading income...',
      income_not_found: 'Income not found',
      failed_load_income: 'Failed to load income',
      edit_income_details_description: 'Update the income details and payment information',

      // User Management Updates
      user_management: 'User Management',
      manage_building_users: 'Manage building users and neighbors',
      filter_users_description: 'Filter users by building',
      recent_users: 'Recent Users',
      create_user_description: 'Add a new user to the building with account details',
      back_to_users: 'Back to Users',
      user_details: 'User Details',
      user_details_description: 'Enter the user details and account information',

      // Building Management Updates
      manage_buildings_description: 'Manage buildings and monthly fees',
      filter_buildings_description: 'Filter buildings by status and fees',
      total_residents: 'Total Residents',
      status: 'Status',
      all_statuses: 'All Statuses',
      active: 'Active',
      inactive: 'Inactive',
      monthly_fee_range: 'Monthly Fee Range',
      all_ranges: 'All Ranges',
      fee_range: 'Fee Range',

      // Building Create/Edit Updates
      create_building_description: 'Add a new building with management details and fees',
      back_to_buildings: 'Back to Buildings',
      building_details: 'Building Details',
      building_details_description: 'Enter the building details and management information',
      edit_building_description: 'Update building details and monthly fees',
      edit_building_details_description: 'Update the building details and management information',

      // Home Page
      building_management: 'Building Management',
      building_management_short: 'The Building Committee is a group of selected residents responsible for managing shared responsibilities within the building. This includes organizing maintenance, tracking income and expenses, collecting dues, and ensuring transparency and fairness in all financial matters related to the buildings upkeep and shared services. The committee serves as a point of contact between neighbors and service providers to ensure smooth and cooperative living.',
      get_started_login: 'Get Started',
      generate_monthly_subscriptions: 'Generate Monthly Subscriptions',
      generating: 'Generating...',
      home: 'Home',
      about: 'About',
      services: 'Services',
      contact: 'Contact',

      // About Us Page
      about_us_title: 'About Building Committee',
      about_us_subtitle: 'We provide comprehensive solutions for residential building management',
      about_mission_title: 'Our Mission',
      about_mission_text: 'We aim to simplify residential building management by providing a comprehensive digital platform that helps building administrations track expenses and income and manage resident affairs efficiently and transparently.',
      about_vision_title: 'Our Vision',
      about_vision_text: 'To be the leading platform in the region for residential building management, contributing to improving the quality of life for residents and enhancing transparency in financial management.',
      about_values_title: 'Our Values',
      about_transparency: 'Transparency',
      about_transparency_desc: 'We believe in the importance of complete transparency in all financial transactions',
      about_efficiency: 'Efficiency',
      about_efficiency_desc: 'We strive to provide tools that increase building management efficiency',
      about_innovation: 'Innovation',
      about_innovation_desc: 'We develop innovative technical solutions to meet modern needs',

      // Services Page
      services_title: 'Our Services',
      services_subtitle: 'Comprehensive solutions for residential building management',
      service_expense_title: 'Expense Management',
      service_expense_desc: 'Track and manage all building expenses in an organized and transparent manner with the ability to generate detailed reports',
      service_income_title: 'Income Management',
      service_income_desc: 'Record and track all payments and income from residents with an alert system for overdue payments',
      service_residents_title: 'Resident Management',
      service_residents_desc: 'Comprehensive resident database with the ability to track payments and dues for each resident',
      service_reports_title: 'Reports and Statistics',
      service_reports_desc: 'Detailed financial reports and statistics that help in making sound administrative decisions',
      service_notifications_title: 'Notification System',
      service_notifications_desc: 'Automatic notifications for due payments and important dates',
      service_security_title: 'Security and Protection',
      service_security_desc: 'High data protection with advanced permissions system to ensure privacy',

      // Contact Page
      contact_title: 'Contact Us',
      contact_subtitle: 'We are here to help you',
      contact_info_title: 'Contact Information',
      contact_info_description: 'Get in touch with us through any of the following methods and we will be happy to help you',
      contact_form_title: 'Send us a message',
      contact_name: 'Name',
      contact_email: 'Email',
      contact_subject: 'Subject',
      contact_message: 'Message',
      contact_send: 'Send Message',
      contact_address: 'Address',
      contact_phone: 'Phone',
      contact_email_label: 'Email',
      contact_hours: 'Working Hours',
      contact_hours_value: 'Sunday - Thursday: 9:00 AM - 6:00 PM',

      // Pricing and Sign Up
      pricing: 'Pricing',
      get_started: 'Get Started',
      sign_up: 'Sign Up',
      create_your_account: 'Create Your Account',
      signup_subtitle: 'Join us to manage your building efficiently',
      personal_information: 'Personal Information',
      full_name: 'Full Name',
      enter_full_name: 'Enter full name',
      email_address: 'Email Address',
      enter_email: 'Enter email address',
      enter_phone_number: 'Enter phone number',
      enter_apartment_number: 'Enter apartment number',
      enter_password: 'Enter password',
      building_information: 'Building Information',
      building_name: 'Building Name',
      enter_building_name: 'Enter building name',
      building_address: 'Building Address',
      enter_building_address: 'Enter building address',
      city: 'City',
      enter_city: 'Enter city',
      postal_code: 'Postal Code',
      enter_postal_code: 'Enter postal code',
      monthly_fee_per_neighbor: 'Monthly Fee per Neighbor',
      monthly_fee_help: 'Monthly fee each neighbor pays for shared expenses',
      pricing_summary: 'Pricing Summary',
      setup_fee: 'Setup Fee',
      monthly_subscription: 'Monthly Subscription',
      first_month_total: 'First Month Total',
      one_time: 'One Time',
      per_building_per_month: 'per building/month',
      agree_to: 'I agree to the',
      terms_and_conditions: 'Terms and Conditions',
      registration_failed: 'Registration Failed',
      creating_account: 'Creating Account...',
      create_account: 'Create Account',
      already_have_account: 'Already have an account?',
      sign_in: 'Sign In',
      registration_error: 'An error occurred during registration',

      // Pricing page
      simple_transparent_pricing: 'Simple, Transparent Pricing',
      pricing_subtitle: 'Pay only for what you need, no hidden fees',
      setup_fee_description: 'One-time setup fee for building registration and system setup',
      monthly_subscription_description: 'Monthly subscription per building for access to all features',
      building_registration: 'Building Registration',
      admin_account_setup: 'Admin Account Setup',
      initial_configuration: 'Initial Configuration',
      onboarding_support: 'Onboarding Support',
      recommended: 'Recommended',
      unlimited_neighbors: 'Unlimited Neighbors',
      expense_management: 'Expense Management',
      payment_tracking: 'Payment Tracking',
      automated_billing: 'Automated Billing',
      priority_support: 'Priority Support',
      ready_to_get_started: 'Ready to Get Started?',
      start_free_trial: 'Start Free Trial',
      contact_sales: 'Contact Sales',
      frequently_asked_questions: 'Frequently Asked Questions',
      faq_subtitle: 'Answers to the most common questions',
      faq_setup_fee_question: 'What is the setup fee?',
      faq_setup_fee_answer: 'The setup fee is a one-time charge for building registration, system setup, and admin account creation.',
      faq_monthly_fee_question: 'How does the monthly subscription work?',
      faq_monthly_fee_answer: 'The monthly subscription is based on your selected package per building per month, regardless of the number of neighbors.',
      faq_neighbor_fees_question: 'What about neighbor fees?',
      faq_neighbor_fees_answer: 'You can set monthly fees per neighbor for building shared expenses.',
      view_pricing: 'View Pricing',

      // Sign up success
      account_created_successfully: 'Account Created Successfully',
      signup_success_message: 'Your account and building have been created successfully. You can now start managing your building.',
      welcome_aboard: 'Welcome Aboard',
      registration_successful_message: 'Your account has been created successfully',
      welcome_message: 'Welcome',
      account_details: 'Account Details',
      building_admin: 'Building Admin',
      neighbor: 'Neighbor',
      whats_next: "What's Next?",
      access_dashboard_now: 'You can now access your dashboard',
      manage_building_expenses: 'Manage building expenses and view reports',
      view_expenses_make_payments: 'View expenses and make payments',
      need_help: 'Need help?',
      next_steps: 'Next Steps',
      step_1_verify_email: 'Verify your email (if required)',
      step_2_setup_fee: 'Pay setup fee',
      step_3_add_neighbors: 'Add neighbors to the system',
      step_4_start_managing: 'Start managing building expenses and income',
      pricing_reminder: 'Pricing Reminder',
      billing_info: 'You will be billed monthly starting next month',
      contact_for_pricing: 'Contact for pricing',
      based_on_selected_package: 'Based on selected package',
      sign_in_to_dashboard: 'Sign In to Dashboard',
      need_help: 'Need Help?',
      questions_or_issues: 'Questions or issues?',
      phone: 'Phone',
      phone_number: 'Phone Number',
      helpful_resources: 'Helpful Resources',
      user_guide: 'User Guide',
      user_guide_description: 'Comprehensive guide to using the system',
      video_tutorials: 'Video Tutorials',
      video_tutorials_description: 'Step-by-step video tutorials',
      community_forum: 'Community Forum',
      community_forum_description: 'Connect with other users',
      registration_successful: 'Registration Successful',
      today: 'Today',
      starts_next_month: 'Starts Next Month',
      total_due_today: 'Total Due Today',
      subscription_billing_note: 'Monthly subscription billing will start next month',
      create_account_and_pay: 'Create Account & Pay',
      payment_after_account_creation: 'You will be redirected to pay the setup fee after account creation',
      processing_payment: 'Processing payment...',
      payment_successful: 'Payment Successful',
      setup_fee_paid_successfully: 'Setup fee has been paid successfully',
      continue_to_dashboard: 'Continue to Dashboard',
      payment_failed: 'Payment Failed',
      payment_processing_error: 'An error occurred while processing payment',
      retry_payment: 'Retry Payment',
      contact_support: 'Contact Support',
      payment_processing: 'Payment Processing',
      building_settings: 'Building Settings',
      building_settings_description: 'Manage building information and preferred currency',
      currency: 'Currency',
      currency_display_note: 'This currency is for display purposes only. Payments are processed in USD.',
      description: 'Description',
      building_description_placeholder: 'Enter a description for the building (optional)',
      payment_information: 'Payment Information',
      payment_currency_note: 'All payments are processed in USD regardless of the selected display currency.',
      save_changes: 'Save Changes',
      saving: 'Saving...',
      building_updated_successfully: 'Building information updated successfully',
      error_loading_building_data: 'Error loading building data',
      error_updating_building: 'Error updating building',

      // Terms and Conditions
      terms_and_conditions: 'Terms and Conditions',
      terms_subtitle: 'Please read these terms and conditions carefully before using our services',
      last_updated: 'Last Updated',
      terms_last_updated_date: 'January 15, 2025',
      all_rights_reserved: 'All rights reserved',
      privacy_policy: 'Privacy Policy',
      contact_us: 'Contact Us',
      back_to_home: 'Back to Home',
      back_to_top: 'Back to Top',

      // Terms sections
      terms_introduction_title: '1. Introduction and Definitions',
      terms_introduction_text: 'Welcome to the Amaretna Platform ("Platform" or "Service"). This Agreement ("Terms") constitutes a legally binding contract between you ("User" or "Client") and Amaretna Limited Company ("Company" or "We"). These Terms govern your use of our digital platform for residential building management and related services.',

      terms_acceptance_title: '2. Acceptance of Terms and Eligibility',
      terms_acceptance_text: 'By accessing or using the Platform, you acknowledge and agree that: (a) you have read and understood these Terms in their entirety; (b) you have full legal capacity to enter into this Agreement; (c) you will comply with all terms and conditions set forth herein. If you do not agree to any of these Terms, you must immediately cease using the Platform.',

      terms_service_title: '3. Service Description and Features',
      terms_service_text: 'The Amaretna Platform provides comprehensive technological solutions for residential building management, including but not limited to:',
      terms_service_feature_1: 'Advanced expense and income management system with automated tracking and categorization',
      terms_service_feature_2: 'Payment and dues tracking platform with intelligent notifications and reminders',
      terms_service_feature_3: 'Comprehensive database for managing resident information and residential units',
      terms_service_feature_4: 'Detailed financial reporting and advanced data analytics with export capabilities',

      terms_responsibilities_title: '4. User Obligations and Responsibilities',
      terms_responsibilities_text: 'As a Platform user, you covenant and agree to the following:',
      terms_responsibility_1: 'Provide accurate, current, and complete information at all times, and promptly report any changes',
      terms_responsibility_2: 'Maintain the confidentiality and security of your login credentials and not share them with third parties',
      terms_responsibility_3: 'Use the Platform in accordance with applicable laws and in a manner that does not harm others or the Platform',
      terms_responsibility_4: 'Pay all fees and charges by their due dates without delay',

      terms_payment_title: '5. Financial Terms and Payment',
      terms_payment_text: 'The services provided are subject to the following fee structure, which forms an integral part of this Agreement:',
      terms_payment_fees_title: 'Fee Structure and Pricing',
      terms_setup_fee_text: 'Initial setup and configuration fee (one-time, non-refundable payment)',
      terms_monthly_fee_text: 'Monthly subscription fee based on selected package per building (charged in advance)',

      terms_privacy_title: '6. Privacy and Data Protection',
      terms_privacy_text: 'We are fully committed to protecting your privacy and data security in accordance with the highest international standards. Our separate Privacy Policy governs the collection, processing, use, and protection of your personal information. By agreeing to these Terms, you acknowledge that you have read and understood the Privacy Policy and consent to our data handling practices.',

      terms_liability_title: '7. Disclaimer of Warranties and Limitation of Liability',
      terms_liability_text: 'While we strive to provide high-quality service, the Platform is provided "as is" without any express or implied warranties. We do not guarantee uninterrupted, error-free, or virus-free service. Our total liability to you is limited to the total amounts paid to us during the twelve months preceding the relevant incident, and shall not exceed $1,000 USD in any case.',

      terms_termination_title: '8. Service and Account Termination',
      terms_termination_text: 'Either party may terminate this Agreement with thirty (30) days prior written notice. We may also terminate your account immediately for violation of these Terms. Upon termination, you will immediately lose all access rights to the Platform and stored data, and we will retain data for only ninety (90) days for transition purposes.',

      terms_changes_title: '9. Modifications to Terms and Notices',
      terms_changes_text: 'We reserve the absolute right to modify or update these Terms at any time at our sole discretion. You will be notified of any material changes thirty (30) days prior to their effective date via registered email or prominent notice on the Platform. Your continued use of the Platform after the modifications take effect constitutes your acceptance of the modified Terms.',

      terms_contact_title: '10. Contact Information and Support',
      terms_contact_text: 'For any inquiries or concerns regarding these Terms and Conditions or to request technical support, please contact us through the following channels:',

      terms_law_title: '11. Governing Law and Dispute Resolution',
      terms_law_text: 'This Agreement shall be governed by and construed in accordance with the laws of the Hashemite Kingdom of Jordan, without regard to conflict of law principles. Any dispute or controversy arising out of or relating to this Agreement shall be subject to the exclusive jurisdiction of the competent courts in Amman, Jordan. The parties waive any objection to such jurisdiction.',
      agree_to: 'I agree to the',

      // New Professional Guest Mode Translations
      // Navigation
      get_started: 'Get Started',
      sign_up_here: 'Sign up here',
      dont_have_account: "Don't have an account?",
      already_have_account: 'Already have an account?',
      sign_in: 'Sign In',
      welcome_back: 'Welcome Back',
      sign_in_to_your_account: 'Sign in to your account',

      // Home Page
      building_management: 'Building Management',
      made_simple: 'Made Simple',
      building_management_description: 'A comprehensive platform for managing residential buildings efficiently and transparently. Track expenses, income, and manage residents with ease.',
      trusted_by_buildings: 'Trusted by Buildings',
      no_setup_fees: 'No Setup Fees',
      free_trial: 'Free Trial',
      '24_7_support': '24/7 Support',
      building_dashboard: 'Building Dashboard',
      neighbors: 'Neighbors',
      monthly_income: 'Monthly Income',
      collection_rate: 'Collection Rate',
      new_payment: 'New Payment',
      apartment_3a: 'Apartment 3A',
      maintenance_due: 'Maintenance Due',

      // Features
      why_choose_us: 'Why Choose Us?',
      features_description: 'We provide comprehensive solutions for residential building management with the latest technology',
      expense_management: 'Expense Management',
      expense_management_desc: 'Track and manage all building expenses in an organized and transparent way',
      payment_tracking: 'Payment Tracking',
      payment_tracking_desc: 'Accurate monitoring of all payments and dues from residents',
      notifications: 'Notifications',
      notifications_desc: 'Smart notification system to alert residents and management of important dates',

      // Testimonials
      what_customers_say: 'What Our Customers Say',
      testimonials_description: 'Real opinions from building managers who use our platform',
      testimonial_1_text: 'Amazing platform that made building management much easier for us. Now everything is organized and tidy.',
      testimonial_1_name: 'Ahmed Hassan',
      testimonial_1_role: 'Building Manager',
      testimonial_2_text: 'Financial reports have become clear and transparent. Residents are satisfied with the level of transparency.',
      testimonial_2_name: 'Mariam Khalid',
      testimonial_2_role: 'Treasurer',
      testimonial_3_text: 'Excellent customer service and fast, helpful technical support. I highly recommend it.',
      testimonial_3_name: 'Samer Ali',
      testimonial_3_role: 'Committee Chairman',

      // CTA Sections
      ready_to_get_started: 'Ready to Get Started?',
      cta_description: 'Join hundreds of buildings using our platform for better and more transparent management',
      start_free_trial: 'Start Free Trial',
      contact_sales: 'Contact Sales',
      no_credit_card_required: 'No credit card required',

      // About Page
      established_2025: 'Established 2025',
      buildings_managed: 'Buildings Managed',
      happy_residents: 'Happy Residents',
      satisfaction_rate: 'Satisfaction Rate',
      our_mission_vision: 'Our Mission & Vision',
      mission_vision_description: 'We strive to transform residential building management into an easy and transparent experience',
      our_mission: 'Our Mission',
      mission_point_1: 'Simplify residential building management',
      mission_point_2: 'Increase financial transparency',
      mission_point_3: 'Improve communication between residents',
      our_vision: 'Our Vision',
      vision_point_1: 'To be the leading platform in the region',
      vision_point_2: 'Empower residential communities',
      vision_point_3: 'Continuous innovation in solutions',

      // Values
      our_values: 'Our Values',
      values_description: 'The values we believe in and that guide our daily work',
      transparency: 'Transparency',
      about_transparency_desc: 'We believe in complete transparency in all financial and administrative operations',
      efficiency: 'Efficiency',
      about_efficiency_desc: 'We strive to provide effective solutions that save time and effort',
      innovation: 'Innovation',
      about_innovation_desc: 'We continuously develop innovative solutions to meet our customers needs',
      reliability: 'Reliability',
      about_reliability_desc: 'We guarantee reliable and stable service around the clock',

      // Team
      meet_our_team: 'Meet Our Team',
      team_description: 'A team of experts specialized in developing building management solutions',
      team_member_1_name: 'Ahmed Hassan',
      team_member_1_role: 'CEO',
      team_member_1_bio: '15 years of experience in building management and real estate development',
      team_member_2_name: 'Mariam Khalid',
      team_member_2_role: 'Development Manager',
      team_member_2_bio: 'Specialist in software development and user experience',
      team_member_3_name: 'Samer Ali',
      team_member_3_role: 'Sales Manager',
      team_member_3_bio: 'Expert in customer service and business development',

      ready_to_join_us: 'Ready to Join Us?',
      about_cta_description: 'Start your journey with us today and discover how we can improve your building management',
      get_started_today: 'Get Started Today',

      // Services Page
      comprehensive_solutions: 'Comprehensive Solutions',
      core_services: 'Core Services',
      support_available: 'Support Available',
      cloud_based: 'Cloud Based',
      our_core_services: 'Our Core Services',
      core_services_description: 'We provide a comprehensive set of services for efficient residential building management',
      track_all_expenses: 'Track all expenses',
      categorize_expenses: 'Categorize expenses',
      generate_reports: 'Generate reports',
      income_management: 'Income Management',
      monthly_fee_collection: 'Monthly fee collection',
      automated_reminders: 'Automated reminders',
      resident_management: 'Resident Management',
      resident_profiles: 'Resident profiles',
      contact_management: 'Contact management',
      apartment_assignments: 'Apartment assignments',
      additional_features: 'Additional Features',
      additional_features_description: 'More advanced features to enhance your building management experience',
      reports_analytics: 'Reports & Analytics',
      smart_notifications: 'Smart Notifications',
      security_privacy: 'Security & Privacy',
      how_it_works: 'How It Works',
      how_it_works_description: 'Three simple steps to start managing your building efficiently',
      step_1_title: 'Create Account',
      step_1_description: 'Register your account and add basic building information',
      step_2_title: 'Setup System',
      step_2_description: 'Add residents and apartments, set monthly fees',
      step_3_title: 'Start Managing',
      step_3_description: 'Track expenses and income, send notifications to residents',
      simple_transparent_pricing: 'Simple, Transparent Pricing',
      pricing_integration_description: 'One plan that fits all your needs at a fair price',
      month: 'month',
      per_building: 'per building',
      setup_fee_70: 'One-time setup fee',
      view_full_pricing: 'View Full Pricing',
      ready_to_transform_building: 'Ready to Transform Your Building Management?',
      services_cta_description: 'Join hundreds of buildings that trust us for better and more efficient management',

      // Contact Page
      get_in_touch: 'Get in Touch',
      call_us: 'Call Us',
      email_us: 'Email Us',
      business_hours: 'Business Hours',
      contact_hours_value: 'Sunday - Thursday: 9:00 AM - 6:00 PM',
      our_office: 'Our Office',
      middle_east: 'Middle East',
      phone_support: 'Phone Support',
      available_24_7: 'Available 24/7',
      email_support: 'Email Support',
      response_within_24h: 'Response within 24 hours',
      jordan_timezone: 'Jordan Timezone',
      send_us_message: 'Send Us a Message',
      contact_form_description: 'Fill out the form below and we will get back to you as soon as possible',
      enter_your_name: 'Enter your name',
      enter_your_email: 'Enter your email',
      select_subject: 'Select subject',
      general_inquiry: 'General Inquiry',
      technical_support: 'Technical Support',
      sales_inquiry: 'Sales Inquiry',
      partnership: 'Partnership',
      other: 'Other',
      enter_your_message: 'Enter your message',
      privacy_notice: 'We respect your privacy. We will not share your information with third parties.',
      send_message: 'Send Message',
      sending: 'Sending',
      message_sent_successfully: 'Message sent successfully',
      we_will_respond_soon: 'We will respond to you as soon as possible',
      message_send_failed: 'Failed to send message',
      please_try_again: 'Please try again',
      frequently_asked_questions: 'Frequently Asked Questions',
      faq_description: 'Answers to the most common questions about our services',
      faq_1_question: 'How much does it cost to use the platform?',
      faq_1_answer: 'We offer various packages to suit all building sizes with a one-time setup fee.',
      faq_2_question: 'Can I try the platform for free?',
      faq_2_answer: 'Yes, we offer a 30-day free trial with all features included.',
      faq_3_question: 'Is the data secure?',
      faq_3_answer: 'Yes, we use the latest encryption technologies to protect your data.',
      faq_4_question: 'Is there technical support?',
      faq_4_answer: 'Yes, we provide 24/7 technical support via phone and email.',
      still_have_questions: 'Still have questions?',
      contact_cta_description: 'Start your journey with us today and discover how we can improve your building management',

      // Login/Register
      email_address: 'Email Address',
      enter_your_password: 'Enter your password',
      remember_me: 'Remember me',
      forgot_password: 'Forgot password?',
      signing_in: 'Signing in',
      login_failed: 'Login failed',
      create_your_account: 'Create Your Account',
      signup_subtitle: 'Join thousands of managers who trust us to manage their buildings',
      account_info: 'Account Info',
      building_info: 'Building Info',
      complete: 'Complete',
      personal_information: 'Personal Information',
      full_name: 'Full Name',
      enter_full_name: 'Enter your full name',
      enter_email: 'Enter your email',
      password_requirements: 'Password must be at least 8 characters long',
      confirm_password: 'Confirm Password',

      // Footer
      footer_description: 'A comprehensive platform for managing residential buildings efficiently and transparently in the Middle East',
      quick_links: 'Quick Links',
      support: 'Support',
      made_with_love_in_jordan: 'Made with love in Jordan',
      jordan: 'Jordan',
      back_to_home: 'Back to Home',
      
      // Authentication & Access
      authentication_required: 'مطلوب تسجيل الدخول',
      access_denied: 'تم رفض الوصول',
      advanced_reporting_not_available: 'التقارير المتقدمة غير متاحة لباقتك الحالية',
      
      // Advanced Reporting
      generating_report: 'جاري إنشاء التقرير: {name}',
      report_generated_successfully: 'تم إنشاء التقرير بنجاح',
      error_generating_report: 'خطأ في إنشاء التقرير',
      report_created_successfully: 'تم إنشاء التقرير بنجاح',
      report_duplicated_successfully: 'تم نسخ التقرير بنجاح',
      error_duplicating_report: 'خطأ في نسخ التقرير',
      report_shared_successfully: 'تم مشاركة التقرير بنجاح',
      report_unshared_successfully: 'تم إلغاء مشاركة التقرير بنجاح',
      error_sharing_report: 'خطأ في مشاركة التقرير',
      report_deleted_successfully: 'تم حذف التقرير بنجاح',
      error_deleting_report: 'خطأ في حذف التقرير',
      edit_feature_coming_soon: 'ميزة التعديل قريباً',
      error_loading_reports: 'خطأ في تحميل التقارير',
      error_loading_reporting_stats: 'خطأ في تحميل إحصائيات التقارير',
      error_loading_analytics: 'خطأ في تحميل التحليلات',
      analytics_refreshed: 'تم تحديث التحليلات',
      error_loading_templates: 'خطأ في تحميل القوالب',

      // Pricing Page
      transparent_pricing: 'Transparent Pricing',
      no_hidden_fees: 'No Hidden Fees',
      what_you_see_is_what_you_pay: 'What you see is what you pay',
      instant_setup: 'Instant Setup',
      get_started_in_minutes: 'Get started in minutes',
      money_back_guarantee: 'Money Back Guarantee',
      '30_day_guarantee': '30-day guarantee',
      choose_your_plan: 'Choose Your Plan',
      pricing_plans_description: 'Simple and transparent plans that fit all building sizes',
      one_time_payment: 'One-time payment',
      data_migration: 'Data migration',
      advanced_reports: 'Advanced reports',
      mobile_app_access: 'Mobile app access',
      ready_to_start_saving: 'Ready to Start Saving?',
      pricing_cta_description: 'Join hundreds of buildings saving time and money with our platform',
      cancel_anytime: 'Cancel anytime',
      pricing_faqs: 'Pricing FAQs',
      pricing_faqs_description: 'Answers to the most common questions about our pricing plans',
      pricing_faq_1_question: 'Can I change my plan later?',
      pricing_faq_1_answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect from the next billing cycle.',
      pricing_faq_2_question: 'What happens if I exceed the apartment limit?',
      pricing_faq_2_answer: 'There are no limits on the number of apartments. You can add unlimited apartments and residents.',
      pricing_faq_3_question: 'Are there long-term contracts?',
      pricing_faq_3_answer: 'No, all our plans are monthly and you can cancel at any time without additional fees.',
      pricing_faq_4_question: 'Do you offer discounts for large buildings?',
      pricing_faq_4_answer: 'Yes, we offer special discounts for large buildings and companies. Contact us for a custom quote.',
      more_questions: 'Have more questions?',
      transform_building_management: 'Transform Your Building Management Today',
      pricing_final_cta_description: 'Join thousands of managers who trust us to manage their buildings more efficiently',
      get_started_now: 'Get Started Now',
      schedule_demo: 'Schedule Demo',
      join_satisfied_customers: 'Join our satisfied customers',
      
      // Authentication & Access
      authentication_required: 'Authentication required',
      access_denied: 'Access denied',
      advanced_reporting_not_available: 'Advanced reporting not available for your package',
      
      // Advanced Reporting
      generating_report: 'Generating report: {name}',
      report_generated_successfully: 'Report generated successfully',
      error_generating_report: 'Error generating report',
      report_created_successfully: 'Report created successfully',
      report_duplicated_successfully: 'Report duplicated successfully',
      error_duplicating_report: 'Error duplicating report',
      report_shared_successfully: 'Report shared successfully',
      report_unshared_successfully: 'Report unshared successfully',
      error_sharing_report: 'Error sharing report',
      report_deleted_successfully: 'Report deleted successfully',
      error_deleting_report: 'Error deleting report',
      edit_feature_coming_soon: 'Edit feature coming soon',
      error_loading_reports: 'Error loading reports',
      error_loading_reporting_stats: 'Error loading reporting stats',
      error_loading_analytics: 'Error loading analytics',
      analytics_refreshed: 'Analytics refreshed',
      error_loading_templates: 'Error loading templates',
    };
  }

  t(key, params = {}) {
    // Handle nested keys like 'reports.title'
    const keys = key.split('.');
    let translation = this.translations[this.currentLocale];

    // Navigate through nested object
    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k];
      } else {
        // If key not found, return the original key
        translation = key;
        break;
      }
    }

    // If translation is not a string, return the key
    if (typeof translation !== 'string') {
      translation = key;
    }

    // Simple parameter replacement
    return Object.keys(params).reduce((str, param) => {
      return str.replace(`{${param}}`, params[param]);
    }, translation);
  }

  setLocale(locale) {
    this.currentLocale = locale;
    localStorage.setItem('locale', locale);

    // Update document direction and language
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = locale;

    // Update reactive state if available (set by Vue plugin)
    if (this.reactiveLocale) {
      this.reactiveLocale.locale = locale;
    }

    // Force update of all Vue components by triggering a global reactive update
    this.triggerGlobalUpdate();

    // Trigger a custom event for components to react to language change
    window.dispatchEvent(new CustomEvent('localeChanged', { detail: { locale } }));
  }

  triggerGlobalUpdate() {
    // This will force all components to re-render by updating a reactive property
    window.dispatchEvent(new CustomEvent('forceUpdate'));
  }

  getLocale() {
    return this.currentLocale;
  }

  isRTL() {
    return this.currentLocale === 'ar';
  }
}

// Create global instance
const i18n = new I18n();

// Set initial direction
document.documentElement.dir = i18n.isRTL() ? 'rtl' : 'ltr';
document.documentElement.lang = i18n.getLocale();

export default i18n;