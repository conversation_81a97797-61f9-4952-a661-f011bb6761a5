import { createRouter, createWebHistory } from 'vue-router';
import { createRouteComponent } from './utils/lazyLoading.js';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('./views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('./views/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('./views/Pricing.vue')
  },
  {
    path: '/terms-and-conditions',
    name: 'TermsAndConditions',
    component: () => import('./views/TermsAndConditions.vue')
  },
  {
    path: '/signup',
    name: 'SignUp',
    component: () => import('./views/SignUp.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/signup-success',
    name: 'SignUpSuccess',
    component: () => import('./views/SignUpSuccess.vue')
  },
  {
    path: '/payment/success',
    name: 'PaymentSuccess',
    component: () => import('./views/PaymentSuccess.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/payment/cancel',
    name: 'PaymentCancel',
    component: () => import('./views/SignUp.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/registration-success',
    name: 'RegistrationSuccess',
    component: () => import('./views/RegistrationSuccess.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/verify-email',
    name: 'VerifyEmail',
    component: () => import('./views/VerifyEmail.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: createRouteComponent(() => import('./views/AdminDashboard.vue'), true),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/neighbor',
    name: 'NeighborDashboard',
    component: createRouteComponent(() => import('./views/NeighborDashboard.vue'), true),
    meta: { requiresAuth: true, role: 'neighbor' }
  },
  {
    path: '/neighbor/profile',
    name: 'NeighborProfile',
    component: () => import('./views/neighbor/Profile.vue'),
    meta: { requiresAuth: true, role: 'neighbor' }
  },
  {
    path: '/notifications',
    name: 'Notifications',
    component: () => import('./views/Notifications.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'SearchResults',
    component: () => import('./views/SearchResults.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/users',
    name: 'admin.users',
    component: () => import('./views/admin/UserManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/users/create',
    name: 'admin.users.create',
    component: () => import('./views/admin/CreateUser.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/super-admin/subscriptions',
    name: 'super-admin.subscriptions',
    component: () => import('./views/super-admin/SubscriptionManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/super-admin/packages',
    name: 'super-admin.packages',
    component: () => import('./views/super-admin/PackageManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/super-admin/package-change-requests',
    name: 'super-admin.package-change-requests',
    component: () => import('./views/super-admin/PackageChangeRequests.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/super-admin/expense-types',
    name: 'super-admin.expense-types',
    component: () => import('./views/super-admin/ExpenseTypeManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/super-admin/building-expense-types',
    name: 'super-admin.building-expense-types',
    component: () => import('./views/super-admin/BuildingExpenseTypeManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/my-building',
    name: 'admin.my-building',
    component: () => import('./views/admin/MyBuilding.vue'),
    meta: { requiresAuth: true, role: 'admin' }
  },
  {
    path: '/admin/building-settings',
    name: 'admin.building-settings',
    component: () => import('./views/admin/BuildingSettings.vue'),
    meta: { requiresAuth: true, role: 'admin' }
  },
  {
    path: '/admin/expenses',
    name: 'admin.expenses',
    component: () => import('./views/admin/ExpenseManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/expenses/create',
    name: 'admin.expenses.create',
    component: () => import('./views/admin/CreateExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/expenses/:id/edit',
    name: 'admin.expenses.edit',
    component: () => import('./views/admin/EditExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/expenses/:id',
    name: 'admin.expenses.view',
    component: () => import('./views/admin/ViewExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/building-expenses',
    name: 'admin.building-expenses',
    component: () => import('./views/admin/BuildingExpenseManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/building-expenses/create',
    name: 'admin.building-expenses.create',
    component: () => import('./views/admin/CreateBuildingExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/building-expenses/:id/edit',
    name: 'admin.building-expenses.edit',
    component: () => import('./views/admin/EditBuildingExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/building-expenses/:id',
    name: 'admin.building-expenses.view',
    component: () => import('./views/admin/ViewBuildingExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes',
    name: 'IncomeManagement',
    component: () => import('./views/admin/IncomeManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes/create',
    name: 'CreateIncome',
    component: () => import('./views/admin/CreateIncome.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes/:id/edit',
    name: 'admin.incomes.edit',
    component: () => import('./views/admin/EditIncome.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes/:id',
    name: 'admin.incomes.view',
    component: () => import('./views/admin/ViewIncome.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/buildings',
    name: 'admin.buildings',
    component: () => import('./views/admin/BuildingManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/buildings/create',
    name: 'admin.buildings.create',
    component: () => import('./views/admin/CreateBuilding.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/buildings/:id/edit',
    name: 'admin.buildings.edit',
    component: () => import('./views/admin/EditBuilding.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/exports',
    name: 'admin.exports',
    component: () => import('./views/admin/ExportManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/advanced-reporting',
    name: 'admin.advanced-reporting',
    component: () => import('./views/admin/AdvancedReporting.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/export-reports',
    name: 'admin.export-reports',
    component: () => import('./views/admin/ExportManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/expense-templates',
    name: 'admin.expense-templates',
    component: () => import('./views/admin/ExpenseTemplateManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/notification-templates',
    name: 'admin.notification-templates',
    component: () => import('./views/admin/NotificationTemplateManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/sms-management',
    name: 'SmsManagement',
    component: () => import('./views/admin/SmsManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/payment-gateway-settings',
    name: 'admin.payment-gateway-settings',
    component: () => import('./views/admin/PaymentGatewaySettings.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/',
    name: 'Home',
    component: () => import('./views/Home.vue'),
    meta: { requiresGuest: false }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('./views/About.vue'),
    meta: { requiresGuest: false }
  },
  {
    path: '/services',
    name: 'Services',
    component: () => import('./views/Services.vue'),
    meta: { requiresGuest: false }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('./views/Contact.vue'),
    meta: { requiresGuest: false }
  }
];

const router = createRouter({
  history: createWebHistory('/'),
  routes
});

  // Navigation Guard
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  // Debug: Log navigation attempts to detect infinite loops
  if (from && from.name === to.name && from.path === to.path) {
    console.warn('Potential infinite redirect detected:', to.name, to.path);
    next(false); // Cancel navigation
    return;
  }

  console.log('Router guard - navigating from:', from?.name || 'initial', 'to:', to.name);

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!token) {
      // No token, redirect to login
      console.log('Router guard - no token, redirecting to login');
      next({ name: 'Login' });
      return;
    }

    // Check role-based access
    if (to.meta.role && user) {
      const requiredRole = to.meta.role;
      const userRole = user.role;

      // Prevent infinite redirects by checking if we're already on the target dashboard
      const targetDashboard = (userRole === 'admin' || userRole === 'super_admin') ? 'AdminDashboard' : 'NeighborDashboard';

      if (to.name === targetDashboard) {
        // Already on the correct dashboard, allow navigation
        next();
        return;
      }

      let hasAccess = false;

      if (Array.isArray(requiredRole)) {
        hasAccess = requiredRole.includes(userRole);
      } else {
        if (requiredRole === 'super_admin') {
          hasAccess = userRole === 'super_admin';
        } else if (requiredRole === 'admin') {
          hasAccess = userRole === 'admin' || userRole === 'super_admin';
        } else if (requiredRole === 'neighbor') {
          hasAccess = userRole === 'neighbor';
        } else {
          hasAccess = requiredRole === userRole;
        }
      }

      if (!hasAccess) {
        console.log('Router guard - access denied, redirecting. Required:', requiredRole, 'User role:', userRole);
        next({ name: targetDashboard });
        return;
      }
    }
  }

  // Check if route requires guest (not logged in)
  if (to.meta.requiresGuest) {
    if (token) {
      // User is logged in, redirect to appropriate dashboard
      console.log('Router guard - user logged in, redirecting from guest route');
      next({ name: (user?.role === 'admin' || user?.role === 'super_admin') ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  console.log('Router guard - allowing navigation');
  next();
});

// Update document title after navigation
router.afterEach((to) => {
  // Import i18n to get current translations
  import('./i18n/index.js').then(({ default: i18n }) => {
    document.title = i18n.t('app_name');
  });
});

export default router;
