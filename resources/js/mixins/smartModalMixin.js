/**
 * Smart Modal Mixin
 * 
 * Provides intelligent modal closing behavior with form change detection
 * and user-friendly alerts for unsaved changes.
 * 
 * Usage:
 * 1. Import and add to mixins array in your modal component
 * 2. Define your form data structure in the component
 * 3. Call initializeForm() when opening the modal
 * 4. Use handleOutsideClick() and handleCancel() methods
 * 5. Add @click="handleOutsideClick" to modal backdrop
 * 6. Add @click="handleCancel" to cancel buttons
 */

export default {
  data() {
    return {
      // Original form state for change detection
      originalFormState: {},
      // Flag to track if form has been initialized
      formInitialized: false
    };
  },

  computed: {
    /**
     * Check if the current form has changes compared to original state
     * Override this computed property in your component if you need custom logic
     */
    hasFormChanges() {
      if (!this.formInitialized) return false;
      
      // Get the form data - components should define a 'form' data property
      const currentForm = this.form || {};
      
      return JSON.stringify(currentForm) !== JSON.stringify(this.originalFormState);
    }
  },

  methods: {
    /**
     * Initialize form tracking
     * Call this method when opening the modal or after populating form data
     * 
     * @param {Object} formData - The form data to track (optional, uses this.form by default)
     */
    initializeFormTracking(formData = null) {
      const form = formData || this.form || {};
      this.originalFormState = JSON.parse(JSON.stringify(form));
      this.formInitialized = true;
    },

    /**
     * Reset form tracking
     * Call this when closing the modal or resetting the form
     */
    resetFormTracking() {
      this.originalFormState = {};
      this.formInitialized = false;
    },

    /**
     * Handle outside click on modal backdrop
     * Only closes modal if no changes have been made
     */
    handleOutsideClick() {
      if (!this.hasFormChanges) {
        this.closeModal();
      } else {
        this.showUnsavedChangesAlert();
      }
    },

    /**
     * Handle cancel button click
     * Shows confirmation if there are unsaved changes
     */
    handleCancel() {
      if (!this.hasFormChanges) {
        this.closeModal();
      } else {
        this.showDiscardChangesConfirmation();
      }
    },

    /**
     * Show alert for unsaved changes when clicking outside
     */
    showUnsavedChangesAlert() {
      const message = this.$t ? this.$t('unsaved_changes_warning') : 
        'You have unsaved changes. Please save or cancel your changes before closing.';
      this.$toast.warning(message);
    },

    /**
     * Show confirmation dialog for discarding changes
     * @returns {boolean} - true if user confirms, false otherwise
     */
    showDiscardChangesConfirmation() {
      const message = this.$t ? this.$t('confirm_discard_changes') : 
        'You have unsaved changes. Are you sure you want to discard them?';
      
      if (confirm(message)) {
        this.closeModal();
        return true;
      }
      return false;
    },

    /**
     * Close the modal
     * Override this method in your component to implement specific closing logic
     */
    closeModal() {
      // Reset form tracking
      this.resetFormTracking();
      
      // Emit close event (standard pattern for Vue modals)
      this.$emit('close');
      
      // If component has a show/visible property, set it to false
      try{
        if (this.hasOwnProperty('show')) {
          this.show = false;
        }
        if (this.hasOwnProperty('visible')) {
          this.visible = false;
        }
        if (this.hasOwnProperty('isOpen')) {
          this.isOpen = false;
        }
      }
      catch(err){
        console.log(err);
      }
    },

    /**
     * Handle successful form submission
     * Call this method after successful form submission to close modal cleanly
     */
    handleFormSuccess() {
      this.resetFormTracking();
      this.closeModal();
    },

    /**
     * Utility method to check if a specific form field has changed
     * @param {string} fieldName - The name of the field to check
     * @returns {boolean} - true if field has changed
     */
    hasFieldChanged(fieldName) {
      if (!this.formInitialized) return false;
      
      const currentForm = this.form || {};
      const originalForm = this.originalFormState || {};
      
      return JSON.stringify(currentForm[fieldName]) !== JSON.stringify(originalForm[fieldName]);
    },

    /**
     * Get the list of changed fields
     * @returns {Array} - Array of field names that have changed
     */
    getChangedFields() {
      if (!this.formInitialized) return [];
      
      const currentForm = this.form || {};
      const originalForm = this.originalFormState || {};
      const changedFields = [];
      
      for (const key in currentForm) {
        if (JSON.stringify(currentForm[key]) !== JSON.stringify(originalForm[key])) {
          changedFields.push(key);
        }
      }
      
      return changedFields;
    }
  },

  /**
   * Lifecycle hooks
   */
  beforeUnmount() {
    // Clean up form tracking when component is destroyed
    this.resetFormTracking();
  }
};
