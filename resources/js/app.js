import './bootstrap';
import { createApp } from 'vue';
import App from './components/App.vue';
import router from './router';
import axios from 'axios';
import i18nPlugin from './plugins/i18n.js';
import { registerServiceWorker, addNetworkListeners, requestPersistentStorage } from './utils/serviceWorker.js';
import { lazyImageDirective } from './utils/lazyLoading.js';

// Configure Axios
// Base URL is already set in bootstrap.js
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  // Prevent browser authentication popup by setting custom header
  config.headers['X-Requested-With'] = 'XMLHttpRequest';
  
  return config;
});

// Handle 401 Unauthorized responses
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      const currentRoute = router.currentRoute.value;
      const isGuestPage = ['Home', 'About', 'Services', 'Contact', 'Login', 'Register'].includes(currentRoute.name);

      // Only redirect to login if:
      // 1. We're not already on the login page
      // 2. We're not on a guest/public page
      // 3. The route requires authentication
      if (currentRoute.name !== 'Login' &&
        (!isGuestPage || currentRoute.meta?.requiresAuth)) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Dispatch custom event to notify components
        window.dispatchEvent(new CustomEvent('auth-state-changed'));

        router.push('/login');
      }
    }
    return Promise.reject(error);
  }
);

const app = createApp(App);
app.config.globalProperties.$axios = axios;
app.config.globalProperties.$http = axios; // Alias for consistency

// Enhanced toast implementation
app.config.globalProperties.$toast = {
  show(message, type = 'info', duration = 5000) {
    // Remove existing toast if any
    const existingToast = document.getElementById('toast-notification');
    if (existingToast) {
      existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.id = 'toast-notification';
    toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-out`;
    
    // Set initial position (off-screen)
    toast.style.transform = 'translateX(100%)';
    toast.style.opacity = '0';

    // Determine icon and colors based on type
    const icons = {
      success: `<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
      </svg>`,
      error: `<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
      </svg>`,
      warning: `<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>`,
      info: `<svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
      </svg>`
    };

    const colors = {
      success: 'border-green-500',
      error: 'border-red-500',
      warning: 'border-yellow-500',
      info: 'border-blue-500'
    };

    toast.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            ${icons[type] || icons.info}
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">${message}</p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="this.closest('#toast-notification').remove()">
              <span class="sr-only">Close</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;

    // Add border color
    toast.classList.add(colors[type] || colors.info);
    toast.style.borderLeft = '4px solid';

    // Add to page
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
      toast.style.opacity = '1';
    }, 10);

    // Auto remove after duration
    setTimeout(() => {
      if (toast.parentNode) {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.remove();
          }
        }, 300);
      }
    }, duration);
  },

  success(message, duration = 5000) {
    this.show(message, 'success', duration);
  },

  error(message, duration = 7000) {
    this.show(message, 'error', duration);
  },

  info(message, duration = 5000) {
    this.show(message, 'info', duration);
  },

  warning(message, duration = 6000) {
    this.show(message, 'warning', duration);
  }
};

// Use plugins
app.use(router);
app.use(i18nPlugin);

// Register global directives
app.directive('lazy-img', lazyImageDirective);

// Performance optimizations and service worker
if (import.meta.env.PROD) {
    // Register service worker in production
    registerServiceWorker().then(registration => {
        if (registration) {
            console.log('Service Worker registered successfully');
            // Request persistent storage for better caching
            requestPersistentStorage();
        }
    });
} else {
    // Development mode - initialize cache manager
    import('./utils/cacheManager.js').then(({ initCacheManager }) => {
        initCacheManager();
    });
}

// Add network status listeners
addNetworkListeners();

// Performance monitoring
if (import.meta.env.DEV) {
    // Add performance monitoring in development
    const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
                console.log('Navigation timing:', {
                    domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                    loadComplete: entry.loadEventEnd - entry.loadEventStart,
                    totalTime: entry.loadEventEnd - entry.fetchStart
                });
            }
        }
    });

    observer.observe({ entryTypes: ['navigation'] });
}

// Mount the app
app.mount('#app');
