<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 transition-opacity" @click="handleOutsideClick"></div>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ $t('preview_template') }}: {{ template.name }}
              </h3>

              <!-- Template Info -->
              <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span class="font-medium text-gray-700">{{ $t('type') }}:</span>
                    <span class="ml-2 text-gray-900">{{ $t(template.type) }}</span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">{{ $t('priority') }}:</span>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                          :class="getPriorityClass(template.priority)">
                      {{ $t(template.priority || 'normal') }}
                    </span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">{{ $t('status') }}:</span>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                          :class="template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                      {{ template.is_active ? $t('active') : $t('inactive') }}
                    </span>
                  </div>
                  <div>
                    <span class="font-medium text-gray-700">{{ $t('delivery_methods') }}:</span>
                    <div class="ml-2 flex space-x-1">
                      <span v-if="template.supports_email" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {{ $t('email') }}
                      </span>
                      <span v-if="template.supports_sms" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        {{ $t('sms') }}
                      </span>
                      <span v-if="template.supports_push" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                        {{ $t('push') }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Preview Tabs -->
              <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8">
                  <button
                    v-if="template.supports_email"
                    @click="activeTab = 'email'"
                    :class="[
                      'py-2 px-1 border-b-2 font-medium text-sm',
                      activeTab === 'email'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    ]"
                  >
                    {{ $t('email_preview') }}
                  </button>
                  <button
                    v-if="template.supports_sms"
                    @click="activeTab = 'sms'"
                    :class="[
                      'py-2 px-1 border-b-2 font-medium text-sm',
                      activeTab === 'sms'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    ]"
                  >
                    {{ $t('sms_preview') }}
                  </button>
                  <button
                    v-if="template.supports_push"
                    @click="activeTab = 'push'"
                    :class="[
                      'py-2 px-1 border-b-2 font-medium text-sm',
                      activeTab === 'push'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    ]"
                  >
                    {{ $t('push_preview') }}
                  </button>
                </nav>
              </div>

              <!-- Email Preview -->
              <div v-if="activeTab === 'email' && template.supports_email" class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                  <div class="border-b border-gray-200 pb-3 mb-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">{{ $t('email_subject') }}</h4>
                    <div class="bg-gray-50 p-3 rounded border text-sm">
                      {{ processTemplate(template.email_subject) }}
                    </div>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">{{ $t('email_body') }}</h4>
                    <div class="bg-gray-50 p-4 rounded border text-sm whitespace-pre-wrap">
                      {{ processTemplate(template.email_body) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- SMS Preview -->
              <div v-if="activeTab === 'sms' && template.supports_sms" class="space-y-4">
                <div class="max-w-sm mx-auto">
                  <div class="bg-green-500 text-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center mb-2">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      <span class="text-xs font-medium">{{ $t('sms_message') }}</span>
                    </div>
                    <div class="text-sm">
                      {{ processTemplate(template.sms_body) }}
                    </div>
                    <div class="text-xs opacity-75 mt-2">
                      {{ processTemplate(template.sms_body).length }}/160 {{ $t('characters') }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Push Notification Preview -->
              <div v-if="activeTab === 'push' && template.supports_push" class="space-y-4">
                <div class="max-w-sm mx-auto">
                  <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                    <div class="flex items-center mb-2">
                      <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900">
                          {{ processTemplate(template.push_title) }}
                        </div>
                        <div class="text-xs text-gray-500">{{ $t('app_name') }}</div>
                      </div>
                    </div>
                    <div class="text-sm text-gray-700">
                      {{ processTemplate(template.push_body) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Sample Data Info -->
              <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex">
                  <svg class="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div class="text-sm">
                    <p class="text-yellow-800 font-medium">{{ $t('preview_note') }}</p>
                    <p class="text-yellow-700 mt-1">{{ $t('preview_note_description') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="sendTestNotification"
            :disabled="isSendingTest"
            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isSendingTest" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSendingTest ? $t('sending') : $t('send_test') }}
          </button>
          <button
            type="button"
            @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            {{ $t('close') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'NotificationTemplatePreviewModal',
  mixins: [i18nMixin, smartModalMixin],
  props: {
    template: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'email',
      isSendingTest: false,
      sampleData: {
        user_name: 'John Doe',
        building_name: 'Sunset Apartments',
        amount: '$150.00',
        due_date: '2025-08-15',
        apartment_number: '4B'
      }
    };
  },
  computed: {
    // Override smart modal mixin - preview modal has no forms to track
    hasFormChanges() {
      return false;
    }
  },
  mounted() {
    // Set default active tab based on supported methods
    if (this.template.supports_email) {
      this.activeTab = 'email';
    } else if (this.template.supports_sms) {
      this.activeTab = 'sms';
    } else if (this.template.supports_push) {
      this.activeTab = 'push';
    }
  },
  methods: {
    processTemplate(content) {
      if (!content) return '';
      
      let processed = content;
      Object.entries(this.sampleData).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processed = processed.replace(regex, value);
      });
      
      return processed;
    },

    getPriorityClass(priority) {
      const classes = {
        'low': 'bg-gray-100 text-gray-800',
        'normal': 'bg-blue-100 text-blue-800',
        'high': 'bg-yellow-100 text-yellow-800',
        'urgent': 'bg-red-100 text-red-800'
      };
      return classes[priority] || classes['normal'];
    },

    async sendTestNotification() {
      this.isSendingTest = true;
      try {
        await this.$axios.post(`/notification-templates/${this.template.id}/test`);
        this.$toast.success(this.$t('test_notification_sent'));
      } catch (error) {
        console.error('Error sending test notification:', error);
        this.$toast.error(error.response?.data?.message || this.$t('test_send_failed'));
      } finally {
        this.isSendingTest = false;
      }
    }
  }
};
</script>
