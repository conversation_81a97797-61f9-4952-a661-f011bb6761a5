<template>
  <div class="mobile-dashboard">
    <!-- Mobile Header -->
    <div class="mobile-header bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 safe-area-inset-top">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-xl font-bold">{{ $t('dashboard') }}</h1>
          <p class="text-blue-100 text-sm">{{ user?.name }}</p>
        </div>
        <div class="flex items-center space-x-3">
          <!-- Search Button -->
          <button @click="toggleSearch" class="p-2 rounded-full hover:bg-blue-500 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </button>
          <!-- Notifications -->
          <button @click="toggleNotifications" class="p-2 rounded-full hover:bg-blue-500 transition-colors relative">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
            </svg>
            <span v-if="unreadCount > 0" 
                  class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {{ unreadCount > 9 ? '9+' : unreadCount }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search Bar (Expandable) -->
    <div v-if="showSearch" class="bg-white border-b border-gray-200 p-4">
      <div class="relative">
        <input 
          v-model="searchQuery"
          type="text" 
          :placeholder="$t('search_placeholder')"
          class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          @input="performSearch"
        >
        <svg class="absolute left-3 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
      </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="p-4 space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <!-- Total Expenses -->
        <div class="mobile-card bg-gradient-to-br from-red-50 to-red-100 border border-red-200">
          <div class="flex items-center">
            <div class="p-2 bg-red-500 rounded-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-600 font-medium">{{ $t('total_expenses') }}</p>
              <p class="text-lg font-bold text-red-700">{{ formatCurrency(stats.totalExpenses) }}</p>
            </div>
          </div>
        </div>

        <!-- Total Incomes -->
        <div class="mobile-card bg-gradient-to-br from-green-50 to-green-100 border border-green-200">
          <div class="flex items-center">
            <div class="p-2 bg-green-500 rounded-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-600 font-medium">{{ $t('total_incomes') }}</p>
              <p class="text-lg font-bold text-green-700">{{ formatCurrency(stats.totalIncomes) }}</p>
            </div>
          </div>
        </div>

        <!-- Pending Payments -->
        <div class="mobile-card bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-500 rounded-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-600 font-medium">{{ $t('pending_payments') }}</p>
              <p class="text-lg font-bold text-yellow-700">{{ stats.pendingPayments }}</p>
            </div>
          </div>
        </div>

        <!-- Collection Rate -->
        <div class="mobile-card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200">
          <div class="flex items-center">
            <div class="p-2 bg-blue-500 rounded-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-600 font-medium">{{ $t('collection_rate') }}</p>
              <p class="text-lg font-bold text-blue-700">{{ stats.collectionRate }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mobile-card">
        <h3 class="text-lg font-semibold mb-4">{{ $t('quick_actions') }}</h3>
        <div class="grid grid-cols-2 gap-3">
          <button
            v-if="isAdmin"
            @click="navigateTo('/admin/expenses')"
            class="mobile-btn bg-blue-600 text-white hover:bg-blue-700"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {{ $t('add_expense') }}
          </button>
          <button
            v-if="isAdmin"
            @click="navigateTo('/admin/building-expenses')"
            class="mobile-btn bg-green-600 text-white hover:bg-green-700"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            {{ $t('building_expenses') }}
          </button>
          
          <button 
            v-if="isAdmin"
            @click="navigateTo('/admin/incomes')"
            class="mobile-btn bg-green-600 text-white hover:bg-green-700"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {{ $t('add_income') }}
          </button>
          
          <button 
            @click="navigateTo('/reports')"
            class="mobile-btn bg-purple-600 text-white hover:bg-purple-700"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            {{ $t('reports') }}
          </button>
          
          <button 
            @click="navigateTo('/settings')"
            class="mobile-btn bg-gray-600 text-white hover:bg-gray-700"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            {{ $t('settings') }}
          </button>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="mobile-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">{{ $t('recent_activity') }}</h3>
          <button @click="refreshActivity" class="text-blue-600 text-sm font-medium">
            {{ $t('refresh') }}
          </button>
        </div>
        
        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        
        <div v-else-if="recentActivity.length === 0" class="text-center py-8 text-gray-500">
          <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
          </svg>
          <p>{{ $t('no_recent_activity') }}</p>
        </div>
        
        <div v-else class="space-y-3">
          <div 
            v-for="activity in recentActivity" 
            :key="activity.id"
            class="flex items-center p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex-shrink-0">
              <div :class="getActivityIconClass(activity.type)" class="p-2 rounded-full">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getActivityIconPath(activity.type)"/>
                </svg>
              </div>
            </div>
            <div class="ml-3 flex-1">
              <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
              <p class="text-xs text-gray-500">{{ formatDate(activity.created_at) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from '../utils/lazyLoading.js';

export default {
  name: 'MobileDashboard',
  
  data() {
    return {
      showSearch: false,
      searchQuery: '',
      unreadCount: 0,
      loading: false,
      stats: {
        totalExpenses: 0,
        totalIncomes: 0,
        pendingPayments: 0,
        collectionRate: 0
      },
      recentActivity: []
    };
  },
  
  computed: {
    user() {
      return JSON.parse(localStorage.getItem('user') || 'null');
    },
    
    isAdmin() {
      return this.user?.role === 'admin' || this.user?.role === 'super_admin';
    }
  },
  
  mounted() {
    this.loadDashboardData();
    this.loadRecentActivity();
    this.performSearch = debounce(this.performSearch, 300);
  },
  
  methods: {
    toggleSearch() {
      this.showSearch = !this.showSearch;
      if (!this.showSearch) {
        this.searchQuery = '';
      }
    },
    
    toggleNotifications() {
      // This will be handled by the parent component or mobile navigation
      this.$emit('toggle-notifications');
    },
    
    navigateTo(path) {
      this.$router.push(path);
    },
    
    async loadDashboardData() {
      try {
        const response = await this.$axios.get('/dashboard/stats');
        this.stats = response.data;
      } catch (error) {
        console.error('Failed to load dashboard stats:', error);
      }
    },
    
    async loadRecentActivity() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/dashboard/recent-activity');
        this.recentActivity = response.data.slice(0, 5); // Show only 5 recent items
      } catch (error) {
        console.error('Failed to load recent activity:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async refreshActivity() {
      await this.loadRecentActivity();
    },
    
    async performSearch() {
      if (!this.searchQuery.trim()) return;
      
      try {
        const response = await this.$axios.get('/search', {
          params: { q: this.searchQuery }
        });
        // Handle search results
        console.log('Search results:', response.data);
      } catch (error) {
        console.error('Search failed:', error);
      }
    },
    
    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD'
      }).format(amount || 0);
    },
    
    formatDate(date) {
      return new Intl.DateTimeFormat('ar-SA', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(new Date(date));
    },
    
    getActivityIconClass(type) {
      const classes = {
        expense: 'bg-red-500',
        income: 'bg-green-500',
        payment: 'bg-blue-500',
        notification: 'bg-purple-500'
      };
      return classes[type] || 'bg-gray-500';
    },
    
    getActivityIconPath(type) {
      const paths = {
        expense: 'M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z',
        income: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
        payment: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
        notification: 'M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3'
      };
      return paths[type] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    }
  }
};
</script>

<style scoped>
.mobile-dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.mobile-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Pull-to-refresh animation */
.mobile-dashboard {
  overscroll-behavior-y: contain;
}

/* Smooth transitions */
.mobile-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:active {
  transform: scale(0.98);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
