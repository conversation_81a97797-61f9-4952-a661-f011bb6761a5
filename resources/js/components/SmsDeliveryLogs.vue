<template>
  <div class="sms-delivery-logs">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-medium text-gray-900">{{ $t('sms_delivery_logs') }}</h2>
        <p class="mt-1 text-sm text-gray-500">{{ $t('track_sms_delivery_status') }}</p>
      </div>
      <button
        @click="$emit('refresh')"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {{ $t('refresh') }}
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('status') }}</label>
          <select 
            v-model="filters.status"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('all_statuses') }}</option>
            <option value="pending">{{ $t('pending') }}</option>
            <option value="sent">{{ $t('sent') }}</option>
            <option value="delivered">{{ $t('delivered') }}</option>
            <option value="failed">{{ $t('failed') }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('provider') }}</label>
          <select 
            v-model="filters.provider"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('all_providers') }}</option>
            <option value="twilio">Twilio</option>
            <option value="aws_sns">AWS SNS</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('date_from') }}</label>
          <input
            v-model="filters.dateFrom"
            type="date"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('date_to') }}</label>
          <input
            v-model="filters.dateTo"
            type="date"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
      </div>
    </div>

    <!-- Delivery Logs Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('recipient') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('message') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('status') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('provider') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('cost') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('sent_at') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('actions') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="log in filteredLogs" :key="log.id" class="hover:bg-gray-50">
              <!-- Recipient -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ log.user?.name || $t('unknown_user') }}</div>
                    <div class="text-sm text-gray-500">{{ formatPhoneNumber(log.phone_number) }}</div>
                  </div>
                </div>
              </td>

              <!-- Message -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">
                  <div class="max-w-xs truncate" :title="log.message_content">
                    {{ log.message_content }}
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    {{ log.message_content.length }} {{ $t('characters') }}
                  </div>
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(log.status)">
                  {{ getStatusLabel(log.status) }}
                </span>
                <div v-if="log.error_message" class="text-xs text-red-600 mt-1">
                  {{ log.error_message }}
                </div>
              </td>

              <!-- Provider -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getProviderName(log.provider) }}
              </td>

              <!-- Cost -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span v-if="log.cost">{{ formatCurrency(log.cost) }}</span>
                <span v-else class="text-gray-400">-</span>
              </td>

              <!-- Sent At -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>{{ formatDate(log.created_at) }}</div>
                <div class="text-xs">{{ formatTime(log.created_at) }}</div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="viewDetails(log)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    {{ $t('details') }}
                  </button>
                  <button
                    v-if="log.status === 'failed'"
                    @click="retryDelivery(log)"
                    class="text-green-600 hover:text-green-900"
                  >
                    {{ $t('retry') }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredLogs.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.681L3 21l2.319-5.094A7.96 7.96 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_delivery_logs') }}</h3>
        <p class="mt-1 text-sm text-gray-500">{{ $t('no_delivery_logs_description') }}</p>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="filteredLogs.length > 0" class="mt-6 flex items-center justify-between">
      <div class="text-sm text-gray-700">
        {{ $t('showing_results', { from: 1, to: filteredLogs.length, total: logs.length }) }}
      </div>
      <div class="flex space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ $t('previous') }}
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage >= totalPages"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ $t('next') }}
        </button>
      </div>
    </div>

    <!-- Details Modal -->
    <SmsDeliveryDetailsModal
      v-if="showDetailsModal"
      :log="selectedLog"
      @close="showDetailsModal = false"
    />
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SmsDeliveryLogs',
  mixins: [i18nMixin],
  props: {
    logs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showDetailsModal: false,
      selectedLog: null,
      currentPage: 1,
      perPage: 20,
      filters: {
        status: '',
        provider: '',
        dateFrom: '',
        dateTo: ''
      }
    };
  },
  computed: {
    filteredLogs() {
      let filtered = [...this.logs];

      if (this.filters.status) {
        filtered = filtered.filter(log => log.status === this.filters.status);
      }

      if (this.filters.provider) {
        filtered = filtered.filter(log => log.provider === this.filters.provider);
      }

      if (this.filters.dateFrom) {
        filtered = filtered.filter(log => new Date(log.created_at) >= new Date(this.filters.dateFrom));
      }

      if (this.filters.dateTo) {
        filtered = filtered.filter(log => new Date(log.created_at) <= new Date(this.filters.dateTo));
      }

      return filtered;
    },

    totalPages() {
      return Math.ceil(this.filteredLogs.length / this.perPage);
    }
  },
  methods: {
    applyFilters() {
      this.currentPage = 1;
    },

    getStatusClass(status) {
      const classes = {
        'pending': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800',
        'sent': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800',
        'delivered': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800',
        'failed': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800'
      };
      return classes[status] || classes['pending'];
    },

    getStatusLabel(status) {
      const labels = {
        'pending': this.$t('pending'),
        'sent': this.$t('sent'),
        'delivered': this.$t('delivered'),
        'failed': this.$t('failed')
      };
      return labels[status] || status;
    },

    getProviderName(provider) {
      const providers = {
        'twilio': 'Twilio',
        'aws_sns': 'AWS SNS'
      };
      return providers[provider] || provider;
    },

    formatPhoneNumber(phoneNumber) {
      if (!phoneNumber) return '';
      // Format phone number for display
      return phoneNumber.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1 $2 $3 $4');
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 4
      }).format(amount);
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    },

    formatTime(dateString) {
      return new Date(dateString).toLocaleTimeString();
    },

    viewDetails(log) {
      this.selectedLog = log;
      this.showDetailsModal = true;
    },

    async retryDelivery(log) {
      try {
        await this.$axios.post(`/api/sms/delivery-logs/${log.id}/retry`);
        this.$emit('refresh');
        this.$toast.success(this.$t('sms_retry_initiated'));
      } catch (error) {
        console.error('Error retrying SMS delivery:', error);
        this.$toast.error(this.$t('retry_failed'));
      }
    },

    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    }
  }
};
</script>
