<template>
  <div class="relative" ref="searchContainer">
    <!-- Search Input -->
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      
      <input
        ref="searchInput"
        v-model="searchQuery"
        @input="handleInput"
        @focus="showSuggestions = true"
        @keydown="handleKeydown"
        type="text"
        :placeholder="$t('search_placeholder')"
        class="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      />
      
      <!-- Clear Button -->
      <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
        <button
          @click="clearSearch"
          class="text-gray-400 hover:text-gray-600"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Search Suggestions Dropdown -->
    <div
      v-if="showSuggestions && (suggestions.length > 0 || loading)"
      class="absolute z-50 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none"
    >
      <!-- Loading State -->
      <div v-if="loading" class="px-4 py-2 text-sm text-gray-500">
        {{ $t('searching') }}...
      </div>
      
      <!-- Suggestions -->
      <div v-else>
        <div
          v-for="(suggestion, index) in suggestions"
          :key="`${suggestion.type}-${suggestion.id}`"
          @click="selectSuggestion(suggestion)"
          @mouseenter="highlightedIndex = index"
          :class="[
            'cursor-pointer px-4 py-2 text-sm',
            highlightedIndex === index ? 'bg-blue-50 text-blue-900' : 'text-gray-900 hover:bg-gray-50'
          ]"
        >
          <div class="flex items-center space-x-3">
            <!-- Type Icon -->
            <div class="flex-shrink-0">
              <div
                class="w-6 h-6 rounded flex items-center justify-center text-xs font-medium"
                :class="getTypeIconClass(suggestion.type)"
              >
                {{ getTypeIcon(suggestion.type) }}
              </div>
            </div>
            
            <!-- Suggestion Content -->
            <div class="flex-1 min-w-0">
              <p class="font-medium truncate">{{ suggestion.text }}</p>
              <p class="text-xs text-gray-500 capitalize">{{ $t(suggestion.type) }}</p>
            </div>
          </div>
        </div>
        
        <!-- View All Results -->
        <div
          v-if="searchQuery.length >= 2"
          @click="viewAllResults"
          class="border-t border-gray-100 px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 cursor-pointer"
        >
          {{ $t('view_all_results') }} "{{ searchQuery }}"
        </div>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="showSuggestions"
      class="fixed inset-0 z-40"
      @click="showSuggestions = false"
    ></div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SearchBar',
  mixins: [i18nMixin],
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    showAdvanced: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchQuery: '',
      suggestions: [],
      showSuggestions: false,
      loading: false,
      highlightedIndex: -1,
      debounceTimer: null,
    };
  },
  mounted() {
    if (this.autoFocus) {
      this.$nextTick(() => {
        this.$refs.searchInput.focus();
      });
    }

    // Handle clicks outside
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  },
  methods: {
    handleInput() {
      // Clear previous timer
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // Debounce the search
      this.debounceTimer = setTimeout(() => {
        if (this.searchQuery.length >= 2) {
          this.fetchSuggestions();
        } else {
          this.suggestions = [];
          this.showSuggestions = false;
        }
      }, 300);
    },

    async fetchSuggestions() {
      if (this.searchQuery.length < 2) return;

      this.loading = true;
      try {
        const response = await this.$axios.get('/search/suggestions', {
          params: {
            query: this.searchQuery,
            limit: 8
          }
        });
        
        this.suggestions = response.data.suggestions;
        this.showSuggestions = true;
        this.highlightedIndex = -1;
      } catch (error) {
        console.error('Failed to fetch suggestions:', error);
        this.suggestions = [];
      } finally {
        this.loading = false;
      }
    },

    handleKeydown(event) {
      if (!this.showSuggestions || this.suggestions.length === 0) {
        if (event.key === 'Enter') {
          this.performSearch();
        }
        return;
      }

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.suggestions.length - 1
          );
          break;
        
        case 'ArrowUp':
          event.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, -1);
          break;
        
        case 'Enter':
          event.preventDefault();
          if (this.highlightedIndex >= 0) {
            this.selectSuggestion(this.suggestions[this.highlightedIndex]);
          } else {
            this.performSearch();
          }
          break;
        
        case 'Escape':
          this.showSuggestions = false;
          this.highlightedIndex = -1;
          break;
      }
    },

    selectSuggestion(suggestion) {
      this.searchQuery = suggestion.value;
      this.showSuggestions = false;
      this.highlightedIndex = -1;
      
      // Emit selection event
      this.$emit('suggestion-selected', suggestion);
      
      // Navigate based on suggestion type
      this.navigateToSuggestion(suggestion);
    },

    navigateToSuggestion(suggestion) {
      switch (suggestion.type) {
        case 'user':
          // Navigate to user profile or expenses for this user
          this.$router.push({
            name: 'Expenses',
            query: { user_id: suggestion.id }
          });
          break;
        
        case 'expense_type':
          // Navigate to expenses filtered by type
          this.$router.push({
            name: 'Expenses',
            query: { expense_type_id: suggestion.id }
          });
          break;
        
        default:
          // Perform general search
          this.performSearch();
          break;
      }
    },

    performSearch() {
      if (this.searchQuery.trim().length < 2) return;
      
      this.showSuggestions = false;
      this.$emit('search', this.searchQuery.trim());
      
      // Navigate to search results page
      this.$router.push({
        name: 'SearchResults',
        query: { q: this.searchQuery.trim() }
      });
    },

    viewAllResults() {
      this.performSearch();
    },

    clearSearch() {
      this.searchQuery = '';
      this.suggestions = [];
      this.showSuggestions = false;
      this.highlightedIndex = -1;
      this.$emit('clear');
    },

    handleClickOutside(event) {
      if (!this.$refs.searchContainer.contains(event.target)) {
        this.showSuggestions = false;
        this.highlightedIndex = -1;
      }
    },

    getTypeIconClass(type) {
      const classes = {
        user: 'bg-blue-100 text-blue-600',
        expense: 'bg-red-100 text-red-600',
        income: 'bg-green-100 text-green-600',
        payment: 'bg-yellow-100 text-yellow-600',
        expense_type: 'bg-purple-100 text-purple-600',
      };
      return classes[type] || 'bg-gray-100 text-gray-600';
    },

    getTypeIcon(type) {
      const icons = {
        user: '👤',
        expense: '💸',
        income: '💰',
        payment: '💳',
        expense_type: '📋',
      };
      return icons[type] || '📄';
    },
  },
};
</script>

<style scoped>
/* Custom scrollbar for suggestions dropdown */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
