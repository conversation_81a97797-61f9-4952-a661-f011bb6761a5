<template>
  <div class="report-templates">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">{{ $t('report_templates') }}</h2>
        <p class="text-sm text-gray-600">{{ $t('choose_from_predefined_templates') }}</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="i in 6" :key="i" class="bg-white rounded-lg shadow p-6 animate-pulse">
        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div class="h-3 bg-gray-200 rounded w-full mb-2"></div>
        <div class="h-3 bg-gray-200 rounded w-2/3"></div>
      </div>
    </div>

    <!-- Templates Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="template in templates"
        :key="template.id"
        class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200 hover:border-blue-300"
        @click="selectTemplate(template)"
      >
        <div class="p-6">
          <!-- Template Icon -->
          <div class="flex items-center mb-4">
            <div :class="[
              'w-10 h-10 rounded-lg flex items-center justify-center',
              template.color || 'bg-blue-100'
            ]">
              <component :is="template.icon" class="w-6 h-6" :class="template.iconColor || 'text-blue-600'" />
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-semibold text-gray-900">{{ $t(template.name) }}</h3>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {{ $t(template.category) }}
              </span>
            </div>
          </div>

          <!-- Template Description -->
          <p class="text-sm text-gray-600 mb-4">{{ $t(template.description) }}</p>

          <!-- Template Features -->
          <div class="space-y-2 mb-4">
            <div v-for="feature in template.features" :key="feature" class="flex items-center text-sm text-gray-600">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              {{ $t(feature) }}
            </div>
          </div>

          <!-- Template Actions -->
          <div class="flex justify-between items-center pt-4 border-t border-gray-100">
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {{ template.estimatedTime }}
            </div>
            <button
              @click.stop="previewTemplate(template)"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              {{ $t('preview') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && templates.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_templates_available') }}</h3>
      <p class="mt-1 text-sm text-gray-500">{{ $t('templates_will_be_available_soon') }}</p>
    </div>

    <!-- Template Preview Modal -->
    <TemplatePreviewModal
      v-if="showPreviewModal"
      :template="selectedTemplate"
      @close="showPreviewModal = false"
      @use-template="useTemplate"
    />
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin';
import TemplatePreviewModal from './TemplatePreviewModal.vue';

// Template Icons
const ChartBarIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>`
};

const CurrencyDollarIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>`
};

const UsersIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" /></svg>`
};

const CogIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`
};

export default {
  name: 'ReportTemplatesList',
  mixins: [i18nMixin],
  components: {
    TemplatePreviewModal,
    ChartBarIcon,
    CurrencyDollarIcon,
    UsersIcon,
    CogIcon
  },
  data() {
    return {
      loading: true,
      templates: [],
      showPreviewModal: false,
      selectedTemplate: null
    };
  },
  async mounted() {
    await this.loadTemplates();
  },
  methods: {
    async loadTemplates() {
      try {
        this.loading = true;

        const response = await this.$http.get('/advanced-reporting/templates');
        const apiTemplates = response.data || [];

        // Convert API templates to the format expected by the UI
        this.templates = apiTemplates.map(template => ({
          id: template.slug,
          name: template.name,
          description: template.description,
          category: template.category,
          icon: this.getIconForCategory(template.category),
          color: this.getColorForCategory(template.category),
          iconColor: this.getIconColorForCategory(template.category),
          features: this.getFeaturesForTemplate(template),
          estimatedTime: '2-4 min',
          config: {
            charts: template.chart_options || ['bar', 'line', 'table'],
            fields: template.fields || [],
            filters: template.filters || []
          }
        }));

        // If no templates from API, fall back to predefined ones
        if (this.templates.length === 0) {
          this.templates = this.getPredefinedTemplates();
        }
      } catch (error) {
        console.error('Error loading templates:', error);
        // Fall back to predefined templates
        this.templates = this.getPredefinedTemplates();
        this.$toast.error(this.$t('error_loading_templates'));
      } finally {
        this.loading = false;
      }
    },
    getPredefinedTemplates() {
      return [
        {
          id: 'financial-summary',
          name: 'financial_summary_report',
          description: 'comprehensive_financial_overview',
          category: 'financial',
          icon: 'CurrencyDollarIcon',
          color: 'bg-green-100',
          iconColor: 'text-green-600',
          features: ['income_expense_analysis', 'monthly_trends', 'category_breakdown'],
          estimatedTime: '2-3 min',
          config: {
            charts: ['bar', 'pie', 'line'],
            fields: ['income', 'expenses', 'categories', 'dates'],
            filters: ['date_range', 'categories']
          }
        },
        {
          id: 'user-activity',
          name: 'user_activity_report',
          description: 'track_user_engagement_patterns',
          category: 'analytics',
          icon: 'UsersIcon',
          color: 'bg-blue-100',
          iconColor: 'text-blue-600',
          features: ['login_patterns', 'feature_usage', 'user_demographics'],
          estimatedTime: '1-2 min',
          config: {
            charts: ['line', 'bar', 'heatmap'],
            fields: ['users', 'sessions', 'actions', 'timestamps'],
            filters: ['date_range', 'user_roles']
          }
        },
        {
          id: 'maintenance-tracking',
          name: 'maintenance_tracking_report',
          description: 'monitor_building_maintenance_activities',
          category: 'operations',
          icon: 'CogIcon',
          color: 'bg-orange-100',
          iconColor: 'text-orange-600',
          features: ['maintenance_schedule', 'cost_analysis', 'completion_rates'],
          estimatedTime: '2-4 min',
          config: {
            charts: ['gantt', 'bar', 'pie'],
            fields: ['maintenance_items', 'costs', 'dates', 'status'],
            filters: ['date_range', 'maintenance_type', 'status']
          }
        }
      ];
    },
    selectTemplate(template) {
      this.$emit('create-from-template', template);
    },
    previewTemplate(template) {
      this.selectedTemplate = template;
      this.showPreviewModal = true;
    },
    useTemplate(template) {
      this.showPreviewModal = false;
      this.$emit('create-from-template', template);
    },
    getIconForCategory(category) {
      const iconMap = {
        'financial': 'CurrencyDollarIcon',
        'operational': 'CogIcon',
        'analytics': 'ChartBarIcon',
        'custom': 'UsersIcon'
      };
      return iconMap[category] || 'ChartBarIcon';
    },
    getColorForCategory(category) {
      const colorMap = {
        'financial': 'bg-green-100',
        'operational': 'bg-orange-100',
        'analytics': 'bg-blue-100',
        'custom': 'bg-purple-100'
      };
      return colorMap[category] || 'bg-gray-100';
    },
    getIconColorForCategory(category) {
      const colorMap = {
        'financial': 'text-green-600',
        'operational': 'text-orange-600',
        'analytics': 'text-blue-600',
        'custom': 'text-purple-600'
      };
      return colorMap[category] || 'text-gray-600';
    },
    getFeaturesForTemplate(template) {
      // Extract features from template configuration
      const features = [];
      if (template.fields && template.fields.length > 0) {
        features.push('custom_fields');
      }
      if (template.filters && template.filters.length > 0) {
        features.push('advanced_filters');
      }
      if (template.chart_options && template.chart_options.length > 0) {
        features.push('multiple_charts');
      }
      return features.length > 0 ? features : ['basic_reporting'];
    }
  }
};
</script>

<style scoped>
.report-templates {
  /* Custom styles if needed */
}
</style>
