<template>
  <div class="sms-settings">
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-lg font-medium text-gray-900">{{ $t('sms_settings') }}</h2>
      <p class="mt-1 text-sm text-gray-500">{{ $t('configure_sms_provider_and_settings') }}</p>
    </div>

    <!-- SMS Enable/Disable -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900">{{ $t('sms_notifications') }}</h3>
          <p class="text-sm text-gray-500">{{ $t('enable_disable_sms_notifications') }}</p>
        </div>
        <div class="flex items-center">
          <button
            @click="toggleSms"
            :class="[
              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
              settings.sms_enabled ? 'bg-blue-600' : 'bg-gray-200'
            ]"
          >
            <span
              :class="[
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                settings.sms_enabled ? 'translate-x-5' : 'translate-x-0'
              ]"
            />
          </button>
        </div>
      </div>
    </div>

    <!-- SMS Provider Configuration -->
    <div v-if="settings.sms_enabled" class="bg-white shadow rounded-lg p-6 mb-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('sms_provider_configuration') }}</h3>
      
      <form @submit.prevent="saveSettings">
        <!-- Provider Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('sms_provider') }} <span class="text-red-500">*</span>
          </label>
          <select 
            v-model="form.sms_provider" 
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('select_provider') }}</option>
            <option 
              v-for="(provider, key) in supportedProviders" 
              :key="key" 
              :value="key"
            >
              {{ provider.name }}
            </option>
          </select>
        </div>

        <!-- Provider-specific Configuration -->
        <div v-if="form.sms_provider" class="space-y-4 mb-6">
          <!-- Twilio Configuration -->
          <div v-if="form.sms_provider === 'twilio'">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('twilio_account_sid') }} <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.provider_config.account_sid"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_twilio_account_sid')"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('twilio_auth_token') }} <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.provider_config.auth_token"
                  type="password"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_twilio_auth_token')"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('twilio_phone_number') }} <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.provider_config.from_number"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_twilio_phone_number')"
                >
              </div>
            </div>
          </div>

          <!-- AWS SNS Configuration -->
          <div v-if="form.sms_provider === 'aws_sns'">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('aws_access_key_id') }} <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.provider_config.access_key_id"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_aws_access_key_id')"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('aws_secret_access_key') }} <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.provider_config.secret_access_key"
                  type="password"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_aws_secret_access_key')"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ $t('aws_region') }} <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.provider_config.region"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">{{ $t('select_aws_region') }}</option>
                  <option value="us-east-1">US East (N. Virginia)</option>
                  <option value="us-west-2">US West (Oregon)</option>
                  <option value="eu-west-1">Europe (Ireland)</option>
                  <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- SMS Limits and Costs -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('monthly_sms_limit') }}
            </label>
            <input
              v-model.number="form.monthly_sms_limit"
              type="number"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('enter_monthly_limit')"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('cost_per_sms') }}
            </label>
            <input
              v-model.number="form.cost_per_sms"
              type="number"
              step="0.0001"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('enter_cost_per_sms')"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('default_country_code') }}
            </label>
            <input
              v-model="form.default_country_code"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('enter_country_code')"
            >
          </div>
        </div>

        <!-- Notification Types -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">
            {{ $t('enabled_notification_types') }}
          </label>
          <div class="space-y-2">
            <label 
              v-for="type in notificationTypes" 
              :key="type.value"
              class="flex items-center"
            >
              <input
                v-model="form.notification_types"
                :value="type.value"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              >
              <span class="ml-2 text-sm text-gray-700">{{ $t(type.label) }}</span>
            </label>
          </div>
        </div>

        <!-- User Opt-in Requirement -->
        <div class="mb-6">
          <label class="flex items-center">
            <input
              v-model="form.require_user_opt_in"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <span class="ml-2 text-sm text-gray-700">{{ $t('require_user_opt_in') }}</span>
          </label>
          <p class="mt-1 text-xs text-gray-500">{{ $t('require_user_opt_in_description') }}</p>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? $t('saving') : $t('save_settings') }}
          </button>
        </div>
      </form>
    </div>

    <!-- Usage Statistics -->
    <div v-if="settings.sms_enabled" class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('usage_statistics') }}</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ settings.sms_sent_this_month || 0 }}</div>
          <div class="text-sm text-gray-500">{{ $t('sms_sent_this_month') }}</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ settings.monthly_sms_limit || 0 }}</div>
          <div class="text-sm text-gray-500">{{ $t('monthly_limit') }}</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ remainingMessages }}</div>
          <div class="text-sm text-gray-500">{{ $t('remaining_messages') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SmsSettings',
  mixins: [i18nMixin],
  props: {
    settings: {
      type: Object,
      default: () => ({})
    },
    supportedProviders: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isSubmitting: false,
      form: {
        sms_enabled: false,
        sms_provider: '',
        provider_config: {},
        monthly_sms_limit: 1000,
        cost_per_sms: 0.01,
        notification_types: [],
        require_user_opt_in: true,
        default_country_code: '+1'
      },
      notificationTypes: [
        { value: 'payment_reminder', label: 'payment_reminder' },
        { value: 'expense_created', label: 'expense_created' },
        { value: 'income_received', label: 'income_received' },
        { value: 'general_announcement', label: 'general_announcement' },
        { value: 'payment_received', label: 'payment_received' },
        { value: 'overdue_payment', label: 'overdue_payment' }
      ]
    };
  },
  computed: {
    remainingMessages() {
      const sent = this.settings.sms_sent_this_month || 0;
      const limit = this.settings.monthly_sms_limit || 0;
      return Math.max(0, limit - sent);
    }
  },
  watch: {
    settings: {
      handler(newSettings) {
        if (newSettings) {
          this.populateForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    populateForm() {
      this.form = {
        sms_enabled: this.settings.sms_enabled || false,
        sms_provider: this.settings.sms_provider || '',
        provider_config: this.settings.provider_config || {},
        monthly_sms_limit: this.settings.monthly_sms_limit || 1000,
        cost_per_sms: this.settings.cost_per_sms || 0.01,
        notification_types: this.settings.notification_types || [],
        require_user_opt_in: this.settings.require_user_opt_in !== false,
        default_country_code: this.settings.default_country_code || '+1'
      };
    },

    async toggleSms() {
      this.form.sms_enabled = !this.form.sms_enabled;
      await this.saveSettings();
    },

    async saveSettings() {
      this.isSubmitting = true;
      try {
        this.$emit('update', this.form);
      } catch (error) {
        console.error('Error saving SMS settings:', error);
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>
