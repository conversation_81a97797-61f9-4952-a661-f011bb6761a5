<template>
  <div class="sms-analytics">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-medium text-gray-900">{{ $t('sms_analytics') }}</h2>
        <p class="mt-1 text-sm text-gray-500">{{ $t('sms_usage_statistics_and_insights') }}</p>
      </div>
      <button
        @click="$emit('refresh')"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {{ $t('refresh') }}
      </button>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total SMS Sent -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.681L3 21l2.319-5.094A7.96 7.96 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">{{ $t('total_sms_sent') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_sent || 0 }}</p>
          </div>
        </div>
      </div>

      <!-- Delivery Rate -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">{{ $t('delivery_rate') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatPercentage(statistics.delivery_rate) }}</p>
          </div>
        </div>
      </div>

      <!-- Total Cost -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">{{ $t('total_cost') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(statistics.total_cost) }}</p>
          </div>
        </div>
      </div>

      <!-- Failed Messages -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">{{ $t('failed_messages') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.failed_count || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Monthly Usage Chart -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('monthly_usage_trend') }}</h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <p class="mt-2 text-sm text-gray-500">{{ $t('chart_placeholder') }}</p>
          </div>
        </div>
      </div>

      <!-- Message Types Distribution -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('message_types_distribution') }}</h3>
        <div class="space-y-4">
          <div 
            v-for="(count, type) in statistics.by_type" 
            :key="type"
            class="flex items-center justify-between"
          >
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full mr-3" :style="{ backgroundColor: getTypeColor(type) }"></div>
              <span class="text-sm font-medium text-gray-700">{{ getTypeLabel(type) }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-900">{{ count }}</span>
              <span class="text-xs text-gray-500">({{ formatPercentage(count / statistics.total_sent) }})</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Provider Performance -->
    <div class="bg-white rounded-lg shadow p-6 mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('provider_performance') }}</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('provider') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('messages_sent') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('delivery_rate') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('average_cost') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('total_cost') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(provider, name) in statistics.by_provider" :key="name">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ getProviderName(name) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ provider.sent || 0 }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span :class="getDeliveryRateClass(provider.delivery_rate)">
                  {{ formatPercentage(provider.delivery_rate) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatCurrency(provider.average_cost) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatCurrency(provider.total_cost) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('recent_activity') }}</h3>
      <div class="space-y-4">
        <div 
          v-for="activity in statistics.recent_activity" 
          :key="activity.id"
          class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex-shrink-0">
            <div :class="getActivityIconClass(activity.type)">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.681L3 21l2.319-5.094A7.96 7.96 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm font-medium text-gray-900">{{ activity.description }}</p>
            <p class="text-xs text-gray-500">{{ formatDateTime(activity.created_at) }}</p>
          </div>
          <div class="flex-shrink-0">
            <span :class="getActivityStatusClass(activity.status)">
              {{ getActivityStatusLabel(activity.status) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!statistics.recent_activity || statistics.recent_activity.length === 0" class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_recent_activity') }}</h3>
        <p class="mt-1 text-sm text-gray-500">{{ $t('no_recent_activity_description') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SmsAnalytics',
  mixins: [i18nMixin],
  props: {
    statistics: {
      type: Object,
      default: () => ({
        total_sent: 0,
        delivery_rate: 0,
        total_cost: 0,
        failed_count: 0,
        by_type: {},
        by_provider: {},
        recent_activity: []
      })
    }
  },
  methods: {
    formatPercentage(value) {
      if (!value && value !== 0) return '0%';
      return `${(value * 100).toFixed(1)}%`;
    },

    formatCurrency(amount) {
      if (!amount && amount !== 0) return '$0.00';
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
      }).format(amount);
    },

    formatDateTime(dateString) {
      return new Date(dateString).toLocaleString();
    },

    getTypeColor(type) {
      const colors = {
        'payment_reminder': '#3B82F6',
        'expense_created': '#10B981',
        'income_received': '#8B5CF6',
        'general_announcement': '#F59E0B',
        'payment_received': '#06B6D4',
        'overdue_payment': '#EF4444'
      };
      return colors[type] || '#6B7280';
    },

    getTypeLabel(type) {
      const labels = {
        'payment_reminder': this.$t('payment_reminder'),
        'expense_created': this.$t('expense_created'),
        'income_received': this.$t('income_received'),
        'general_announcement': this.$t('general_announcement'),
        'payment_received': this.$t('payment_received'),
        'overdue_payment': this.$t('overdue_payment')
      };
      return labels[type] || type;
    },

    getProviderName(provider) {
      const providers = {
        'twilio': 'Twilio',
        'aws_sns': 'AWS SNS'
      };
      return providers[provider] || provider;
    },

    getDeliveryRateClass(rate) {
      if (rate >= 0.95) return 'text-green-600 font-medium';
      if (rate >= 0.85) return 'text-yellow-600 font-medium';
      return 'text-red-600 font-medium';
    },

    getActivityIconClass(type) {
      const baseClass = 'w-8 h-8 rounded-lg flex items-center justify-center';
      const typeClasses = {
        'bulk_sms': 'bg-blue-100 text-blue-600',
        'template_created': 'bg-green-100 text-green-600',
        'settings_updated': 'bg-purple-100 text-purple-600'
      };
      return `${baseClass} ${typeClasses[type] || 'bg-gray-100 text-gray-600'}`;
    },

    getActivityStatusClass(status) {
      const classes = {
        'success': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800',
        'failed': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800',
        'pending': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800'
      };
      return classes[status] || classes['pending'];
    },

    getActivityStatusLabel(status) {
      const labels = {
        'success': this.$t('success'),
        'failed': this.$t('failed'),
        'pending': this.$t('pending')
      };
      return labels[status] || status;
    }
  }
};
</script>
