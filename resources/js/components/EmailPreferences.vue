<template>
  <div class="email-preferences-component">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900">{{ $t('email_preferences') }}</h3>
      <p class="mt-1 text-sm text-gray-600">{{ $t('email_preferences_description') }}</p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">{{ $t('loading') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">{{ $t('error') }}</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Preferences Form -->
    <form v-else @submit.prevent="savePreferences" class="space-y-6">
      <!-- Master Toggle -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-blue-900">{{ $t('email_notifications') }}</h4>
            <p class="text-sm text-blue-700">{{ $t('master_email_toggle_description') }}</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input
              v-model="preferences.email_notifications_enabled"
              type="checkbox"
              class="sr-only peer"
            />
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      <!-- Notification Types -->
      <div v-if="preferences.email_notifications_enabled" class="space-y-4">
        <h4 class="text-sm font-medium text-gray-900">{{ $t('notification_types') }}</h4>
        
        <div class="grid grid-cols-1 gap-4">
          <!-- Payment Reminders -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <label class="text-sm font-medium text-gray-900">{{ $t('payment_reminders') }}</label>
              <p class="text-xs text-gray-600">{{ $t('payment_reminders_description') }}</p>
            </div>
            <input
              v-model="preferences.email_payment_reminders"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
          </div>

          <!-- Expense Notifications -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <label class="text-sm font-medium text-gray-900">{{ $t('expense_notifications') }}</label>
              <p class="text-xs text-gray-600">{{ $t('expense_notifications_description') }}</p>
            </div>
            <input
              v-model="preferences.email_expense_notifications"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
          </div>

          <!-- Income Notifications -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <label class="text-sm font-medium text-gray-900">{{ $t('income_notifications') }}</label>
              <p class="text-xs text-gray-600">{{ $t('income_notifications_description') }}</p>
            </div>
            <input
              v-model="preferences.email_income_notifications"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
          </div>

          <!-- General Announcements -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <label class="text-sm font-medium text-gray-900">{{ $t('general_announcements') }}</label>
              <p class="text-xs text-gray-600">{{ $t('general_announcements_description') }}</p>
            </div>
            <input
              v-model="preferences.email_general_announcements"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
          </div>

          <!-- Overdue Notifications -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <label class="text-sm font-medium text-gray-900">{{ $t('overdue_notifications') }}</label>
              <p class="text-xs text-gray-600">{{ $t('overdue_notifications_description') }}</p>
            </div>
            <input
              v-model="preferences.email_overdue_notifications"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
          </div>
        </div>

        <!-- Email Frequency -->
        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-900 mb-2">{{ $t('email_frequency') }}</label>
          <select
            v-model="preferences.email_frequency"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          >
            <option value="immediate">{{ $t('immediate') }}</option>
            <option value="daily">{{ $t('daily') }}</option>
            <option value="weekly">{{ $t('weekly') }}</option>
          </select>
          <p class="mt-1 text-xs text-gray-600">{{ $t('email_frequency_description') }}</p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
        <button
          type="submit"
          :disabled="saving"
          class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="saving" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ saving ? $t('saving') : $t('save_preferences') }}
        </button>

        <button
          type="button"
          @click="testEmail"
          :disabled="testingEmail"
          class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="testingEmail" class="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ testingEmail ? $t('sending_test_email') : $t('send_test_email') }}
        </button>

        <button
          type="button"
          @click="resetToDefaults"
          class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {{ $t('reset_to_defaults') }}
        </button>
      </div>
    </form>

    <!-- Success Message -->
    <div v-if="successMessage" class="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">{{ successMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'EmailPreferences',
  mixins: [i18nMixin],
  data() {
    return {
      loading: true,
      saving: false,
      testingEmail: false,
      error: null,
      successMessage: null,
      preferences: {
        email_notifications_enabled: true,
        email_payment_reminders: true,
        email_expense_notifications: true,
        email_income_notifications: true,
        email_general_announcements: true,
        email_overdue_notifications: true,
        email_frequency: 'immediate'
      }
    };
  },
  mounted() {
    this.loadPreferences();
  },
  methods: {
    async loadPreferences() {
      this.loading = true;
      this.error = null;

      try {
        const response = await this.$axios.get('/preferences/email');
        this.preferences = { ...this.preferences, ...response.data.preferences };
      } catch (error) {
        console.error('Error loading email preferences:', error);
        this.error = error.response?.data?.message || this.$t('error_loading_preferences');
      } finally {
        this.loading = false;
      }
    },

    async savePreferences() {
      this.saving = true;
      this.error = null;
      this.successMessage = null;

      try {
        const response = await this.$axios.put('/preferences/email', this.preferences);
        this.successMessage = response.data.message || this.$t('preferences_saved_successfully');
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = null;
        }, 3000);
      } catch (error) {
        console.error('Error saving email preferences:', error);
        this.error = error.response?.data?.message || this.$t('error_saving_preferences');
      } finally {
        this.saving = false;
      }
    },

    async testEmail() {
      this.testingEmail = true;
      this.error = null;
      this.successMessage = null;

      try {
        const response = await this.$axios.post('/preferences/email/test');
        this.successMessage = response.data.message || this.$t('test_email_sent');
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          this.successMessage = null;
        }, 5000);
      } catch (error) {
        console.error('Error sending test email:', error);
        this.error = error.response?.data?.message || this.$t('error_sending_test_email');
      } finally {
        this.testingEmail = false;
      }
    },

    async resetToDefaults() {
      if (!confirm(this.$t('confirm_reset_preferences'))) {
        return;
      }

      this.saving = true;
      this.error = null;
      this.successMessage = null;

      try {
        const response = await this.$axios.post('/preferences/email/reset');
        this.preferences = { ...this.preferences, ...response.data.preferences };
        this.successMessage = response.data.message || this.$t('preferences_reset_successfully');
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = null;
        }, 3000);
      } catch (error) {
        console.error('Error resetting email preferences:', error);
        this.error = error.response?.data?.message || this.$t('error_resetting_preferences');
      } finally {
        this.saving = false;
      }
    }
  }
};
</script>

<style scoped>
.email-preferences-component {
  max-width: 42rem;
}
</style>
