<template>
  <div class="custom-reports">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">{{ $t('custom_reports') }}</h2>
        <p class="text-sm text-gray-600">{{ $t('manage_your_custom_reports') }}</p>
      </div>
      <button
        @click="$emit('create')"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('create_new_report') }}
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-64">
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('search_reports')"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <select
          v-model="selectedCategory"
          class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">{{ $t('all_categories') }}</option>
          <option value="financial">{{ $t('financial') }}</option>
          <option value="analytics">{{ $t('analytics') }}</option>
          <option value="operations">{{ $t('operations') }}</option>
        </select>
        <select
          v-model="selectedStatus"
          class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">{{ $t('all_statuses') }}</option>
          <option value="active">{{ $t('active') }}</option>
          <option value="draft">{{ $t('draft') }}</option>
          <option value="archived">{{ $t('archived') }}</option>
        </select>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="bg-white rounded-lg shadow p-6 animate-pulse">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
          <div class="h-8 bg-gray-200 rounded w-20"></div>
        </div>
        <div class="flex space-x-4">
          <div class="h-3 bg-gray-200 rounded w-16"></div>
          <div class="h-3 bg-gray-200 rounded w-20"></div>
          <div class="h-3 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    </div>

    <!-- Reports List -->
    <div v-else-if="filteredReports.length > 0" class="space-y-4">
      <div
        v-for="report in filteredReports"
        :key="report.id"
        class="bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200"
      >
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <h3 class="text-lg font-semibold text-gray-900 mr-3">{{ report.name }}</h3>
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getStatusColor(report.status)
                ]">
                  {{ $t(report.status) }}
                </span>
              </div>
              <p class="text-sm text-gray-600 mb-3">{{ report.description }}</p>
              <div class="flex items-center space-x-4 text-sm text-gray-500">
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  {{ $t(report.category) }}
                </span>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  {{ report.chart_type ? $t(report.chart_type + '_chart') : $t('no_chart') }}
                </span>
                <span class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ formatDate(report.updated_at) }}
                </span>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="runReport(report)"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded border border-blue-200 hover:bg-blue-50"
                :disabled="report.status === 'draft'"
              >
                {{ $t('run_report') }}
              </button>
              <div class="relative">
                <button
                  @click="toggleReportMenu(report.id)"
                  class="text-gray-400 hover:text-gray-600 p-1"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
                <div
                  v-if="activeReportMenu === report.id"
                  class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                >
                  <div class="py-1">
                    <button
                      @click="editReport(report)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      {{ $t('edit') }}
                    </button>
                    <button
                      @click="duplicateReport(report)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      {{ $t('duplicate') }}
                    </button>
                    <button
                      @click="shareReport(report)"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      {{ $t('share') }}
                    </button>
                    <hr class="my-1">
                    <button
                      @click="deleteReport(report)"
                      class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      {{ $t('delete') }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error States -->
    <div v-else-if="error" class="text-center py-12">
      <!-- Package Upgrade Required -->
      <div v-if="error === 'package_upgrade_required'" class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('package_upgrade_required') }}</h3>
        <p class="text-gray-600 mb-6">{{ $t('custom_reports_require_upgrade') }}</p>
        <div class="space-y-3">
          <button
            @click="showUpgradeModal = true"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
          >
            {{ $t('upgrade_package') }}
          </button>
          <button
            @click="loadReports"
            class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium"
          >
            {{ $t('try_again') }}
          </button>
        </div>
      </div>

      <!-- Authentication Required -->
      <div v-else-if="error === 'authentication_required'" class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('authentication_required') }}</h3>
        <p class="text-gray-600 mb-6">{{ $t('please_login_to_access_reports') }}</p>
        <button
          @click="$router.push('/login')"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
        >
          {{ $t('go_to_login') }}
        </button>
      </div>

      <!-- Advanced Reporting Not Available -->
      <div v-else-if="error === 'advanced_reporting_not_available'" class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('advanced_reporting_not_available') }}</h3>
        <p class="text-gray-600 mb-6">{{ $t('feature_not_available_for_your_package') }}</p>
        <button
          @click="loadReports"
          class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium"
        >
          {{ $t('try_again') }}
        </button>
      </div>

      <!-- General Error -->
      <div v-else class="max-w-md mx-auto">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('error_loading_reports') }}</h3>
        <p class="text-gray-600 mb-6">{{ $t('something_went_wrong_try_again') }}</p>
        <button
          @click="loadReports"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
        >
          {{ $t('try_again') }}
        </button>
      </div>
    </div>

    <!-- Empty State (No Reports) -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_custom_reports') }}</h3>
      <p class="mt-1 text-sm text-gray-500">{{ $t('get_started_by_creating_first_report') }}</p>
      <div class="mt-6">
        <button
          @click="$emit('create')"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
        >
          {{ $t('create_first_report') }}
        </button>
      </div>
    </div>

    <!-- Upgrade Modal -->
    <div v-if="showUpgradeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
            <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mt-4">{{ $t('upgrade_required') }}</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500 mb-4">
              {{ $t('custom_reports_require_standard_or_pro_package') }}
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 class="font-medium text-blue-900 mb-2">{{ $t('available_packages') }}</h4>
              <ul class="text-sm text-blue-800 space-y-1">
                <li>• {{ $t('standard_package_features') }}</li>
                <li>• {{ $t('pro_package_features') }}</li>
              </ul>
            </div>
          </div>
          <div class="items-center px-4 py-3">
            <button
              @click="showUpgradeModal = false"
              class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
            >
              {{ $t('close') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin';

export default {
  name: 'CustomReportsList',
  mixins: [i18nMixin],
  data() {
    return {
      loading: true,
      reports: [],
      searchQuery: '',
      selectedCategory: '',
      selectedStatus: '',
      activeReportMenu: null,
      error: null,
      showUpgradeModal: false
    };
  },
  computed: {
    filteredReports() {
      let filtered = this.reports;

      if (this.searchQuery) {
        filtered = filtered.filter(report =>
          report.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          report.description.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      }

      if (this.selectedCategory) {
        filtered = filtered.filter(report => report.category === this.selectedCategory);
      }

      if (this.selectedStatus) {
        filtered = filtered.filter(report => report.status === this.selectedStatus);
      }

      return filtered;
    }
  },
  async mounted() {
    await this.loadReports();
    // Close menu when clicking outside
    document.addEventListener('click', this.closeReportMenu);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeReportMenu);
  },
  methods: {
    async loadReports() {
      try {
        this.loading = true;
        this.error = null;

        // Check if user is authenticated
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('No authentication token found');
          this.error = 'authentication_required';
          this.reports = [];
          return;
        }

        console.log('Loading reports with token:', token.substring(0, 20) + '...');

        const response = await this.$http.get('/advanced-reporting/reports');
        console.log('Reports API response:', response);
        
        this.reports = response.data.data || response.data || [];

        console.log('Loaded reports:', this.reports.length);

        // Clear any previous errors
        this.error = null;
      } catch (error) {
        console.error('Error loading reports:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);
        
        // Handle different error types
        if (error.response?.status === 401) {
          this.error = 'authentication_required';
          this.$toast.error(this.$t('authentication_required'));
          // Redirect to login
          this.$router.push('/login');
        } else if (error.response?.status === 403) {
          this.error = 'package_upgrade_required';
          this.showUpgradeModal = true;
        } else if (error.response?.status === 404) {
          this.error = 'advanced_reporting_not_available';
          this.$toast.error(this.$t('advanced_reporting_not_available'));
        } else {
          this.error = 'error_loading_reports';
          this.$toast.error(this.$t('error_loading_reports'));
        }
        
        this.reports = [];
      } finally {
        this.loading = false;
      }
    },

    getStatusColor(status) {
      const colors = {
        active: 'bg-green-100 text-green-800',
        draft: 'bg-yellow-100 text-yellow-800',
        archived: 'bg-gray-100 text-gray-800'
      };
      return colors[status] || 'bg-gray-100 text-gray-800';
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },
    toggleReportMenu(reportId) {
      this.activeReportMenu = this.activeReportMenu === reportId ? null : reportId;
    },
    closeReportMenu() {
      this.activeReportMenu = null;
    },
    async runReport(report) {
      try {
        this.$toast.info(this.$t('generating_report', { name: report.name }));

        // Generate the report and get the export request
        const response = await this.$http.post(`/advanced-reporting/reports/${report.id}/generate`);
        const data = response.data;

        // Check if the response has the expected structure
        if (!data || typeof data !== 'object') {
          throw new Error('Invalid response format from server');
        }

        // Check if we have an export request ID
        if (data.export_request_id) {
          // Poll for the export completion
          await this.waitForExportCompletion(data.export_request_id);
        } else {
          // If no export request, try to download directly
          await this.downloadReport(report.id);
        }

        this.$toast.success(this.$t('report_generated_successfully'));
      } catch (error) {
        console.error('Error generating report:', error);
        
        // Handle different error response structures
        let errorMessage = this.$t('error_generating_report');
        
        if (error.response) {
          // Server responded with error status
          const responseData = error.response.data;
          if (responseData && responseData.message) {
            errorMessage = responseData.message;
          } else if (responseData && responseData.error) {
            errorMessage = responseData.error;
          } else if (typeof responseData === 'string') {
            errorMessage = responseData;
          }
        } else if (error.message) {
          // Network error or other error
          errorMessage = error.message;
        }
        
        this.$toast.error(errorMessage);
      }
    },
    async waitForExportCompletion(exportRequestId) {
      const maxAttempts = 30; // 30 seconds
      let attempts = 0;
      
      while (attempts < maxAttempts) {
        try {
          const response = await this.$http.get(`/api/exports/${exportRequestId}/status`);
          const status = response.data.status;
          
          if (status === 'completed') {
            // Download the completed file
            await this.downloadExport(exportRequestId);
            return;
          } else if (status === 'failed') {
            throw new Error('Export failed');
          }
          
          // Wait 1 second before next check
          await new Promise(resolve => setTimeout(resolve, 1000));
          attempts++;
        } catch (error) {
          console.error('Error checking export status:', error);
          throw error;
        }
      }
      
      throw new Error('Export timed out');
    },
    async downloadReport(reportId) {
      try {
        // Add date parameters for the last month
        const dateFrom = new Date();
        dateFrom.setMonth(dateFrom.getMonth() - 1);
        const dateTo = new Date();
        
        const params = {
          date_from: dateFrom.toISOString().split('T')[0],
          date_to: dateTo.toISOString().split('T')[0]
        };
        
        const response = await this.$http.get(`/advanced-reporting/reports/${reportId}/download`, {
          responseType: 'blob',
          params: params
        });
        
        // Check if response is actually a PDF
        const contentType = response.headers['content-type'];
        if (!contentType || !contentType.includes('application/pdf')) {
          throw new Error('Server did not return a PDF file');
        }
        
        // Create download link
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `report-${reportId}-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Error downloading report:', error);
        
        // Check if it's a JSON error response
        if (error.response && error.response.data) {
          try {
            const errorText = await error.response.data.text();
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.message || 'Failed to download report');
          } catch (parseError) {
            throw new Error('Failed to download report');
          }
        }
        
        throw new Error('Failed to download report');
      }
    },
    async downloadExport(exportRequestId) {
      try {
        const response = await this.$http.get(`/api/exports/${exportRequestId}/download`, {
          responseType: 'blob'
        });
        
        // Create download link
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // Use the filename from the Content-Disposition header or fallback to a default name
        let filename = `export-${exportRequestId}-${new Date().toISOString().split('T')[0]}.pdf`;
        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="([^"]+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }
        
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Error downloading export:', error);
        throw new Error('Failed to download export');
      }
    },
    editReport(report) {
      this.closeReportMenu();
      // TODO: Implement report editing
      this.$toast.info(this.$t('edit_feature_coming_soon'));
    },
    async duplicateReport(report) {
      this.closeReportMenu();
      try {
        // Clone the report with a new name
        const response = await this.$http.post('/advanced-reporting/reports', {
          template_id: report.template_id,
          name: `Copy of ${report.name}`,
          description: report.description,
          configuration: report.configuration,
          chart_config: report.chart_config,
          is_public: false
        });

        this.$toast.success(this.$t('report_duplicated_successfully'));
        await this.loadReports();
        this.$emit('refresh');
      } catch (error) {
        console.error('Error duplicating report:', error);
        this.$toast.error(this.$t('error_duplicating_report'));
      }
    },
    async shareReport(report) {
      this.closeReportMenu();
      try {
        // Toggle the public status of the report
        const newPublicStatus = !report.is_public;
        await this.$http.put(`/advanced-reporting/reports/${report.id}`, {
          is_public: newPublicStatus
        });

        const message = newPublicStatus
          ? this.$t('report_shared_successfully')
          : this.$t('report_unshared_successfully');
        this.$toast.success(message);
        await this.loadReports();
      } catch (error) {
        console.error('Error sharing report:', error);
        this.$toast.error(this.$t('error_sharing_report'));
      }
    },
    async deleteReport(report) {
      this.closeReportMenu();
      if (confirm(this.$t('confirm_delete_report', { name: report.name }))) {
        try {
          await this.$http.delete(`/advanced-reporting/reports/${report.id}`);
          this.$toast.success(this.$t('report_deleted_successfully'));
          await this.loadReports();
          this.$emit('refresh');
        } catch (error) {
          console.error('Error deleting report:', error);
          this.$toast.error(this.$t('error_deleting_report'));
        }
      }
    }
  }
};
</script>

<style scoped>
.custom-reports {
  /* Custom styles if needed */
}
</style>
