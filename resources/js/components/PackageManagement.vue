<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900">{{ $t('package_management') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('package_management_description') }}</p>
        </div>
        <button
          v-if="!isSuperAdmin && currentPackage"
          @click="showUpgradeModal = true"
          class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
          </svg>
          {{ $t('upgrade_package') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-1/4"></div>
        <div class="h-20 bg-gray-200 rounded"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>

    <!-- Super Admin Message -->
    <div v-else-if="isSuperAdmin" class="p-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">{{ $t('super_admin_unlimited_access') }}</h3>
            <p class="mt-1 text-sm text-blue-700">{{ $t('super_admin_no_package_restrictions') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Current Package Info -->
    <div v-else-if="currentPackage" class="p-6">
      <!-- Package Details -->
      <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 mb-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center">
              <h3 class="text-xl font-semibold text-gray-900">{{ currentPackage.name }}</h3>
              <span v-if="subscription && subscription.is_trial" class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {{ $t('trial') }}
              </span>
            </div>
            <p class="mt-1 text-sm text-gray-600">{{ currentPackage.description }}</p>
            <div class="mt-3 flex items-center space-x-4">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                ${{ currentPackage.price }}/{{ $t('month') }}
              </div>
              <div v-if="subscription" class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                </svg>
                {{ subscription.billing_cycle === 'annual' ? $t('annual_billing') : $t('monthly_billing') }}
              </div>
            </div>
          </div>
          <div class="text-right">
            <div v-if="subscription" class="text-sm text-gray-500">
              <div>{{ $t('expires_on') }}: {{ formatDate(subscription.ends_at) }}</div>
              <div class="mt-1 font-medium" :class="subscription.days_remaining < 7 ? 'text-red-600' : 'text-gray-900'">
                {{ subscription.days_remaining }} {{ $t('days_remaining') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Usage Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Neighbors Usage -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">{{ $t('neighbors_usage') }}</h4>
            <span class="text-sm text-gray-500">
              {{ usageStats.neighbors.current }}/{{ usageStats.neighbors.unlimited ? '∞' : usageStats.neighbors.limit }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="h-2 rounded-full transition-all duration-300"
              :class="usageStats.neighbors.percentage > 80 ? 'bg-red-500' : usageStats.neighbors.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
              :style="{ width: Math.min(usageStats.neighbors.percentage, 100) + '%' }"
            ></div>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            {{ usageStats.neighbors.unlimited ? $t('unlimited_neighbors') : usageStats.neighbors.percentage + '% ' + $t('used') }}
          </p>
        </div>

        <!-- Storage Usage -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-medium text-gray-900">{{ $t('storage_usage') }}</h4>
            <span class="text-sm text-gray-500">
              {{ storageUsage.formatted_size }}/{{ usageStats.storage.unlimited ? '∞' : usageStats.storage.limit_gb + 'GB' }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="h-2 rounded-full transition-all duration-300"
              :class="usageStats.storage.percentage > 80 ? 'bg-red-500' : usageStats.storage.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
              :style="{ width: Math.min(usageStats.storage.percentage, 100) + '%' }"
            ></div>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            {{ usageStats.storage.unlimited ? $t('unlimited_storage') : usageStats.storage.percentage + '% ' + $t('used') }}
          </p>
        </div>
      </div>

      <!-- Package Features -->
      <div class="border-t border-gray-200 pt-6">
        <h4 class="text-sm font-medium text-gray-900 mb-3">{{ $t('current_features') }}</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <div v-for="feature in currentPackage.features" :key="feature" class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            {{ $t('feature_' + feature) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="p-6">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">{{ $t('error_loading_package') }}</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Package Upgrade Modal -->
    <PackageUpgradeModal
      v-if="showUpgradeModal"
      :current-package="currentPackage"
      @close="showUpgradeModal = false"
      @package-changed="handlePackageChanged"
    />
  </div>
</template>

<script>
import PackageUpgradeModal from './PackageUpgradeModal.vue'

export default {
  name: 'PackageManagement',
  components: {
    PackageUpgradeModal
  },
  data() {
    return {
      loading: true,
      error: null,
      currentPackage: null,
      subscription: null,
      storageUsage: null,
      usageStats: null,
      isSuperAdmin: false,
      showUpgradeModal: false,
    }
  },
  async mounted() {
    await this.loadPackageInfo()
  },
  methods: {
    async loadPackageInfo() {
      try {
        this.loading = true
        this.error = null

        const response = await this.$axios.get('/admin/package/current')
        
        if (response.data.is_super_admin) {
          this.isSuperAdmin = true
        } else {
          this.currentPackage = response.data.current_package
          this.subscription = response.data.subscription
          this.storageUsage = response.data.storage_usage
          this.usageStats = response.data.usage_stats
        }
      } catch (error) {
        console.error('Error loading package info:', error)
        this.error = error.response?.data?.message || 'Failed to load package information'
      } finally {
        this.loading = false
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString()
    },
    
    handlePackageChanged() {
      this.showUpgradeModal = false
      this.loadPackageInfo()
    }
  }
}
</script>
