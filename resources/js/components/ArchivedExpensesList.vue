<template>
  <div class="archived-expenses-list">
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow mb-6 p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('search') }}</label>
          <input
            v-model="filters.search"
            type="text"
            :placeholder="$t('search_archived_expenses')"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @input="debouncedSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('archived_from') }}</label>
          <input
            v-model="filters.archived_from"
            type="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @change="loadExpenses"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('archived_to') }}</label>
          <input
            v-model="filters.archived_to"
            type="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @change="loadExpenses"
          />
        </div>
      </div>
    </div>

    <!-- Expenses Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">{{ $t('archived_expenses') }}</h3>
          <div class="flex space-x-2">
            <button
              v-if="selectedExpenses.length > 0"
              @click="unarchiveSelected"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              :disabled="processing"
            >
              {{ $t('unarchive_selected') }} ({{ selectedExpenses.length }})
            </button>
          </div>
        </div>
      </div>

      <div v-if="loading" class="p-8 text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ $t('loading') }}...
        </div>
      </div>

      <div v-else-if="expenses.data && expenses.data.length === 0" class="p-8 text-center text-gray-500">
        {{ $t('no_archived_expenses') }}
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  :checked="allSelected"
                  @change="toggleSelectAll"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('type') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('neighbor') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('amount') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('month_year') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('archived_date') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('archived_by') }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ $t('actions') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="expense in expenses.data" :key="expense.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <input
                  type="checkbox"
                  :value="expense.id"
                  v-model="selectedExpenses"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ expense.expense_type?.name || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ expense.user?.name || '-' }}
                <div class="text-xs text-gray-500">{{ expense.user?.apartment_number || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatCurrency(expense.amount) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ expense.month }}/{{ expense.year }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(expense.archived_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ expense.archived_by?.name || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="unarchiveExpense(expense.id)"
                  class="text-green-600 hover:text-green-900"
                  :disabled="processing"
                >
                  {{ $t('unarchive') }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="expenses.data && expenses.data.length > 0" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="previousPage"
              :disabled="!expenses.prev_page_url"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              {{ $t('previous') }}
            </button>
            <button
              @click="nextPage"
              :disabled="!expenses.next_page_url"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              {{ $t('next') }}
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                {{ $t('showing') }}
                <span class="font-medium">{{ expenses.from }}</span>
                {{ $t('to') }}
                <span class="font-medium">{{ expenses.to }}</span>
                {{ $t('of') }}
                <span class="font-medium">{{ expenses.total }}</span>
                {{ $t('results') }}
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  @click="previousPage"
                  :disabled="!expenses.prev_page_url"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  <span class="sr-only">{{ $t('previous') }}</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <button
                  @click="nextPage"
                  :disabled="!expenses.next_page_url"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  <span class="sr-only">{{ $t('next') }}</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import { debounce } from 'lodash';

export default {
  name: 'ArchivedExpensesList',
  mixins: [i18nMixin],
  data() {
    return {
      expenses: { data: [] },
      selectedExpenses: [],
      loading: true,
      processing: false,
      filters: {
        search: '',
        archived_from: '',
        archived_to: ''
      },
      currentPage: 1
    };
  },
  computed: {
    allSelected() {
      return this.expenses.data && this.expenses.data.length > 0 &&
             this.selectedExpenses.length === this.expenses.data.length;
    }
  },
  async mounted() {
    await this.loadExpenses();
  },
  methods: {
    debouncedSearch: debounce(function() {
      this.currentPage = 1;
      this.loadExpenses();
    }, 300),

    async loadExpenses() {
      try {
        this.loading = true;
        const params = new URLSearchParams({
          page: this.currentPage,
          per_page: 15,
          ...this.filters
        });

        // Remove empty filters
        Object.keys(this.filters).forEach(key => {
          if (!this.filters[key]) {
            params.delete(key);
          }
        });

        const response = await this.$http.get(`/api/archive/expenses?${params}`);
        this.expenses = response.data;
        this.selectedExpenses = [];
      } catch (error) {
        console.error('Error loading archived expenses:', error);
        this.$toast.error(this.$t('error_loading_archived_expenses'));
      } finally {
        this.loading = false;
      }
    },

    toggleSelectAll() {
      if (this.allSelected) {
        this.selectedExpenses = [];
      } else {
        this.selectedExpenses = this.expenses.data.map(expense => expense.id);
      }
    },

    async unarchiveExpense(expenseId) {
      await this.unarchiveSelected([expenseId]);
    },

    async unarchiveSelected(expenseIds = null) {
      const ids = expenseIds || this.selectedExpenses;
      if (ids.length === 0) return;

      try {
        this.processing = true;
        this.$emit('unarchive', ids);
        await this.loadExpenses();
      } catch (error) {
        console.error('Error unarchiving expenses:', error);
      } finally {
        this.processing = false;
      }
    },

    async previousPage() {
      if (this.expenses.prev_page_url) {
        this.currentPage--;
        await this.loadExpenses();
      }
    },

    async nextPage() {
      if (this.expenses.next_page_url) {
        this.currentPage++;
        await this.loadExpenses();
      }
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    },

    formatDate(dateString) {
      if (!dateString) return '-';
      return new Date(dateString).toLocaleDateString();
    }
  }
};
</script>

<style scoped>
.archived-expenses-list {
  /* Component-specific styles */
}
</style>
