<template>
  <div class="package-selection-component">
    <!-- Header -->
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ $t('choose_your_package') }}</h2>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        {{ $t('package_selection_description') }}
      </p>
    </div>

    <!-- Billing Cycle Toggle -->
    <div class="flex justify-center mb-8">
      <div class="bg-gray-100 p-1 rounded-lg">
        <button
          @click="billingCycle = 'monthly'"
          :class="[
            'px-4 py-2 rounded-md text-sm font-medium transition-colors',
            billingCycle === 'monthly' 
              ? 'bg-white text-gray-900 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          {{ $t('monthly') }}
        </button>
        <button
          @click="billingCycle = 'annual'"
          :class="[
            'px-4 py-2 rounded-md text-sm font-medium transition-colors',
            billingCycle === 'annual' 
              ? 'bg-white text-gray-900 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          {{ $t('annual') }}
          <span class="ml-1 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
            {{ $t('save_up_to') }} 20%
          </span>
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">{{ $t('loading_packages') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <h3 class="text-lg font-medium text-red-800 mb-2">{{ $t('error_loading_packages') }}</h3>
      <p class="text-red-600 mb-4">{{ error }}</p>
      <button
        @click="loadPackages"
        class="bg-red-100 text-red-800 px-4 py-2 rounded-md hover:bg-red-200 transition-colors"
      >
        {{ $t('retry') }}
      </button>
    </div>

    <!-- Package Cards -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <package-card
        v-for="pkg in packages"
        :key="pkg.id"
        :package="pkg"
        :billing-cycle="billingCycle"
        :is-selected="selectedPackage?.id === pkg.id"
        :is-current="currentSubscription?.package_id === pkg.id"
        @select="selectPackage"
      />
    </div>

    <!-- Selected Package Summary -->
    <div v-if="selectedPackage && selectedPackage.id !== currentSubscription?.package_id" 
         class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
      <h3 class="text-lg font-medium text-blue-900 mb-4">{{ $t('selected_package_summary') }}</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 class="font-medium text-blue-800">{{ getPackageName(selectedPackage) }}</h4>
          <p class="text-blue-600 text-sm">{{ getPackageDescription(selectedPackage) }}</p>
        </div>
        <div class="text-right">
          <div class="text-2xl font-bold text-blue-900">
            {{ selectedPackage.is_free ? $t('free') : formatPrice(getEffectivePrice(selectedPackage)) }}
          </div>
          <div v-if="!selectedPackage.is_free" class="text-sm text-blue-600">
            {{ billingCycle === 'annual' ? $t('per_year') : $t('per_month') }}
          </div>
          <div v-if="selectedPackage.trial_days > 0" class="text-sm text-green-600 mt-1">
            {{ $t('includes_free_trial', { days: selectedPackage.trial_days }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div v-if="selectedPackage && selectedPackage.id !== currentSubscription?.package_id" 
         class="flex flex-col sm:flex-row gap-4 justify-center">
      <button
        @click="confirmSelection"
        :disabled="subscribing"
        class="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg v-if="subscribing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ subscribing ? $t('processing') : getActionButtonText() }}
      </button>
      
      <button
        @click="selectedPackage = null"
        class="px-8 py-3 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors"
      >
        {{ $t('cancel') }}
      </button>
    </div>

    <!-- Current Subscription Info -->
    <div v-if="currentSubscription" class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('current_subscription') }}</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p class="text-sm text-gray-600">{{ $t('package') }}</p>
          <p class="font-medium">{{ getCurrentPackageName() }}</p>
        </div>
        <div>
          <p class="text-sm text-gray-600">{{ $t('status') }}</p>
          <span :class="getStatusClass(currentSubscription.status)">
            {{ $t(`subscription_status_${currentSubscription.status}`) }}
          </span>
        </div>
        <div>
          <p class="text-sm text-gray-600">{{ $t('expires_on') }}</p>
          <p class="font-medium">{{ formatDate(currentSubscription.ends_at) }}</p>
        </div>
      </div>
      
      <div v-if="currentSubscription.days_remaining <= 7" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <div class="flex">
          <svg class="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <div>
            <p class="text-sm font-medium text-yellow-800">
              {{ $t('subscription_expiring_soon') }}
            </p>
            <p class="text-sm text-yellow-700">
              {{ $t('days_remaining', { days: currentSubscription.days_remaining }) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex">
        <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <p class="text-sm font-medium text-green-800">{{ successMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import PackageCard from './PackageCard.vue';

export default {
  name: 'PackageSelection',
  mixins: [i18nMixin],
  components: {
    PackageCard
  },
  data() {
    return {
      packages: [],
      selectedPackage: null,
      currentSubscription: null,
      billingCycle: 'monthly',
      loading: true,
      subscribing: false,
      error: null,
      successMessage: null
    };
  },
  mounted() {
    this.loadPackages();
    this.loadCurrentSubscription();
  },
  methods: {
    async loadPackages() {
      this.loading = true;
      this.error = null;

      try {
        const response = await this.$axios.get('/packages');
        this.packages = response.data.packages;
      } catch (error) {
        console.error('Error loading packages:', error);
        this.error = error.response?.data?.message || this.$t('error_loading_packages');
      } finally {
        this.loading = false;
      }
    },

    async loadCurrentSubscription() {
      try {
        const response = await this.$axios.get('/subscription/current');
        this.currentSubscription = response.data.subscription;
      } catch (error) {
        console.error('Error loading current subscription:', error);
        // Don't show error for missing subscription
      }
    },

    selectPackage(pkg) {
      this.selectedPackage = pkg;
      this.successMessage = null;
    },

    async confirmSelection() {
      if (!this.selectedPackage) return;

      this.subscribing = true;
      this.error = null;

      try {
        const response = await this.$axios.post('/subscription/subscribe', {
          package_id: this.selectedPackage.id,
          billing_cycle: this.billingCycle
        });

        this.successMessage = response.data.message;
        this.selectedPackage = null;
        
        // Reload current subscription
        await this.loadCurrentSubscription();
        
        // Emit event for parent components
        this.$emit('subscription-updated', response.data.subscription);

      } catch (error) {
        console.error('Error subscribing to package:', error);
        this.error = error.response?.data?.message || this.$t('error_subscribing_package');
      } finally {
        this.subscribing = false;
      }
    },

    getPackageName(pkg) {
      if (this.$locale() === 'en' && pkg.name_en) {
        return pkg.name_en;
      }
      return pkg.name;
    },

    getPackageDescription(pkg) {
      if (this.$locale() === 'en' && pkg.description_en) {
        return pkg.description_en;
      }
      return pkg.description;
    },

    getEffectivePrice(pkg) {
      if (this.billingCycle === 'annual' && pkg.annual_price) {
        return pkg.annual_price;
      }
      return pkg.price;
    },

    formatPrice(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString(this.$locale() === 'en' ? 'en-US' : 'ar-SA');
    },

    getCurrentPackageName() {
      if (!this.currentSubscription) return '';
      const pkg = this.packages.find(p => p.id === this.currentSubscription.package_id);
      return pkg ? pkg.name : '';
    },

    getStatusClass(status) {
      const classes = {
        active: 'inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full',
        trial: 'inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full',
        expired: 'inline-flex px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full',
        cancelled: 'inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full'
      };
      return classes[status] || classes.active;
    },

    getActionButtonText() {
      if (!this.selectedPackage) return '';
      
      if (this.currentSubscription) {
        return this.$t('upgrade_package');
      }
      
      if (this.selectedPackage.is_free) {
        return this.$t('get_started');
      }
      
      return this.$t('start_trial');
    }
  }
};
</script>

<style scoped>
.package-selection-component {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .package-selection-component {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .package-selection-component {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
</style>
