<template>
  <div class="file-upload-component">
    <!-- Upload Area -->
    <div
      ref="uploadArea"
      @drop="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="[
        'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200',
        isDragging ? 'border-blue-500 bg-blue-50 scale-105' : 'border-gray-300 hover:border-gray-400',
        uploading ? 'pointer-events-none opacity-50' : ''
      ]"
    >
      <div class="space-y-4">
        <!-- Upload Icon -->
        <div :class="[
          'mx-auto w-12 h-12 transition-colors duration-200',
          isDragging ? 'text-blue-500' : 'text-gray-400'
        ]">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>

        <!-- Upload Text -->
        <div>
          <p :class="[
            'text-lg font-medium transition-colors duration-200',
            isDragging ? 'text-blue-700' : 'text-gray-900'
          ]">
            {{ isDragging ? $t('drop_files_now') : $t('drop_files_here') }}
          </p>
          <p class="text-sm text-gray-500">
            {{ $t('or') }}
            <button
              type="button"
              @click="$refs.fileInput.click()"
              :disabled="uploading"
              class="text-blue-600 hover:text-blue-800 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ $t('browse_files') }}
            </button>
          </p>
        </div>

        <!-- File Type Info -->
        <div class="text-xs text-gray-500">
          <p>{{ $t('supported_formats') }}: {{ allowedExtensions.join(', ').toUpperCase() }}</p>
          <p>{{ $t('max_file_size') }}: {{ maxFileSizeMB }}MB</p>
        </div>
      </div>

      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        multiple
        :accept="acceptedTypes"
        @change="handleFileSelect"
        class="hidden"
      />
    </div>

    <!-- File List -->
    <div v-if="files.length > 0" class="mt-6 space-y-3">
      <h4 class="text-sm font-medium text-gray-900">{{ $t('selected_files') }}</h4>
      
      <div class="space-y-2">
        <div
          v-for="(file, index) in files"
          :key="index"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-3 flex-1">
            <!-- File Icon -->
            <div class="flex-shrink-0">
              <div
                class="w-8 h-8 rounded flex items-center justify-center"
                :class="getFileIconClass(file.type)"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path :d="getFileIconPath(file.type)" />
                </svg>
              </div>
            </div>

            <!-- File Info -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ file.name }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatFileSize(file.size) }}
              </p>
            </div>

            <!-- Upload Progress -->
            <div v-if="file.uploading" class="flex-shrink-0">
              <div class="w-16 bg-gray-200 rounded-full h-2">
                <div
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: file.progress + '%' }"
                ></div>
              </div>
            </div>

            <!-- Upload Status -->
            <div v-else-if="file.uploaded" class="flex-shrink-0">
              <div class="w-6 h-6 text-green-500">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>

            <!-- Error Status -->
            <div v-else-if="file.error" class="flex-shrink-0">
              <div class="w-6 h-6 text-red-500">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Remove Button -->
          <button
            type="button"
            @click="removeFile(index)"
            class="ml-3 text-gray-400 hover:text-red-500 transition-colors"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Error Messages -->
      <div v-if="errors.length > 0" class="mt-4">
        <div class="bg-red-50 border border-red-200 rounded-lg p-3">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">{{ $t('upload_errors') }}</h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <li v-for="error in errors" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upload Actions -->
      <div class="flex items-center justify-between pt-4">
        <div class="text-sm text-gray-500">
          {{ files.length }} {{ $t('files_selected') }}
        </div>
        
        <div class="flex space-x-3">
          <button
            type="button"
            @click="clearFiles"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            {{ $t('clear_all') }}
          </button>

          <button
            type="button"
            @click="uploadFiles"
            :disabled="uploading || files.length === 0"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="uploading">{{ $t('uploading') }}...</span>
            <span v-else>{{ $t('upload_files') }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Description Input -->
    <div v-if="files.length > 0" class="mt-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        {{ $t('description') }} ({{ $t('optional') }})
      </label>
      <textarea
        v-model="description"
        rows="3"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        :placeholder="$t('file_description_placeholder')"
      ></textarea>
    </div>

    <!-- Public/Private Toggle -->
    <div v-if="files.length > 0" class="mt-4">
      <label class="flex items-center">
        <input
          v-model="isPublic"
          type="checkbox"
          class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
        />
        <span class="ml-2 text-sm text-gray-700">
          {{ $t('make_files_public') }}
        </span>
      </label>
      <p class="mt-1 text-xs text-gray-500">
        {{ $t('public_files_description') }}
      </p>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'FileUpload',
  mixins: [i18nMixin],
  props: {
    attachableType: {
      type: String,
      required: true,
      validator: value => ['App\\Models\\Expense', 'App\\Models\\Income', 'App\\Models\\BuildingExpense'].includes(value)
    },
    attachableId: {
      type: [Number, String],
      required: true
    },
    maxFiles: {
      type: Number,
      default: 5
    },
    autoUpload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      files: [],
      isDragging: false,
      uploading: false,
      errors: [],
      description: '',
      isPublic: false,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'csv', 'zip', 'rar'],
      maxFileSizeMB: 10,
      configLoaded: false,
    };
  },
  computed: {
    acceptedTypes() {
      return this.allowedExtensions.map(ext => `.${ext}`).join(',');
    },
  },
  methods: {
    async loadFileConfig() {
      try {
        const response = await this.$axios.get('/files/config');
        const config = response.data;

        this.maxFileSizeMB = config.max_file_size_mb;
        this.allowedExtensions = config.allowed_extensions;
        this.configLoaded = true;

        // Update maxFiles prop if it's using the default value
        if (this.maxFiles === 5) {
          this.$emit('update:maxFiles', config.max_files_per_record);
        }
      } catch (error) {
        console.error('Failed to load file configuration:', error);
        // Keep default values if loading fails
        this.configLoaded = true;
      }
    },

    handleDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      this.isDragging = false;
      const droppedFiles = Array.from(e.dataTransfer.files);
      this.processFiles(droppedFiles);
    },

    handleDragEnter(e) {
      e.preventDefault();
      e.stopPropagation();
      this.isDragging = true;
    },

    handleDragOver(e) {
      e.preventDefault();
      e.stopPropagation();
      this.isDragging = true;
    },

    handleDragLeave(e) {
      e.preventDefault();
      e.stopPropagation();
      // Only set isDragging to false if we're leaving the upload area entirely
      if (!this.$refs.uploadArea.contains(e.relatedTarget)) {
        this.isDragging = false;
      }
    },

    handleFileSelect(e) {
      const selectedFiles = Array.from(e.target.files);
      this.processFiles(selectedFiles);
      e.target.value = ''; // Reset input
    },

    processFiles(newFiles) {
      this.errors = [];

      if (!newFiles || newFiles.length === 0) {
        return;
      }

      // Check total file count
      if (this.files.length + newFiles.length > this.maxFiles) {
        this.errors.push(this.$t('max_files_exceeded', { max: this.maxFiles }));
        return;
      }

      const validFiles = [];

      newFiles.forEach(file => {
        // Check file size
        if (file.size > this.maxFileSizeMB * 1024 * 1024) {
          this.errors.push(this.$t('file_size_exceeded', {
            filename: file.name,
            maxSize: this.maxFileSizeMB
          }));
          return;
        }

        // Check file extension
        const extension = file.name.split('.').pop()?.toLowerCase();
        if (!extension || !this.allowedExtensions.includes(extension)) {
          this.errors.push(this.$t('file_type_not_allowed', {
            filename: file.name,
            allowedTypes: this.allowedExtensions.join(', ')
          }));
          return;
        }

        // Check for duplicates
        if (this.files.some(f => f.name === file.name && f.size === file.size)) {
          this.errors.push(this.$t('file_already_selected', { filename: file.name }));
          return;
        }

        validFiles.push({
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          uploading: false,
          uploaded: false,
          error: false,
          progress: 0,
          id: null // Will be set after upload
        });
      });

      this.files.push(...validFiles);

      if (this.autoUpload && validFiles.length > 0) {
        this.uploadFiles();
      }

      // Emit event for parent components
      this.$emit('files-added', validFiles);
    },

    async uploadFiles() {
      if (this.uploading) return;

      this.uploading = true;
      this.errors = [];

      const uploadPromises = this.files
        .filter(fileObj => !fileObj.uploaded && !fileObj.uploading)
        .map(fileObj => this.uploadSingleFile(fileObj));

      try {
        await Promise.all(uploadPromises);

        // Get the actual uploaded file data from responses, not the local file objects
        const uploadedFiles = this.files
          .filter(f => f.uploaded && f.response)
          .map(f => f.response.file || f.response)
          .filter(file => file && typeof file === 'object');

        this.$emit('upload-complete', uploadedFiles);

        if (!this.autoUpload) {
          // Clear files after manual upload
          this.clearFiles();
        }
      } catch (error) {
        console.error('Upload error:', error);
      } finally {
        this.uploading = false;
      }
    },

    async uploadSingleFile(fileObj) {
      fileObj.uploading = true;
      fileObj.progress = 0;

      const formData = new FormData();
      formData.append('file', fileObj.file);
      formData.append('attachable_type', this.attachableType);
      formData.append('attachable_id', this.attachableId);
      formData.append('description', this.description);
      formData.append('is_public', this.isPublic ? '1' : '0');

      try {
        const response = await this.$axios.post('/files/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            fileObj.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          },
        });

        fileObj.uploaded = true;
        fileObj.uploading = false;
        fileObj.response = response.data;

        // Ensure we're emitting the actual file data, not a Promise
        const uploadedFile = response.data.file || response.data;

        console.log('Raw server response:', response.data);
        console.log('Extracted file data:', uploadedFile);
        console.log('File original_name type:', typeof uploadedFile.original_name);
        console.log('File original_name value:', uploadedFile.original_name);
        console.log('Is original_name a Promise?', this.isPromise(uploadedFile.original_name));
        console.log('Is name a Promise?', this.isPromise(uploadedFile.name));

        // Validate that uploadedFile is a proper object with required properties
        if (uploadedFile && typeof uploadedFile === 'object' &&
            (uploadedFile.original_name || uploadedFile.name)) {

          // Create a clean copy to avoid any reference issues
          const cleanFile = {
            id: uploadedFile.id,
            original_name: String(uploadedFile.original_name || uploadedFile.name || 'Unknown'),
            filename: String(uploadedFile.filename || ''),
            file_size: uploadedFile.file_size || 0,
            file_extension: String(uploadedFile.file_extension || ''),
            mime_type: String(uploadedFile.mime_type || ''),
            description: String(uploadedFile.description || ''),
            is_public: Boolean(uploadedFile.is_public),
            created_at: uploadedFile.created_at,
            updated_at: uploadedFile.updated_at,
            url: String(uploadedFile.url || '')
          };

          console.log('Clean file object:', cleanFile);
          this.$emit('file-uploaded', cleanFile);
        } else {
          console.error('Invalid file response structure:', uploadedFile);
          throw new Error('Invalid file response from server');
        }
        
      } catch (error) {
        fileObj.error = true;
        fileObj.uploading = false;
        
        const errorMessage = error.response?.data?.message || 'Upload failed';
        this.errors.push(`${fileObj.name}: ${errorMessage}`);
        
        this.$emit('upload-error', { file: fileObj, error: errorMessage });
      }
    },

    removeFile(index) {
      this.files.splice(index, 1);
    },

    clearFiles() {
      this.files = [];
      this.errors = [];
      this.description = '';
      this.isPublic = false;
    },

    formatFileSize(bytes) {
      const units = ['B', 'KB', 'MB', 'GB'];
      let size = bytes;
      let unitIndex = 0;
      
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      
      return `${size.toFixed(1)} ${units[unitIndex]}`;
    },

    getFileIconClass(mimeType) {
      if (mimeType.startsWith('image/')) {
        return 'bg-green-100 text-green-600';
      } else if (mimeType === 'application/pdf') {
        return 'bg-red-100 text-red-600';
      } else if (mimeType.includes('word') || mimeType.includes('document')) {
        return 'bg-blue-100 text-blue-600';
      } else if (mimeType.includes('sheet') || mimeType.includes('excel')) {
        return 'bg-green-100 text-green-600';
      }
      return 'bg-gray-100 text-gray-600';
    },

    getFileIconPath(mimeType) {
      if (mimeType.startsWith('image/')) {
        return 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z';
      } else if (mimeType === 'application/pdf') {
        return 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
      }
      return 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
    },
  },

  mounted() {
    // Load file configuration
    this.loadFileConfig();

    // Prevent default drag behaviors on document level
    this.preventDefaults = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      document.addEventListener(eventName, this.preventDefaults, false);
    });
  },

  beforeUnmount() {
    // Clean up event listeners
    if (this.preventDefaults) {
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.removeEventListener(eventName, this.preventDefaults, false);
      });
    }
  },
};
</script>

<style scoped>
.file-upload-component {
  width: 100%;
}
</style>
