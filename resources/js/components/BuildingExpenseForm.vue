<template>
  <div>
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label for="building_expense_type" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('building_expense_type') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="building_expense_type" v-model="formData.building_expense_type_id" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_building_expense_type') }}</option>
              <option v-for="type in buildingExpenseTypes" :key="type.id" :value="type.id">
                {{ type.name }}
              </option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('amount') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              v-model="formData.amount"
              :class="[
                'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors', 'pr-3 pl-3'
              ]"
              :placeholder="$t('enter_amount')"
              required
            />
          </div>
        </div>
      </div>

      <!-- Date Section -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div>
          <label for="month" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('month') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="month" v-model="formData.month" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_month') }}</option>
              <option value="01">{{ $t('january') }}</option>
              <option value="02">{{ $t('february') }}</option>
              <option value="03">{{ $t('march') }}</option>
              <option value="04">{{ $t('april') }}</option>
              <option value="05">{{ $t('may') }}</option>
              <option value="06">{{ $t('june') }}</option>
              <option value="07">{{ $t('july') }}</option>
              <option value="08">{{ $t('august') }}</option>
              <option value="09">{{ $t('september') }}</option>
              <option value="10">{{ $t('october') }}</option>
              <option value="11">{{ $t('november') }}</option>
              <option value="12">{{ $t('december') }}</option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        <div>
          <label for="year" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('year') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="year" v-model="formData.year" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_year') }}</option>
              <option v-for="year in yearOptions" :key="year" :value="year">
                {{ year }}
              </option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div>
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
          {{ $t('notes') }}
        </label>
        <textarea
          id="notes"
          v-model="formData.notes"
          rows="4"
          class="w-full py-2.5 px-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
          :placeholder="$t('enter_notes')"
        ></textarea>
      </div>
      
      <!-- File Upload Section -->
      <div v-if="showFileUpload">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ $t('attachments') }}
        </label>
        <file-manager
          :attachable-type="'App\\Models\\BuildingExpense'"
          :attachable-id="isEdit ? buildingExpense.id : null"
          :auto-load="true"
          :can-manage="true"
          :show-upload="true"
          @files-updated="handleFilesUpdated"
          @upload-pending="pendingFileUploads = $event"
        />
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
        <button
          type="submit"
          :disabled="processing || pendingFileUploads"
          class="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <svg v-if="processing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ processing ? $t('processing') : (isEdit ? $t('update_building_expense') : $t('create_building_expense')) }}
        </button>
        
        <button
          type="button"
          @click="$emit('cancel')"
          class="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
        >
          {{ $t('cancel') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import FileManager from './FileManager.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'BuildingExpenseForm',
  mixins: [i18nMixin],
  components: {
    FileManager
  },
  props: {
    buildingExpense: {
      type: Object,
      default: () => ({
        building_expense_type_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        notes: ''
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      buildingExpenseTypes: [],
      uploadedFiles: [],
      pendingFileUploads: false,
      formData: {
        building_expense_type_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        notes: ''
      }
    };
  },
  computed: {
    showFileUpload() {
      // Show file upload for existing building expenses or when creating new ones
      return this.isEdit || !this.isEdit;
    },
    yearOptions() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let year = currentYear - 2; year <= currentYear + 5; year++) {
        years.push(year);
      }
      return years;
    }
  },
  watch: {
    buildingExpense: {
      handler(newBuildingExpense) {
        if (newBuildingExpense && this.isEdit) {
          this.formData = {
            building_expense_type_id: newBuildingExpense.building_expense_type_id || '',
            month: newBuildingExpense.month || '',
            year: newBuildingExpense.year || new Date().getFullYear(),
            amount: newBuildingExpense.amount || '',
            notes: newBuildingExpense.notes || ''
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.fetchBuildingExpenseTypes();
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.buildingExpense) {
      this.formData = {
        building_expense_type_id: this.buildingExpense.building_expense_type_id || '',
        month: this.buildingExpense.month || '',
        year: this.buildingExpense.year || new Date().getFullYear(),
        amount: this.buildingExpense.amount || '',
        notes: this.buildingExpense.notes || ''
      };
    }
  },
  methods: {
    async fetchBuildingExpenseTypes() {
      try {
        const response = await this.$axios.get('/building-expense-types');
        this.buildingExpenseTypes = response.data;
      } catch (error) {
        console.error('Error fetching building expense types:', error);
      }
    },
    async handleSubmit() {
      this.processing = true;
      try {
        const url = this.isEdit
          ? `/building-expenses/${this.buildingExpense.id}`
          : '/building-expenses';
        const method = this.isEdit ? 'put' : 'post';

        const response = await this.$axios[method](url, this.formData);

        this.$emit('success', response.data);
      } catch (error) {
        console.error('Error saving building expense:', error);
        const message = error.response?.data?.message || 'An error occurred while saving the building expense.';
        this.$emit('error', message);
      } finally {
        this.processing = false;
      }
    },
    handleFilesUpdated(files) {
      this.uploadedFiles = files;
    }
  }
};
</script>
