<template>
  <div>
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label for="expense_type" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('expense_type') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="expense_type" v-model="formData.expense_type_id" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_expense_type') }}</option>
              <option v-for="type in expenseTypes" :key="type.id" :value="type.id">
                {{ type.name }}
              </option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        <div>
          <label for="user" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('neighbor') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="user" v-model="formData.user_id" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_neighbor') }}</option>
              <option v-for="user in neighbors" :key="user.id" :value="user.id">
                {{ user.name }} ({{ $t('apartment') }}: {{ user.apartment_number }})
              </option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Date and Amount Section -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label for="month" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('month') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select id="month" v-model="formData.month" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]" required>
              <option value="">{{ $t('select_month') }}</option>
              <option value="01">{{ $t('january') }}</option>
              <option value="02">{{ $t('february') }}</option>
              <option value="03">{{ $t('march') }}</option>
              <option value="04">{{ $t('april') }}</option>
              <option value="05">{{ $t('may') }}</option>
              <option value="06">{{ $t('june') }}</option>
              <option value="07">{{ $t('july') }}</option>
              <option value="08">{{ $t('august') }}</option>
              <option value="09">{{ $t('september') }}</option>
              <option value="10">{{ $t('october') }}</option>
              <option value="11">{{ $t('november') }}</option>
              <option value="12">{{ $t('december') }}</option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        <div>
          <label for="year" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('year') }}
            <span class="text-red-500">*</span>
          </label>
          <input type="number" id="year" v-model="formData.year"
            class="w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            min="2023" max="2030" :placeholder="new Date().getFullYear().toString()" required />
        </div>

        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('amount') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <input type="number" id="amount" v-model="formData.amount" :class="[
              'w-full py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
              $isRTL() ? 'pr-8 pl-3' : 'pl-8 pr-3'
            ]" placeholder="0.00" step="0.01" min="0" required />
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div>
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
          {{ $t('notes') }}
          <span class="text-gray-400 text-xs">({{ $t('optional') }})</span>
        </label>
        <textarea id="notes" v-model="formData.notes" rows="4"
          class="w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"
          :placeholder="$t('notes_placeholder')"></textarea>
      </div>

      <!-- File Management Section -->
      <div v-if="showFileUpload" class="border-t border-gray-200 pt-6">
        <div class="mb-4">
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('attachments') }}</h3>
          <p class="text-sm text-gray-500">{{ $t('expense_attachments_description') }}</p>
        </div>
        <file-manager :attachable-type="'App\\Models\\Expense'" :attachable-id="expense?.id || 'temp'"
          :can-manage="true" :show-upload="true" :auto-load="isEdit" @upload-complete="handleFileUploadComplete"
          @file-uploaded="handleFileUploaded" @upload-error="handleFileUploadError" @file-deleted="handleFileDeleted"
          @show-message="handleShowMessage" />
      </div>

      <!-- Action Buttons -->
      <div
        class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
        <button type="button" @click="$emit('cancel')"
          class="inline-flex items-center justify-center px-4 py-2.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          {{ $t('cancel') }}
        </button>
        <button type="submit" :disabled="processing"
          class="inline-flex items-center justify-center px-4 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg v-if="processing" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ processing ? $t('saving') : (isEdit ? $t('update_expense') : $t('create_expense')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import FileUpload from './FileUpload.vue';
import FileManager from './FileManager.vue';

export default {
  name: 'ExpenseForm',
  mixins: [i18nMixin],
  components: {
    FileManager
  },
  props: {
    expense: {
      type: Object,
      default: () => ({
        expense_type_id: '',
        user_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        notes: '',
        is_automatic: false
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      expenseTypes: [],
      neighbors: [],
      uploadedFiles: [],
      pendingFileUploads: false,
      formData: {
        expense_type_id: '',
        user_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        notes: '',
        is_automatic: false
      }
    };
  },
  computed: {
    showFileUpload() {
      // Show file upload for existing expenses or when creating new ones
      return this.isEdit || !this.isEdit;
    }
  },
  watch: {
    expense: {
      handler(newExpense) {
        if (newExpense && this.isEdit) {
          this.formData = {
            expense_type_id: newExpense.expense_type_id || '',
            user_id: newExpense.user_id || '',
            month: newExpense.month || '',
            year: newExpense.year || new Date().getFullYear(),
            amount: newExpense.amount || '',
            notes: newExpense.notes || '',
            is_automatic: newExpense.is_automatic || false
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.fetchExpenseTypes();
    this.fetchNeighbors();
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.expense) {
      this.formData = {
        expense_type_id: this.expense.expense_type_id || '',
        user_id: this.expense.user_id || '',
        month: this.expense.month || '',
        year: this.expense.year || new Date().getFullYear(),
        amount: this.expense.amount || '',
        notes: this.expense.notes || '',
        is_automatic: this.expense.is_automatic || false
      };
    }
  },
  methods: {
    async fetchExpenseTypes() {
      try {
        const response = await this.$axios.get('/expense-types');
        this.expenseTypes = response.data;
      } catch (error) {
        console.error('Error fetching expense types:', error);
      }
    },
    async fetchNeighbors() {
      try {
        const response = await this.$axios.get('/admin/users');
        const allUsers = response.data.data || response.data;
        // Filter neighbors - they will already be filtered by building on the backend
        this.neighbors = allUsers.filter(user => user.role === 'neighbor');
      } catch (error) {
        console.error('Error fetching neighbors:', error);
      }
    },
    async handleSubmit() {
      this.processing = true;
      try {
        // Ensure month is 2-digit string and year is integer
        const submitData = {
          ...this.formData,
          month: this.formData.month.toString().padStart(2, '0'),
          year: parseInt(this.formData.year)
        };

        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/expenses/${this.expense.id}` : '/expenses',
          submitData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save expense');
      } finally {
        this.processing = false;
      }
    },

    handleFileUploadComplete(files) {
      this.uploadedFiles = Array.isArray(files) ? files : [files];
      this.pendingFileUploads = false;
      console.log('All files uploaded:', this.uploadedFiles);
    },

    handleFileUploaded(file) {
      console.log('File uploaded:', file);
      // Ensure file is properly structured
      if (file && typeof file === 'object') {
        this.uploadedFiles.push(file);
      }
    },

    handleFileUploadError(error) {
      console.error('File upload error:', error);

      // Handle different error formats
      let errorMessage = 'File upload failed';

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        if (error.error) {
          errorMessage = error.error;
        } else if (error.message) {
          errorMessage = error.message;
        } else if (error.file && error.file.name) {
          errorMessage = `Failed to upload ${error.file.name}`;
        } else {
          errorMessage = JSON.stringify(error);
        }
      }

      this.$emit('error', errorMessage);
    },

    handleFileDeleted({ file, index }) {
      console.log('File deleted:', file);
      // Remove from local uploaded files array if it exists
      if (this.uploadedFiles && Array.isArray(this.uploadedFiles)) {
        const fileIndex = this.uploadedFiles.findIndex(f =>
          (f.id && f.id === file.id) ||
          (f.name && f.name === file.name)
        );
        if (fileIndex !== -1) {
          this.uploadedFiles.splice(fileIndex, 1);
        }
      }
    },

    handleShowMessage({ type, message }) {
      // Ensure message is a string
      const messageText = typeof message === 'string' ? message :
        (message && message.toString ? message.toString() : 'Unknown message');

      // Don't emit success/error events for file upload messages
      // These should only be emitted for actual form submissions
      // File upload messages should be handled by the FileManager component itself
      console.log('File upload message:', type, messageText);

      // You could show a toast notification here instead of emitting events
      // For now, we'll just log the message to avoid triggering form submission events
    }
  }
};
</script>