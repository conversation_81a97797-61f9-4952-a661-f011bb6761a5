<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleOutsideClick">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          {{ isEdit ? $t('edit_template') : $t('create_template') }}
        </h3>
        <button @click="handleCancel" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <form @submit.prevent="saveTemplate" class="py-6">
        <div class="space-y-4">
                  <!-- Template Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('template_name') }} <span class="text-red-500">*</span>
                    </label>
                    <input 
                      type="text" 
                      v-model="form.name"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('template_name_placeholder')"
                    >
                  </div>

                  <!-- Expense Type -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('expense_type') }} <span class="text-red-500">*</span>
                    </label>
                    <select v-model="form.expense_type_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="">{{ $t('select_expense_type') }}</option>
                      <option v-for="type in expenseTypes" :key="type.id" :value="type.id">
                        {{ type.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Amount and Currency -->
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('amount') }} <span class="text-red-500">*</span>
                      </label>
                      <input 
                        type="number" 
                        step="0.01"
                        v-model="form.amount"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('currency') }}
                      </label>
                      <select v-model="form.currency" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="USD">USD</option>
                        <option value="JOD">JOD</option>
                        <option value="ILS">ILS</option>
                      </select>
                    </div>
                  </div>

                  <!-- Frequency -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('frequency') }} <span class="text-red-500">*</span>
                    </label>
                    <select v-model="form.frequency" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="monthly">{{ $t('monthly') }}</option>
                      <option value="quarterly">{{ $t('quarterly') }}</option>
                      <option value="yearly">{{ $t('yearly') }}</option>
                    </select>
                  </div>

                  <!-- Auto Generate -->
                  <div>
                    <label class="flex items-center">
                      <input 
                        type="checkbox" 
                        v-model="form.auto_generate"
                        class="mr-2"
                      >
                      <span class="text-sm">{{ $t('auto_generate_expenses') }}</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">{{ $t('auto_generate_description') }}</p>
                  </div>

                  <!-- Due Days After -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('due_days_after') }}
                    </label>
                    <input 
                      type="number" 
                      v-model="form.due_days_after"
                      min="1"
                      max="365"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <p class="text-xs text-gray-500 mt-1">{{ $t('due_days_after_description') }}</p>
                  </div>

                  <!-- Description -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('description') }}
                    </label>
                    <textarea 
                      v-model="form.description"
                      rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('template_description_placeholder')"
                    ></textarea>
                  </div>

                  <!-- Active Status -->
                  <div>
                    <label class="flex items-center">
                      <input 
                        type="checkbox" 
                        v-model="form.is_active"
                        class="mr-2"
                      >
                      <span class="text-sm">{{ $t('template_active') }}</span>
                    </label>
                  </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            @click="handleCancel"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {{ $t('cancel') }}
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? $t('saving') : (isEdit ? $t('update_template') : $t('create_template')) }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'ExpenseTemplateModal',
  mixins: [i18nMixin, smartModalMixin],
  props: {
    template: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isSubmitting: false,
      expenseTypes: [],
      form: {
        name: '',
        expense_type_id: '',
        amount: '',
        currency: 'USD',
        frequency: 'monthly',
        auto_generate: true,
        due_days_after: 30,
        description: '',
        is_active: true
      }
    };
  },
  computed: {
    isEdit() {
      return !!this.template;
    }
  },
  async mounted() {
    await this.loadExpenseTypes();
    if (this.template) {
      this.populateForm();
    }
    // Initialize form tracking after form is populated
    this.$nextTick(() => {
      this.initializeFormTracking();
    });
  },
  methods: {
    async loadExpenseTypes() {
      try {
        const response = await this.$axios.get('/expense-types');
        this.expenseTypes = response.data;
      } catch (error) {
        console.error('Error loading expense types:', error);
      }
    },

    populateForm() {
      this.form = {
        name: this.template.name || '',
        expense_type_id: this.template.expense_type_id || '',
        amount: this.template.amount || '',
        currency: this.template.currency || 'USD',
        frequency: this.template.frequency || 'monthly',
        auto_generate: this.template.auto_generate || false,
        due_days_after: this.template.due_days_after || 30,
        description: this.template.description || '',
        is_active: this.template.is_active !== undefined ? this.template.is_active : true
      };
    },

    async saveTemplate() {
      this.isSubmitting = true;
      try {
        const url = this.isEdit ? `/expense-templates/${this.template.id}` : '/expense-templates';
        const method = this.isEdit ? 'put' : 'post';
        
        const response = await this.$axios[method](url, this.form);
        
        this.$emit('success', response.data);
        this.$toast.success(this.$t(this.isEdit ? 'template_updated_successfully' : 'template_created_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error saving template:', error);
        const message = error.response?.data?.message || this.$t('save_failed');
        this.$toast.error(message);
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>
