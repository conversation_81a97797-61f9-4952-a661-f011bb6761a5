<template>
  <div class="sms-templates-list">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-medium text-gray-900">{{ $t('sms_templates') }}</h2>
        <p class="mt-1 text-sm text-gray-500">{{ $t('manage_sms_templates_description') }}</p>
      </div>
      <button
        @click="$emit('create')"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('create_sms_template') }}
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('notification_type') }}</label>
          <select 
            v-model="filters.type"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('all_types') }}</option>
            <option value="payment_reminder">{{ $t('payment_reminder') }}</option>
            <option value="expense_created">{{ $t('expense_created') }}</option>
            <option value="income_received">{{ $t('income_received') }}</option>
            <option value="general_announcement">{{ $t('general_announcement') }}</option>
            <option value="payment_received">{{ $t('payment_received') }}</option>
            <option value="overdue_payment">{{ $t('overdue_payment') }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('status') }}</label>
          <select 
            v-model="filters.status"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('all_statuses') }}</option>
            <option value="active">{{ $t('active') }}</option>
            <option value="inactive">{{ $t('inactive') }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('template_type') }}</label>
          <select 
            v-model="filters.templateType"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{{ $t('all_template_types') }}</option>
            <option value="system">{{ $t('system_templates') }}</option>
            <option value="custom">{{ $t('custom_templates') }}</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Templates Grid -->
    <div v-if="filteredTemplates.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div 
        v-for="template in filteredTemplates" 
        :key="template.id"
        class="bg-white shadow rounded-lg p-6 hover:shadow-md transition-shadow"
      >
        <!-- Template Header -->
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center space-x-2">
              <h3 class="text-lg font-medium text-gray-900">{{ template.name }}</h3>
              <span 
                v-if="template.is_system_template"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {{ $t('system') }}
              </span>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ getNotificationTypeLabel(template.notification_type) }}</p>
          </div>
          <div class="flex items-center space-x-2">
            <!-- Status Toggle -->
            <button
              @click="toggleTemplate(template)"
              :class="[
                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                template.is_active ? 'bg-green-600' : 'bg-gray-200'
              ]"
            >
              <span
                :class="[
                  'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                  template.is_active ? 'translate-x-5' : 'translate-x-0'
                ]"
              />
            </button>
            <!-- Actions Menu -->
            <div class="relative">
              <button
                @click="toggleMenu(template.id)"
                class="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>
              <div 
                v-if="activeMenu === template.id"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
              >
                <div class="py-1">
                  <button
                    @click="editTemplate(template)"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {{ $t('edit') }}
                  </button>
                  <button
                    @click="previewTemplate(template)"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {{ $t('preview') }}
                  </button>
                  <button
                    @click="duplicateTemplate(template)"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    {{ $t('duplicate') }}
                  </button>
                  <button
                    v-if="!template.is_system_template"
                    @click="deleteTemplate(template)"
                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    {{ $t('delete') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Template Content Preview -->
        <div class="mb-4">
          <div class="bg-gray-50 rounded-lg p-3">
            <p class="text-sm text-gray-700 font-mono">{{ truncateTemplate(template.template) }}</p>
          </div>
        </div>

        <!-- Template Info -->
        <div class="flex justify-between items-center text-sm text-gray-500">
          <div class="flex items-center space-x-4">
            <span>{{ template.character_count }} {{ $t('characters') }}</span>
            <span>{{ Math.ceil(template.character_count / 160) }} {{ $t('sms_segments') }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span 
              :class="[
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              ]"
            >
              {{ template.is_active ? $t('active') : $t('inactive') }}
            </span>
          </div>
        </div>

        <!-- Variables Info -->
        <div v-if="template.variables && template.variables.length > 0" class="mt-3">
          <div class="text-xs text-gray-500 mb-1">{{ $t('variables') }}:</div>
          <div class="flex flex-wrap gap-1">
            <span 
              v-for="variable in template.variables" 
              :key="variable"
              class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800"
            >
              {{ '{' + variable + '}' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.681L3 21l2.319-5.094A7.96 7.96 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_sms_templates') }}</h3>
      <p class="mt-1 text-sm text-gray-500">{{ $t('no_sms_templates_description') }}</p>
      <div class="mt-6">
        <button
          @click="$emit('create')"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('create_first_sms_template') }}
        </button>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <SmsTemplatePreviewModal
      v-if="showPreviewModal"
      :template="selectedTemplate"
      @close="showPreviewModal = false"
    />
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SmsTemplatesList',
  mixins: [i18nMixin],
  props: {
    templates: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeMenu: null,
      showPreviewModal: false,
      selectedTemplate: null,
      filters: {
        type: '',
        status: '',
        templateType: ''
      }
    };
  },
  computed: {
    filteredTemplates() {
      let filtered = [...this.templates];

      if (this.filters.type) {
        filtered = filtered.filter(template => template.notification_type === this.filters.type);
      }

      if (this.filters.status) {
        const isActive = this.filters.status === 'active';
        filtered = filtered.filter(template => template.is_active === isActive);
      }

      if (this.filters.templateType) {
        const isSystem = this.filters.templateType === 'system';
        filtered = filtered.filter(template => template.is_system_template === isSystem);
      }

      return filtered;
    }
  },
  mounted() {
    // Close menu when clicking outside
    document.addEventListener('click', this.closeMenu);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeMenu);
  },
  methods: {
    toggleMenu(templateId) {
      this.activeMenu = this.activeMenu === templateId ? null : templateId;
    },

    closeMenu() {
      this.activeMenu = null;
    },

    applyFilters() {
      // Filters are applied automatically via computed property
    },

    getNotificationTypeLabel(type) {
      const labels = {
        'payment_reminder': this.$t('payment_reminder'),
        'expense_created': this.$t('expense_created'),
        'income_received': this.$t('income_received'),
        'general_announcement': this.$t('general_announcement'),
        'payment_received': this.$t('payment_received'),
        'overdue_payment': this.$t('overdue_payment')
      };
      return labels[type] || type;
    },

    truncateTemplate(template) {
      if (template.length <= 100) return template;
      return template.substring(0, 100) + '...';
    },

    async toggleTemplate(template) {
      try {
        await this.$axios.put(`/api/sms/templates/${template.id}`, {
          is_active: !template.is_active
        });
        template.is_active = !template.is_active;
        this.$toast.success(this.$t('template_updated_successfully'));
      } catch (error) {
        console.error('Error toggling template:', error);
        this.$toast.error(this.$t('update_failed'));
      }
    },

    editTemplate(template) {
      this.closeMenu();
      this.$emit('edit', template);
    },

    previewTemplate(template) {
      this.closeMenu();
      this.selectedTemplate = template;
      this.showPreviewModal = true;
    },

    async duplicateTemplate(template) {
      this.closeMenu();
      try {
        const response = await this.$axios.post(`/api/sms/templates/${template.id}/duplicate`);
        this.$emit('refresh');
        this.$toast.success(this.$t('template_duplicated_successfully'));
      } catch (error) {
        console.error('Error duplicating template:', error);
        this.$toast.error(this.$t('duplicate_failed'));
      }
    },

    async deleteTemplate(template) {
      this.closeMenu();
      if (confirm(this.$t('confirm_delete_sms_template'))) {
        try {
          await this.$axios.delete(`/api/sms/templates/${template.id}`);
          this.$emit('refresh');
          this.$toast.success(this.$t('template_deleted_successfully'));
        } catch (error) {
          console.error('Error deleting template:', error);
          this.$toast.error(this.$t('delete_failed'));
        }
      }
    }
  }
};
</script>
