<template>
  <div class="file-preview-component">
    <!-- File List Header -->
    <div v-if="files.length > 0" class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-medium text-gray-900">
        {{ $t('attachments') }} ({{ files.length }})
      </h4>
      <button
        type="button"
        v-if="canManage"
        @click="$emit('add-files')"
        class="text-sm text-blue-600 hover:text-blue-800 font-medium"
      >
        {{ $t('add_more_files') }}
      </button>
    </div>

    <!-- Empty State -->
    <div v-if="files.length === 0" class="text-center py-6 text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <p class="mt-2 text-sm">{{ $t('no_attachments') }}</p>
    </div>

    <!-- File Grid -->
    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="(file, index) in files"
        :key="file.id || index"
        class="relative group bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
      >
        <!-- File Preview -->
        <div class="aspect-w-16 aspect-h-9 bg-gray-50">
          <!-- Image Preview -->
          <div v-if="isImage(file)" class="w-full h-32 flex items-center justify-center">
            <img
              :src="getFileUrl(file)"
              :alt="file.original_name || file.name"
              class="max-w-full max-h-full object-contain"
              @error="handleImageError"
            />
          </div>
          
          <!-- PDF Preview -->
          <div v-else-if="isPdf(file)" class="w-full h-32 flex items-center justify-center bg-red-50">
            <div class="text-center">
              <svg class="mx-auto h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 18h12V6l-4-4H4v16zm8-14v4h4l-4-4z"/>
              </svg>
              <p class="mt-1 text-xs text-red-600 font-medium">PDF</p>
            </div>
          </div>
          
          <!-- Document Preview -->
          <div v-else-if="isDocument(file)" class="w-full h-32 flex items-center justify-center bg-blue-50">
            <div class="text-center">
              <svg class="mx-auto h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 18h12V6l-4-4H4v16zm8-14v4h4l-4-4z"/>
              </svg>
              <p class="mt-1 text-xs text-blue-600 font-medium">{{ getFileTypeLabel(file) }}</p>
            </div>
          </div>
          
          <!-- Spreadsheet Preview -->
          <div v-else-if="isSpreadsheet(file)" class="w-full h-32 flex items-center justify-center bg-green-50">
            <div class="text-center">
              <svg class="mx-auto h-8 w-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              <p class="mt-1 text-xs text-green-600 font-medium">{{ getFileTypeLabel(file) }}</p>
            </div>
          </div>
          
          <!-- Other File Types -->
          <div v-else class="w-full h-32 flex items-center justify-center bg-gray-50">
            <div class="text-center">
              <svg class="mx-auto h-8 w-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 18h12V6l-4-4H4v16zm8-14v4h4l-4-4z"/>
              </svg>
              <p class="mt-1 text-xs text-gray-600 font-medium">{{ getFileTypeLabel(file) }}</p>
            </div>
          </div>

          <!-- Overlay Actions -->
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div class="flex space-x-2">
              <!-- Download Button -->
              <button
                type="button"
                @click="downloadFile(file)"
                class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                :title="$t('download')"
              >
                <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>

              <!-- View Button (for images and PDFs) -->
              <button
                type="button"
                v-if="canPreview(file)"
                @click="previewFile(file)"
                class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                :title="$t('preview')"
              >
                <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
              
              <!-- Delete Button -->
              <button
                type="button"
                v-if="canManage"
                @click="deleteFile(file, index)"
                class="p-2 bg-white rounded-full shadow-lg hover:bg-red-50 transition-colors"
                :title="$t('delete')"
              >
                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- File Info -->
        <div class="p-3">
          <h5 class="text-sm font-medium text-gray-900 truncate" :title="getFileName(file)">
            {{ getFileName(file) }}
          </h5>
          <div class="mt-1 flex items-center justify-between text-xs text-gray-500">
            <span>{{ formatFileSize(file.file_size || file.size) }}</span>
            <span v-if="file.created_at">{{ formatDate(file.created_at) }}</span>
          </div>
          <p v-if="file.description" class="mt-1 text-xs text-gray-600 truncate" :title="file.description">
            {{ file.description }}
          </p>
        </div>

        <!-- Public/Private Badge -->
        <div v-if="file.is_public !== undefined" class="absolute top-2 right-2">
          <span
            :class="[
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              file.is_public ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            ]"
          >
            {{ file.is_public ? $t('public') : $t('private') }}
          </span>
        </div>
      </div>
    </div>

    <!-- File Preview Modal -->
    <div
      v-if="previewModal.show"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click="closePreview"
    >
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              {{ getFileName(previewModal.file) }}
            </h3>
            <button
              type="button"
              @click="closePreview"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Preview Content -->
          <div class="max-h-96 overflow-auto">
            <img
              v-if="previewModal.file && isImage(previewModal.file)"
              :src="getFileUrl(previewModal.file)"
              :alt="previewModal.file.original_name || previewModal.file.name"
              class="max-w-full h-auto mx-auto"
            />
            <iframe
              v-else-if="previewModal.file && isPdf(previewModal.file)"
              :src="getFileUrl(previewModal.file)"
              class="w-full h-96 border-0"
            ></iframe>
          </div>

          <!-- Modal Actions -->
          <div class="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              @click="downloadFile(previewModal.file)"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {{ $t('download') }}
            </button>
            <button
              type="button"
              @click="closePreview"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              {{ $t('close') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'FilePreview',
  mixins: [i18nMixin],
  props: {
    files: {
      type: Array,
      default: () => []
    },
    canManage: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      previewModal: {
        show: false,
        file: null
      },
      resolvedFileNames: {}, // Cache for resolved file names
      loadingFileNames: new Set() // Track which files are being resolved
    };
  },

  methods: {
    isImage(file) {
      const mimeType = file.mime_type || file.type || '';
      const extension = file.file_extension || file.name?.split('.').pop()?.toLowerCase() || '';
      return mimeType.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
    },

    isPdf(file) {
      const mimeType = file.mime_type || file.type || '';
      const extension = file.file_extension || file.name?.split('.').pop()?.toLowerCase() || '';
      return mimeType === 'application/pdf' || extension === 'pdf';
    },

    isDocument(file) {
      const mimeType = file.mime_type || file.type || '';
      const extension = file.file_extension || file.name?.split('.').pop()?.toLowerCase() || '';
      return mimeType.includes('document') || mimeType.includes('word') || 
             ['doc', 'docx', 'txt', 'rtf'].includes(extension);
    },

    isSpreadsheet(file) {
      const mimeType = file.mime_type || file.type || '';
      const extension = file.file_extension || file.name?.split('.').pop()?.toLowerCase() || '';
      return mimeType.includes('sheet') || mimeType.includes('excel') || 
             ['xls', 'xlsx', 'csv'].includes(extension);
    },

    canPreview(file) {
      return this.isImage(file) || this.isPdf(file);
    },

    getFileUrl(file) {
      if (file.url) {
        return file.url;
      }
      if (file.id) {
        return `/api/files/${file.id}/download`;
      }
      // For newly uploaded files that might have a blob URL
      if (file.file && typeof file.file === 'object') {
        return URL.createObjectURL(file.file);
      }
      return '';
    },

    async resolveFileName(file) {
      const fileId = file.id || file.filename || Math.random().toString();

      // Check if we already have a resolved name
      if (this.resolvedFileNames[fileId]) {
        return this.resolvedFileNames[fileId];
      }

      // Check if we're already resolving this file
      if (this.loadingFileNames.has(fileId)) {
        return 'Loading...';
      }

      const fileName = file.original_name || file.name;

      // If it's already a string, cache and return it
      if (typeof fileName === 'string') {
        this.resolvedFileNames[fileId] = fileName;
        return fileName;
      }

      // If it's a Promise, resolve it
      if (fileName && typeof fileName === 'object' && fileName.then) {
        this.loadingFileNames.add(fileId);
        try {
          const resolvedName = await fileName;
          this.resolvedFileNames[fileId] = String(resolvedName);
          this.loadingFileNames.delete(fileId);
          this.$forceUpdate(); // Force re-render with resolved name
          return String(resolvedName);
        } catch (error) {
          console.error('Error resolving file name promise:', error);
          this.resolvedFileNames[fileId] = 'Error loading name';
          this.loadingFileNames.delete(fileId);
          return 'Error loading name';
        }
      }

      // If it's an object, try to stringify it
      if (fileName && typeof fileName === 'object') {
        const stringified = JSON.stringify(fileName);
        this.resolvedFileNames[fileId] = stringified;
        return stringified;
      }

      // Fallback
      this.resolvedFileNames[fileId] = 'Unknown file';
      return 'Unknown file';
    },

    getFileName(file) {
      // Try original_name first, then name, then filename
      const fileName = file.original_name || file.name || file.filename;

      // If it's a string, return it
      if (typeof fileName === 'string' && fileName.length > 0) {
        return fileName;
      }

      // If it's a Promise, return loading message
      if (fileName && typeof fileName === 'object' && typeof fileName.then === 'function') {
        return 'Loading...';
      }

      // If it's any other object, try to stringify it
      if (fileName && typeof fileName === 'object') {
        try {
          return JSON.stringify(fileName);
        } catch (e) {
          return '[Complex Object]';
        }
      }

      // Fallback
      return `File ${file.id || 'Unknown'}`;
    },

    getFileTypeLabel(file) {
      const extension = file.file_extension || file.name?.split('.').pop()?.toLowerCase() || '';
      return extension.toUpperCase();
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const units = ['B', 'KB', 'MB', 'GB'];
      let size = bytes;
      let unitIndex = 0;
      
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      
      return `${size.toFixed(1)} ${units[unitIndex]}`;
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    previewFile(file) {
      this.previewModal.file = file;
      this.previewModal.show = true;
    },

    closePreview() {
      this.previewModal.show = false;
      this.previewModal.file = null;
    },

    downloadFile(file) {
      const url = this.getFileUrl(file);
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = file.original_name || file.name || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },

    deleteFile(file, index) {
      if (confirm(this.$t('confirm_delete_file'))) {
        this.$emit('delete-file', { file, index });
      }
    },

    handleImageError(event) {
      // Hide broken image and show file icon instead
      event.target.style.display = 'none';
    }
  }
};
</script>

<style scoped>
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.aspect-h-9 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
