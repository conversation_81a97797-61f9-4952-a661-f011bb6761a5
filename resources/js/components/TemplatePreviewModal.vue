<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
      <!-- Modal Header -->
      <div class="flex justify-between items-center pb-4 border-b border-gray-200">
        <div class="flex items-center">
          <div :class="[
            'w-10 h-10 rounded-lg flex items-center justify-center mr-3',
            template?.color || 'bg-blue-100'
          ]">
            <component :is="template?.icon" class="w-6 h-6" :class="template?.iconColor || 'text-blue-600'" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ $t(template?.name || '') }}</h3>
            <p class="text-sm text-gray-600">{{ $t('template_preview') }}</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="py-6">
        <!-- Template Description -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-2">{{ $t('description') }}</h4>
          <p class="text-gray-600">{{ $t(template?.description || '') }}</p>
        </div>

        <!-- Template Features -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">{{ $t('included_features') }}</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div v-for="feature in template?.features || []" :key="feature" class="flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="text-gray-700">{{ $t(feature) }}</span>
            </div>
          </div>
        </div>

        <!-- Chart Types -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">{{ $t('available_chart_types') }}</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="chart in template?.config?.charts || []"
              :key="chart"
              class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
            >
              {{ $t(chart + '_chart') }}
            </span>
          </div>
        </div>

        <!-- Data Fields -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">{{ $t('data_fields') }}</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div
              v-for="field in template?.config?.fields || []"
              :key="field"
              class="flex items-center p-2 bg-gray-50 rounded-lg"
            >
              <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span class="text-sm text-gray-700">{{ $t(field) }}</span>
            </div>
          </div>
        </div>

        <!-- Available Filters -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">{{ $t('available_filters') }}</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="filter in template?.config?.filters || []"
              :key="filter"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
            >
              {{ $t(filter) }}
            </span>
          </div>
        </div>

        <!-- Sample Preview -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">{{ $t('sample_preview') }}</h4>
          <div class="bg-gray-50 rounded-lg p-4 border-2 border-dashed border-gray-300">
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">{{ $t('sample_chart_will_appear_here') }}</p>
              <p class="text-xs text-gray-400">{{ $t('preview_available_after_data_selection') }}</p>
            </div>
          </div>
        </div>

        <!-- Estimated Time -->
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-sm font-medium text-blue-900">
              {{ $t('estimated_creation_time') }}: {{ template?.estimatedTime || '2-3 min' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          @click="$emit('use-template', template)"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {{ $t('use_this_template') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin';

// Template Icons (same as in ReportTemplatesList)
const ChartBarIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>`
};

const CurrencyDollarIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>`
};

const UsersIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" /></svg>`
};

const CogIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`
};

export default {
  name: 'TemplatePreviewModal',
  mixins: [i18nMixin],
  components: {
    ChartBarIcon,
    CurrencyDollarIcon,
    UsersIcon,
    CogIcon
  },
  props: {
    template: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'use-template']
};
</script>

<style scoped>
/* Modal styles */
</style>
