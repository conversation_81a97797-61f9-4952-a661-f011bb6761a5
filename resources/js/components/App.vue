<template>
    <div class="min-h-screen" :class="isGuestPage ? 'bg-gray-50' : 'bg-gray-100'">
        <!-- Authenticated User Navigation -->
        <nav v-if="isAuthenticated" :key="currentLocale" class="w-full bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Left - App Name -->
                    <div class="flex-shrink-0">
                        <router-link :to="dashboardRoute"
                            class="flex items-center text-xl font-bold text-blue-900 hover:text-blue-700 transition-colors">
                            <img src="/images/logo.png" alt="Logo" class="h-8 w-auto" />
                        </router-link>
                    </div>

                    <!-- Center - Primary Navigation (Desktop) -->
                    <div class="hidden lg:flex flex-1 justify-center">
                        <div class="flex items-center space-x-1" :class="$isRTL() ? 'space-x-reverse' : ''">
                            <!-- Primary Menu Items -->
                            <router-link v-if="isAdmin" to="/admin"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('dashboard') }}
                            </router-link>
                            <router-link v-if="isAdmin && user?.role === 'admin'" to="/admin/expenses"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('expenses') }}
                            </router-link>
                            <router-link v-if="isAdmin && user?.role === 'admin'" to="/admin/building-expenses"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('building_expenses') }}
                            </router-link>

                            <router-link v-if="isAdmin && user?.role === 'admin'" to="/admin/incomes"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('incomes') }}
                            </router-link>
                            <router-link v-if="isAdmin" to="/admin/users"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('users') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/admin/buildings"
                                            class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('buildings') }}
                                        </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/subscriptions"
                                            class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('subscriptions') }}
                                        </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/packages"
                                            class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('packages') }}
                                        </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/package-change-requests"
                                            class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('package_change_requests') }}
                                        </router-link>

                            <!-- Super Admin More Menu -->
                            <div v-if="user?.role === 'super_admin'" class="relative" ref="superAdminMoreMenu">
                                <button @click="showSuperAdminMoreMenu = !showSuperAdminMoreMenu"
                                    class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all flex items-center"
                                    :class="{ 'text-blue-600 bg-blue-50': showSuperAdminMoreMenu }">
                                    {{ $t('more') }}
                                    <svg class="ml-1 h-4 w-4" :class="{ 'rotate-180': showSuperAdminMoreMenu }" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- Super Admin Dropdown Menu -->
                                <div v-show="showSuperAdminMoreMenu"
                                    class="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                    <div class="py-1">
                                        <router-link to="/super-admin/expense-types"
                                            @click="showSuperAdminMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                                            active-class="bg-blue-50 text-blue-600 font-medium">
                                            {{ $t('expense_types') }}
                                        </router-link>
                                        <router-link to="/super-admin/building-expense-types"
                                            @click="showSuperAdminMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                                            active-class="bg-blue-50 text-blue-600 font-medium">
                                            {{ $t('building_expense_types') }}
                                        </router-link>
                                        <router-link to="/admin/notification-templates"
                                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors">
                                            {{ $t('notification_templates') }}
                                        </router-link>
                                        <router-link to="/admin/sms-management"
                                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors">
                                            {{ $t('sms_management') }}
                                        </router-link>
                                        <router-link to="/admin/payment-gateway-settings"
                                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors">
                                            {{ $t('payment_gateway_settings') }}
                                        </router-link>
                                    </div>
                                </div>
                            </div>

                            <!-- More Menu for Additional Items -->
                            <div v-if="user?.role === 'admin'" class="relative" ref="moreMenu">
                                <button @click="showMoreMenu = !showMoreMenu"
                                    class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all flex items-center"
                                    :class="{ 'text-blue-600 bg-blue-50': showMoreMenu }">
                                    {{ $t('more') }}
                                    <svg class="ml-1 h-4 w-4" :class="{ 'rotate-180': showMoreMenu }" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div v-if="showMoreMenu"
                                    class="absolute top-full mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
                                    :class="$isRTL() ? 'right-0' : 'left-0'">
                                    <div class="py-1">
                                        <router-link v-if="user?.role !== 'super_admin'" to="/admin/my-building"
                                            @click="showMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('my_building') }}
                                        </router-link>

                                        <!-- Advanced Reports -->
                                        <router-link v-if="hasAdvancedReporting" to="/admin/advanced-reporting"
                                            @click="showMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('advance_report') }}
                                        </router-link>

                                        <!-- Export Reports -->
                                        <router-link v-if="hasExportReports" to="/admin/export-reports"
                                            @click="showMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('export_report') }}
                                        </router-link>

                                        <!-- Notification Templates (package-gated) -->
                                        <router-link v-if="hasNotificationsFeature" to="/admin/notification-templates"
                                            @click="showMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('notification_templates') }}
                                        </router-link>

                                        <!-- SMS Management (package-gated) -->
                                        <router-link v-if="hasSmsFeature" to="/admin/sms-management"
                                            @click="showMoreMenu = false"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            active-class="text-blue-600 bg-blue-50 font-semibold">
                                            {{ $t('sms_management') }}
                                        </router-link>

                                        <!-- Add future menu items here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Neighbor Navigation -->
                            <router-link v-if="isNeighbor" to="/neighbor"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('dashboard') }}
                            </router-link>
                            <router-link v-if="isNeighbor" to="/neighbor/profile"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('my_profile') }}
                            </router-link>
                        </div>
                    </div>

                    <!-- Right - User Actions -->
                    <div class="flex items-center space-x-2" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <!-- Notifications -->
                        <notification-center />

                        <!-- Language Toggle -->
                        <language-toggle />

                        <!-- User Menu -->
                        <div class="relative" ref="userMenu">
                            <button @click="showUserMenu = !showUserMenu"
                                class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                :class="$isRTL() ? 'space-x-reverse' : ''">
                                <div
                                    class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                    {{ user?.name?.charAt(0)?.toUpperCase() }}
                                </div>
                                <span class="hidden sm:block max-w-24 truncate">{{ user?.name }}</span>
                                <svg class="h-4 w-4" :class="{ 'rotate-180': showUserMenu }" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            <!-- User Dropdown -->
                            <div v-if="showUserMenu"
                                class="absolute top-full mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
                                :class="$isRTL() ? 'left-0' : 'right-0'">
                                <div class="py-1">
                                    <div class="px-4 py-2 text-sm text-gray-500 border-b border-gray-100">
                                        <div class="font-medium text-gray-900">{{ user?.name }}</div>
                                        <div class="text-xs">{{ user?.email }}</div>
                                    </div>
                                    <router-link v-if="isNeighbor" to="/neighbor/profile" @click="showUserMenu = false"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        {{ $t('my_profile') }}
                                    </router-link>
                                    <button @click="logout"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                        </svg>
                                        {{ $t('logout') }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Menu Button -->
                        <button @click="mobileMenuOpen = !mobileMenuOpen"
                            class="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div v-if="mobileMenuOpen" class="lg:hidden border-t border-gray-200">
                    <div class="px-2 pt-2 pb-3 space-y-1">
                        <!-- Admin Mobile Menu -->
                        <template v-if="isAdmin">
                            <router-link to="/admin" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('dashboard') }}
                            </router-link>
                            <router-link v-if="user?.role === 'admin'" to="/admin/expenses" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('expenses') }}
                            </router-link>
                            <router-link v-if="user?.role === 'admin'" to="/admin/building-expenses" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('building_expenses') }}
                            </router-link>
                            <router-link v-if="user?.role === 'admin'" to="/admin/incomes" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('incomes') }}
                            </router-link>
                            <router-link to="/admin/users" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('users') }}
                            </router-link>
                            <router-link to="/admin/admins" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('admins') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/admin/buildings"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('buildings') }}
                            </router-link>

                            <!-- Management Links (Super Admin only) -->
                            <div v-if="user?.role === 'super_admin'" class="border-t border-gray-200 pt-2 mt-2">
                                <p class="px-3 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">{{ $t('management') }}</p>
                                <router-link to="/admin/exports" @click="mobileMenuOpen = false"
                                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                    active-class="text-blue-600 bg-blue-50 font-semibold">
                                    {{ $t('export_management') }}
                                </router-link>
                                <router-link to="/admin/expense-templates" @click="mobileMenuOpen = false"
                                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                    active-class="text-blue-600 bg-blue-50 font-semibold">
                                    {{ $t('expense_templates') }}
                                </router-link>
                                <router-link to="/admin/notification-templates" @click="mobileMenuOpen = false"
                                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                    active-class="text-blue-600 bg-blue-50 font-semibold">
                                    {{ $t('notification_templates') }}
                                </router-link>
                                <router-link to="/admin/payment-gateway-settings" @click="mobileMenuOpen = false"
                                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                    active-class="text-blue-600 bg-blue-50 font-semibold">
                                    {{ $t('payment_gateway_settings') }}
                                </router-link>
                            </div>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/subscriptions"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('subscriptions') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/packages"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('packages') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/package-change-requests"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('package_change_requests') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/expense-types"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('expense_types') }}
                            </router-link>
                            <router-link v-if="user?.role === 'super_admin'" to="/super-admin/building-expense-types"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('building_expense_types') }}
                            </router-link>
                            <router-link v-if="user?.role !== 'super_admin'" to="/admin/my-building"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('my_building') }}
                            </router-link>

                            <!-- Advanced Reports -->
                            <router-link v-if="hasAdvancedReporting" to="/admin/advanced-reporting"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('advance_report') }}
                            </router-link>

                            <!-- Export Reports -->
                            <router-link v-if="hasExportReports" to="/admin/export-reports"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('export_report') }}
                            </router-link>

                            <!-- Notification Templates (package-gated) -->
                            <router-link v-if="hasNotificationsFeature" to="/admin/notification-templates"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('notification_templates') }}
                            </router-link>

                            <!-- SMS Management (package-gated) -->
                            <router-link v-if="hasSmsFeature" to="/admin/sms-management"
                                @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('sms_management') }}
                            </router-link>
                        </template>

                        <!-- Neighbor Mobile Menu -->
                        <template v-if="isNeighbor">
                            <router-link to="/neighbor" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('dashboard') }}
                            </router-link>
                            <router-link to="/neighbor/profile" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all"
                                active-class="text-blue-600 bg-blue-50 font-semibold">
                                {{ $t('my_profile') }}
                            </router-link>
                        </template>
                    </div>

                    <!-- Mobile User Section -->
                    <div class="pt-4 pb-3 border-t border-gray-200">
                        <div class="flex items-center px-5">
                            <div
                                class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                                {{ user?.name?.charAt(0)?.toUpperCase() }}
                            </div>
                            <div class="ml-3">
                                <div class="text-base font-medium text-gray-800">{{ user?.name }}</div>
                                <div class="text-sm text-gray-500">{{ user?.email }}</div>
                            </div>
                        </div>
                        <div class="mt-3 px-2 space-y-1">
                            <router-link v-if="isNeighbor" to="/neighbor/profile" @click="mobileMenuOpen = false"
                                class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                                {{ $t('my_profile') }}
                            </router-link>
                            <button @click="logout"
                                class="w-full text-left px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-800 hover:bg-red-50 flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                {{ $t('logout') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Guest Navigation for all guest pages -->
        <nav v-if="!isAuthenticated && isGuestPage" :key="currentLocale" class="w-full bg-white shadow-lg sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16 lg:h-20">
                    <!-- Left - App Name/Logo -->
                    <router-link to="/" class="flex items-center space-x-2 group">
                        <img src="images/logo.png" alt="Logo" class="h-8 lg:h-10 w-auto transition-transform group-hover:scale-105" />
                        <span class="hidden sm:block text-xl lg:text-2xl font-bold text-blue-900 group-hover:text-blue-700 transition-colors">
                            {{ $t('app_name') }}
                        </span>
                    </router-link>

                    <!-- Center - Navigation Menu (Desktop) -->
                    <div class="hidden lg:flex items-center space-x-1">
                        <router-link to="/"
                            class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            {{ $t('home') }}
                        </router-link>
                        <router-link to="/about"
                            class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            {{ $t('about') }}
                        </router-link>
                        <router-link to="/services"
                            class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            {{ $t('services') }}
                        </router-link>
                        <router-link to="/pricing"
                            class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            {{ $t('pricing') }}
                        </router-link>
                        <router-link to="/contact"
                            class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            {{ $t('contact') }}
                        </router-link>
                    </div>

                    <!-- Right - Actions -->
                    <div class="flex items-center space-x-3" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <!-- Language Toggle -->
                        <language-toggle />

                        <!-- Login Button -->
                        <router-link to="/login"
                            class="hidden sm:inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                            {{ $t('login') }}
                        </router-link>

                        <!-- Get Started Button -->
                        <router-link to="/signup"
                            class="hidden md:inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                            {{ $t('get_started') }}
                        </router-link>

                        <!-- Mobile Menu Button -->
                        <button @click="guestMobileMenuOpen = !guestMobileMenuOpen"
                            class="lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                            :class="{ 'text-blue-600 bg-blue-50': guestMobileMenuOpen }">
                            <svg class="h-6 w-6" :class="{ 'hidden': guestMobileMenuOpen, 'block': !guestMobileMenuOpen }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg class="h-6 w-6" :class="{ 'block': guestMobileMenuOpen, 'hidden': !guestMobileMenuOpen }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div v-if="guestMobileMenuOpen" class="lg:hidden border-t border-gray-200 bg-white shadow-lg">
                    <div class="px-4 pt-4 pb-6 space-y-2">
                        <router-link to="/" @click="guestMobileMenuOpen = false"
                            class="block px-4 py-3 rounded-xl text-lg font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 min-h-[48px] flex items-center"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                            </svg>
                            {{ $t('home') }}
                        </router-link>
                        <router-link to="/about" @click="guestMobileMenuOpen = false"
                            class="block px-4 py-3 rounded-xl text-lg font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 min-h-[48px] flex items-center"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            {{ $t('about') }}
                        </router-link>
                        <router-link to="/services" @click="guestMobileMenuOpen = false"
                            class="block px-4 py-3 rounded-xl text-lg font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 min-h-[48px] flex items-center"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                            {{ $t('services') }}
                        </router-link>
                        <router-link to="/pricing" @click="guestMobileMenuOpen = false"
                            class="block px-4 py-3 rounded-xl text-lg font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 min-h-[48px] flex items-center"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            {{ $t('pricing') }}
                        </router-link>
                        <router-link to="/contact" @click="guestMobileMenuOpen = false"
                            class="block px-4 py-3 rounded-xl text-lg font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 min-h-[48px] flex items-center"
                            active-class="text-blue-600 bg-blue-50 font-semibold">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            {{ $t('contact') }}
                        </router-link>

                        <!-- Mobile Action Buttons -->
                        <div class="pt-6 border-t border-gray-200 space-y-3">
                            <router-link to="/login" @click="guestMobileMenuOpen = false"
                                class="block w-full text-center px-6 py-4 bg-blue-600 hover:bg-blue-700 text-white text-lg font-semibold rounded-xl transition-all duration-200 min-h-[56px] flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                </svg>
                                {{ $t('login') }}
                            </router-link>
                            <router-link to="/signup" @click="guestMobileMenuOpen = false"
                                class="block w-full text-center px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-lg font-semibold rounded-xl transition-all duration-200 min-h-[56px] flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                </svg>
                                {{ $t('get_started') }}
                            </router-link>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <main :class="isGuestPage ? '' : 'max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8'">
            <router-view />
        </main>

        <!-- Mobile Navigation -->
        <MobileNavigation />

        <!-- PWA Install Prompt -->
        <div v-if="showPWAPrompt" class="fixed bottom-4 left-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 mx-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="font-semibold">{{ $t('install_app') }}</p>
                    <p class="text-sm opacity-90">{{ $t('install_app_description') }}</p>
                </div>
                <div class="flex space-x-2">
                    <button @click="installPWA" class="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium">
                        {{ $t('install') }}
                    </button>
                    <button @click="dismissPWAPrompt" class="text-white opacity-75 hover:opacity-100">
                        ✕
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LanguageToggle from './LanguageToggle.vue';
import NotificationCenter from './NotificationCenter.vue';
import SearchBar from './SearchBar.vue';
import MobileNavigation from './MobileNavigation.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
    name: 'App',
    mixins: [i18nMixin],
    components: {
        LanguageToggle,
        NotificationCenter,
        SearchBar,
        MobileNavigation
    },
    data() {
        return {
            user: null,
            mobileMenuOpen: false,
            guestMobileMenuOpen: false,
            showMoreMenu: false,
            showSuperAdminMoreMenu: false,
            showUserMenu: false,
            showManagementDropdown: false,
            localeUpdateKey: 0,
            showPWAPrompt: false,
            deferredPrompt: null,
            packageInfo: null,
            packageLoading: false
        };
    },
    computed: {
        isAuthenticated() {
            const token = localStorage.getItem('token');
            const user = this.user || JSON.parse(localStorage.getItem('user') || 'null');
            return !!token && !!user;
        },
        isAdmin() {
            return this.user?.role === 'admin' || this.user?.role === 'super_admin';
        },
        isNeighbor() {
            return this.user?.role === 'neighbor';
        },
        isHomePage() {
            return this.$route.name === 'Home';
        },
        isGuestPage() {
            return ['Home', 'About', 'Services', 'Contact', 'Login', 'Register', 'Pricing', 'SignUp', 'SignUpSuccess', 'TermsAndConditions', 'RegistrationSuccess'].includes(this.$route.name);
        },
        // Determine the appropriate dashboard route based on user role
        dashboardRoute() {
            if (this.isAdmin) {
                return '/admin';
            } else if (this.isNeighbor) {
                return '/neighbor';
            } else {
                return '/';
            }
        },
        // Force reactivity for language changes
        currentLocale() {
            return this.$locale() + this.localeUpdateKey;
        },
        // Check if user has advanced reporting feature
        hasAdvancedReporting() {
            if (this.user?.role === 'super_admin') return true;
            return this.packageInfo?.current_package?.advanced_reporting || false;
        },
        // Check if user has export reports feature
        hasExportReports() {
            if (this.user?.role === 'super_admin') return true;
            return this.packageInfo?.current_package?.exports_enabled || false;
        },
        // Check if notifications feature is available in current package
        hasNotificationsFeature() {
            if (this.user?.role === 'super_admin') return true;
            return this.packageInfo?.current_package?.notifications_enabled || false;
        },
        // Check if SMS feature is available in current package
        hasSmsFeature() {
            if (this.user?.role === 'super_admin') return true;
            return this.packageInfo?.current_package?.sms_notifications_enabled || false;
        }
    },
    watch: {
        '$route'() {
            // Update user data when route changes
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
            // Remove $forceUpdate to prevent refresh loops
        }
    },
    created() {
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        // Listen for storage changes to update authentication state
        window.addEventListener('storage', this.handleStorageChange);
        // Also listen for custom events when localStorage is updated in the same tab
        window.addEventListener('auth-state-changed', this.handleAuthStateChange);
        // Listen for language changes
        window.addEventListener('localeChanged', this.handleLocaleChange);
        window.addEventListener('forceUpdate', this.handleForceUpdate);
        // Listen for clicks to close dropdowns
        document.addEventListener('click', this.handleClickOutside);
    },
    mounted() {
        // Update user data on mount
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        // Load package information if user is authenticated
        if (this.isAuthenticated && this.isAdmin) {
            this.loadPackageInfo();
        }
        // Setup PWA install prompt
        this.setupPWAPrompt();
    },
    beforeUnmount() {
        window.removeEventListener('storage', this.handleStorageChange);
        window.removeEventListener('auth-state-changed', this.handleAuthStateChange);
        window.removeEventListener('localeChanged', this.handleLocaleChange);
        window.removeEventListener('forceUpdate', this.handleForceUpdate);
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        handleStorageChange(event) {
            if (event.key === 'user' || event.key === 'token') {
                this.user = JSON.parse(localStorage.getItem('user') || 'null');
            }
        },
        handleAuthStateChange() {
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
            this.localeUpdateKey++;
            // Load package information when auth state changes
            if (this.isAuthenticated && this.isAdmin) {
                this.loadPackageInfo();
            }
            // Remove excessive $forceUpdate calls that can cause refresh loops
        },
        handleLocaleChange() {
            this.localeUpdateKey++;
            // Remove $forceUpdate to prevent refresh loops
        },
        handleForceUpdate() {
            this.localeUpdateKey++;
            // Remove $forceUpdate to prevent refresh loops
        },
        async loadPackageInfo() {
            if (this.packageLoading || !this.isAuthenticated || !this.isAdmin) return;

            try {
                this.packageLoading = true;
                const response = await this.$axios.get('/admin/package/current');

                if (!response.data.is_super_admin) {
                    this.packageInfo = response.data;
                }
            } catch (error) {
                console.error('Error loading package info:', error);
                // Don't show error to user as this is background loading
            } finally {
                this.packageLoading = false;
            }
        },
        handleClickOutside(event) {
            // Close more menu if clicking outside
            if (this.$refs.moreMenu && !this.$refs.moreMenu.contains(event.target)) {
                this.showMoreMenu = false;
            }

            // Close super admin more menu if clicking outside
            if (this.$refs.superAdminMoreMenu && !this.$refs.superAdminMoreMenu.contains(event.target)) {
                this.showSuperAdminMoreMenu = false;
            }

            // Close user menu if clicking outside
            if (this.$refs.userMenu && !this.$refs.userMenu.contains(event.target)) {
                this.showUserMenu = false;
            }
        },
        async logout() {
            try {
                await this.$axios.post('/logout');
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                this.user = null;
                // Dispatch custom event to notify other components
                window.dispatchEvent(new CustomEvent('auth-state-changed'));
                this.$router.push('/login');
            }
        },

        // PWA Methods
        setupPWAPrompt() {
            // Listen for PWA install prompt
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                this.deferredPrompt = e;

                // Don't show if previously dismissed
                if (localStorage.getItem('pwa-install-dismissed') !== 'true') {
                    //this.showPWAPrompt = true;
                }
            });

            // Handle app installed
            window.addEventListener('appinstalled', () => {
                console.log('PWA was installed');
                this.showPWAPrompt = false;
                this.deferredPrompt = null;
            });
        },

        async installPWA() {
            if (this.deferredPrompt) {
                this.deferredPrompt.prompt();
                const { outcome } = await this.deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                this.deferredPrompt = null;
                this.showPWAPrompt = false;
            }
        },

        dismissPWAPrompt() {
            this.showPWAPrompt = false;
            localStorage.setItem('pwa-install-dismissed', 'true');
        }
    }
}
</script>