<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" style="z-index: 9999;">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 transition-opacity" @click="handleOutsideClick"></div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full relative z-10">
        <form @submit.prevent="saveTemplate">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {{ isEdit ? $t('edit_notification_template') : $t('create_notification_template') }}
                </h3>

                <div class="space-y-6">
                  <!-- Basic Information -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('template_name') }} <span class="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        v-model="form.name"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$t('template_name_placeholder')"
                      >
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('notification_type') }} <span class="text-red-500">*</span>
                      </label>
                      <select v-model="form.type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{{ $t('select_notification_type') }}</option>
                        <option value="payment_reminder">{{ $t('payment_reminder') }}</option>
                        <option value="expense_created">{{ $t('expense_created') }}</option>
                        <option value="income_received">{{ $t('income_received') }}</option>
                        <option value="general_announcement">{{ $t('general_announcement') }}</option>
                        <option value="payment_received">{{ $t('payment_received') }}</option>
                        <option value="overdue_payment">{{ $t('overdue_payment') }}</option>
                        <option value="welcome_message">{{ $t('welcome_message') }}</option>
                      </select>
                    </div>
                  </div>

                  <!-- Description -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('description') }}
                    </label>
                    <textarea
                      v-model="form.description"
                      rows="2"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('template_description_placeholder')"
                    ></textarea>
                  </div>

                  <!-- Priority -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('priority') }}
                    </label>
                    <select v-model="form.priority" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="low">{{ $t('low') }}</option>
                      <option value="medium">{{ $t('medium') }}</option>
                      <option value="high">{{ $t('high') }}</option>
                    </select>
                  </div>

                  <!-- Title Template -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('title_template') }} <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      v-model="form.title_template"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('title_template_placeholder')"
                    >
                  </div>

                  <!-- Message Template -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('message_template') }} <span class="text-red-500">*</span>
                    </label>
                    <textarea
                      v-model="form.message_template"
                      required
                      rows="6"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('message_template_placeholder')"
                    ></textarea>
                  </div>



                  <!-- Variables Help -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">{{ $t('available_variables') }}</h4>
                    <div class="text-xs text-blue-800 space-y-1">
                      <p><code>{{ userNameVar }}</code> - {{ $t('user_full_name') }}</p>
                      <p><code>{{ buildingNameVar }}</code> - {{ $t('building_name') }}</p>
                      <p><code>{{ amountVar }}</code> - {{ $t('payment_amount') }}</p>
                      <p><code>{{ dueDateVar }}</code> - {{ $t('payment_due_date') }}</p>
                      <p><code>{{ apartmentNumberVar }}</code> - {{ $t('apartment_number') }}</p>
                    </div>
                  </div>

                  <!-- Active Status -->
                  <div>
                    <label class="flex items-center">
                      <input 
                        type="checkbox" 
                        v-model="form.is_active"
                        class="mr-2"
                      >
                      <span class="text-sm">{{ $t('template_active') }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? $t('saving') : (isEdit ? $t('update_template') : $t('create_template')) }}
            </button>
            <button
              type="button"
              @click="handleCancel"
              class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              {{ $t('cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'NotificationTemplateModal',
  mixins: [i18nMixin, smartModalMixin],
  props: {
    template: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isSubmitting: false,
      form: {
        name: '',
        description: '',
        type: '',
        title_template: '',
        message_template: '',
        priority: 'medium',
        is_active: true
      }
    };
  },
  computed: {
    isEdit() {
      return !!this.template;
    },
    userNameVar() {
      return '{{user_name}}';
    },
    buildingNameVar() {
      return '{{building_name}}';
    },
    amountVar() {
      return '{{amount}}';
    },
    dueDateVar() {
      return '{{due_date}}';
    },
    apartmentNumberVar() {
      return '{{apartment_number}}';
    }
  },
  mounted() {
    console.log('NotificationTemplateModal mounted', { template: this.template });

    if (this.template) {
      this.populateForm();
    }
    // Initialize form tracking after form is populated
    this.$nextTick(() => {
      this.initializeFormTracking();
    });
  },
  methods: {
    populateForm() {
      this.form = {
        name: this.template.name || '',
        description: this.template.description || '',
        type: this.template.type || '',
        title_template: this.template.title_template || '',
        message_template: this.template.message_template || '',
        priority: this.template.priority || 'medium',
        is_active: this.template.is_active !== undefined ? this.template.is_active : true
      };
    },

    async saveTemplate() {
      this.isSubmitting = true;
      try {
        const url = this.isEdit ? `/notification-templates/${this.template.id}` : '/notification-templates';
        const method = this.isEdit ? 'put' : 'post';

        const response = await this.$axios[method](url, this.form);

        this.$emit('success', response.data);
        this.$toast.success(this.$t(this.isEdit ? 'template_updated_successfully' : 'template_created_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error saving template:', error);
        const message = error.response?.data?.message || this.$t('save_failed');
        this.$toast.error(message);
      } finally {
        this.isSubmitting = false;
      }
    },

    handleFormSuccess() {
      // Reset form and close modal
      this.resetForm();
      this.$emit('close');
    },

    handleOutsideClick() {
      // Close modal when clicking outside
      this.$emit('close');
    },

    resetForm() {
      this.form = {
        name: '',
        description: '',
        type: '',
        title_template: '',
        message_template: '',
        priority: 'medium',
        is_active: true
      };
    },

    initializeFormTracking() {
      // Initialize any form tracking if needed
      // This method was referenced in mounted() but was missing
    },

    handleCancel() {
      // Close modal without saving
      this.$emit('close');
    }
  }
};
</script>
