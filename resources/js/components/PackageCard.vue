<template>
  <div :class="[
    'relative bg-white rounded-lg border-2 transition-all duration-200 hover:shadow-lg',
    isSelected ? 'border-blue-500 shadow-lg' : 'border-gray-200',
    isPopular ? 'ring-2 ring-blue-500 ring-opacity-50' : '',
    disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer hover:border-blue-300'
  ]" @click="!disabled && $emit('select', package)">
    
    <!-- Popular Badge -->
    <div v-if="isPopular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
      <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
        {{ $t('most_popular') }}
      </span>
    </div>

    <!-- Current Package Badge -->
    <div v-if="isCurrent" class="absolute -top-3 right-4">
      <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
        {{ $t('current_package') }}
      </span>
    </div>

    <div class="p-6">
      <!-- Package Header -->
      <div class="text-center mb-6">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ getPackageName() }}</h3>
        <p class="text-gray-600 text-sm">{{ getDescription() }}</p>
      </div>

      <!-- Pricing -->
      <div class="text-center mb-6">
        <div v-if="package.is_free" class="text-3xl font-bold text-green-600">
          {{ $t('free') }}
        </div>
        <div v-else>
          <!-- Monthly Price -->
          <div v-if="billingCycle === 'monthly'" class="mb-2">
            <span class="text-3xl font-bold text-gray-900">{{ formatPrice(package.price) }}</span>
            <span class="text-gray-600">/ {{ $t('month') }}</span>
          </div>
          
          <!-- Annual Price -->
          <div v-else-if="billingCycle === 'annual' && package.annual_price">
            <div class="mb-1">
              <span class="text-3xl font-bold text-gray-900">{{ formatPrice(package.annual_price) }}</span>
              <span class="text-gray-600">/ {{ $t('year') }}</span>
            </div>
            <div class="text-sm text-green-600">
              {{ $t('save') }} {{ formatPrice(package.annual_savings) }} ({{ Math.round(package.annual_savings_percentage) }}%)
            </div>
            <div class="text-xs text-gray-500">
              {{ formatPrice(package.monthly_equivalent) }} / {{ $t('month') }}
            </div>
          </div>

          <!-- Trial Info -->
          <div v-if="package.trial_days > 0" class="mt-2 text-sm text-blue-600">
            {{ $t('free_trial_days', { days: package.trial_days }) }}
          </div>
        </div>
      </div>

      <!-- Features List -->
      <div class="space-y-3 mb-6">
        <!-- Neighbors -->
        <div class="flex items-center text-sm">
          <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>
            {{ package.unlimited_neighbors ? $t('unlimited_neighbors') : $t('max_neighbors_count', { count: package.max_neighbors }) }}
          </span>
        </div>

        <!-- Storage -->
        <div class="flex items-center text-sm">
          <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span>
            {{ package.unlimited_storage ? $t('unlimited_storage') : $t('storage_limit_gb', { gb: package.storage_limit_gb }) }}
          </span>
        </div>

        <!-- Notifications -->
        <div class="flex items-center text-sm">
          <svg v-if="package.notifications_enabled" class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <span :class="package.notifications_enabled ? 'text-gray-900' : 'text-gray-400'">
            {{ $t('in_app_notifications') }}
          </span>
        </div>

        <!-- Email Notifications -->
        <div class="flex items-center text-sm">
          <svg v-if="package.email_notifications_enabled" class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <span :class="package.email_notifications_enabled ? 'text-gray-900' : 'text-gray-400'">
            {{ $t('email_notifications') }}
          </span>
        </div>

        <!-- File Attachments -->
        <div class="flex items-center text-sm">
          <svg v-if="package.file_attachments_enabled" class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <span :class="package.file_attachments_enabled ? 'text-gray-900' : 'text-gray-400'">
            {{ package.file_attachments_enabled ? $t('file_attachments_enabled') : $t('file_attachments_disabled') }}
          </span>
        </div>

        <!-- Priority Support -->
        <div class="flex items-center text-sm">
          <svg v-if="package.priority_support" class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <span :class="package.priority_support ? 'text-gray-900' : 'text-gray-400'">
            {{ package.priority_support ? $t('priority_support') : $t('standard_support') }}
          </span>
        </div>
      </div>

      <!-- Action Button -->
      <button
        v-if="!disabled"
        :disabled="isCurrent"
        :class="[
          'w-full py-2 px-4 rounded-md font-medium transition-colors',
          isCurrent 
            ? 'bg-gray-100 text-gray-500 cursor-not-allowed' 
            : isSelected
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-blue-50 text-blue-600 hover:bg-blue-100 border border-blue-200'
        ]"
        @click.stop="!isCurrent && $emit('select', package)"
      >
        {{ getButtonText() }}
      </button>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'PackageCard',
  mixins: [i18nMixin],
  props: {
    package: {
      type: Object,
      required: true
    },
    billingCycle: {
      type: String,
      default: 'monthly',
      validator: value => ['monthly', 'annual'].includes(value)
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    isCurrent: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isPopular() {
      return this.package.is_popular;
    }
  },
  methods: {
    getPackageName() {
      // Use English name if available and locale is English
      if (this.$locale() === 'en' && this.package.name_en) {
        return this.package.name_en;
      }
      return this.package.name;
    },

    getDescription() {
      // Use English description if available and locale is English
      if (this.$locale() === 'en' && this.package.description_en) {
        return this.package.description_en;
      }
      return this.package.description;
    },

    formatPrice(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    },

    getButtonText() {
      if (this.isCurrent) {
        return this.$t('current_package');
      }
      if (this.isSelected) {
        return this.$t('selected');
      }
      if (this.package.is_free) {
        return this.$t('get_started');
      }
      return this.$t('select_package');
    }
  }
};
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
