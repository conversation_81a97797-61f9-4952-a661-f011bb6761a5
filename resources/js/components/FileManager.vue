<template>
  <div class="file-manager-component">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">{{ $t('loading_files') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">{{ $t('error_loading_files') }}</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
          <div class="mt-4">
            <button
              @click="loadFiles"
              class="text-sm bg-red-100 text-red-800 rounded-md px-3 py-1 hover:bg-red-200"
            >
              {{ $t('retry') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- File Preview Component -->
    <FilePreview
      v-else
      :files="files"
      :can-manage="canManage"
      @add-files="$emit('add-files')"
      @delete-file="handleDeleteFile"
    />

    <!-- Upload Section (if enabled) -->
    <div v-if="showUpload && canManage" class="mt-6 border-t border-gray-200 pt-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('add_new_attachments') }}</h3>
      <FileUpload
        :attachable-type="attachableType"
        :attachable-id="attachableId"
        :auto-upload="false"
        @upload-complete="handleUploadComplete"
        @file-uploaded="handleFileUploaded"
        @upload-error="handleUploadError"
      />
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import FilePreview from './FilePreview.vue';
import FileUpload from './FileUpload.vue';

export default {
  name: 'FileManager',
  mixins: [i18nMixin],
  components: {
    FilePreview,
    FileUpload
  },
  props: {
    attachableType: {
      type: String,
      required: true,
      validator: value => ['App\\Models\\Expense', 'App\\Models\\Income', 'App\\Models\\BuildingExpense'].includes(value)
    },
    attachableId: {
      type: [Number, String],
      required: true
    },
    canManage: {
      type: Boolean,
      default: true
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    autoLoad: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      files: [],
      loading: false,
      error: null
    };
  },
  watch: {
    files: {
      handler(newFiles) {
        console.log('=== FileManager files changed ===');
        console.log('New files:', newFiles);
        if (newFiles && newFiles.length > 0) {
          newFiles.forEach((file, index) => {
            console.log(`File ${index}:`, file);
            console.log(`File ${index} original_name:`, file.original_name, 'type:', typeof file.original_name);
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (this.autoLoad && this.attachableId && this.attachableId !== 'temp') {
      this.loadFiles();
    }
  },
  watch: {
    attachableId: {
      handler(newId, oldId) {
        if (newId !== oldId && newId && newId !== 'temp') {
          this.loadFiles();
        }
      },
      immediate: true
    }
  },
  methods: {
    async loadFiles() {
      if (!this.attachableId || this.attachableId === 'temp') {
        this.files = [];
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await this.$axios.get('/files/attachments', {
          params: {
            attachable_type: this.attachableType,
            attachable_id: this.attachableId
          }
        });

        console.log('FileManager - Raw server response:', response.data);
        const files = response.data.files || [];
        console.log('FileManager - Extracted files:', files);

        // Validate each file and log any issues
        files.forEach((file, index) => {
          console.log(`FileManager - File ${index}:`, file);
          console.log(`FileManager - File ${index} original_name:`, file.original_name, 'type:', typeof file.original_name);

          // Check if any property is a Promise
          Object.keys(file).forEach(key => {
            const value = file[key];
            if (value && typeof value === 'object' && typeof value.then === 'function') {
              console.error(`FileManager - Promise detected in file.${key}:`, value);
            }
          });
        });

        this.files = files;
        this.$emit('files-loaded', this.files);
      } catch (error) {
        console.error('Error loading files:', error);
        this.error = error.response?.data?.message || this.$t('error_loading_files');
        this.$emit('load-error', error);
      } finally {
        this.loading = false;
      }
    },

    async handleDeleteFile({ file, index }) {
      if (!file.id) {
        // If it's a local file (not yet uploaded), just remove from array
        this.files.splice(index, 1);
        this.$emit('file-deleted', { file, index });
        return;
      }

      try {
        await this.$axios.delete(`/files/${file.id}`);
        this.files.splice(index, 1);
        this.$emit('file-deleted', { file, index });
        
        // Show success message
        this.$emit('show-message', {
          type: 'success',
          message: this.$t('file_deleted_successfully')
        });
      } catch (error) {
        console.error('Error deleting file:', error);
        this.$emit('show-message', {
          type: 'error',
          message: error.response?.data?.message || this.$t('error_deleting_file')
        });
      }
    },

    handleUploadComplete(uploadedFiles) {
      // Ensure uploadedFiles is an array and contains valid file objects
      const validFiles = Array.isArray(uploadedFiles) ? uploadedFiles : [uploadedFiles];
      const processedFiles = validFiles.filter(file => file && typeof file === 'object');
      
      console.log('FileManager - Upload complete:', processedFiles);
      
      // Add newly uploaded files to the list
      this.files.push(...processedFiles);
      this.$emit('upload-complete', processedFiles);
    },

    handleFileUploaded(file) {
      // Ensure file is a valid object before adding
      if (file && typeof file === 'object' && !file.then) { // Check it's not a Promise
        // Validate required properties
        if (file.original_name || file.name) {
          console.log('FileManager - File uploaded:', file);
          this.files.push(file);
          this.$emit('file-uploaded', file);
        } else {
          console.error('FileManager - File missing required properties:', file);
        }
      } else {
        console.error('FileManager - Invalid file object received (possibly a Promise):', file);
      }
    },

    handleUploadError(error) {
      console.error('FileManager - Upload error:', error);
      
      // Ensure error is properly formatted
      let formattedError = error;
      if (typeof error === 'object' && error !== null) {
        if (error.error) {
          formattedError = error.error;
        } else if (error.message) {
          formattedError = error.message;
        } else {
          formattedError = JSON.stringify(error);
        }
      }
      
      this.$emit('upload-error', formattedError);
    },

    // Public method to refresh files
    refresh() {
      this.loadFiles();
    },

    // Public method to add files programmatically
    addFiles(newFiles) {
      if (Array.isArray(newFiles)) {
        this.files.push(...newFiles);
      } else {
        this.files.push(newFiles);
      }
    },

    // Public method to remove files programmatically
    removeFile(fileId) {
      const index = this.files.findIndex(f => f.id === fileId);
      if (index !== -1) {
        this.files.splice(index, 1);
      }
    },

    // Public method to get current files
    getFiles() {
      return this.files;
    }
  }
};
</script>

<style scoped>
.file-manager-component {
  width: 100%;
}
</style>
