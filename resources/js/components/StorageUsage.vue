<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900">{{ $t('storage_usage') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('storage_usage_description') }}</p>
        </div>
        <button
          @click="refreshData"
          :disabled="loading"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {{ $t('refresh') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-1/4"></div>
        <div class="h-32 bg-gray-200 rounded"></div>
        <div class="grid grid-cols-3 gap-4">
          <div class="h-20 bg-gray-200 rounded"></div>
          <div class="h-20 bg-gray-200 rounded"></div>
          <div class="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <!-- Super Admin Message -->
    <div v-else-if="isSuperAdmin" class="p-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">{{ $t('unlimited_storage') }}</h3>
            <p class="mt-1 text-sm text-blue-700">{{ $t('super_admin_unlimited_storage') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Storage Content -->
    <div v-else-if="storageData" class="p-6">
      <!-- Overall Usage -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ $t('overall_usage') }}</h3>
          <div class="text-right">
            <div class="text-2xl font-bold text-gray-900">{{ storageData.storage_usage.formatted_size }}</div>
            <div class="text-sm text-gray-500">
              {{ storageData.storage_usage.unlimited ? $t('unlimited') : `of ${storageData.package_limit}GB` }}
            </div>
          </div>
        </div>

        <!-- Progress Bar -->
        <div v-if="!storageData.storage_usage.unlimited" class="mb-4">
          <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>{{ storageData.storage_usage.used_gb.toFixed(2) }}GB {{ $t('used') }}</span>
            <span>{{ (storageData.package_limit - storageData.storage_usage.used_gb).toFixed(2) }}GB {{ $t('remaining') }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div 
              class="h-3 rounded-full transition-all duration-500"
              :class="getUsageColorClass(storageData.storage_usage.usage_percentage)"
              :style="{ width: Math.min(storageData.storage_usage.usage_percentage, 100) + '%' }"
            ></div>
          </div>
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>0GB</span>
            <span>{{ storageData.package_limit }}GB</span>
          </div>
        </div>

        <!-- Usage Warning -->
        <div v-if="storageData.storage_usage.is_near_limit" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div class="flex">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">{{ $t('storage_warning') }}</h3>
              <p class="mt-1 text-sm text-yellow-700">{{ $t('storage_near_limit_message') }}</p>
            </div>
          </div>
        </div>

        <div v-if="storageData.storage_usage.is_over_limit" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">{{ $t('storage_limit_exceeded') }}</h3>
              <p class="mt-1 text-sm text-red-700">{{ $t('storage_over_limit_message') }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- File Type Breakdown -->
      <div v-if="storageData.file_type_breakdown && storageData.file_type_breakdown.length > 0" class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('file_type_breakdown') }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div 
            v-for="fileType in storageData.file_type_breakdown" 
            :key="fileType.type"
            class="bg-gray-50 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div 
                  class="w-3 h-3 rounded-full mr-2"
                  :class="getFileTypeColor(fileType.type)"
                ></div>
                <span class="text-sm font-medium text-gray-900">{{ $t('file_type_' + fileType.type) }}</span>
              </div>
              <span class="text-sm text-gray-500">{{ fileType.percentage }}%</span>
            </div>
            <div class="text-xs text-gray-600">
              <div>{{ fileType.count }} {{ $t('files') }}</div>
              <div>{{ fileType.formatted_size }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Monthly Usage Trend -->
      <div v-if="storageData.monthly_usage && storageData.monthly_usage.length > 0" class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('monthly_usage_trend') }}</h3>
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div 
              v-for="month in storageData.monthly_usage.slice(-6)" 
              :key="month.month"
              class="text-center"
            >
              <div class="text-xs text-gray-500 mb-1">{{ month.month_name }}</div>
              <div class="text-sm font-medium text-gray-900">{{ month.cumulative_size_gb.toFixed(2) }}GB</div>
              <div class="text-xs text-gray-600">+{{ month.files_added }} {{ $t('files') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Storage Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-gray-900">{{ storageData.storage_usage.total_files }}</div>
          <div class="text-sm text-gray-600">{{ $t('total_files') }}</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-gray-900">{{ storageData.storage_usage.used_gb.toFixed(2) }}GB</div>
          <div class="text-sm text-gray-600">{{ $t('total_storage_used') }}</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-gray-900">
            {{ storageData.storage_usage.unlimited ? '∞' : (storageData.package_limit - storageData.storage_usage.used_gb).toFixed(2) + 'GB' }}
          </div>
          <div class="text-sm text-gray-600">{{ $t('storage_remaining') }}</div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="p-6">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">{{ $t('error_loading_storage') }}</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StorageUsage',
  data() {
    return {
      loading: true,
      error: null,
      storageData: null,
      isSuperAdmin: false,
    }
  },
  async mounted() {
    await this.loadStorageData()
  },
  methods: {
    async loadStorageData() {
      try {
        this.loading = true
        this.error = null

        const response = await this.$axios.get('/admin/package/storage-usage')
        
        if (response.data.unlimited) {
          this.isSuperAdmin = true
        } else {
          this.storageData = response.data
        }
      } catch (error) {
        console.error('Error loading storage data:', error)
        this.error = error.response?.data?.message || 'Failed to load storage information'
      } finally {
        this.loading = false
      }
    },
    
    async refreshData() {
      await this.loadStorageData()
    },
    
    getUsageColorClass(percentage) {
      if (percentage > 90) return 'bg-red-500'
      if (percentage > 80) return 'bg-yellow-500'
      if (percentage > 60) return 'bg-blue-500'
      return 'bg-green-500'
    },
    
    getFileTypeColor(type) {
      const colors = {
        images: 'bg-blue-500',
        pdf: 'bg-red-500',
        documents: 'bg-green-500',
        spreadsheets: 'bg-yellow-500',
        archives: 'bg-purple-500',
        other: 'bg-gray-500'
      }
      return colors[type] || 'bg-gray-500'
    }
  }
}
</script>
