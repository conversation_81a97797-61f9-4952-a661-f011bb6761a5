<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" style="z-index: 9999;">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="handleOutsideClick"></div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full relative z-10">
        <form @submit.prevent="saveTemplate">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="w-full">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                  <h3 class="text-lg font-medium text-gray-900">
                    {{ isEdit ? $t('edit_sms_template') : $t('create_sms_template') }}
                  </h3>
                  <button type="button" @click="handleCancel" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Left Column - Form Fields -->
                  <div class="space-y-6">
                    <!-- Template Name -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('template_name') }} <span class="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        v-model="form.name"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$t('template_name_placeholder')"
                      >
                    </div>

                    <!-- Notification Type -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('notification_type') }} <span class="text-red-500">*</span>
                      </label>
                      <select v-model="form.notification_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{{ $t('select_notification_type') }}</option>
                        <option value="payment_reminder">{{ $t('payment_reminder') }}</option>
                        <option value="expense_created">{{ $t('expense_created') }}</option>
                        <option value="income_received">{{ $t('income_received') }}</option>
                        <option value="general_announcement">{{ $t('general_announcement') }}</option>
                        <option value="payment_received">{{ $t('payment_received') }}</option>
                        <option value="overdue_payment">{{ $t('overdue_payment') }}</option>
                      </select>
                    </div>

                    <!-- SMS Template -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('sms_template') }} <span class="text-red-500">*</span>
                      </label>
                      <textarea
                        v-model="form.template"
                        required
                        rows="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        :placeholder="$t('sms_template_placeholder')"
                        @input="updateCharacterCount"
                      ></textarea>
                      
                      <!-- Character Count -->
                      <div class="mt-2 flex justify-between text-sm">
                        <span :class="characterCountClass">
                          {{ characterCount }} {{ $t('characters') }} 
                          ({{ smsSegments }} {{ smsSegments === 1 ? $t('sms_segment') : $t('sms_segments') }})
                        </span>
                        <span v-if="characterCount > 160" class="text-amber-600">
                          {{ $t('multiple_sms_warning') }}
                        </span>
                      </div>
                    </div>

                    <!-- Active Status -->
                    <div>
                      <label class="flex items-center">
                        <input
                          type="checkbox"
                          v-model="form.is_active"
                          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">{{ $t('template_active') }}</span>
                      </label>
                    </div>
                  </div>

                  <!-- Right Column - Variables and Preview -->
                  <div class="space-y-6">
                    <!-- Available Variables -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-blue-900 mb-3">{{ $t('available_variables') }}</h4>
                      <div class="space-y-2">
                        <div 
                          v-for="variable in availableVariables" 
                          :key="variable.name"
                          class="flex items-center justify-between"
                        >
                          <div class="flex items-center space-x-2">
                            <code class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {{ '{' + variable.name + '}' }}
                            </code>
                            <span class="text-xs text-blue-700">{{ variable.description }}</span>
                          </div>
                          <button
                            type="button"
                            @click="insertVariable(variable.name)"
                            class="text-xs text-blue-600 hover:text-blue-800 focus:outline-none"
                          >
                            {{ $t('insert') }}
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Template Preview -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-gray-900 mb-3">{{ $t('preview') }}</h4>
                      <div class="bg-white border rounded-lg p-3">
                        <div class="text-xs text-gray-500 mb-2">{{ $t('sms_preview') }}:</div>
                        <div class="text-sm text-gray-900 font-mono whitespace-pre-wrap">{{ previewText }}</div>
                      </div>
                      <div class="mt-2 text-xs text-gray-500">
                        {{ $t('preview_note') }}
                      </div>
                    </div>

                    <!-- SMS Limits Info -->
                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-amber-900 mb-2">{{ $t('sms_limits') }}</h4>
                      <div class="text-xs text-amber-800 space-y-1">
                        <p>• {{ $t('sms_limit_160') }}</p>
                        <p>• {{ $t('sms_limit_segments') }}</p>
                        <p>• {{ $t('sms_limit_cost') }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? $t('saving') : (isEdit ? $t('update_template') : $t('create_template')) }}
            </button>
            <button
              type="button"
              @click="handleCancel"
              class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              {{ $t('cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'CreateSmsTemplateModal',
  mixins: [i18nMixin, smartModalMixin],
  props: {
    template: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isSubmitting: false,
      form: {
        name: '',
        notification_type: '',
        template: '',
        is_active: true
      },
      availableVariables: [
        { name: 'user_name', description: this.$t('user_full_name') },
        { name: 'building_name', description: this.$t('building_name') },
        { name: 'amount', description: this.$t('payment_amount') },
        { name: 'due_date', description: this.$t('payment_due_date') },
        { name: 'expense_type', description: this.$t('expense_type_name') },
        { name: 'apartment_number', description: this.$t('apartment_number') },
        { name: 'days_overdue', description: this.$t('days_overdue') }
      ]
    };
  },
  computed: {
    isEdit() {
      return !!this.template;
    },
    characterCount() {
      return this.form.template.length;
    },
    smsSegments() {
      return Math.ceil(this.characterCount / 160) || 1;
    },
    characterCountClass() {
      if (this.characterCount <= 160) return 'text-green-600';
      if (this.characterCount <= 320) return 'text-amber-600';
      return 'text-red-600';
    },
    previewText() {
      if (!this.form.template) return this.$t('template_preview_empty');
      
      // Replace variables with sample data for preview
      let preview = this.form.template;
      const sampleData = {
        user_name: 'أحمد محمد',
        building_name: 'عمارة النور',
        amount: '500 ريال',
        due_date: '2024-01-15',
        expense_type: 'رسوم الصيانة',
        apartment_number: '3A',
        days_overdue: '5'
      };
      
      Object.keys(sampleData).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        preview = preview.replace(regex, sampleData[key]);
      });
      
      return preview;
    }
  },
  mounted() {
    if (this.template) {
      this.populateForm();
    }
    
    // Initialize form tracking after form is populated
    this.$nextTick(() => {
      this.initializeFormTracking();
    });
  },
  methods: {
    populateForm() {
      this.form = {
        name: this.template.name || '',
        notification_type: this.template.notification_type || '',
        template: this.template.template || '',
        is_active: this.template.is_active !== false
      };
    },

    updateCharacterCount() {
      // Character count is computed automatically
    },

    insertVariable(variableName) {
      const textarea = this.$el.querySelector('textarea');
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = this.form.template;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      
      this.form.template = before + `{${variableName}}` + after;
      
      // Set cursor position after inserted variable
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variableName.length + 2, start + variableName.length + 2);
      });
    },

    async saveTemplate() {
      this.isSubmitting = true;
      try {
        const url = this.isEdit ? `/api/sms/templates/${this.template.id}` : '/api/sms/templates';
        const method = this.isEdit ? 'put' : 'post';

        const response = await this.$axios[method](url, this.form);

        this.$emit('success', response.data);
        this.$toast.success(this.$t(this.isEdit ? 'template_updated_successfully' : 'template_created_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error saving SMS template:', error);
        const message = error.response?.data?.message || this.$t('save_failed');
        this.$toast.error(message);
      } finally {
        this.isSubmitting = false;
      }
    },

    handleFormSuccess() {
      // Reset form and close modal
      this.resetForm();
      this.$emit('close');
    },

    handleOutsideClick() {
      // Close modal when clicking outside
      this.$emit('close');
    },

    handleCancel() {
      // Close modal without saving
      this.$emit('close');
    },

    resetForm() {
      this.form = {
        name: '',
        notification_type: '',
        template: '',
        is_active: true
      };
    },

    initializeFormTracking() {
      // Initialize any form tracking if needed
    }
  }
};
</script>
