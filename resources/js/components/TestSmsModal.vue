<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" style="z-index: 9999;">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="handleOutsideClick"></div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full relative z-10">
        <form @submit.prevent="sendTestSms">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="w-full">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                  <h3 class="text-lg font-medium text-gray-900">{{ $t('send_test_sms') }}</h3>
                  <button type="button" @click="handleCancel" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div class="space-y-6">
                  <!-- Phone Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('phone_number') }} <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      v-model="form.phoneNumber"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('enter_phone_number')"
                    >
                    <p class="mt-1 text-xs text-gray-500">{{ $t('phone_number_format_hint') }}</p>
                  </div>

                  <!-- Template Selection -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('message_template') }}
                    </label>
                    <select 
                      v-model="form.templateId"
                      @change="loadTemplate"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">{{ $t('select_template_or_write_custom') }}</option>
                      <option 
                        v-for="template in availableTemplates" 
                        :key="template.id" 
                        :value="template.id"
                      >
                        {{ template.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Test Message -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ $t('test_message') }} <span class="text-red-500">*</span>
                    </label>
                    <textarea
                      v-model="form.message"
                      required
                      rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="$t('enter_test_message')"
                      @input="updateCharacterCount"
                    ></textarea>
                    
                    <!-- Character Count -->
                    <div class="mt-2 flex justify-between text-sm">
                      <span :class="characterCountClass">
                        {{ characterCount }} {{ $t('characters') }} 
                        ({{ smsSegments }} {{ smsSegments === 1 ? $t('sms_segment') : $t('sms_segments') }})
                      </span>
                      <span v-if="characterCount > 160" class="text-amber-600">
                        {{ $t('multiple_sms_warning') }}
                      </span>
                    </div>
                  </div>

                  <!-- Available Variables -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-3">{{ $t('available_variables') }}</h4>
                    <div class="grid grid-cols-2 gap-2">
                      <div 
                        v-for="variable in availableVariables" 
                        :key="variable.name"
                        class="flex items-center justify-between"
                      >
                        <div class="flex items-center space-x-2">
                          <code class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {{ '{' + variable.name + '}' }}
                          </code>
                          <span class="text-xs text-blue-700">{{ variable.description }}</span>
                        </div>
                        <button
                          type="button"
                          @click="insertVariable(variable.name)"
                          class="text-xs text-blue-600 hover:text-blue-800 focus:outline-none"
                        >
                          {{ $t('insert') }}
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Message Preview -->
                  <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">{{ $t('message_preview') }}</h4>
                    <div class="bg-white border rounded-lg p-3">
                      <div class="text-xs text-gray-500 mb-2">{{ $t('sms_preview') }}:</div>
                      <div class="text-sm text-gray-900 font-mono whitespace-pre-wrap">{{ previewText }}</div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                      {{ $t('preview_note') }}
                    </div>
                  </div>

                  <!-- Cost Information -->
                  <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-amber-900 mb-2">{{ $t('cost_information') }}</h4>
                    <div class="text-sm text-amber-800 space-y-1">
                      <div class="flex justify-between">
                        <span>{{ $t('sms_segments') }}:</span>
                        <span class="font-medium">{{ smsSegments }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span>{{ $t('cost_per_segment') }}:</span>
                        <span class="font-medium">{{ formatCurrency(costPerSms) }}</span>
                      </div>
                      <div class="flex justify-between border-t border-amber-200 pt-1">
                        <span class="font-medium">{{ $t('total_cost') }}:</span>
                        <span class="font-bold">{{ formatCurrency(totalCost) }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Test Options -->
                  <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-green-900 mb-3">{{ $t('test_options') }}</h4>
                    <div class="space-y-2">
                      <label class="flex items-center">
                        <input
                          v-model="form.testMode"
                          type="checkbox"
                          class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-green-700">{{ $t('test_mode_enabled') }}</span>
                      </label>
                      <p class="text-xs text-green-600 ml-6">{{ $t('test_mode_description') }}</p>
                    </div>
                  </div>

                  <!-- Warning -->
                  <div v-if="!form.testMode" class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">{{ $t('real_sms_warning') }}</h3>
                        <div class="mt-2 text-sm text-red-700">
                          <p>{{ $t('real_sms_warning_description') }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isSubmitting || !canSend"
              class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? $t('sending') : $t('send_test_sms') }}
            </button>
            <button
              type="button"
              @click="handleCancel"
              class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              {{ $t('cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'TestSmsModal',
  mixins: [i18nMixin],
  data() {
    return {
      isSubmitting: false,
      availableTemplates: [],
      costPerSms: 0.01,
      form: {
        phoneNumber: '',
        templateId: '',
        message: '',
        testMode: true
      },
      availableVariables: [
        { name: 'user_name', description: this.$t('user_full_name') },
        { name: 'building_name', description: this.$t('building_name') },
        { name: 'apartment_number', description: this.$t('apartment_number') },
        { name: 'amount', description: this.$t('payment_amount') },
        { name: 'due_date', description: this.$t('payment_due_date') }
      ]
    };
  },
  computed: {
    characterCount() {
      return this.form.message.length;
    },
    smsSegments() {
      return Math.ceil(this.characterCount / 160) || 1;
    },
    characterCountClass() {
      if (this.characterCount <= 160) return 'text-green-600';
      if (this.characterCount <= 320) return 'text-amber-600';
      return 'text-red-600';
    },
    totalCost() {
      return this.smsSegments * this.costPerSms;
    },
    canSend() {
      return this.form.phoneNumber.trim() && this.form.message.trim();
    },
    previewText() {
      if (!this.form.message) return this.$t('message_preview_empty');
      
      // Replace variables with sample data for preview
      let preview = this.form.message;
      const sampleData = {
        user_name: 'أحمد محمد',
        building_name: 'عمارة النور',
        apartment_number: '3A',
        amount: '500 ريال',
        due_date: '2024-01-15'
      };
      
      Object.keys(sampleData).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        preview = preview.replace(regex, sampleData[key]);
      });
      
      return preview;
    }
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadTemplates(),
        this.loadSettings()
      ]);
    },

    async loadTemplates() {
      try {
        const response = await this.$axios.get('/api/sms/templates');
        this.availableTemplates = response.data.templates || [];
      } catch (error) {
        console.error('Error loading templates:', error);
      }
    },

    async loadSettings() {
      try {
        const response = await this.$axios.get('/api/sms/settings');
        this.costPerSms = response.data.settings.cost_per_sms || 0.01;
      } catch (error) {
        console.error('Error loading SMS settings:', error);
      }
    },

    loadTemplate() {
      if (this.form.templateId) {
        const template = this.availableTemplates.find(t => t.id == this.form.templateId);
        if (template) {
          this.form.message = template.template;
        }
      }
    },

    updateCharacterCount() {
      // Character count is computed automatically
    },

    insertVariable(variableName) {
      const textarea = this.$el.querySelector('textarea');
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = this.form.message;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      
      this.form.message = before + `{${variableName}}` + after;
      
      // Set cursor position after inserted variable
      this.$nextTick(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variableName.length + 2, start + variableName.length + 2);
      });
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 4
      }).format(amount);
    },

    async sendTestSms() {
      this.isSubmitting = true;
      try {
        const payload = {
          phone_number: this.form.phoneNumber,
          message: this.form.message,
          test_mode: this.form.testMode
        };

        const response = await this.$axios.post('/api/sms/test-send', payload);
        
        this.$emit('success', response.data);
        this.$toast.success(this.$t('test_sms_sent_successfully'));
      } catch (error) {
        console.error('Error sending test SMS:', error);
        const message = error.response?.data?.message || this.$t('send_failed');
        this.$toast.error(message);
      } finally {
        this.isSubmitting = false;
      }
    },

    handleOutsideClick() {
      this.$emit('close');
    },

    handleCancel() {
      this.$emit('close');
    }
  }
};
</script>
