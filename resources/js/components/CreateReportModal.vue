<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white" @click.stop>
      <!-- <PERSON><PERSON>er -->
      <div class="flex justify-between items-center pb-4 border-b border-gray-200">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">{{ $t('create_custom_report') }}</h3>
          <p class="text-sm text-gray-600">{{ $t('build_your_custom_report_step_by_step') }}</p>
        </div>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Progress Steps -->
      <div class="py-4">
        <div class="flex items-center justify-center space-x-4">
          <div v-for="(step, index) in steps" :key="step.id" class="flex items-center">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
              currentStep >= index + 1 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            ]">
              {{ index + 1 }}
            </div>
            <span :class="[
              'ml-2 text-sm font-medium',
              currentStep >= index + 1 ? 'text-blue-600' : 'text-gray-500'
            ]">
              {{ $t(step.name) }}
            </span>
            <svg v-if="index < steps.length - 1" class="w-5 h-5 text-gray-300 ml-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="py-6" style="max-height: 70vh; overflow-y: auto;">
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 1" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('report_name') }}</label>
            <input
              v-model="reportForm.name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('enter_report_name')"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('description') }}</label>
            <textarea
              v-model="reportForm.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('describe_your_report')"
            ></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('category') }}</label>
            <select
              v-model="reportForm.category"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{{ $t('select_category') }}</option>
              <option value="financial">{{ $t('financial') }}</option>
              <option value="analytics">{{ $t('analytics') }}</option>
              <option value="operations">{{ $t('operations') }}</option>
            </select>
          </div>
        </div>

        <!-- Step 2: Data Source -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">{{ $t('select_data_source') }}</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="source in dataSources"
                :key="source.id"
                :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors',
                  reportForm.dataSource === source.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                ]"
                @click="reportForm.dataSource = source.id"
              >
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <component :is="source.icon" class="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-900">{{ $t(source.name) }}</h5>
                    <p class="text-sm text-gray-600">{{ $t(source.description) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Fields Selection -->
        <div v-if="currentStep === 3" class="space-y-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">{{ $t('select_fields_to_include') }}</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Available Fields -->
              <div>
                <h5 class="text-sm font-medium text-gray-700 mb-3">{{ $t('available_fields') }}</h5>
                <div class="border border-gray-200 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div
                    v-for="field in availableFields"
                    :key="field.id"
                    class="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer"
                    @click="addField(field)"
                  >
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span class="text-sm">{{ $t(field.name) }}</span>
                    </div>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Selected Fields -->
              <div>
                <h5 class="text-sm font-medium text-gray-700 mb-3">{{ $t('selected_fields') }}</h5>
                <div class="border border-gray-200 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div
                    v-for="field in reportForm.selectedFields"
                    :key="field.id"
                    class="flex items-center justify-between p-2 bg-blue-50 rounded mb-2"
                  >
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span class="text-sm">{{ $t(field.name) }}</span>
                    </div>
                    <button
                      @click="removeField(field)"
                      class="text-red-600 hover:text-red-800"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-if="reportForm.selectedFields.length === 0" class="text-center py-8 text-gray-500">
                    <p class="text-sm">{{ $t('no_fields_selected') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Visualization -->
        <div v-if="currentStep === 4" class="space-y-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">{{ $t('choose_visualization') }}</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="chart in chartTypes"
                :key="chart.type"
                :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors text-center',
                  reportForm.chartType === chart.type
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                ]"
                @click="reportForm.chartType = chart.type"
              >
                <div class="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center">
                  <component :is="chart.icon" class="w-6 h-6 text-gray-600" />
                </div>
                <h5 class="font-medium text-gray-900 text-sm">{{ $t(chart.name) }}</h5>
                <p class="text-xs text-gray-600 mt-1">{{ $t(chart.description) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-between items-center pt-4 border-t border-gray-200">
        <button
          v-if="currentStep > 1"
          @click="previousStep"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          {{ $t('previous') }}
        </button>
        <div v-else></div>

        <div class="flex space-x-3">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {{ $t('cancel') }}
          </button>
          <button
            v-if="currentStep < steps.length"
            @click="nextStep"
            :disabled="!canProceed"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ $t('next') }}
          </button>
          <button
            v-else
            @click="createReport"
            :disabled="!canCreate"
            class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ $t('create_report') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin';

// Chart type icons
const BarChartIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>`
};

const LineChartIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>`
};

const PieChartIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" /></svg>`
};

const TableIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H5a1 1 0 01-1-1z" /></svg>`
};

const CurrencyDollarIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>`
};

const UsersIcon = {
  template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" /></svg>`
};

export default {
  name: 'CreateReportModal',
  mixins: [i18nMixin],
  props: {
    template: {
      type: Object,
      default: null
    }
  },
  components: {
    BarChartIcon,
    LineChartIcon,
    PieChartIcon,
    TableIcon,
    CurrencyDollarIcon,
    UsersIcon
  },
  data() {
    return {
      currentStep: 1,
      steps: [
        { id: 1, name: 'basic_info' },
        { id: 2, name: 'data_source' },
        { id: 3, name: 'fields' },
        { id: 4, name: 'visualization' }
      ],
      reportForm: {
        name: '',
        description: '',
        category: '',
        dataSource: '',
        selectedFields: [],
        chartType: ''
      },
      dataSources: [
        {
          id: 'expenses',
          name: 'expenses_data',
          description: 'building_expenses_and_costs',
          icon: 'CurrencyDollarIcon'
        },
        {
          id: 'users',
          name: 'users_data',
          description: 'user_activity_and_engagement',
          icon: 'UsersIcon'
        }
      ],
      availableFields: [],
      chartTypes: [
        {
          type: 'bar',
          name: 'bar_chart',
          description: 'compare_values_across_categories',
          icon: 'BarChartIcon'
        },
        {
          type: 'line',
          name: 'line_chart',
          description: 'show_trends_over_time',
          icon: 'LineChartIcon'
        },
        {
          type: 'pie',
          name: 'pie_chart',
          description: 'show_proportions_and_percentages',
          icon: 'PieChartIcon'
        },
        {
          type: 'table',
          name: 'table_view',
          description: 'display_data_in_rows_and_columns',
          icon: 'TableIcon'
        }
      ]
    };
  },
  mounted() {
    console.log('CreateReportModal mounted');
    // Initialize with default values
    this.reportForm = {
      name: '',
      description: '',
      category: 'financial',
      dataSource: 'expenses',
      selectedFields: [],
      chartType: 'bar'
    };
    
    this.loadFieldsForDataSource(this.reportForm.dataSource);
    console.log('Modal initialized with fields:', this.availableFields);
  },
  computed: {
    canProceed() {
      switch (this.currentStep) {
        case 1:
          return this.reportForm.name && this.reportForm.category;
        case 2:
          return this.reportForm.dataSource;
        case 3:
          return this.reportForm.selectedFields.length > 0;
        case 4:
          return this.reportForm.chartType;
        default:
          return false;
      }
    },
    canCreate() {
      return this.reportForm.name && 
             this.reportForm.category && 
             this.reportForm.dataSource && 
             this.reportForm.selectedFields.length > 0 && 
             this.reportForm.chartType;
    }
  },
  watch: {
    'reportForm.dataSource'(newSource) {
      if (newSource) {
        this.loadFieldsForDataSource(newSource);
      }
    },
    template: {
      immediate: true,
      handler(newTemplate) {
        if (newTemplate) {
          this.applyTemplate(newTemplate);
        }
      }
    }
  },
  methods: {
    nextStep() {
      if (this.canProceed && this.currentStep < this.steps.length) {
        this.currentStep++;
      }
    },
    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },
    loadFieldsForDataSource(source) {
      // Use correct field names that match the backend template
      const fieldsBySource = {
        expenses: [
          { id: 'amount', name: 'Amount', type: 'currency' },
          { id: 'created_at', name: 'Date', type: 'date' },
          { id: 'expense_type_name', name: 'Expense Type', type: 'string' },
          { id: 'user_name', name: 'User Name', type: 'string' },
          { id: 'apartment_number', name: 'Apartment', type: 'string' },
          { id: 'notes', name: 'Notes', type: 'text' }
        ],
        users: [
          { id: 'user_count', name: 'User Count', type: 'number' },
          { id: 'login_date', name: 'Login Date', type: 'date' },
          { id: 'role', name: 'Role', type: 'string' },
          { id: 'activity_score', name: 'Activity Score', type: 'number' }
        ]
      };
      
      this.availableFields = fieldsBySource[source] || [];
      this.reportForm.selectedFields = [];
      
      // Auto-select some default fields for better UX
      if (source === 'expenses' && this.availableFields.length > 0) {
        // Select amount and date by default
        const defaultFields = this.availableFields.filter(f => ['amount', 'created_at'].includes(f.id));
        this.reportForm.selectedFields = defaultFields;
      }
    },
    addField(field) {
      if (!this.reportForm.selectedFields.find(f => f.id === field.id)) {
        this.reportForm.selectedFields.push(field);
      }
    },
    removeField(field) {
      this.reportForm.selectedFields = this.reportForm.selectedFields.filter(f => f.id !== field.id);
    },
    async createReport() {
      try {
        // Validate required fields
        if (!this.reportForm.name || !this.reportForm.name.trim()) {
          this.$toast.error('Report name is required');
          return;
        }

        if (!this.reportForm.selectedFields || this.reportForm.selectedFields.length === 0) {
          this.$toast.error('Please select at least one field');
          return;
        }

        // Find a template that matches the selected category and data source
        const templateResponse = await this.$http.get('/advanced-reporting/templates');
        const templates = templateResponse.data;

        // For now, use the first financial template as default
        const template = templates.find(t => t.category === this.reportForm.category) || templates[0];

        if (!template) {
          throw new Error('No suitable template found');
        }

        const reportData = {
          template_id: template.id,
          name: this.reportForm.name.trim(),
          description: this.reportForm.description?.trim() || '',
          configuration: {
            fields: this.reportForm.selectedFields.map(f => f.id),
            filters: {},
            grouping: [],
            data_source: this.reportForm.dataSource
          },
          chart_config: {
            type: this.reportForm.chartType || 'bar',
            title: this.reportForm.name
          },
          is_public: false
        };

        console.log('Creating report with data:', reportData);

        const response = await this.$http.post('/advanced-reporting/reports', reportData);

        console.log('Report created successfully:', response.data);
        this.$toast.success(this.$t('report_created_successfully'));
        this.$emit('success');
      } catch (error) {
        console.error('Error creating report:', error);
        
        // Handle specific error types
        if (error.response?.status === 422) {
          const errors = error.response.data.errors;
          const errorMessages = Object.values(errors).flat();
          this.$toast.error(errorMessages.join(', '));
        } else if (error.response?.status === 403) {
          this.$toast.error(error.response.data.message || 'Access denied');
        } else if (error.response?.status === 400) {
          this.$toast.error(error.response.data.message || 'Invalid request');
        } else {
          const message = error.response?.data?.message || this.$t('error_creating_report');
          this.$toast.error(message);
        }
      }
    },
    setTemplate(template) {
      this.applyTemplate(template);
    },
    applyTemplate(template) {
      if (template) {
        this.reportForm.name = template.name || this.$t(template.name);
        this.reportForm.description = template.description || this.$t(template.description);
        this.reportForm.category = template.category;

        // Set data source based on template
        if (template.config && template.config.data_source) {
          this.reportForm.dataSource = template.config.data_source;
        } else {
          // Default data source based on category
          this.reportForm.dataSource = template.category === 'financial' ? 'expenses' : 'users';
        }

        // Set default chart type
        if (template.config && template.config.charts && template.config.charts.length > 0) {
          this.reportForm.chartType = template.config.charts[0];
        }
      }
    }
  }
};
</script>

<style scoped>
/* Modal styles */
</style>
