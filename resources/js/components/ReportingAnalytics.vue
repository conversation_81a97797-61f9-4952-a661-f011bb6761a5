<template>
  <div class="reporting-analytics">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">{{ $t('reporting_analytics') }}</h2>
        <p class="text-sm text-gray-600">{{ $t('insights_and_trends_from_your_reports') }}</p>
      </div>
      <div class="flex space-x-3">
        <select
          v-model="selectedTimeRange"
          @change="loadAnalytics"
          class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="7d">{{ $t('last_7_days') }}</option>
          <option value="30d">{{ $t('last_30_days') }}</option>
          <option value="90d">{{ $t('last_90_days') }}</option>
          <option value="1y">{{ $t('last_year') }}</option>
        </select>
        <button
          @click="refreshAnalytics"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          :disabled="loading"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {{ $t('refresh') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div v-for="i in 4" :key="i" class="bg-white rounded-lg shadow p-6 animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div class="h-8 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow p-6 animate-pulse">
        <div class="h-64 bg-gray-200 rounded"></div>
      </div>
    </div>

    <!-- Analytics Content -->
    <div v-else class="space-y-6">
      <!-- Key Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('reports_generated') }}</p>
              <p class="text-2xl font-semibold text-gray-900">
                {{ analytics.reportsGenerated || 0 }}
                <span v-if="analytics.reportsGeneratedChange" :class="[
                  'text-sm ml-2',
                  analytics.reportsGeneratedChange > 0 ? 'text-green-600' : 'text-red-600'
                ]">
                  {{ analytics.reportsGeneratedChange > 0 ? '+' : '' }}{{ analytics.reportsGeneratedChange }}%
                </span>
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('avg_generation_time') }}</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.avgGenerationTime || '0s' }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('most_used_chart') }}</p>
              <p class="text-2xl font-semibold text-gray-900">{{ $t(analytics.mostUsedChart || 'bar_chart') }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('exports_this_period') }}</p>
              <p class="text-2xl font-semibold text-gray-900">{{ analytics.exportsCount || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Report Generation Trend -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ $t('report_generation_trend') }}</h3>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              {{ $t('daily_reports') }}
            </div>
          </div>
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">{{ $t('chart_will_appear_here') }}</p>
              <p class="text-xs text-gray-400">{{ $t('chart_library_integration_needed') }}</p>
            </div>
          </div>
        </div>

        <!-- Chart Type Distribution -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ $t('chart_type_distribution') }}</h3>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
              </svg>
              {{ $t('usage_by_type') }}
            </div>
          </div>
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">{{ $t('pie_chart_will_appear_here') }}</p>
              <p class="text-xs text-gray-400">{{ $t('chart_library_integration_needed') }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Reports -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">{{ $t('most_popular_reports') }}</h3>
          <p class="text-sm text-gray-600">{{ $t('reports_generated_most_frequently') }}</p>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="report in analytics.popularReports || []"
              :key="report.id"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">{{ report.name }}</h4>
                  <p class="text-sm text-gray-600">{{ $t(report.category) }} • {{ $t(report.chart_type + '_chart') }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-lg font-semibold text-gray-900">{{ report.usage_count }}</p>
                <p class="text-sm text-gray-500">{{ $t('times_generated') }}</p>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!analytics.popularReports || analytics.popularReports.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_reports_generated_yet') }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ $t('start_creating_reports_to_see_analytics') }}</p>
          </div>
        </div>
      </div>

      <!-- Insights -->
      <div class="bg-blue-50 rounded-lg p-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-900">{{ $t('insights_and_recommendations') }}</h3>
            <div class="mt-2 text-sm text-blue-800">
              <ul class="list-disc list-inside space-y-1">
                <li v-for="insight in analytics.insights || []" :key="insight">{{ $t(insight) }}</li>
              </ul>
              <div v-if="!analytics.insights || analytics.insights.length === 0">
                <p>{{ $t('no_insights_available_yet') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin';

export default {
  name: 'ReportingAnalytics',
  mixins: [i18nMixin],
  data() {
    return {
      loading: true,
      selectedTimeRange: '30d',
      analytics: {}
    };
  },
  async mounted() {
    await this.loadAnalytics();
  },
  methods: {
    async loadAnalytics() {
      try {
        this.loading = true;

        // Check if user is authenticated
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('No authentication token found');
          this.$toast.error(this.$t('authentication_required'));
          this.analytics = this.getMockAnalytics();
          return;
        }

        // Convert time range to days
        const daysMap = {
          '7d': 7,
          '30d': 30,
          '90d': 90,
          '1y': 365
        };
        const days = daysMap[this.selectedTimeRange] || 30;

        console.log('Loading analytics with token:', token.substring(0, 20) + '...');
        const response = await this.$http.get(`/advanced-reporting/analytics?days=${days}`);
        console.log('Analytics API response:', response);

        // Transform API response to match expected format
        const apiData = response.data;
        this.analytics = {
          reportsGenerated: apiData.summary?.total_generations || 0,
          reportsGeneratedChange: apiData.summary?.generation_change_percent || 0,
          avgGenerationTime: apiData.summary?.avg_generation_time || '0s',
          mostUsedChart: apiData.summary?.most_used_chart_type || 'bar_chart',
          exportsCount: apiData.summary?.total_exports || 0,
          popularReports: apiData.top_reports || [],
          insights: this.generateInsights(apiData)
        };
      } catch (error) {
        console.error('Error loading analytics:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);
        
        // Handle different error types
        if (error.response?.status === 401) {
          this.$toast.error(this.$t('authentication_required'));
        } else if (error.response?.status === 403) {
          this.$toast.error(this.$t('access_denied'));
        } else if (error.response?.status === 404) {
          this.$toast.error(this.$t('advanced_reporting_not_available'));
        } else {
          this.$toast.error(this.$t('error_loading_analytics'));
        }
        
        // Fall back to mock data
        this.analytics = this.getMockAnalytics();
      } finally {
        this.loading = false;
      }
    },
    async refreshAnalytics() {
      await this.loadAnalytics();
      this.$toast.success(this.$t('analytics_refreshed'));
    },
    getMockAnalytics() {
      return {
        reportsGenerated: 24,
        reportsGeneratedChange: 15,
        avgGenerationTime: '2.3s',
        mostUsedChart: 'bar_chart',
        exportsCount: 8,
        popularReports: [
          {
            id: 1,
            name: 'Monthly Financial Summary',
            category: 'financial',
            chart_type: 'bar',
            usage_count: 12
          },
          {
            id: 2,
            name: 'User Activity Report',
            category: 'analytics',
            chart_type: 'line',
            usage_count: 8
          },
          {
            id: 3,
            name: 'Expense Categories',
            category: 'financial',
            chart_type: 'pie',
            usage_count: 6
          }
        ],
        insights: [
          'bar_charts_most_popular_this_month',
          'report_generation_increased_15_percent',
          'financial_reports_generated_most_frequently'
        ]
      };
    },
    generateInsights(apiData) {
      const insights = [];

      if (apiData.summary?.most_used_chart_type) {
        insights.push(`${apiData.summary.most_used_chart_type}_charts_most_popular_this_period`);
      }

      if (apiData.summary?.generation_change_percent > 0) {
        insights.push(`report_generation_increased_${Math.round(apiData.summary.generation_change_percent)}_percent`);
      }

      if (apiData.top_reports && apiData.top_reports.length > 0) {
        const topCategory = apiData.top_reports[0].category;
        insights.push(`${topCategory}_reports_generated_most_frequently`);
      }

      return insights.length > 0 ? insights : [
        'bar_charts_most_popular_this_month',
        'report_generation_increased_15_percent',
        'financial_reports_generated_most_frequently'
      ];
    }
  }
};
</script>

<style scoped>
.reporting-analytics {
  /* Custom styles if needed */
}
</style>
