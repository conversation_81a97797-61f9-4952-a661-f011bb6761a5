<template>
  <div>
    <h2 v-if="!isEdit" class="text-xl font-semibold text-gray-900 mb-6">{{ $t('create_user') }}</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-4 lg:space-y-6">
      <!-- Name and Email in a grid on larger screens -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('name') }}</label>
          <input
            type="text"
            id="name"
            v-model="formData.name"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('email') }}</label>
          <input
            type="email"
            id="email"
            v-model="formData.email"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>
      </div>

      <!-- Phone Number -->
      <div>
        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('phone_number') }}</label>
        <input
          type="tel"
          id="phone_number"
          v-model="formData.phone_number"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 phone-number"
          dir="ltr"
          placeholder="+962 78 123 4567"
        />
      </div>

      <!-- Apartment Number -->
      <div>
        <label for="apartment_number" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('apartment_number') }}</label>
        <input
          type="text"
          id="apartment_number"
          v-model="formData.apartment_number"
          class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <!-- Role and Building fields - only show for super admin -->
      <div v-if="isSuperAdmin" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div>
          <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('role') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="role"
              v-model="formData.role"
              :class="[
                'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
                $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
              ]"
              required
            >
              <option value="">{{ $t('select_role') }}</option>
              <option value="neighbor" v-if="!isSuperAdmin">{{ $t('neighbor') }}</option>
              <option value="admin" v-if="canCreateAdmin">{{ $t('admin') }}</option>
              <option value="super_admin" v-if="isSuperAdmin">{{ $t('super_admin') }}</option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <!-- Package limitation warning for admin role -->
          <div v-if="!canCreateAdmin && packageLimitation" class="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-amber-800">{{ $t('admin_role_limited') }}</p>
                <p class="text-sm text-amber-700">{{ packageLimitation }}</p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <label for="building_id" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('building') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="building_id"
              v-model="formData.building_id"
              :class="[
                'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
                $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
              ]"
              required
            >
              <option value="">{{ $t('select_building') }}</option>
              <option v-for="building in buildings" :key="building.id" :value="building.id">
                {{ building.name }}
              </option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Role field for Regular Admin -->
      <div v-else>
        <div>
          <label for="role_admin" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('role') }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <select
              id="role_admin"
              v-model="formData.role"
              :class="[
                'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
                $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
              ]"
              required
            >
              <option value="">{{ $t('select_role') }}</option>
              <option value="neighbor">{{ $t('neighbor') }}</option>
              <option value="admin" v-if="canCreateAdminAsRegularAdmin">{{ $t('admin') }}</option>
            </select>
            <div :class="[
              'absolute inset-y-0 flex items-center pointer-events-none',
              $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
            ]">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <!-- Package limitation warning for admin role -->
          <div v-if="!canCreateAdminAsRegularAdmin && packageLimitationForRegularAdmin" class="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-amber-800">{{ $t('admin_role_limited') }}</p>
                <p class="text-sm text-amber-700">{{ packageLimitationForRegularAdmin }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Information for Regular Admin -->
        <div v-if="formData.role === 'neighbor'" class="mt-4 bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-blue-800">{{ $t('creating_neighbor_user') }}</p>
              <p class="text-sm text-blue-700">{{ $t('neighbor_user_description') }}</p>
            </div>
          </div>
        </div>

        <!-- Admin Role Information -->
        <div v-if="formData.role === 'admin'" class="mt-4 bg-green-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">{{ $t('creating_admin_user') }}</p>
              <p class="text-sm text-green-700">{{ $t('admin_user_description') }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Field -->
      <div>
        <label for="is_active" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('status') }}</label>
        <div class="relative">
          <select
            id="is_active"
            v-model="formData.is_active"
            :class="[
              'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
              $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
            ]"
          >
            <option :value="true">{{ $t('active') }}</option>
            <option :value="false">{{ $t('inactive') }}</option>
          </select>
          <div :class="[
            'absolute inset-y-0 flex items-center pointer-events-none',
            $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
          ]">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        <p class="mt-1 text-sm text-gray-500">
          {{ $t('inactive_users_excluded_from_monthly_expenses') }}
        </p>
      </div>

      <!-- Password fields in a grid on larger screens -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('password') }} {{ isEdit ? $t('leave_blank_keep_current') : '' }}
          </label>
          <input
            type="password"
            id="password"
            v-model="formData.password"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :required="!isEdit"
          />
        </div>

        <div>
          <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('confirm_password') }}</label>
          <input
            type="password"
            id="password_confirmation"
            v-model="formData.password_confirmation"
            class="w-full px-3 py-2 text-base lg:text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :required="!isEdit"
          />
        </div>
      </div>

      <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? $t('loading') : (isEdit ? $t('edit_user') : $t('create_user')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'UserForm',
  mixins: [i18nMixin],
  props: {
    user: {
      type: Object,
      default: () => ({
        name: '',
        email: '',
        phone_number: '',
        apartment_number: '',
        building_id: null,
        is_active: true,
        password: '',
        password_confirmation: ''
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isSuperAdmin: {
      type: Boolean,
      default: false
    },
    adminBuildingId: {
      type: [Number, String],
      default: null
    },
    buildings: {
      type: Array,
      default: () => []
    },
    packageRestrictions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        name: '',
        email: '',
        phone_number: '',
        apartment_number: '',
        building_id: null,
        role: '',
        is_active: true,
        password: '',
        password_confirmation: ''
      }
    };
  },
  computed: {
    canCreateAdmin() {
      if (!this.isSuperAdmin) return false;

      // If no package restrictions data, allow admin creation (fallback)
      if (!this.packageRestrictions || Object.keys(this.packageRestrictions).length === 0) {
        return true;
      }

      // Check if the selected building supports multiple admins
      const selectedBuilding = this.buildings.find(b => b.id == this.formData.building_id);
      if (!selectedBuilding) return true; // Allow if no building selected yet

      return this.packageRestrictions.can_add_more_admins !== false;
    },
    canCreateAdminAsRegularAdmin() {
      if (this.isSuperAdmin) return false; // This is for regular admins only

      // If no package restrictions data, don't allow admin creation (conservative approach)
      if (!this.packageRestrictions || Object.keys(this.packageRestrictions).length === 0) {
        return false;
      }

      // Check if the package supports multi-admin feature and can add more admins
      return this.packageRestrictions.has_multi_admin_feature &&
             this.packageRestrictions.can_add_more_admins;
    },
    packageLimitation() {
      if (!this.packageRestrictions || this.canCreateAdmin) return null;

      const restrictions = this.packageRestrictions;
      if (restrictions.admin_limit_reached) {
        return this.$t('admin_limit_reached_for_package', {
          current: restrictions.current_admin_count,
          limit: restrictions.admin_limit
        });
      }

      if (!restrictions.has_multi_admin_feature) {
        return this.$t('package_does_not_support_multiple_admins');
      }

      return this.$t('cannot_create_admin_check_package');
    },
    packageLimitationForRegularAdmin() {
      if (!this.packageRestrictions || this.canCreateAdminAsRegularAdmin) return null;

      const restrictions = this.packageRestrictions;
      if (restrictions.admin_limit_reached) {
        return this.$t('admin_limit_reached_for_package', {
          current: restrictions.current_admin_count,
          limit: restrictions.admin_limit
        });
      }

      if (!restrictions.has_multi_admin_feature) {
        return this.$t('package_does_not_support_multiple_admins');
      }

      return this.$t('cannot_create_admin_check_package');
    }
  },
  watch: {
    user: {
      handler(newUser) {
        if (newUser && this.isEdit) {
          this.formData = {
            name: newUser.name || '',
            email: newUser.email || '',
            phone_number: newUser.phone_number || '',
            apartment_number: newUser.apartment_number || '',
            building_id: newUser.building_id || null,
            role: newUser.role || '',
            is_active: newUser.is_active !== false, // Default to true if null/undefined
            password: '',
            password_confirmation: ''
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.user) {
      this.formData = {
        name: this.user.name || '',
        email: this.user.email || '',
        phone_number: this.user.phone_number || '',
        apartment_number: this.user.apartment_number || '',
        building_id: this.user.building_id || null,
        role: this.user.role || '',
        is_active: this.user.is_active !== false, // Default to true if null/undefined
        password: '',
        password_confirmation: ''
      };
    }
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      try {
        const submitData = {
          ...this.formData
        };

        // Remove empty password fields for edit
        if (this.isEdit && !submitData.password) {
          delete submitData.password;
          delete submitData.password_confirmation;
        }

        // Set role based on user type and form selection
        if (this.isSuperAdmin) {
          // Super admin can select any role from the form
          submitData.role = this.formData.role;
        } else {
          // Regular admin can create neighbor or admin users based on package
          submitData.role = this.formData.role || 'neighbor';
          // Regular admin can only create users in their building
          submitData.building_id = this.adminBuildingId;
        }

        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/admin/users/${this.user.id}` : '/admin/users',
          submitData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || this.$t('failed_to_save_user'));
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
