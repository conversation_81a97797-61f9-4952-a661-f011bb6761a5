<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleOutsideClick">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ $t('create_export') }}</h3>
        <button @click="handleCancel" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <form @submit.prevent="createExport" class="py-6">
        <div class="space-y-4">
          <!-- Export Type -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('export_type') }} <span class="text-red-500">*</span>
            </label>
            <select v-model="form.type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">{{ $t('select_export_type') }}</option>
              <option v-for="type in availableTypes" :key="type.key" :value="type.key">
                {{ type.name }}
              </option>
            </select>
          </div>

          <!-- Export Format -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('export_format') }} <span class="text-red-500">*</span>
            </label>
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input type="radio" v-model="form.format" value="pdf" class="mr-2" required>
                <span class="text-sm">PDF</span>
              </label>
              <label class="flex items-center">
                <input type="radio" v-model="form.format" value="excel" class="mr-2" required>
                <span class="text-sm">Excel</span>
              </label>
            </div>
          </div>

          <!-- Date Range -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('from_date') }}
              </label>
              <input 
                type="date" 
                v-model="form.parameters.date_from"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('to_date') }}
              </label>
              <input 
                type="date" 
                v-model="form.parameters.date_to"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
            </div>
          </div>
          
          <!-- Date Range Help Text -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="flex">
              <svg class="h-5 w-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div class="text-sm">
                <p class="text-blue-800 font-medium">{{ $t('date_range_info') }}</p>
                <p class="text-blue-700 mt-1">{{ $t('default_date_range_help') }}</p>
              </div>
            </div>
          </div>

          <!-- Include Attachments -->
          <div v-if="form.type === 'expense_report'">
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="form.parameters.include_attachments"
                class="mr-2"
              >
              <span class="text-sm">{{ $t('include_attachments') }}</span>
            </label>
          </div>

          <!-- Export Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('description') }} ({{ $t('optional') }})
            </label>
            <textarea 
              v-model="form.parameters.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('export_description_placeholder')"
            ></textarea>
          </div>

          <!-- Quota Information -->
          <div v-if="quotaInfo" class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="flex">
              <svg class="h-5 w-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div class="text-sm">
                <p class="text-blue-800 font-medium">{{ $t('export_quota_info') }}</p>
                <p class="text-blue-700 mt-1">
                  {{ $t('monthly_exports_used') }}: {{ quotaInfo.monthly_used }}/{{ quotaInfo.monthly_limit }}
                </p>
                <p v-if="quotaInfo.remaining_today" class="text-blue-700">
                  {{ $t('remaining_today') }}: {{ quotaInfo.remaining_today }}
                </p>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-lg p-3">
            <div class="flex">
              <svg class="h-5 w-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div class="text-sm">
                <p class="text-red-800 font-medium">{{ $t('error') }}</p>
                <p class="text-red-700 mt-1">{{ errorMessage }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            {{ $t('cancel') }}
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isSubmitting ? $t('creating') : $t('create_export') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'CreateExportModal',
  mixins: [i18nMixin, smartModalMixin],
  data() {
    return {
      isSubmitting: false,
      errorMessage: '',
      availableTypes: [],
      quotaInfo: null,
      form: {
        type: '',
        format: 'pdf',
        parameters: {
          date_from: '2025-06-01', // Default to June 2025 when data was created
          date_to: '2025-08-02',   // Default to August 2, 2025 (latest data date)
          include_attachments: false,
          description: ''
        }
      }
    };
  },
  async mounted() {
    await this.loadAvailableTypes();
    // Initialize form tracking
    this.$nextTick(() => {
      this.initializeFormTracking();
    });
  },
  methods: {
    async loadAvailableTypes() {
      try {
        const response = await this.$axios.get('/exports/types');
        this.availableTypes = Object.entries(response.data.available_types || {}).map(([key, config]) => ({
          key,
          name: this.$t(config.name) || config.name,
          description: config.description
        }));
        this.quotaInfo = response.data.quota_info;
      } catch (error) {
        console.error('Error loading export types:', error);
        this.errorMessage = this.$t('error_loading_export_types');
      }
    },

    async createExport() {
      this.isSubmitting = true;
      this.errorMessage = '';
      
      try {
        // Validate required fields
        if (!this.form.type) {
          this.errorMessage = this.$t('please_select_export_type');
          return;
        }

        if (!this.form.format) {
          this.errorMessage = this.$t('please_select_export_format');
          return;
        }

        // Clean up parameters - remove empty values
        const parameters = {};
        if (this.form.parameters.date_from) parameters.date_from = this.form.parameters.date_from;
        if (this.form.parameters.date_to) parameters.date_to = this.form.parameters.date_to;
        if (this.form.parameters.include_attachments) parameters.include_attachments = this.form.parameters.include_attachments;
        if (this.form.parameters.description) parameters.description = this.form.parameters.description;

        const payload = {
          type: this.form.type,
          format: this.form.format,
          parameters
        };

        const response = await this.$axios.post('/exports/create', payload);
        
        this.$emit('success', response.data);
        this.$toast.success(this.$t('export_created_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error creating export:', error);
        const message = error.response?.data?.message || this.$t('export_creation_failed');
        this.errorMessage = message;
        this.$toast.error(message);
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>
