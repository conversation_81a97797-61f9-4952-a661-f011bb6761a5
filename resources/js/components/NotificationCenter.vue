<template>
  <div class="relative">
    <!-- Notification Bell Button -->
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full"
      :class="{ 'text-blue-600': hasUnread }"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      
      <!-- Unread Count Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Notification Dropdown -->
    <div
      v-if="showDropdown"
      class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
      @click.stop
    >
      <!-- Header -->
      <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">{{ $t('notifications') }}</h3>
        <div class="flex items-center space-x-2">
          <button
            v-if="unreadCount > 0"
            @click="markAllAsRead"
            class="text-sm text-blue-600 hover:text-blue-800"
          >
            {{ $t('mark_all_read') }}
          </button>
          <button
            @click="showDropdown = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="px-4 py-2 border-b border-gray-100">
        <div class="flex space-x-2">
          <button
            @click="currentFilter = 'all'"
            :class="filterButtonClass('all')"
            class="px-3 py-1 text-xs rounded-full"
          >
            {{ $t('all') }}
          </button>
          <button
            @click="currentFilter = 'unread'"
            :class="filterButtonClass('unread')"
            class="px-3 py-1 text-xs rounded-full"
          >
            {{ $t('unread') }}
          </button>
        </div>
      </div>

      <!-- Notifications List -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="loading" class="p-4 text-center text-gray-500">
          {{ $t('loading') }}...
        </div>
        
        <div v-else-if="filteredNotifications.length === 0" class="p-4 text-center text-gray-500">
          {{ $t('no_notifications') }}
        </div>
        
        <div v-else>
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="border-b border-gray-100 last:border-b-0"
          >
            <div
              class="p-4 hover:bg-gray-50 cursor-pointer"
              :class="{ 'bg-blue-50': !notification.read_at }"
              @click="markAsRead(notification)"
            >
              <div class="flex items-start space-x-3">
                <!-- Notification Icon -->
                <div class="flex-shrink-0">
                  <div
                    class="w-8 h-8 rounded-full flex items-center justify-center"
                    :class="getNotificationIconClass(notification.type)"
                  >
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path :d="getNotificationIconPath(notification.type)" />
                    </svg>
                  </div>
                </div>

                <!-- Notification Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ notification.title }}
                    </p>
                    <div class="flex items-center space-x-2">
                      <span
                        class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                        :class="getPriorityClass(notification.priority)"
                      >
                        {{ $t(notification.priority) }}
                      </span>
                      <span
                        v-if="!notification.read_at"
                        class="w-2 h-2 bg-blue-500 rounded-full"
                      ></span>
                    </div>
                  </div>
                  
                  <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                    {{ notification.message }}
                  </p>
                  
                  <div class="flex items-center justify-between mt-2">
                    <span class="text-xs text-gray-500">
                      {{ formatDate(notification.created_at) }}
                    </span>
                    
                    <div class="flex items-center space-x-1">
                      <button
                        v-if="!notification.read_at"
                        @click.stop="markAsRead(notification)"
                        class="text-xs text-blue-600 hover:text-blue-800"
                      >
                        {{ $t('mark_read') }}
                      </button>
                      <button
                        @click.stop="deleteNotification(notification)"
                        class="text-xs text-red-600 hover:text-red-800"
                      >
                        {{ $t('delete') }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="px-4 py-3 border-t border-gray-200 text-center">
        <router-link
          to="/notifications"
          class="text-sm text-blue-600 hover:text-blue-800"
          @click="showDropdown = false"
        >
          {{ $t('view_all_notifications') }}
        </router-link>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="showDropdown"
      class="fixed inset-0 z-40"
      @click="showDropdown = false"
    ></div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'NotificationCenter',
  mixins: [i18nMixin],
  data() {
    return {
      showDropdown: false,
      notifications: [],
      unreadCount: 0,
      loading: false,
      currentFilter: 'all',
      refreshInterval: null,
    };
  },
  computed: {
    isAuthenticated() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      return !!token && !!user && user !== 'null';
    },
    hasUnread() {
      return this.unreadCount > 0;
    },
    filteredNotifications() {
      if (this.currentFilter === 'unread') {
        return this.notifications.filter(n => !n.read_at);
      }
      return this.notifications;
    },
  },
  mounted() {
    // Only fetch notifications if user is authenticated
    if (this.isAuthenticated) {
      this.fetchNotifications();
      this.fetchUnreadCount();

      // Set up auto-refresh every 30 seconds
      this.refreshInterval = setInterval(() => {
        // Only fetch if still authenticated
        if (this.isAuthenticated) {
          this.fetchUnreadCount();
          if (this.showDropdown) {
            this.fetchNotifications();
          }
        }
      }, 30000);
    }
  },
  beforeDestroy() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    // Remove event listeners
    window.removeEventListener('auth-state-changed', this.handleAuthStateChange);
  },
  created() {
    // Listen for authentication state changes
    window.addEventListener('auth-state-changed', this.handleAuthStateChange);
  },
  methods: {
    async toggleDropdown() {
      this.showDropdown = !this.showDropdown;
      if (this.showDropdown && this.isAuthenticated) {
        await this.fetchNotifications();
      }
    },
    
    async fetchNotifications() {
      if (!this.isAuthenticated) {
        return;
      }

      this.loading = true;
      try {
        const response = await this.$axios.get('/notifications', {
          params: {
            per_page: 20,
            unread_only: this.currentFilter === 'unread'
          }
        });
        this.notifications = response.data.notifications.data;
        this.unreadCount = response.data.unread_count;
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        // If authentication fails, clear the data
        if (error.response?.status === 401) {
          this.notifications = [];
          this.unreadCount = 0;
        }
      } finally {
        this.loading = false;
      }
    },
    
    async fetchUnreadCount() {
      if (!this.isAuthenticated) {
        this.unreadCount = 0;
        return;
      }

      try {
        const response = await this.$axios.get('/notifications/unread-count');
        this.unreadCount = response.data.unread_count;
      } catch (error) {
        console.error('Failed to fetch unread count:', error);
        // If authentication fails, reset the count
        if (error.response?.status === 401) {
          this.unreadCount = 0;
        }
      }
    },
    
    async markAsRead(notification) {
      if (notification.read_at || !this.isAuthenticated) return;

      try {
        await this.$axios.put(`/notifications/${notification.id}/mark-as-read`);
        notification.read_at = new Date().toISOString();
        this.unreadCount = Math.max(0, this.unreadCount - 1);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },
    
    async markAllAsRead() {
      if (!this.isAuthenticated) return;

      try {
        await this.$axios.put('/notifications/mark-all-as-read');
        this.notifications.forEach(n => {
          if (!n.read_at) {
            n.read_at = new Date().toISOString();
          }
        });
        this.unreadCount = 0;
      } catch (error) {
        console.error('Failed to mark all notifications as read:', error);
      }
    },
    
    async deleteNotification(notification) {
      if (!this.isAuthenticated || !confirm(this.$t('confirm_delete_notification'))) return;

      try {
        await this.$axios.delete(`/notifications/${notification.id}`);
        this.notifications = this.notifications.filter(n => n.id !== notification.id);
        if (!notification.read_at) {
          this.unreadCount = Math.max(0, this.unreadCount - 1);
        }
      } catch (error) {
        console.error('Failed to delete notification:', error);
      }
    },

    handleAuthStateChange() {
      // Clear notification data when user logs out
      if (!this.isAuthenticated) {
        this.notifications = [];
        this.unreadCount = 0;
        this.showDropdown = false;
        // Clear the refresh interval
        if (this.refreshInterval) {
          clearInterval(this.refreshInterval);
          this.refreshInterval = null;
        }
      } else {
        // User logged in, start fetching notifications
        this.fetchNotifications();
        this.fetchUnreadCount();

        // Restart the refresh interval if it's not already running
        if (!this.refreshInterval) {
          this.refreshInterval = setInterval(() => {
            if (this.isAuthenticated) {
              this.fetchUnreadCount();
              if (this.showDropdown) {
                this.fetchNotifications();
              }
            }
          }, 30000);
        }
      }
    },

    filterButtonClass(filter) {
      return this.currentFilter === filter
        ? 'bg-blue-100 text-blue-800'
        : 'bg-gray-100 text-gray-600 hover:bg-gray-200';
    },
    
    getNotificationIconClass(type) {
      const classes = {
        payment_reminder: 'bg-yellow-100 text-yellow-600',
        expense_created: 'bg-blue-100 text-blue-600',
        income_received: 'bg-green-100 text-green-600',
        general_announcement: 'bg-gray-100 text-gray-600',
        payment_received: 'bg-green-100 text-green-600',
        overdue_payment: 'bg-red-100 text-red-600',
      };
      return classes[type] || 'bg-gray-100 text-gray-600';
    },
    
    getNotificationIconPath(type) {
      const paths = {
        payment_reminder: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        expense_created: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        income_received: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        general_announcement: 'M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z',
        payment_received: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        overdue_payment: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z',
      };
      return paths[type] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    },
    
    getPriorityClass(priority) {
      const classes = {
        high: 'bg-red-100 text-red-800',
        medium: 'bg-yellow-100 text-yellow-800',
        low: 'bg-green-100 text-green-800',
      };
      return classes[priority] || 'bg-gray-100 text-gray-800';
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      
      if (diffInMinutes < 1) return this.$t('just_now');
      if (diffInMinutes < 60) return `${diffInMinutes} ${this.$t('minutes_ago')}`;
      
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours} ${this.$t('hours_ago')}`;
      
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 7) return `${diffInDays} ${this.$t('days_ago')}`;
      
      return date.toLocaleDateString();
    },
  },
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
