<template>
  <div class="package-usage-dashboard">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $t('package_usage') }}</h3>
      <p class="text-gray-600">{{ $t('package_usage_description') }}</p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">{{ $t('loading_usage_data') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <div>
          <h3 class="text-sm font-medium text-red-800">{{ $t('error') }}</h3>
          <p class="text-sm text-red-700 mt-1">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Usage Dashboard -->
    <div v-else class="space-y-6">
      <!-- Current Package Info -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900">{{ $t('current_package') }}</h4>
          <button
            @click="$emit('upgrade-package')"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            {{ $t('upgrade') }}
          </button>
        </div>
        
        <div v-if="usage.subscription" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm text-gray-600">{{ $t('package_name') }}</p>
            <p class="font-medium text-lg">{{ getPackageName(usage.package) }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">{{ $t('status') }}</p>
            <span :class="getStatusClass(usage.subscription.status)">
              {{ $t(`subscription_status_${usage.subscription.status}`) }}
            </span>
          </div>
          <div>
            <p class="text-sm text-gray-600">{{ $t('expires_on') }}</p>
            <p class="font-medium">{{ formatDate(usage.subscription.ends_at) }}</p>
            <p v-if="usage.subscription.days_remaining <= 7" class="text-sm text-orange-600">
              {{ $t('days_remaining', { days: usage.subscription.days_remaining }) }}
            </p>
          </div>
        </div>
      </div>

      <!-- Usage Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Neighbors Usage -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h5 class="text-lg font-medium text-gray-900">{{ $t('neighbors_usage') }}</h5>
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900">{{ usage.usage.neighbors.current }}</span>
              <span v-if="!usage.usage.neighbors.unlimited" class="text-sm text-gray-600">
                / {{ usage.usage.neighbors.limit }}
              </span>
            </div>

            <div v-if="!usage.usage.neighbors.unlimited" class="w-full bg-gray-200 rounded-full h-2">
              <div
                :class="[
                  'h-2 rounded-full transition-all duration-300',
                  usage.usage.neighbors.limit_exceeded ? 'bg-red-500' :
                  usage.usage.neighbors.percentage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                ]"
                :style="{ width: Math.min(usage.usage.neighbors.percentage, 100) + '%' }"
              ></div>
            </div>

            <p class="text-sm text-gray-600">{{ getNeighborsDescription() }}</p>
          </div>
        </div>

        <!-- Storage Usage -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h5 class="text-lg font-medium text-gray-900">{{ $t('storage_usage') }}</h5>
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
            </svg>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900">{{ usage.usage.storage.used_gb }}GB</span>
              <span v-if="!usage.usage.storage.unlimited" class="text-sm text-gray-600">
                / {{ usage.usage.storage.limit_gb }}GB
              </span>
            </div>

            <div v-if="!usage.usage.storage.unlimited" class="w-full bg-gray-200 rounded-full h-2">
              <div
                :class="[
                  'h-2 rounded-full transition-all duration-300',
                  usage.usage.storage.limit_exceeded ? 'bg-red-500' :
                  usage.usage.storage.percentage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                ]"
                :style="{ width: Math.min(usage.usage.storage.percentage, 100) + '%' }"
              ></div>
            </div>

            <p class="text-sm text-gray-600">{{ getStorageDescription() }}</p>
          </div>
        </div>

        <!-- Notifications Usage -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h5 class="text-lg font-medium text-gray-900">{{ $t('notifications_usage') }}</h5>
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.868 19.462A17.013 17.013 0 003 12c0-9.941 8.059-18 18-18v9.94" />
            </svg>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900">{{ usage.usage.notifications.sent_this_month }}</span>
              <span :class="[
                'px-2 py-1 text-xs font-medium rounded-full',
                usage.usage.notifications.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              ]">
                {{ usage.usage.notifications.enabled ? $t('enabled') : $t('disabled') }}
              </span>
            </div>

            <p class="text-sm text-gray-600">{{ $t('notifications_sent_this_month') }}</p>
          </div>
        </div>

        <!-- Email Usage -->
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h5 class="text-lg font-medium text-gray-900">{{ $t('email_usage') }}</h5>
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900">{{ usage.usage.emails.sent_this_month }}</span>
              <span :class="[
                'px-2 py-1 text-xs font-medium rounded-full',
                usage.usage.emails.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              ]">
                {{ usage.usage.emails.enabled ? $t('enabled') : $t('disabled') }}
              </span>
            </div>

            <p class="text-sm text-gray-600">{{ $t('emails_sent_this_month') }}</p>
          </div>
        </div>
      </div>

      <!-- Features Status -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-medium text-gray-900 mb-4">{{ $t('available_features') }}</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- File Attachments -->
          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
            <div :class="[
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
              usage.usage.features.file_attachments ? 'bg-green-100' : 'bg-gray-100'
            ]">
              <svg :class="[
                'w-4 h-4',
                usage.usage.features.file_attachments ? 'text-green-600' : 'text-gray-400'
              ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">{{ $t('file_attachments') }}</p>
              <p class="text-sm text-gray-600">
                {{ usage.usage.features.file_attachments
                  ? $t('max_file_size_mb', { size: usage.usage.features.max_file_size_mb })
                  : $t('feature_not_available') }}
              </p>
            </div>
          </div>

          <!-- Priority Support -->
          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
            <div :class="[
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
              usage.usage.features.priority_support ? 'bg-green-100' : 'bg-gray-100'
            ]">
              <svg :class="[
                'w-4 h-4',
                usage.usage.features.priority_support ? 'text-green-600' : 'text-gray-400'
              ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">{{ $t('priority_support') }}</p>
              <p class="text-sm text-gray-600">
                {{ usage.usage.features.priority_support
                  ? $t('priority_support_available')
                  : $t('standard_support_only') }}
              </p>
            </div>
          </div>

          <!-- Advanced Reporting -->
          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
            <div :class="[
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
              usage.usage.features.advanced_reporting ? 'bg-green-100' : 'bg-gray-100'
            ]">
              <svg :class="[
                'w-4 h-4',
                usage.usage.features.advanced_reporting ? 'text-green-600' : 'text-gray-400'
              ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">{{ $t('advanced_reporting') }}</p>
              <p class="text-sm text-gray-600">
                {{ usage.usage.features.advanced_reporting
                  ? $t('advanced_reports_available')
                  : $t('basic_reports_only') }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Upgrade Recommendation -->
      <div v-if="usage.upgrade_recommended" class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
          <svg class="h-6 w-6 text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="flex-1">
            <h4 class="text-lg font-medium text-blue-900 mb-2">{{ $t('upgrade_recommended') }}</h4>
            <p class="text-blue-700 mb-4">{{ $t('upgrade_recommendation_message') }}</p>
            <button
              @click="$emit('upgrade-package')"
              class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ $t('view_upgrade_options') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Limits Exceeded Warning -->
      <div v-if="hasLimitsExceeded" class="bg-red-50 border border-red-200 rounded-lg p-6">
        <div class="flex items-start">
          <svg class="h-6 w-6 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <div class="flex-1">
            <h4 class="text-lg font-medium text-red-900 mb-2">{{ $t('limits_exceeded') }}</h4>
            <p class="text-red-700 mb-4">{{ $t('limits_exceeded_message') }}</p>
            <ul class="text-sm text-red-600 space-y-1 mb-4">
              <li v-if="usage.limits_exceeded.neighbors">• {{ $t('neighbor_limit_exceeded') }}</li>
              <li v-if="usage.limits_exceeded.storage">• {{ $t('storage_limit_exceeded') }}</li>
              <li v-if="usage.limits_exceeded.notifications">• {{ $t('notifications_not_available') }}</li>
              <li v-if="usage.limits_exceeded.email_notifications">• {{ $t('email_notifications_not_available') }}</li>
              <li v-if="usage.limits_exceeded.file_attachments">• {{ $t('file_attachments_not_available') }}</li>
            </ul>
            <button
              @click="$emit('upgrade-package')"
              class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              {{ $t('upgrade_now') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
// Import components inline to avoid separate files for now

export default {
  name: 'PackageUsageDashboard',
  mixins: [i18nMixin],
  components: {
    // Components are now inline
  },
  data() {
    return {
      usage: null,
      loading: true,
      error: null
    };
  },
  computed: {
    hasLimitsExceeded() {
      if (!this.usage?.limits_exceeded) return false;
      return Object.values(this.usage.limits_exceeded).some(exceeded => exceeded);
    }
  },
  mounted() {
    this.loadUsageData();
  },
  methods: {
    async loadUsageData() {
      this.loading = true;
      this.error = null;

      try {
        const response = await this.$axios.get('/subscription/usage');
        this.usage = response.data;
      } catch (error) {
        console.error('Error loading usage data:', error);
        this.error = error.response?.data?.message || this.$t('error_loading_usage_data');
      } finally {
        this.loading = false;
      }
    },

    getStatusClass(status) {
      const classes = {
        active: 'inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full',
        trial: 'inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full',
        expired: 'inline-flex px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full',
        cancelled: 'inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full'
      };
      return classes[status] || classes.active;
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US');
    },

    getNeighborsDescription() {
      if (!this.usage?.usage?.neighbors) return '';
      const neighbors = this.usage.usage.neighbors;
      
      if (neighbors.unlimited) {
        return this.$t('unlimited_neighbors_available');
      }
      
      return this.$t('neighbors_limit_description', {
        current: neighbors.current,
        limit: neighbors.limit
      });
    },

    getStorageDescription() {
      if (!this.usage?.usage?.storage) return '';
      const storage = this.usage.usage.storage;
      
      if (storage.unlimited) {
        return this.$t('unlimited_storage_available');
      }
      
      return this.$t('storage_limit_description', {
        used: storage.used_gb,
        limit: storage.limit_gb
      });
    },

    // Public method to refresh data
    refresh() {
      this.loadUsageData();
    },

    getPackageName(pkg) {
      if (!pkg) return '';
      if (this.$locale() === 'en' && pkg.name_en) {
        return pkg.name_en;
      }
      return pkg.name;
    }
  }
};
</script>

<style scoped>
.package-usage-dashboard {
  max-width: 72rem;
  margin-left: auto;
  margin-right: auto;
}
</style>
