<template>
  <div v-if="showMonitor" class="fixed bottom-4 left-4 bg-gray-900 text-white p-3 rounded-lg shadow-lg text-xs z-50">
    <div class="flex items-center justify-between mb-2">
      <h4 class="font-semibold">Performance Monitor</h4>
      <button @click="toggleMonitor" class="text-gray-400 hover:text-white">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="space-y-1">
      <div class="flex justify-between">
        <span>FPS:</span>
        <span :class="fpsColor">{{ fps }}</span>
      </div>
      
      <div class="flex justify-between">
        <span>Memory:</span>
        <span>{{ memoryUsage }}MB</span>
      </div>
      
      <div class="flex justify-between">
        <span>Load Time:</span>
        <span>{{ loadTime }}ms</span>
      </div>
      
      <div class="flex justify-between">
        <span>Route:</span>
        <span class="truncate max-w-20">{{ currentRoute }}</span>
      </div>
      
      <div v-if="networkInfo" class="flex justify-between">
        <span>Network:</span>
        <span>{{ networkInfo }}</span>
      </div>
    </div>
    
    <div class="mt-2 pt-2 border-t border-gray-700">
      <div class="flex justify-between text-xs">
        <button @click="clearCache" class="text-blue-400 hover:text-blue-300">
          Clear Cache
        </button>
        <button @click="exportMetrics" class="text-green-400 hover:text-green-300">
          Export
        </button>
      </div>
    </div>
  </div>
  
  <!-- Toggle button when monitor is hidden -->
  <button 
    v-else 
    @click="toggleMonitor" 
    class="fixed bottom-4 left-4 bg-gray-900 text-white p-2 rounded-full shadow-lg z-50"
    title="Show Performance Monitor"
  >
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
  </button>
</template>

<script>
export default {
  name: 'PerformanceMonitor',
  data() {
    return {
      showMonitor: false,
      fps: 0,
      memoryUsage: 0,
      loadTime: 0,
      currentRoute: '',
      networkInfo: '',
      frameCount: 0,
      lastTime: performance.now(),
      metrics: [],
    };
  },
  
  computed: {
    fpsColor() {
      if (this.fps >= 50) return 'text-green-400';
      if (this.fps >= 30) return 'text-yellow-400';
      return 'text-red-400';
    }
  },
  
  mounted() {
    // Only show in development mode
    if (import.meta.env.DEV) {
      this.initializeMonitoring();
      this.startFPSMonitoring();
      this.getNetworkInfo();
      this.measureLoadTime();
    }
  },
  
  beforeUnmount() {
    if (this.fpsInterval) {
      clearInterval(this.fpsInterval);
    }
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }
  },
  
  watch: {
    '$route'(to) {
      this.currentRoute = to.name || to.path;
      this.recordRouteChange(to);
    }
  },
  
  methods: {
    toggleMonitor() {
      this.showMonitor = !this.showMonitor;
      localStorage.setItem('performance-monitor-visible', this.showMonitor);
    },
    
    initializeMonitoring() {
      // Restore monitor visibility from localStorage
      const saved = localStorage.getItem('performance-monitor-visible');
      this.showMonitor = saved === 'true';
      
      // Update current route
      this.currentRoute = this.$route.name || this.$route.path;
      
      // Start metrics collection
      this.metricsInterval = setInterval(() => {
        this.collectMetrics();
      }, 1000);
    },
    
    startFPSMonitoring() {
      let frames = 0;
      let lastTime = performance.now();
      
      const countFrame = () => {
        frames++;
        const currentTime = performance.now();
        
        if (currentTime >= lastTime + 1000) {
          this.fps = Math.round((frames * 1000) / (currentTime - lastTime));
          frames = 0;
          lastTime = currentTime;
        }
        
        requestAnimationFrame(countFrame);
      };
      
      requestAnimationFrame(countFrame);
    },
    
    collectMetrics() {
      // Memory usage
      if (performance.memory) {
        this.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      }
      
      // Store metrics for export
      this.metrics.push({
        timestamp: Date.now(),
        fps: this.fps,
        memory: this.memoryUsage,
        route: this.currentRoute,
      });
      
      // Keep only last 100 metrics
      if (this.metrics.length > 100) {
        this.metrics = this.metrics.slice(-100);
      }
    },
    
    measureLoadTime() {
      // Get navigation timing
      const navigation = performance.getEntriesByType('navigation')[0];
      if (navigation) {
        this.loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart);
      }
    },
    
    getNetworkInfo() {
      if ('connection' in navigator) {
        const connection = navigator.connection;
        this.networkInfo = `${connection.effectiveType} ${connection.downlink}Mbps`;
        
        // Listen for network changes
        connection.addEventListener('change', () => {
          this.networkInfo = `${connection.effectiveType} ${connection.downlink}Mbps`;
        });
      } else {
        this.networkInfo = navigator.onLine ? 'Online' : 'Offline';
        
        window.addEventListener('online', () => {
          this.networkInfo = 'Online';
        });
        
        window.addEventListener('offline', () => {
          this.networkInfo = 'Offline';
        });
      }
    },
    
    recordRouteChange(route) {
      const routeMetric = {
        timestamp: Date.now(),
        route: route.name || route.path,
        loadTime: performance.now(),
      };
      
      // Measure route transition time
      this.$nextTick(() => {
        routeMetric.renderTime = performance.now() - routeMetric.loadTime;
        console.log('Route transition:', routeMetric);
      });
    },
    
    async clearCache() {
      try {
        if ('caches' in window) {
          const cacheNames = await caches.keys();
          await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          );
          console.log('All caches cleared');
          
          // Show notification
          this.showNotification('Cache cleared successfully', 'success');
        }
      } catch (error) {
        console.error('Failed to clear cache:', error);
        this.showNotification('Failed to clear cache', 'error');
      }
    },
    
    exportMetrics() {
      const data = {
        timestamp: new Date().toISOString(),
        metrics: this.metrics,
        browser: {
          userAgent: navigator.userAgent,
          language: navigator.language,
          platform: navigator.platform,
        },
        performance: {
          memory: performance.memory ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit,
          } : null,
          timing: performance.getEntriesByType('navigation')[0],
        }
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-metrics-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      this.showNotification('Metrics exported successfully', 'success');
    },
    
    showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 p-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600' : 'bg-red-600'
      } text-white`;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 3000);
    }
  }
};
</script>
