<template>
  <div class="mobile-navigation">
    <!-- Mobile Bottom Navigation (PWA Style) -->
    <nav v-if="isMobile && isAuthenticated" 
         class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-inset-bottom">
      <div class="flex justify-around items-center py-2">
        <!-- Dashboard -->
        <router-link 
          :to="dashboardRoute" 
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': isActiveRoute('Dashboard') }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
          </svg>
          <span class="text-xs truncate">{{ $t('dashboard') }}</span>
        </router-link>

        <!-- Expenses (Admin only) -->
        <router-link
          v-if="isAdmin"
          to="/admin/expenses"
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': isActiveRoute('AdminExpenses') }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
          <span class="text-xs truncate">{{ $t('expenses') }}</span>
        </router-link>

        <!-- Building Expenses (Admin only) -->
        <router-link
          v-if="isAdmin"
          to="/admin/building-expenses"
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': isActiveRoute('AdminBuildingExpenses') }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          <span class="text-xs truncate">{{ $t('building_expenses') }}</span>
        </router-link>

        <!-- Building Expenses (Admin only) -->
        <router-link
          v-if="isAdmin"
          to="/admin/building-expenses"
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': isActiveRoute('AdminBuildingExpenses') }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          <span class="text-xs truncate">{{ $t('building_expenses') }}</span>
        </router-link>

        <!-- Incomes (Admin only) -->
        <router-link 
          v-if="isAdmin" 
          to="/admin/incomes" 
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': isActiveRoute('AdminIncomes') }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
          <span class="text-xs truncate">{{ $t('incomes') }}</span>
        </router-link>

        <!-- Notifications -->
        <button 
          @click="toggleNotifications" 
          class="flex flex-col items-center p-2 min-w-0 flex-1 relative"
          :class="{ 'text-blue-600': showNotifications }"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
          </svg>
          <span class="text-xs truncate">{{ $t('notifications') }}</span>
          <span v-if="unreadCount > 0" 
                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {{ unreadCount > 9 ? '9+' : unreadCount }}
          </span>
        </button>

        <!-- Profile/Menu -->
        <button 
          @click="toggleProfileMenu" 
          class="flex flex-col items-center p-2 min-w-0 flex-1"
          :class="{ 'text-blue-600': showProfileMenu }"
        >
          <div class="w-6 h-6 mb-1 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
            {{ user?.name?.charAt(0)?.toUpperCase() }}
          </div>
          <span class="text-xs truncate">{{ $t('profile') }}</span>
        </button>
      </div>
    </nav>

    <!-- Mobile Notifications Panel -->
    <div v-if="showNotifications && isMobile" 
         class="fixed inset-0 bg-black bg-opacity-50 z-50"
         @click="showNotifications = false">
      <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-lg max-h-96 overflow-hidden"
           @click.stop>
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">{{ $t('notifications') }}</h3>
            <button @click="showNotifications = false" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="overflow-y-auto max-h-80">
          <NotificationCenter :mobile="true" />
        </div>
      </div>
    </div>

    <!-- Mobile Profile Menu -->
    <div v-if="showProfileMenu && isMobile" 
         class="fixed inset-0 bg-black bg-opacity-50 z-50"
         @click="showProfileMenu = false">
      <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-lg"
           @click.stop>
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                {{ user?.name?.charAt(0)?.toUpperCase() }}
              </div>
              <div class="ml-3">
                <p class="font-semibold">{{ user?.name }}</p>
                <p class="text-sm text-gray-500">{{ user?.email }}</p>
              </div>
            </div>
            <button @click="showProfileMenu = false" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="p-4 space-y-3">
          <!-- Profile Link -->
          <router-link 
            v-if="isNeighbor" 
            to="/neighbor/profile" 
            @click="showProfileMenu = false"
            class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <svg class="w-5 h-5 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
            <span>{{ $t('my_profile') }}</span>
          </router-link>

          <!-- Settings -->
          <button 
            @click="openSettings"
            class="flex items-center w-full p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <svg class="w-5 h-5 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <span>{{ $t('settings') }}</span>
          </button>

          <!-- Language Toggle -->
          <LanguageToggle :mobile="true" />

          <!-- Logout -->
          <button 
            @click="logout"
            class="flex items-center w-full p-3 rounded-lg hover:bg-red-50 text-red-600 transition-colors text-left"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
            </svg>
            <span>{{ $t('logout') }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Spacer for bottom navigation -->
    <div v-if="isMobile && isAuthenticated" class="h-16"></div>
  </div>
</template>

<script>
import NotificationCenter from './NotificationCenter.vue';
import LanguageToggle from './LanguageToggle.vue';

export default {
  name: 'MobileNavigation',
  components: {
    NotificationCenter,
    LanguageToggle
  },
  
  data() {
    return {
      showNotifications: false,
      showProfileMenu: false,
      unreadCount: 0,
    };
  },
  
  computed: {
    isMobile() {
      return window.innerWidth < 768;
    },
    
    isAuthenticated() {
      return !!localStorage.getItem('token');
    },
    
    user() {
      return JSON.parse(localStorage.getItem('user') || 'null');
    },
    
    isAdmin() {
      return this.user?.role === 'admin' || this.user?.role === 'super_admin';
    },
    
    isNeighbor() {
      return this.user?.role === 'neighbor';
    },
    
    dashboardRoute() {
      return this.isAdmin ? '/admin' : '/neighbor';
    }
  },
  
  mounted() {
    // Listen for window resize
    window.addEventListener('resize', this.handleResize);
    
    // Load notification count
    this.loadNotificationCount();
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  
  methods: {
    handleResize() {
      // Close mobile menus on resize
      if (!this.isMobile) {
        this.showNotifications = false;
        this.showProfileMenu = false;
      }
    },
    
    isActiveRoute(routeName) {
      return this.$route.name === routeName;
    },
    
    toggleNotifications() {
      this.showNotifications = !this.showNotifications;
      this.showProfileMenu = false;
    },
    
    toggleProfileMenu() {
      this.showProfileMenu = !this.showProfileMenu;
      this.showNotifications = false;
    },
    
    openSettings() {
      this.showProfileMenu = false;
      // Navigate to settings or open settings modal
      console.log('Open settings');
    },
    
    async loadNotificationCount() {
      if (!this.isAuthenticated) {
        this.unreadCount = 0;
        return;
      }

      try {
        const response = await this.$axios.get('/notifications/unread-count');
        this.unreadCount = response.data.unread_count || 0;
      } catch (error) {
        console.error('Failed to load notification count:', error);
        // If authentication fails, reset the count
        if (error.response?.status === 401) {
          this.unreadCount = 0;
        }
      }
    },
    
    async logout() {
      try {
        await this.$axios.post('/logout');
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        this.$router.push('/login');
      }
    }
  }
};
</script>

<style scoped>
.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-navigation {
  /* Ensure proper z-index stacking */
  position: relative;
  z-index: 40;
}

/* Smooth transitions for mobile panels */
.mobile-navigation .absolute {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Touch-friendly tap targets */
.mobile-navigation button,
.mobile-navigation a {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent text selection on mobile navigation */
.mobile-navigation {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>
