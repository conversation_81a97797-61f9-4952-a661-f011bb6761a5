<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="max-w-3xl mx-auto text-center mb-12">
      <router-link to="/" class="inline-flex items-center space-x-2 mb-8">
        <img src="images/logo.png" alt="Logo" class="h-10 w-auto" />
        <span class="text-2xl font-bold text-gray-900">{{ $t('app_name') }}</span>
      </router-link>

      <h1 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
        {{ $t('create_your_account') }}
      </h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        {{ $t('signup_subtitle') }}
      </p>

      <!-- Progress Indicator -->
      <div class="mt-8 flex justify-center">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-semibold">1</span>
            </div>
            <span class="ml-2 text-sm font-medium text-blue-600">{{ $t('account_info') }}</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span class="text-gray-500 text-sm font-semibold">2</span>
            </div>
            <span class="ml-2 text-sm font-medium text-gray-500">{{ $t('building_info') }}</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span class="text-gray-500 text-sm font-semibold">3</span>
            </div>
            <span class="ml-2 text-sm font-medium text-gray-500">{{ $t('complete') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Container -->
    <div class="max-w-3xl mx-auto">
      <div class="bg-white rounded-2xl shadow-2xl p-8 lg:p-12">
        <form @submit.prevent="handleSubmit" class="space-y-10">
          <!-- Personal Information -->
          <div class="bg-gray-50 rounded-2xl p-6">
            <div class="flex items-center mb-6">
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900">{{ $t('personal_information') }}</h3>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Full Name -->
              <div>
                <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                  {{ $t('full_name') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    v-model="formData.name"
                    required
                    class="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 text-base"
                    :placeholder="$t('enter_full_name')"
                    autocomplete="name"
                    inputmode="text"
                  />
                </div>
              </div>

              <!-- Email -->
              <div>
                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                  {{ $t('email_address') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    v-model="formData.email"
                    required
                    class="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 text-base"
                    :placeholder="$t('enter_email')"
                    autocomplete="email"
                    inputmode="email"
                  />
                </div>
              </div>

              <!-- Phone Number -->
              <div>
                <label for="phone_number" class="block text-sm font-semibold text-gray-700 mb-2">
                  {{ $t('phone_number') }}
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                  </div>
                  <input
                    id="phone_number"
                    name="phone_number"
                    type="tel"
                    v-model="formData.phone_number"
                    class="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 text-base phone-number"
                    :placeholder="$t('enter_phone_number')"
                    autocomplete="tel"
                    inputmode="tel"
                    dir="ltr"
                  />
                </div>
              </div>

              <!-- Password -->
              <div>
                <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                  {{ $t('password') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                  </div>
                  <input
                    id="password"
                    name="password"
                    :type="showPassword ? 'text' : 'password'"
                    v-model="formData.password"
                    required
                    class="w-full pl-10 pr-12 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400"
                    :placeholder="$t('enter_password')"
                  />
                  <button
                    type="button"
                    @click="showPassword = !showPassword"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <svg v-if="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                    <svg v-else class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                    </svg>
                  </button>
                </div>
                <p class="mt-1 text-xs text-gray-500">{{ $t('password_requirements') }}</p>
              </div>

              <!-- Confirm Password -->
              <div>
                <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">
                  {{ $t('confirm_password') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                  </div>
                  <input
                    id="password_confirmation"
                    name="password_confirmation"
                    type="password"
                    v-model="formData.password_confirmation"
                    required
                    class="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400"
                    :placeholder="$t('confirm_password')"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Building Information -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('building_information') }}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="building_name" class="block text-sm font-medium text-gray-700">
                  {{ $t('building_name') }}
                </label>
                <input
                  id="building_name"
                  name="building_name"
                  type="text"
                  v-model="formData.building_name"
                  required
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  :placeholder="$t('enter_building_name')"
                />
              </div>

              <div class="md:col-span-2">
                <label for="building_address" class="block text-sm font-medium text-gray-700">
                  {{ $t('building_address') }}
                </label>
                <input
                  id="building_address"
                  name="building_address"
                  type="text"
                  v-model="formData.building_address"
                  required
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  :placeholder="$t('enter_building_address')"
                />
              </div>

              <div>
                <label for="city" class="block text-sm font-medium text-gray-700">
                  {{ $t('city') }}
                </label>
                <input
                  id="city"
                  name="city"
                  type="text"
                  v-model="formData.city"
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  :placeholder="$t('enter_city')"
                />
              </div>

              <div>
                <label for="postal_code" class="block text-sm font-medium text-gray-700">
                  {{ $t('postal_code') }}
                </label>
                <input
                  id="postal_code"
                  name="postal_code"
                  type="text"
                  v-model="formData.postal_code"
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  :placeholder="$t('enter_postal_code')"
                />
              </div>

              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700">
                  {{ $t('currency') }}
                </label>
                <select
                  id="currency"
                  name="currency"
                  v-model="formData.currency"
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="JOD">JOD - Jordanian Dinar</option>
                  <option value="ILS">ILS - Israeli Shekel</option>
                </select>
              </div>

              <div>
                <label for="monthly_fee" class="block text-sm font-medium text-gray-700">
                  {{ $t('monthly_fee_per_neighbor') }}
                </label>
                <input
                  id="monthly_fee"
                  name="monthly_fee"
                  type="number"
                  step="0.01"
                  min="0"
                  v-model="formData.monthly_fee"
                  class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="70.00"
                />
                <p class="mt-1 text-sm text-gray-500">{{ $t('monthly_fee_help') }}</p>
              </div>
            </div>
          </div>

          <!-- Pricing Summary -->
          <div class="bg-gray-50 p-4 rounded-md" style="display: none;">
            <h4 class="text-sm font-medium text-gray-900 mb-2">{{ $t('pricing_summary') }}</h4>
            <div class="space-y-1 text-sm text-gray-600">
              <div class="flex justify-between">
                <span>{{ $t('setup_fee') }} ({{ $t('today') }}):</span>
                <span>70.00</span>
              </div>
              <div class="flex justify-between">
                <span>{{ $t('monthly_subscription') }} ({{ $t('starts_next_month') }}):</span>
                <span>7.00/{{ $t('month') }}</span>
              </div>
              <div class="flex justify-between font-medium text-gray-900 pt-1 border-t">
                <span>{{ $t('total_due_today') }}:</span>
                <span>70.00</span>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              {{ $t('subscription_billing_note') }}
            </p>
          </div>

          <!-- Terms and Conditions -->
          <div class="flex items-center">
            <input
              id="terms"
              name="terms"
              type="checkbox"
              v-model="formData.terms"
              required
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="terms" class="ml-2 block text-sm text-gray-900">
              {{ $t('agree_to') }}
              <router-link to="/terms-and-conditions" class="text-indigo-600 hover:text-indigo-500">
                {{ $t('terms_and_conditions') }}
              </router-link>
            </label>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {{ $t('registration_failed') }}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  {{ errorMessage }}
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div>
            <button
              type="submit"
              :disabled="processing"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ processing ? $t('creating_account') : $t('create_account_and_pay') }}
            </button>
            <p class="mt-2 text-xs text-gray-500 text-center">
              {{ $t('payment_after_account_creation') }}
            </p>
          </div>

          <!-- Login Link -->
          <div class="text-center">
            <span class="text-sm text-gray-600">
              {{ $t('already_have_account') }}
              <router-link to="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                {{ $t('sign_in') }}
              </router-link>
            </span>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SignUp',
  mixins: [i18nMixin],
  data() {
    return {
      processing: false,
      errorMessage: '',
      formData: {
        name: '',
        email: '',
        phone_number: '',
        password: '',
        password_confirmation: '',
        building_name: '',
        building_address: '',
        city: '',
        postal_code: '',
        monthly_fee: 50.00, // Updated default monthly fee
        currency: 'JOD', // Default to JOD for Jordan
        terms: false
      }
    };
  },
  mounted() {
    document.title = `${this.$t('sign_up')} - ${this.$t('app_name')}`;
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      this.errorMessage = '';

      console.log('Starting signup process...');

      try {
        // Create the account
        console.log('Sending signup request...');
        const response = await this.$axios.post('/signup', {
          name: this.formData.name,
          email: this.formData.email,
          phone_number: this.formData.phone_number,
          password: this.formData.password,
          password_confirmation: this.formData.password_confirmation,
          building: {
            name: this.formData.building_name,
            address: this.formData.building_address,
            city: this.formData.city,
            postal_code: this.formData.postal_code,
            monthly_fee: this.formData.monthly_fee,
            currency: this.formData.currency,
            setup_fee: 70.00,
          }
        });

        console.log('Signup response received:', response.data);

        if (response.data.success) {
          console.log('Signup successful, storing data and redirecting...');
          // Account created successfully, store user data and token
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('user', JSON.stringify(response.data.user));
          localStorage.setItem('building', JSON.stringify(response.data.building));

          // Dispatch custom event to notify other components about auth state change
          window.dispatchEvent(new CustomEvent('auth-state-changed'));

          console.log('Redirecting to registration success page...');
          // Redirect to registration success page
          this.$router.push('/registration-success');
        } else {
          console.log('Signup failed - no success flag in response');
          this.errorMessage = response.data.message || 'Registration failed';
        }
      } catch (error) {
        console.error('Registration error:', error);
        console.error('Error response:', error.response?.data);
        this.errorMessage = error.response?.data?.message || this.$t('registration_error');
      } finally {
        console.log('Signup process completed, setting processing to false');
        this.processing = false;
      }
    }
  }
};
</script>
