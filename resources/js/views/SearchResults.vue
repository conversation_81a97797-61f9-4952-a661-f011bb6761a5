<template>
  <div class="max-w-6xl mx-auto">
    <!-- Search Header -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ $t('search_results') }}</h1>
          <p v-if="searchQuery" class="text-gray-600 mt-1">
            {{ $t('results_for') }} "<span class="font-medium">{{ searchQuery }}</span>"
          </p>
        </div>
        
        <!-- Search Bar -->
        <div class="mt-4 sm:mt-0 sm:w-96">
          <search-bar
            :auto-focus="false"
            @search="handleNewSearch"
            @clear="handleClearSearch"
          />
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('type') }}</label>
          <select
            v-model="filters.type"
            @change="performSearch"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
          >
            <option value="all">{{ $t('all_types') }}</option>
            <option value="users">{{ $t('users') }}</option>
            <option value="expenses">{{ $t('expenses') }}</option>
            <option value="incomes">{{ $t('incomes') }}</option>
            <option value="payments">{{ $t('payments') }}</option>
            <option value="expense_types">{{ $t('expense_types') }}</option>
          </select>
        </div>

        <!-- Date Range -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('date_from') }}</label>
          <input
            v-model="filters.date_from"
            @change="performSearch"
            type="date"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('date_to') }}</label>
          <input
            v-model="filters.date_to"
            @change="performSearch"
            type="date"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
          />
        </div>

        <!-- Amount Range -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('amount_range') }}</label>
          <div class="flex space-x-2">
            <input
              v-model="filters.amount_min"
              @change="performSearch"
              type="number"
              :placeholder="$t('min')"
              class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
              min="0"
              step="0.01"
            />
            <input
              v-model="filters.amount_max"
              @change="performSearch"
              type="number"
              :placeholder="$t('max')"
              class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
              min="0"
              step="0.01"
            />
          </div>
        </div>
      </div>

      <!-- Clear Filters -->
      <div class="mt-4 flex justify-end">
        <button
          @click="clearFilters"
          class="text-sm text-gray-600 hover:text-gray-800"
        >
          {{ $t('clear_filters') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="text-gray-500 mt-2">{{ $t('searching') }}...</p>
    </div>

    <!-- No Results -->
    <div v-else-if="!loading && totalResults === 0 && searchQuery" class="text-center py-12">
      <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('no_results_found') }}</h3>
      <p class="text-gray-500">{{ $t('try_different_search') }}</p>
    </div>

    <!-- Search Results -->
    <div v-else-if="!loading && totalResults > 0" class="space-y-6">
      <!-- Results Summary -->
      <div class="text-sm text-gray-600">
        {{ $t('found') }} {{ totalResults }} {{ $t('results') }}
        <span v-if="filtersApplied.length > 0">
          {{ $t('with_filters') }}: {{ filtersApplied.join(', ') }}
        </span>
      </div>

      <!-- Users Results -->
      <div v-if="results.users && results.users.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded text-sm flex items-center justify-center mr-2">👤</span>
            {{ $t('users') }} ({{ results.users.length }})
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div
            v-for="user in results.users"
            :key="`user-${user.id}`"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">{{ user.name }}</h4>
                <p class="text-sm text-gray-500">
                  {{ $t('apartment') }} {{ user.apartment_number }} • {{ user.building?.name }}
                </p>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="viewUserExpenses(user)"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  {{ $t('view_expenses') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Expenses Results -->
      <div v-if="results.expenses && results.expenses.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <span class="w-6 h-6 bg-red-100 text-red-600 rounded text-sm flex items-center justify-center mr-2">💸</span>
            {{ $t('expenses') }} ({{ results.expenses.length }})
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div
            v-for="expense in results.expenses"
            :key="`expense-${expense.id}`"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <h4 class="font-medium text-gray-900">{{ expense.expense_type?.name }}</h4>
                  <span class="text-lg font-semibold text-red-600">{{ formatCurrency(expense.amount) }}</span>
                </div>
                <p class="text-sm text-gray-500 mt-1">
                  {{ expense.user?.name }} • {{ expense.month }}/{{ expense.year }}
                  <span v-if="expense.notes"> • {{ expense.notes }}</span>
                </p>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="viewExpense(expense)"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  {{ $t('view_details') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Incomes Results -->
      <div v-if="results.incomes && results.incomes.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <span class="w-6 h-6 bg-green-100 text-green-600 rounded text-sm flex items-center justify-center mr-2">💰</span>
            {{ $t('incomes') }} ({{ results.incomes.length }})
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div
            v-for="income in results.incomes"
            :key="`income-${income.id}`"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <h4 class="font-medium text-gray-900">{{ $t('income_payment') }}</h4>
                  <span class="text-lg font-semibold text-green-600">{{ formatCurrency(income.amount) }}</span>
                </div>
                <p class="text-sm text-gray-500 mt-1">
                  {{ income.user?.name }} • {{ formatDate(income.payment_date) }} • {{ $t(income.payment_method) }}
                  <span v-if="income.notes"> • {{ income.notes }}</span>
                </p>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="viewIncome(income)"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  {{ $t('view_details') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payments Results -->
      <div v-if="results.payments && results.payments.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <span class="w-6 h-6 bg-yellow-100 text-yellow-600 rounded text-sm flex items-center justify-center mr-2">💳</span>
            {{ $t('payments') }} ({{ results.payments.length }})
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div
            v-for="payment in results.payments"
            :key="`payment-${payment.id}`"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <h4 class="font-medium text-gray-900">{{ payment.expense?.expense_type?.name }}</h4>
                  <span class="text-lg font-semibold text-yellow-600">{{ formatCurrency(payment.amount) }}</span>
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getPaymentStatusClass(payment.status)"
                  >
                    {{ $t(payment.status) }}
                  </span>
                </div>
                <p class="text-sm text-gray-500 mt-1">
                  {{ payment.user?.name }} • {{ formatDate(payment.payment_date) }} • {{ $t(payment.payment_method) }}
                </p>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="viewPayment(payment)"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  {{ $t('view_details') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Expense Types Results -->
      <div v-if="results.expense_types && results.expense_types.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded text-sm flex items-center justify-center mr-2">📋</span>
            {{ $t('expense_types') }} ({{ results.expense_types.length }})
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <div
            v-for="expenseType in results.expense_types"
            :key="`expense-type-${expenseType.id}`"
            class="px-6 py-4 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">{{ expenseType.name }}</h4>
                <p class="text-sm text-gray-500">{{ expenseType.building?.name }}</p>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="viewExpensesByType(expenseType)"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  {{ $t('view_expenses') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import SearchBar from '../components/SearchBar.vue';

export default {
  name: 'SearchResults',
  mixins: [i18nMixin],
  components: {
    SearchBar
  },
  data() {
    return {
      searchQuery: '',
      results: {},
      totalResults: 0,
      loading: false,
      filters: {
        type: 'all',
        date_from: '',
        date_to: '',
        amount_min: '',
        amount_max: '',
      },
      filtersApplied: [],
    };
  },
  mounted() {
    // Get search query from URL
    this.searchQuery = this.$route.query.q || '';
    
    // Initialize filters from URL
    this.filters = {
      type: this.$route.query.type || 'all',
      date_from: this.$route.query.date_from || '',
      date_to: this.$route.query.date_to || '',
      amount_min: this.$route.query.amount_min || '',
      amount_max: this.$route.query.amount_max || '',
    };

    if (this.searchQuery) {
      this.performSearch();
    }
  },
  watch: {
    '$route.query'() {
      this.searchQuery = this.$route.query.q || '';
      if (this.searchQuery) {
        this.performSearch();
      }
    }
  },
  methods: {
    async performSearch() {
      if (!this.searchQuery || this.searchQuery.length < 2) return;

      this.loading = true;
      try {
        const params = {
          query: this.searchQuery,
          ...this.filters
        };

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === 'all') {
            delete params[key];
          }
        });

        const response = await this.$axios.get('/search', { params });
        
        this.results = response.data.results;
        this.totalResults = response.data.total_results;
        this.updateFiltersApplied(response.data.filters_applied);
        
      } catch (error) {
        console.error('Search failed:', error);
        this.results = {};
        this.totalResults = 0;
      } finally {
        this.loading = false;
      }
    },

    handleNewSearch(query) {
      this.searchQuery = query;
      this.performSearch();
    },

    handleClearSearch() {
      this.searchQuery = '';
      this.results = {};
      this.totalResults = 0;
      this.clearFilters();
    },

    clearFilters() {
      this.filters = {
        type: 'all',
        date_from: '',
        date_to: '',
        amount_min: '',
        amount_max: '',
      };
      this.filtersApplied = [];
      if (this.searchQuery) {
        this.performSearch();
      }
    },

    updateFiltersApplied(appliedFilters) {
      this.filtersApplied = [];
      
      if (appliedFilters.type) {
        this.filtersApplied.push(this.$t(appliedFilters.type));
      }
      
      if (appliedFilters.date_from || appliedFilters.date_to) {
        this.filtersApplied.push(this.$t('date_filter'));
      }
      
      if (appliedFilters.amount_min || appliedFilters.amount_max) {
        this.filtersApplied.push(this.$t('amount_filter'));
      }
    },

    // Navigation methods
    viewUserExpenses(user) {
      this.$router.push({
        name: 'Expenses',
        query: { user_id: user.id }
      });
    },

    viewExpense(expense) {
      this.$router.push({
        name: 'Expenses',
        query: { expense_id: expense.id }
      });
    },

    viewIncome(income) {
      this.$router.push({
        name: 'Incomes',
        query: { income_id: income.id }
      });
    },

    viewPayment(payment) {
      this.$router.push({
        name: 'Payments',
        query: { payment_id: payment.id }
      });
    },

    viewExpensesByType(expenseType) {
      this.$router.push({
        name: 'Expenses',
        query: { expense_type_id: expenseType.id }
      });
    },

    // Utility methods
    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    },

    getPaymentStatusClass(status) {
      const classes = {
        completed: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        failed: 'bg-red-100 text-red-800',
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    },
  },
};
</script>
