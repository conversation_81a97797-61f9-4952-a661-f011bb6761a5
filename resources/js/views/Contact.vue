<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
          <defs>
            <pattern id="contact-grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#contact-grid)" />
        </svg>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        <div class="text-center">
          <div class="mb-6">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-4">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
              {{ $t('get_in_touch') }}
            </span>
          </div>

          <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            {{ $t('contact_title') }}
          </h1>

          <p class="text-xl lg:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed mb-12">
            {{ $t('contact_subtitle') }}
          </p>

          <!-- Quick Contact Options -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-16">
            <a href="tel:+96278291239" class="group bg-white bg-opacity-90 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-100 transition-all duration-300 shadow-lg">
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-all duration-300">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
              </div>
              <div class="text-gray-900 font-medium">{{ $t('call_us') }}</div>
              <div class="text-gray-600 text-sm phone-number" dir="ltr">+962 78 291 2391</div>
            </a>

            <a href="mailto:<EMAIL>" class="group bg-white bg-opacity-90 backdrop-blur-sm rounded-2xl p-6 hover:bg-opacity-100 transition-all duration-300 shadow-lg">
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-all duration-300">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
              </div>
              <div class="text-gray-900 font-medium">{{ $t('email_us') }}</div>
              <div class="text-gray-600 text-sm"><EMAIL></div>
            </a>

            <div class="group bg-white bg-opacity-90 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="text-gray-900 font-medium">{{ $t('business_hours') }}</div>
              <div class="text-gray-600 text-sm">{{ $t('contact_hours_value') }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Content -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">

          <!-- Contact Information -->
          <div>
            <div class="mb-12">
              <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">{{ $t('contact_info_title') }}</h2>
              <p class="text-lg text-gray-600">{{ $t('contact_info_description') }}</p>
            </div>

            <div class="space-y-8">
              <!-- Address -->
              <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-start space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                  <div class="w-14 h-14 bg-blue-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $t('our_office') }}</h3>
                    <p class="text-gray-600 leading-relaxed">
                      Tla'a Al Ali<br>
                      Amman, Jordan 11841<br>
                      {{ $t('middle_east') }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Phone -->
              <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-start space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                  <div class="w-14 h-14 bg-green-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $t('phone_support') }}</h3>
                    <p class="text-gray-600 mb-2">
                      <a href="tel:+96278291239" class="hover:text-green-600 transition-colors phone-number" dir="ltr">+962 78 291 2391</a>
                    </p>
                    <p class="text-sm text-gray-500">{{ $t('available_24_7') }}</p>
                  </div>
                </div>
              </div>

              <!-- Email -->
              <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-start space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                  <div class="w-14 h-14 bg-purple-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-7 h-7 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $t('email_support') }}</h3>
                    <p class="text-gray-600 mb-2">
                      <a href="mailto:<EMAIL>" class="hover:text-purple-600 transition-colors"><EMAIL></a>
                    </p>
                    <p class="text-sm text-gray-500">{{ $t('response_within_24h') }}</p>
                  </div>
                </div>
              </div>

              <!-- Working Hours -->
              <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-start space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                  <div class="w-14 h-14 bg-orange-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-7 h-7 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $t('business_hours') }}</h3>
                    <p class="text-gray-600 mb-1">{{ $t('contact_hours_value') }}</p>
                    <p class="text-sm text-gray-500">{{ $t('jordan_timezone') }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="bg-white rounded-2xl p-8 shadow-2xl">
            <div class="mb-8">
              <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">{{ $t('send_us_message') }}</h2>
              <p class="text-lg text-gray-600">{{ $t('contact_form_description') }}</p>
            </div>

            <form @submit.prevent="submitForm" class="space-y-6">
              <!-- Name and Email Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                  <label for="name" class="block text-sm font-semibold text-gray-700 mb-3">{{ $t('contact_name') }}</label>
                  <input
                    type="text"
                    id="name"
                    v-model="form.name"
                    class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 text-base"
                    :placeholder="$t('enter_your_name')"
                    autocomplete="name"
                    inputmode="text"
                    required
                  >
                </div>

                <!-- Email -->
                <div>
                  <label for="email" class="block text-sm font-semibold text-gray-700 mb-3">{{ $t('contact_email') }}</label>
                  <input
                    type="email"
                    id="email"
                    v-model="form.email"
                    class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 text-base"
                    :placeholder="$t('enter_your_email')"
                    autocomplete="email"
                    inputmode="email"
                    required
                  >
                </div>
              </div>

              <!-- Subject -->
              <div>
                <label for="subject" class="block text-sm font-semibold text-gray-700 mb-3">{{ $t('contact_subject') }}</label>
                <select
                  id="subject"
                  v-model="form.subject"
                  class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900"
                  required
                >
                  <option value="">{{ $t('select_subject') }}</option>
                  <option value="general">{{ $t('general_inquiry') }}</option>
                  <option value="support">{{ $t('technical_support') }}</option>
                  <option value="sales">{{ $t('sales_inquiry') }}</option>
                  <option value="partnership">{{ $t('partnership') }}</option>
                  <option value="other">{{ $t('other') }}</option>
                </select>
              </div>

              <!-- Message -->
              <div>
                <label for="message" class="block text-sm font-semibold text-gray-700 mb-3">{{ $t('contact_message') }}</label>
                <textarea
                  id="message"
                  v-model="form.message"
                  rows="6"
                  class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 resize-none text-base"
                  :placeholder="$t('enter_your_message')"
                  autocomplete="off"
                  required
                ></textarea>
              </div>

              <!-- Privacy Notice -->
              <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-start space-x-3">
                  <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                  <p class="text-sm text-gray-600">
                    {{ $t('privacy_notice') }}
                  </p>
                </div>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                :disabled="isSubmitting"
                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
              >
                <span v-if="!isSubmitting" class="flex items-center">
                  {{ $t('send_message') }}
                  <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                  </svg>
                </span>
                <span v-else class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ $t('sending') }}...
                </span>
              </button>
            </form>

            <!-- Success Message -->
            <div v-if="showSuccess" class="mt-6 p-6 bg-green-50 border-2 border-green-200 text-green-800 rounded-xl">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <div>
                  <h4 class="font-semibold">{{ $t('message_sent_successfully') }}</h4>
                  <p class="text-sm mt-1">{{ $t('we_will_respond_soon') }}</p>
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="showError" class="mt-6 p-6 bg-red-50 border-2 border-red-200 text-red-800 rounded-xl">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-red-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
                <div>
                  <h4 class="font-semibold">{{ $t('message_send_failed') }}</h4>
                  <p class="text-sm mt-1">{{ $t('please_try_again') }}</p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {{ $t('frequently_asked_questions') }}
          </h2>
          <p class="text-lg text-gray-600">
            {{ $t('faq_description') }}
          </p>
        </div>

        <div class="space-y-6">
          <!-- FAQ Item 1 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('faq_1_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('faq_1_answer') }}</p>
          </div>

          <!-- FAQ Item 2 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('faq_2_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('faq_2_answer') }}</p>
          </div>

          <!-- FAQ Item 3 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('faq_3_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('faq_3_answer') }}</p>
          </div>

          <!-- FAQ Item 4 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('faq_4_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('faq_4_answer') }}</p>
          </div>
        </div>

        <div class="text-center mt-12">
          <p class="text-gray-600 mb-6">{{ $t('still_have_questions') }}</p>
          <button
            @click="$router.push('/contact')"
            class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-colors duration-300"
          >
            {{ $t('contact_us') }}
          </button>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl sm:text-4xl font-bold text-white mb-6">
          {{ $t('ready_to_get_started') }}
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
          {{ $t('contact_cta_description') }}
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            @click="$router.push('/signup')"
            class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
          >
            {{ $t('start_free_trial') }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>

          <button
            @click="$router.push('/pricing')"
            class="inline-flex items-center justify-center px-8 py-4 bg-transparent hover:bg-white hover:bg-opacity-10 text-white font-semibold border-2 border-white rounded-xl transition-all duration-300 text-lg min-h-[56px] w-full sm:w-auto"
          >
            {{ $t('view_pricing') }}
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Contact',
  mixins: [i18nMixin],
  data() {
    return {
      form: {
        name: '',
        email: '',
        subject: '',
        message: ''
      },
      isSubmitting: false,
      showSuccess: false,
      showError: false
    };
  },
  methods: {
    async submitForm() {
      this.isSubmitting = true;
      this.showSuccess = false;
      this.showError = false;

      try {
        // Simulate form submission with random success/failure
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            // 90% success rate for demo
            if (Math.random() > 0.1) {
              resolve();
            } else {
              reject(new Error('Submission failed'));
            }
          }, 2000);
        });

        // Success
        this.isSubmitting = false;
        this.showSuccess = true;

        // Reset form
        this.form = {
          name: '',
          email: '',
          subject: '',
          message: ''
        };

        // Hide success message after 5 seconds
        setTimeout(() => {
          this.showSuccess = false;
        }, 5000);

      } catch (error) {
        // Error
        this.isSubmitting = false;
        this.showError = true;

        // Hide error message after 5 seconds
        setTimeout(() => {
          this.showError = false;
        }, 5000);
      }
    }
  }
};
</script>

<style scoped>
/* Custom animations */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
