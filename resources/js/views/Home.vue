<template>
  <div class="min-h-screen bg-white">
    <!-- Guest Landing/Marketing Section -->
    <div v-if="!isAuthenticated">
      <!-- Hero Section -->
      <section class="relative bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-20 lg:pt-24 lg:pb-28">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <!-- Left: Text Content -->
            <div class="text-center lg:text-left">
              <div class="mb-6">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-4">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  {{ $t('trusted_by_buildings') }}
                </span>
              </div>

              <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                {{ $t('building_management') }}
                <span class="text-blue-600">{{ $t('made_simple') }}</span>
              </h1>

              <p class="text-lg sm:text-xl text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {{ $t('building_management_description') }}
              </p>

              <!-- CTA Buttons -->
              <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                <button
                  @click="$router.push('/signup')"
                  class="inline-flex items-center justify-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
                >
                  {{ $t('get_started') }}
                  <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                  </svg>
                </button>

                <button
                  @click="$router.push('/pricing')"
                  class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-50 text-blue-600 font-semibold border-2 border-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
                >
                  {{ $t('view_pricing') }}
                </button>
              </div>

              <!-- Trust Indicators -->
              <div class="flex flex-col sm:flex-row items-center justify-center lg:justify-start space-y-3 sm:space-y-0 sm:space-x-6 text-sm text-gray-500">
                <div class="flex items-center min-h-[32px]">
                  <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="whitespace-nowrap">{{ $t('no_setup_fees') }}</span>
                </div>
                <div class="flex items-center min-h-[32px]">
                  <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="whitespace-nowrap">{{ $t('free_trial') }}</span>
                </div>
                <div class="flex items-center min-h-[32px]">
                  <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="whitespace-nowrap">{{ $t('24_7_support') }}</span>
                </div>
              </div>
            </div>

            <!-- Right: Modern Illustration -->
            <div class="relative">
              <!-- Floating Cards Animation -->
              <div class="relative w-full max-w-lg mx-auto">
                <!-- Main Dashboard Card -->
                <div class="relative bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $t('building_dashboard') }}</h3>
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  </div>

                  <!-- Stats -->
                  <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-blue-50 rounded-lg p-3">
                      <div class="text-2xl font-bold text-blue-600">24</div>
                      <div class="text-sm text-gray-600">{{ $t('neighbors') }}</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-3">
                      <div class="text-2xl font-bold text-green-600">$2,400</div>
                      <div class="text-sm text-gray-600">{{ $t('monthly_income') }}</div>
                    </div>
                  </div>

                  <!-- Progress Bar -->
                  <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                      <span>{{ $t('collection_rate') }}</span>
                      <span>85%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                    </div>
                  </div>
                </div>

                <!-- Floating Notification Card -->
                <div class="absolute -top-4 -right-4 bg-white rounded-xl shadow-lg p-4 transform -rotate-6 hover:rotate-0 transition-transform duration-500 border-l-4 border-blue-500">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-ping"></div>
                    <span class="text-sm font-medium text-gray-900">{{ $t('new_payment') }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ $t('apartment_3a') }}</div>
                </div>

                <!-- Floating Expense Card -->
                <div class="absolute -bottom-6 -left-4 bg-white rounded-xl shadow-lg p-4 transform rotate-6 hover:rotate-0 transition-transform duration-500 border-l-4 border-orange-500">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">{{ $t('maintenance_due') }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">$450</div>
                </div>
              </div>

              <!-- Background Decorations -->
              <div class="absolute inset-0 -z-10">
                <div class="absolute top-10 left-10 w-20 h-20 bg-blue-100 rounded-full opacity-50 animate-pulse"></div>
                <div class="absolute bottom-10 right-10 w-16 h-16 bg-indigo-100 rounded-full opacity-50 animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute top-1/2 left-0 w-12 h-12 bg-blue-200 rounded-full opacity-30 animate-pulse" style="animation-delay: 2s;"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {{ $t('why_choose_us') }}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
              {{ $t('features_description') }}
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-blue-600 transition-colors duration-300">
                <svg class="w-6 h-6 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ $t('expense_management') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('expense_management_desc') }}</p>
            </div>

            <!-- Feature 2 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-green-600 transition-colors duration-300">
                <svg class="w-6 h-6 text-green-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ $t('payment_tracking') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('payment_tracking_desc') }}</p>
            </div>

            <!-- Feature 3 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-purple-600 transition-colors duration-300">
                <svg class="w-6 h-6 text-purple-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ $t('notifications') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('notifications_desc') }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Testimonials Section -->
      <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {{ $t('what_customers_say') }}
            </h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
              {{ $t('testimonials_description') }}
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Testimonial 1 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center mb-4">
                <div class="flex text-yellow-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" v-for="i in 5" :key="i">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed">
                "{{ $t('testimonial_1_text') }}"
              </p>
              <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 font-semibold">AH</span>
                </div>
                <div class="ml-4">
                  <div class="font-semibold text-gray-900">{{ $t('testimonial_1_name') }}</div>
                </div>
              </div>
            </div>

            <!-- Testimonial 2 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center mb-4">
                <div class="flex text-yellow-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" v-for="i in 5" :key="i">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed">
                "{{ $t('testimonial_2_text') }}"
              </p>
              <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <span class="text-green-600 font-semibold">MK</span>
                </div>
                <div class="ml-4">
                  <div class="font-semibold text-gray-900">{{ $t('testimonial_2_name') }}</div>
                </div>
              </div>
            </div>

            <!-- Testimonial 3 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center mb-4">
                <div class="flex text-yellow-400">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" v-for="i in 5" :key="i">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed">
                "{{ $t('testimonial_3_text') }}"
              </p>
              <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span class="text-purple-600 font-semibold">SA</span>
                </div>
                <div class="ml-4">
                  <div class="font-semibold text-gray-900">{{ $t('testimonial_3_name') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 class="text-3xl sm:text-4xl font-bold text-white mb-6">
            {{ $t('ready_to_get_started') }}
          </h2>
          <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            {{ $t('cta_description') }}
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              @click="$router.push('/signup')"
              class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
            >
              {{ $t('start_free_trial') }}
              <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
              </svg>
            </button>

            <button
              @click="$router.push('/contact')"
              class="inline-flex items-center justify-center px-8 py-4 bg-transparent hover:bg-white hover:bg-opacity-10 text-white font-semibold border-2 border-white rounded-xl transition-all duration-300 text-lg min-h-[56px] w-full sm:w-auto"
            >
              {{ $t('contact_sales') }}
            </button>
          </div>

          <p class="text-sm text-blue-200 mt-6">
            {{ $t('no_credit_card_required') }}
          </p>
        </div>
      </section>
    </div>

    <!-- Authenticated User Section: Buildings List -->
    <div v-else class="max-w-5xl mx-auto px-4 py-10">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">{{ $t('buildings') }}</h1>
      <div v-if="loading" class="text-center py-10">
        <span class="text-lg text-gray-500">{{ $t('loading') }}</span>
      </div>
      <div v-else-if="error" class="text-center py-10">
        <span class="text-lg text-red-500">{{ error }}</span>
      </div>
      <div v-else>
        <div v-if="buildings.length === 0" class="text-center text-gray-500 py-10">
          {{ $t('no_buildings_found') }}
        </div>
        <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div v-for="building in buildings" :key="building.id" class="bg-white rounded-lg shadow p-6 flex flex-col">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ building.name }}</h2>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('address') }}:</span>
              <span class="text-gray-800">{{ building.address || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('city') }}:</span>
              <span class="text-gray-800">{{ building.city || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('country') }}:</span>
              <span class="text-gray-800">{{ building.country || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('postal_code') }}:</span>
              <span class="text-gray-800">{{ building.postal_code || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('monthly_fee') }}:</span>
              <span class="text-gray-800">{{ parseFloat(building.monthly_fee).toFixed(2) }}</span>
            </div>
            <div v-if="building.description" class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('description') }}:</span>
              <span class="text-gray-800">{{ building.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer for guest users -->
    <footer v-if="!isAuthenticated" class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="lg:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <img src="images/logo.png" alt="Logo" class="h-8 w-auto" />
              <span class="text-xl font-bold">{{ $t('app_name') }}</span>
            </div>
            <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
              {{ $t('footer_description') }}
            </p>
            <div class="flex space-x-4">
              <a href="https://facebook.com/amaretnacom" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://instagram.com/amaretnacom" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C8.396 0 7.984.013 6.77.072 5.56.132 4.763.333 4.067.63c-.732.322-1.353.78-1.97 1.396-.617.616-1.074 1.238-1.396 1.97-.297.695-.498 1.493-.558 2.703C.054 7.984.04 8.396.04 12.017c0 3.622.013 4.034.072 5.248.06 1.21.261 2.007.558 2.703.322.732.78 1.353 1.396 1.97.616.617 1.238 1.074 1.97 1.396.695.297 1.492.498 2.703.558 1.214.059 1.626.072 5.248.072 3.622 0 4.034-.013 5.248-.072 1.21-.06 2.007-.261 2.703-.558.732-.322 1.353-.78 1.97-1.396.617-.616 1.074-1.238 1.396-1.97.297-.695.498-1.492.558-2.703.059-1.214.072-1.626.072-5.248 0-3.622-.013-4.034-.072-5.248-.06-1.21-.261-2.007-.558-2.703-.322-.732-.78-1.353-1.396-1.97-.616-.617-1.238-1.074-1.97-1.396-.695-.297-1.492-.498-2.703-.558C16.051.013 15.639.001 12.017.001zM12.017 2.163c3.556 0 3.978.013 5.385.072 1.299.059 2.005.276 2.474.458.622.242 1.066.532 1.533.998.466.467.756.91.998 1.533.182.469.399 1.175.458 2.474.059 1.407.072 1.829.072 5.385 0 3.556-.013 3.978-.072 5.385-.059 1.299-.276 2.005-.458 2.474-.242.622-.532 1.066-.998 1.533-.467.466-.91.756-1.533.998-.469.182-1.175.399-2.474.458-1.407.059-1.829.072-5.385.072-3.556 0-3.978-.013-5.385-.072-1.299-.059-2.005-.276-2.474-.458-.622-.242-1.066-.532-1.533-.998-.466-.467-.756-.91-.998-1.533-.182-.469-.399-1.175-.458-2.474-.059-1.407-.072-1.829-.072-5.385 0-3.556.013-3.978.072-5.385.059-1.299.276-2.005.458-2.474.242-.622.532-1.066.998-1.533.467-.466.91-.756 1.533-.998.469-.182 1.175-.399 2.474-.458 1.407-.059 1.829-.072 5.385-.072z"/>
                  <path d="M12.017 5.838a6.18 6.18 0 1 0 0 12.36 6.18 6.18 0 0 0 0-12.36zM12.017 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8z"/>
                  <path d="M19.846 5.595a1.44 1.44 0 1 1-2.88 0 1.44 1.44 0 0 1 2.88 0z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">{{ $t('quick_links') }}</h3>
            <ul class="space-y-3">
              <li>
                <router-link to="/about" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('about') }}
                </router-link>
              </li>
              <li>
                <router-link to="/services" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('services') }}
                </router-link>
              </li>
              <li>
                <router-link to="/pricing" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('pricing') }}
                </router-link>
              </li>
              <li>
                <router-link to="/contact" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('contact') }}
                </router-link>
              </li>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h3 class="text-lg font-semibold mb-4">{{ $t('support') }}</h3>
            <ul class="space-y-3">
              <li>
                <router-link to="/terms-and-conditions" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('terms_and_conditions') }}
                </router-link>
              </li>
              <li>
                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('email_support') }}
                </a>
              </li>
              <li>
                <a href="tel:+962782912391" class="text-gray-300 hover:text-white transition-colors">
                  {{ $t('phone_support') }}
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800 mt-12 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-400 text-sm">
              © 2025 {{ $t('app_name') }}. {{ $t('all_rights_reserved') }}
            </p>
            <div class="flex items-center space-x-4 mt-4 md:mt-0">
              <span class="text-gray-400 text-sm">{{ $t('made_with_love') }}</span>
              <div class="flex items-center space-x-1">
                <span class="text-red-500">❤️</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Home',
  mixins: [i18nMixin],
  data() {
    return {
      buildings: [],
      loading: true,
      error: ''
    };
  },
  computed: {
    isAuthenticated() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      return !!token && !!user && user !== 'null';
    }
  },
  async mounted() {
    // Only make API calls if we're actually authenticated with a valid token and user
    if (this.isAuthenticated) {
      try {
        // Double-check authentication before making API call
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user') || 'null');
        
        if (token && user && user.id) {
          const response = await this.$axios.get('/buildings');
          this.buildings = response.data;
        }
      } catch (err) {
        // If API call fails, just set error message but don't redirect
        console.log('Buildings API call failed:', err);
        this.error = this.$t('failed_load_buildings');
      } finally {
        this.loading = false;
      }
    } else {
      this.loading = false;
    }
  }
};
</script>

<style scoped>
/* Custom animations for the home page */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .responsive-text {
    font-size: 2.5rem;
    line-height: 1.2;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 4rem;
    line-height: 1.1;
  }
}
</style>