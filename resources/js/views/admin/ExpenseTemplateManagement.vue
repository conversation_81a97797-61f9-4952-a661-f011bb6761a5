<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('expense_template_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_automated_expense_templates') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="showCreateModal = true"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_template') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Template Statistics -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('active_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.active || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('auto_generate_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.auto_generate || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('generated_this_month') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.generated_this_month || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Templates Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('expense_templates') }}</h2>
            <div class="mt-4 sm:mt-0 flex space-x-3">
              <select v-model="filters.active" @change="loadTemplates" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option value="">{{ $t('all_templates') }}</option>
                <option value="true">{{ $t('active_only') }}</option>
                <option value="false">{{ $t('inactive_only') }}</option>
              </select>
              <button
                @click="runAutoGeneration"
                :disabled="isGenerating"
                class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                <svg v-if="isGenerating" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isGenerating ? $t('generating') : $t('run_auto_generation') }}
              </button>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('name') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('expense_type') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('amount') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('frequency') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('status') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('next_generation') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('actions') }}</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="template in templates" :key="template.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ template.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ template.expense_type?.name || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatAmount(template.amount, template.currency) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ $t(template.frequency) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                    {{ template.is_active ? $t('active') : $t('inactive') }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ template.next_generation_date ? formatDate(template.next_generation_date) : 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="editTemplate(template)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      {{ $t('edit') }}
                    </button>
                    <button
                      @click="toggleTemplate(template)"
                      :class="template.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'"
                    >
                      {{ template.is_active ? $t('deactivate') : $t('activate') }}
                    </button>
                    <button
                      @click="deleteTemplate(template)"
                      class="text-red-600 hover:text-red-900"
                    >
                      {{ $t('delete') }}
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="templates.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_templates') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('no_templates_description') }}</p>
        </div>
      </div>
    </div>

    <!-- Create/Edit Template Modal -->
    <ExpenseTemplateModal
      v-if="showCreateModal || editingTemplate"
      :template="editingTemplate"
      @close="closeModal"
      @success="handleTemplateSuccess"
    />
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import ExpenseTemplateModal from '../../components/ExpenseTemplateModal.vue';

export default {
  name: 'ExpenseTemplateManagement',
  mixins: [i18nMixin],
  components: {
    ExpenseTemplateModal
  },
  data() {
    return {
      loading: false,
      isGenerating: false,
      templates: [],
      templateStats: {},
      showCreateModal: false,
      editingTemplate: null,
      filters: {
        active: ''
      }
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadTemplates(),
        this.loadTemplateStats()
      ]);
    },

    async loadTemplates() {
      this.loading = true;
      try {
        const params = {};
        if (this.filters.active !== '') params.active = this.filters.active;

        const response = await this.$axios.get('/expense-templates', { params });
        this.templates = response.data.data || response.data || [];
      } catch (error) {
        console.error('Error loading templates:', error);
      } finally {
        this.loading = false;
      }
    },

    async loadTemplateStats() {
      try {
        const response = await this.$axios.get('/expense-templates/stats');
        this.templateStats = response.data;
      } catch (error) {
        console.error('Error loading template stats:', error);
      }
    },

    async runAutoGeneration() {
      if (!confirm(this.$t('confirm_run_auto_generation'))) return;

      this.isGenerating = true;
      try {
        await this.$axios.post('/expense-templates/run-auto-generation');
        this.$toast.success(this.$t('auto_generation_completed'));
        await this.loadData();
      } catch (error) {
        console.error('Error running auto generation:', error);
        this.$toast.error(error.response?.data?.message || this.$t('auto_generation_failed'));
      } finally {
        this.isGenerating = false;
      }
    },

    editTemplate(template) {
      this.editingTemplate = template;
    },

    async toggleTemplate(template) {
      try {
        await this.$axios.put(`/expense-templates/${template.id}`, {
          is_active: !template.is_active
        });
        await this.loadData();
        this.$toast.success(this.$t('template_updated_successfully'));
      } catch (error) {
        console.error('Error toggling template:', error);
        this.$toast.error(this.$t('update_failed'));
      }
    },

    async deleteTemplate(template) {
      if (!confirm(this.$t('confirm_delete_template'))) return;

      try {
        await this.$axios.delete(`/expense-templates/${template.id}`);
        await this.loadData();
        this.$toast.success(this.$t('template_deleted_successfully'));
      } catch (error) {
        console.error('Error deleting template:', error);
        this.$toast.error(this.$t('delete_failed'));
      }
    },

    closeModal() {
      this.showCreateModal = false;
      this.editingTemplate = null;
    },

    handleTemplateSuccess() {
      this.closeModal();
      this.loadData();
    },

    formatAmount(amount, currency = 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(amount);
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    }
  }
};
</script>
