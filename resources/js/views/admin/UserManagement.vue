<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('user_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_building_users') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <router-link to="/admin/users/create"
              class="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('add_new_user') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading" class="text-center py-4">
        <p>{{ $t('loading') }}</p>
      </div>

      <div v-else-if="isAdmin">

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('total_users') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ totalUsers }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ isSuperAdmin ? $t('admins') : $t('neighbors') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ roleBasedCount }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg v-if="isSuperAdmin" class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <svg v-else class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ isSuperAdmin ? $t('buildings') : $t('active_users') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ thirdMetric }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('recent_users') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ recentUsersCount }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('filters') }}</h2>
            <p class="mt-1 text-sm text-gray-500">{{ $t('filter_users_description') }}</p>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div v-if="isSuperAdmin">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('building') }}</label>
                <select v-model="filters.building_id"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">{{ $t('all_buildings') }}</option>
                  <option v-for="building in buildings" :key="building.id" :value="building.id">
                    {{ building.name }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('role') }}</label>
                <select v-model="filters.role"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">{{ $t('all_roles') }}</option>
                  <option v-if="!isSuperAdmin" value="admin">{{ $t('admin') }}</option>
                  <option v-if="!isSuperAdmin" value="neighbor">{{ $t('neighbor') }}</option>
                  <option v-if="isSuperAdmin" value="admin">{{ $t('admin') }}</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('status') }}</label>
                <select v-model="filters.status"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">{{ $t('all_statuses') }}</option>
                  <option value="active">{{ $t('active') }}</option>
                  <option value="inactive">{{ $t('inactive') }}</option>
                </select>
              </div>

              <div class="flex items-end">
                <button @click="applyFilters"
                  class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  {{ $t('apply_filters') }}
                </button>
              </div>
            </div>

            <!-- Active Filters Display -->
            <div v-if="hasActiveFilters" class="mt-4 flex flex-wrap gap-2">
              <span class="text-sm text-gray-500">{{ $t('active_filters') }}:</span>
              <span v-if="filters.building_id"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {{ $t('building') }}: {{ getBuildingName(filters.building_id) }}
                <button @click="filters.building_id = ''; applyFilters()" class="ml-1 text-blue-600 hover:text-blue-800">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
              <span v-if="filters.role"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {{ $t('role') }}: {{ $t(filters.role) }}
                <button @click="filters.role = ''; applyFilters()" class="ml-1 text-green-600 hover:text-green-800">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
              <span v-if="filters.status"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                {{ $t('status') }}: {{ $t(filters.status) }}
                <button @click="filters.status = ''; applyFilters()" class="ml-1 text-purple-600 hover:text-purple-800">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
              <button @click="clearAllFilters"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors">
                {{ $t('clear_all') }}
                <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Users Table -->
      <data-table
        v-if="isAdmin"
        :title="$t('users')"
        :columns="columns"
        :items="filteredUsers"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button
              @click="editUser(item)"
               class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {{ $t('edit') }}
              </button>
            <button
              @click="deleteUser(item)"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {{ $t('delete') }}
            </button>
          </div>
        </template>
      </data-table>

      <!-- Edit Modal -->
      <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center" @click="handleEditModalOutsideClick">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4" @click.stop>
          <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-900">{{ $t('edit_user') }}</h2>
            <button @click="handleEditCancel" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <user-form
              :user="selectedUser"
              :is-edit="true"
              :is-super-admin="isSuperAdmin"
              :admin-building-id="adminBuildingId"
              :buildings="buildings"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="handleEditCancel"
            />
          </div>
        </div>
      </div>

        <!-- Notifications -->
        <notification
          :show="showNotification"
          :type="notificationType"
          :title="notificationTitle"
          :message="notificationMessage"
          @close="closeNotification"
        />
      </div>

      <div v-if="!isAdmin" class="text-center py-8">
        <p class="text-red-500">{{ $t('no_permission_access_page') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import UserForm from '../../components/UserForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';
import smartModalMixin from '../../mixins/smartModalMixin.js';

export default {
  mixins: [i18nMixin, smartModalMixin],
  components: {
    DataTable,
    UserForm,
    Notification
  },
  name: 'UserManagement',
  data() {
    return {
      loading: false,
      users: [],
      buildings: [],
      selectedUser: null,
      showEditModal: false,
      editFormData: null, // Track form data for change detection
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        building_id: '',
        role: '',
        status: ''
      },
      user: null,
      isAdmin: false,
      isSuperAdmin: false,
      adminBuildingId: null,
      userLoaded: false,
      columns: []
    };
  },
  computed: {
    filteredUsers() {
      let filtered = this.users;

      if (this.filters.building_id) {
        filtered = filtered.filter(user => user.building_id == this.filters.building_id);
      }

      if (this.filters.role) {
        filtered = filtered.filter(user => user.role === this.filters.role);
      }

      if (this.filters.status) {
        if (this.filters.status === 'active') {
          filtered = filtered.filter(user => user.is_active !== false); // Include null/undefined as active
        } else if (this.filters.status === 'inactive') {
          filtered = filtered.filter(user => user.is_active === false);
        }
      }

      return filtered;
    },

    // Override smart modal mixin to track UserForm changes
    hasFormChanges() {
      if (!this.editFormData || !this.selectedUser) return false;

      // Compare current user data with original
      const originalData = {
        name: this.selectedUser.name || '',
        email: this.selectedUser.email || '',
        phone: this.selectedUser.phone || '',
        apartment_number: this.selectedUser.apartment_number || '',
        role: this.selectedUser.role || 'neighbor',
        building_id: this.selectedUser.building_id || '',
        is_active: this.selectedUser.is_active !== false
      };

      return JSON.stringify(this.editFormData) !== JSON.stringify(originalData);
    },
    totalUsers() {
      return this.users.length;
    },
    roleBasedCount() {
      if (this.isSuperAdmin) {
        return this.users.filter(user => user.role === 'admin').length;
      } else {
        return this.users.filter(user => user.role === 'neighbor').length;
      }
    },
    thirdMetric() {
      if (this.isSuperAdmin) {
        return this.buildings.length;
      } else {
        return this.users.filter(user => user.role !== 'super_admin').length;
      }
    },
    recentUsersCount() {
      // Users created in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      return this.users.filter(user => {
        const createdAt = new Date(user.created_at);
        return createdAt >= thirtyDaysAgo;
      }).length;
    },
    hasActiveFilters() {
      return this.filters.building_id || this.filters.role || this.filters.status;
    }
  },
  async mounted() {
    // First try to get user data from localStorage for immediate role determination
    this.initializeUserFromStorage();
    await this.fetchUser();
    if (this.isAdmin) {
      this.setupColumns();
      this.loadUsers();
      this.fetchBuildings();
    }
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isAdmin = userData.role === 'admin' || userData.role === 'super_admin';
          this.isSuperAdmin = userData.role === 'super_admin';
          this.adminBuildingId = userData.building_id;
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isAdmin = this.user.role === 'admin' || this.user.role === 'super_admin';
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.adminBuildingId = this.user.building_id;
        this.userLoaded = true;
        // Update localStorage with fresh user data
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
        this.$router.push('/login');
      } finally {
        this.loading = false;
      }
    },
    setupColumns() {
      if (this.isSuperAdmin) {
        this.columns = [
          { key: 'name', label: this.$t('name') },
          { key: 'email', label: this.$t('email') },
          { key: 'role', label: this.$t('role') },
          { key: 'is_active', label: this.$t('status') },
          { key: 'building.name', label: this.$t('building') }
        ];
      } else {
        this.columns = [
          { key: 'name', label: this.$t('name') },
          { key: 'email', label: this.$t('email') },
          { key: 'role', label: this.$t('role') },
          { key: 'is_active', label: this.$t('status') },
          { key: 'apartment_number', label: this.$t('apartment') }
        ];
      }
    },
    async loadUsers() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/admin/users');
        let usersData = response.data.data || response.data;

        if (this.isSuperAdmin) {
          // Super admin sees only admins and buildings
          this.users = usersData.filter(user => user.role === 'admin');
        } else {
          // Regular admin sees both admins and neighbors in their building
          this.users = usersData.filter(user =>
            user.building_id === this.adminBuildingId &&
            (user.role === 'neighbor' || user.role === 'admin')
          );
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        this.showError(this.$t('error'), this.$t('failed_to_load_users'));
      } finally {
        this.loading = false;
      }
    },
    async fetchBuildings() {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (error) {
        console.error('Error fetching buildings:', error);
      }
    },
    applyFilters() {
      // Filters are applied through computed property
    },
    clearAllFilters() {
      this.filters.building_id = '';
      this.filters.role = '';
      this.filters.status = '';
      this.applyFilters();
    },
    editUser(user) {
      this.selectedUser = user;
      this.initializeEditFormTracking();
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedUser = null;
      this.editFormData = null;
    },

    handleEditModalOutsideClick() {
      if (!this.hasFormChanges) {
        this.closeEditModal();
      } else {
        this.showUnsavedChangesAlert();
      }
    },

    handleEditCancel() {
      if (!this.hasFormChanges) {
        this.closeEditModal();
      } else {
        this.showDiscardChangesConfirmation();
      }
    },

    initializeEditFormTracking() {
      if (this.selectedUser) {
        this.editFormData = {
          name: this.selectedUser.name || '',
          email: this.selectedUser.email || '',
          phone: this.selectedUser.phone || '',
          apartment_number: this.selectedUser.apartment_number || '',
          role: this.selectedUser.role || 'neighbor',
          building_id: this.selectedUser.building_id || '',
          is_active: this.selectedUser.is_active !== false
        };
      }
    },
    handleEditSuccess() {
      this.showSuccess(this.$t('success'), this.$t('user_updated_successfully'));
      this.closeEditModal();
      this.loadUsers();
    },
    handleEditError(message) {
      this.showError(this.$t('error'), message);
    },
    async deleteUser(user) {
      if (confirm(this.$t('confirm_delete_user', { name: user.name }))) {
        try {
          await this.$axios.delete(`/admin/users/${user.id}`);
          this.showSuccess(this.$t('success'), this.$t('user_deleted_successfully'));
          this.loadUsers();
        } catch (error) {
          this.showError(this.$t('error'), error.response?.data?.message || this.$t('failed_to_delete_user'));
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    },
    getBuildingName(buildingId) {
      const building = this.buildings.find(b => b.id == buildingId);
      return building ? building.name : '';
    },
    clearAllFilters() {
      this.filters = {
        building_id: ''
      };
      this.applyFilters();
    },
    // Method called by i18nMixin when language changes
    initializeColumns() {
      if (this.isAdmin) {
        this.setupColumns();
      }
    }
  }
};
</script>
