<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <router-link 
                    to="/admin/building-expenses" 
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    {{ $t('building_expenses') }}
                  </router-link>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500">{{ $t('edit_building_expense') }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('edit_building_expense') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('edit_building_expense_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <router-link
              to="/admin/building-expenses"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('back_to_building_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-sm text-gray-500">{{ $t('loading_building_expense') }}</p>
        </div>
      </div>

      <div v-else-if="buildingExpense" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('building_expense_details') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('building_expense_details_description') }}</p>
        </div>
        
        <div class="p-6">
          <building-expense-form
            :building-expense="buildingExpense"
            :is-edit="true"
            @success="handleSuccess"
            @error="handleError"
            @cancel="handleCancel"
          />
        </div>
      </div>

      <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('building_expense_not_found') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('building_expense_not_found_description') }}</p>
          <div class="mt-6">
            <router-link
              to="/admin/building-expenses"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              {{ $t('back_to_building_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </div>
</template>

<script>
import BuildingExpenseForm from '../../components/BuildingExpenseForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    BuildingExpenseForm,
    Notification
  },
  data() {
    return {
      loading: true,
      buildingExpense: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async created() {
    await this.loadBuildingExpense();
  },
  methods: {
    async loadBuildingExpense() {
      this.loading = true;
      try {
        const response = await this.$axios.get(`/building-expenses/${this.$route.params.id}`);
        this.buildingExpense = response.data;
      } catch (error) {
        console.error('Error loading building expense:', error);
        if (error.response?.status === 404) {
          this.buildingExpense = null;
        } else {
          this.showError('Loading Failed', 'Failed to load building expense details.');
        }
      } finally {
        this.loading = false;
      }
    },
    handleSuccess() {
      this.showSuccess('Building Expense Updated', 'The building expense has been updated successfully.');
      this.$router.push('/admin/building-expenses');
    },
    handleError(message) {
      this.showError('Update Failed', message);
    },
    handleCancel() {
      this.$router.push('/admin/building-expenses');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
