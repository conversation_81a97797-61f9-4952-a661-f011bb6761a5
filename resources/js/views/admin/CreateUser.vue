<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>er -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <router-link
                    to="/admin/users"
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    {{ $t('users') }}
                  </router-link>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500">{{ $t('create_user') }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('create_user') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('create_user_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <router-link
              to="/admin/users"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('back_to_users') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('user_details') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('user_details_description') }}</p>
        </div>

        <div class="p-6">
          <user-form
            :is-super-admin="isSuperAdmin"
            :admin-building-id="adminBuildingId"
            :buildings="buildings"
            :package-restrictions="packageRestrictions"
            @success="handleSuccess"
            @error="handleError"
            @cancel="handleCancel"
          />
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </div>
</template>

<script>
import UserForm from '../../components/UserForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    UserForm,
    Notification
  },
  data() {
    return {
      buildings: [],
      user: null,
      isSuperAdmin: false,
      adminBuildingId: null,
      packageRestrictions: {},
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async mounted() {
    await this.fetchUser();
    await this.fetchBuildings();
    // Fetch package restrictions for both super admins and regular admins
    await this.fetchPackageRestrictions();
  },
  methods: {
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.adminBuildingId = this.user.building_id;
      } catch (error) {
        console.error('Error fetching user:', error);
        this.$router.push('/login');
      }
    },
    async fetchBuildings() {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (error) {
        console.error('Error fetching buildings:', error);
      }
    },
    async fetchPackageRestrictions() {
      try {
        // Check admin limits for both super admins and regular admins
        // This endpoint returns admin limits for the current user's building
        const response = await this.$axios.get('/admin-management/limits');
        this.packageRestrictions = {
          can_add_more_admins: response.data.can_add_more,
          has_multi_admin_feature: response.data.has_multi_admin_feature,
          current_admin_count: response.data.current_count,
          admin_limit: response.data.limit,
          admin_limit_reached: !response.data.can_add_more,
          package_name: response.data.package_name
        };
      } catch (error) {
        console.error('Error fetching package restrictions:', error);
        // Set default values if fetch fails
        this.packageRestrictions = {
          can_add_more_admins: true,
          has_multi_admin_feature: true
        };
      }
    },
    handleSuccess() {
      this.showSuccess(this.$t('success'), this.$t('user_created_successfully'));
      setTimeout(() => {
        this.$router.push('/admin/users');
      }, 1500);
    },
    handleError(message) {
      this.showError(this.$t('error'), message);
    },
    handleCancel() {
      this.$router.push('/admin/users');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
