<template>
  <div class="advanced-reporting min-h-screen bg-gray-50" :class="$isRTL() ? 'rtl' : 'ltr'" :dir="$isRTL() ? 'rtl' : 'ltr'">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ $t('reports.title') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('reports.subtitle') }}</p>
          </div>
          <button
            v-if="canCreateReports"
            @click="showCreateModal = true"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4" :class="$isRTL() ? 'ml-2' : 'mr-2'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            {{ $t('reports.create_report') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="text-gray-600" :class="$isRTL() ? 'mr-3' : 'ml-3'">{{ $t('reports.loading') }}</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="mx-auto max-w-md">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="mt-4 text-lg font-medium text-gray-900">{{ getErrorTitle() }}</h3>
          <p class="mt-2 text-sm text-gray-500">{{ getErrorMessage() }}</p>
          <div class="mt-6">
            <button
              @click="handleErrorAction()"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              {{ getErrorActionText() }}
            </button>
          </div>
        </div>
      </div>

      <!-- Success State -->
      <div v-else class="space-y-8">
        <!-- Quick Stats -->
        <div v-if="hasAdvancedReporting" class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">{{ $t('reports.custom_reports_count') }}</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ stats.custom_reports_count || 0 }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">{{ $t('reports.generated') }}</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ stats.total_generations || 0 }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">{{ $t('reports.this_month') }}</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ stats.recent_generations || 0 }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Reports -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">{{ $t('reports.quick_reports') }}</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <button
                @click="generateQuickReport('financial')"
                :disabled="generatingQuick"
                class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-sm font-medium text-gray-900">
                    <span class="absolute inset-0" aria-hidden="true"></span>
                    {{ $t('reports.financial_summary') }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-500">{{ $t('reports.financial_summary_desc') }}</p>
                </div>
                <div v-if="generatingQuick === 'financial'" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              </button>

              <button
                @click="generateQuickReport('expenses')"
                :disabled="generatingQuick"
                class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-sm font-medium text-gray-900">
                    <span class="absolute inset-0" aria-hidden="true"></span>
                    {{ $t('reports.expenses_report') }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-500">{{ $t('reports.expenses_report_desc') }}</p>
                </div>
                <div v-if="generatingQuick === 'expenses'" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                </div>
              </button>

              <button
                @click="generateQuickReport('income')"
                :disabled="generatingQuick"
                class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-purple-500 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-sm font-medium text-gray-900">
                    <span class="absolute inset-0" aria-hidden="true"></span>
                    {{ $t('reports.income_report') }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-500">{{ $t('reports.income_report_desc') }}</p>
                </div>
                <div v-if="generatingQuick === 'income'" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- Custom Reports -->
        <div v-if="hasAdvancedReporting" class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $t('reports.custom_reports') }}</h3>
              <span v-if="canCreateReports" class="text-sm text-gray-500">
                {{ stats.custom_reports_count || 0 }} {{ $t('reports.of') }} {{ stats.max_custom_reports || '∞' }} {{ $t('reports.reports') }}
              </span>
            </div>

            <!-- Empty State -->
            <div v-if="customReports.length === 0" class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-4 text-sm font-medium text-gray-900">{{ $t('reports.no_custom_reports') }}</h3>
              <p class="mt-2 text-sm text-gray-500">{{ $t('reports.no_custom_reports_desc') }}</p>
              <div class="mt-6">
                <button
                  v-if="canCreateReports"
                  @click="showCreateModal = true"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <svg class="w-4 h-4" :class="$isRTL() ? 'ml-2' : 'mr-2'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  {{ $t('reports.create_report') }}
                </button>
              </div>
            </div>

            <!-- Reports List -->
            <div v-else class="space-y-4">
              <div
                v-for="report in customReports"
                :key="report.id"
                class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
              >
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-gray-900 truncate">{{ report.name }}</h4>
                    <p class="mt-1 text-sm text-gray-500">{{ report.description || $t('reports.no_description_provided') }}</p>
                    <div class="mt-2 flex items-center text-xs text-gray-400 space-x-4">
                      <span>{{ $t('reports.created') }} {{ formatDate(report.created_at) }}</span>
                      <span>{{ report.generations_count || 0 }} {{ $t('reports.generations') }}</span>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2" :class="[$isRTL() ? 'mr-4 space-x-reverse' : 'ml-4']">
                    <button
                      @click="runReport(report)"
                      :disabled="report.generating"
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    >
                      <svg v-if="report.generating" class="animate-spin h-3 w-3 text-white" :class="$isRTL() ? 'ml-1 -mr-1' : '-ml-1 mr-1'" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <svg v-else class="w-3 h-3" :class="$isRTL() ? 'ml-1' : 'mr-1'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      {{ report.generating ? $t('reports.running') : $t('reports.run') }}
                    </button>
                    <button
                      @click="deleteReport(report)"
                      class="inline-flex items-center p-1.5 border border-transparent text-xs font-medium rounded text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Report Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 transition-opacity" aria-hidden="true" @click="showCreateModal = false"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal panel -->
        <div class="relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6" @click.stop>
          <div>
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">{{ $t('reports.create_new_report') }}</h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">{{ $t('reports.create_report_description') }}</p>
              </div>
            </div>
          </div>

          <form @submit.prevent="createReport" class="mt-6 space-y-4">
            <div>
              <label for="report-name" class="block text-sm font-medium text-gray-700">{{ $t('reports.report_name') }}</label>
              <div class="mt-1">
                <input
                  id="report-name"
                  v-model="newReport.name"
                  type="text"
                  required
                  class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  :placeholder="$t('reports.report_name_placeholder')"
                />
              </div>
            </div>

            <div>
              <label for="report-description" class="block text-sm font-medium text-gray-700">{{ $t('reports.description') }}</label>
              <div class="mt-1">
                <textarea
                  id="report-description"
                  v-model="newReport.description"
                  rows="3"
                  class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  :placeholder="$t('reports.description_placeholder')"
                ></textarea>
              </div>
            </div>

            <div>
              <label for="report-type" class="block text-sm font-medium text-gray-700">{{ $t('reports.report_type') }}</label>
              <div class="mt-1">
                <select
                  id="report-type"
                  v-model="newReport.type"
                  class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="financial">{{ $t('reports.financial_summary') }}</option>
                  <option value="expenses">{{ $t('reports.expenses_only') }}</option>
                  <option value="incomes">{{ $t('reports.income_only') }}</option>
                </select>
              </div>
            </div>
          </form>

          <div class="mt-6 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              @click="createReport"
              :disabled="!newReport.name.trim() || creating"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="creating" class="animate-spin h-4 w-4 text-white" :class="$isRTL() ? 'ml-2 -mr-1' : '-ml-1 mr-2'" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ creating ? $t('reports.creating') : $t('reports.create_report') }}
            </button>
            <button
              @click="showCreateModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm"
            >
              {{ $t('common.cancel') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  name: 'AdvancedReporting',
  mixins: [i18nMixin],
  data() {
    return {
      loading: true,
      creating: false,
      generatingQuick: null,
      showCreateModal: false,
      stats: {},
      customReports: [],
      error: null,
      newReport: {
        name: '',
        description: '',
        type: 'financial'
      }
    };
  },
  computed: {
    hasAdvancedReporting() {
      return this.stats.has_advanced_reporting || false;
    },
    canCreateReports() {
      return this.stats.has_custom_reports && this.stats.can_create_more;
    }
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      try {
        this.loading = true;
        this.error = null;

        // Load stats first
        const statsResponse = await this.$http.get('/advanced-reporting/stats');
        this.stats = statsResponse.data;

        // Only load custom reports if the user has access
        if (this.stats.has_custom_reports) {
          try {
            const reportsResponse = await this.$http.get('/advanced-reporting/reports');
            const reports = reportsResponse.data.data || reportsResponse.data.reports || [];

            // Ensure each report has a generating property for reactivity
            this.customReports = reports.map(report => ({
              ...report,
              generating: false
            }));
          } catch (reportsError) {
            console.warn('Could not load custom reports:', reportsError);
            this.customReports = [];
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
        this.handleError(error);
      } finally {
        this.loading = false;
      }
    },

    handleError(error) {
      if (error.response?.status === 403) {
        this.error = 'package_upgrade_required';
      } else if (error.response?.status === 401) {
        this.error = 'authentication_required';
      } else {
        this.error = 'error_loading_data';
      }
    },

    getErrorTitle() {
      switch (this.error) {
        case 'package_upgrade_required':
          return this.$t('reports.errors.upgrade_required');
        case 'authentication_required':
          return this.$t('reports.errors.authentication_required');
        default:
          return this.$t('reports.errors.something_wrong');
      }
    },

    getErrorMessage() {
      switch (this.error) {
        case 'package_upgrade_required':
          return this.$t('reports.errors.upgrade_required_desc');
        case 'authentication_required':
          return this.$t('reports.errors.authentication_required_desc');
        default:
          return this.$t('reports.errors.something_wrong_desc');
      }
    },

    getErrorActionText() {
      switch (this.error) {
        case 'package_upgrade_required':
          return this.$t('reports.errors.view_packages');
        case 'authentication_required':
          return this.$t('reports.errors.go_to_login');
        default:
          return this.$t('reports.errors.try_again');
      }
    },

    handleErrorAction() {
      switch (this.error) {
        case 'package_upgrade_required':
          this.$router.push('/admin/package-management');
          break;
        case 'authentication_required':
          this.$router.push('/login');
          break;
        default:
          this.loadData();
          break;
      }
    },

    async generateQuickReport(type) {
      try {
        this.generatingQuick = type;

        const response = await this.$http.post('/advanced-reporting/quick-generate', {
          type
        }, {
          responseType: 'blob'
        });

        // Create blob and download
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${type}-report-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} report downloaded successfully!`);

        // Refresh stats to update generation count
        const statsResponse = await this.$http.get('/advanced-reporting/stats');
        this.stats = statsResponse.data;
      } catch (error) {
        console.error('Error generating quick report:', error);
        if (error.response?.status === 403) {
          this.$toast.error('You need to upgrade your package to generate reports');
        } else if (error.response?.status === 401) {
          this.$toast.error('Please log in to generate reports');
        } else {
          this.$toast.error('Failed to generate report. Please try again.');
        }
      } finally {
        this.generatingQuick = null;
      }
    },

    async createReport() {
      // Validate required fields
      if (!this.newReport.name.trim()) {
        this.$toast.error('Please enter a report name');
        return;
      }

      if (this.newReport.name.trim().length < 3) {
        this.$toast.error('Report name must be at least 3 characters');
        return;
      }

      try {
        this.creating = true;

        // Get the default template ID
        const templateId = await this.getDefaultTemplateId();
        if (!templateId) {
          this.$toast.error('No report templates available. Please contact support.');
          return;
        }

        // Configure report based on type
        const configuration = this.getReportConfiguration(this.newReport.type);

        await this.$http.post('/advanced-reporting/reports', {
          name: this.newReport.name.trim(),
          description: this.newReport.description.trim(),
          template_id: templateId,
          configuration,
          chart_config: {
            type: 'bar',
            title: this.newReport.name.trim()
          },
          is_public: false
        });

        this.$toast.success('Report created successfully!');
        this.showCreateModal = false;
        this.resetNewReport();
        await this.loadData();
      } catch (error) {
        console.error('Error creating report:', error);
        this.handleCreateError(error);
      } finally {
        this.creating = false;
      }
    },

    async getDefaultTemplateId() {
      try {
        const response = await this.$http.get('/advanced-reporting/templates');
        const templates = response.data;

        // Find the financial summary template first, or use the first available
        const financialTemplate = templates.find(t => t.slug === 'financial-summary');
        if (financialTemplate) {
          return financialTemplate.id;
        }

        // Fallback to first template
        return templates.length > 0 ? templates[0].id : null;
      } catch (error) {
        console.warn('Could not load templates:', error);
        return 1; // Fallback to ID 1
      }
    },

    getReportConfiguration(type) {
      const baseConfig = {
        filters: {},
        grouping: []
      };

      switch (type) {
        case 'financial':
          return {
            ...baseConfig,
            fields: ['amount', 'created_at', 'expense_type_name', 'user_name'],
            data_source: 'expenses'
          };
        case 'expenses':
          return {
            ...baseConfig,
            fields: ['amount', 'created_at', 'expense_type_name', 'description'],
            data_source: 'expenses'
          };
        case 'incomes':
          return {
            ...baseConfig,
            fields: ['amount', 'created_at', 'description', 'user_name'],
            data_source: 'incomes'
          };
        default:
          return {
            ...baseConfig,
            fields: ['amount', 'created_at'],
            data_source: 'expenses'
          };
      }
    },

    handleCreateError(error) {
      if (error.response?.status === 422) {
        const errors = error.response.data.errors;
        const errorMessages = Object.values(errors).flat();
        this.$toast.error(errorMessages.join(', '));
      } else if (error.response?.status === 403) {
        this.$toast.error(error.response.data.message || 'You need to upgrade your package to create custom reports');
      } else if (error.response?.status === 400) {
        this.$toast.error(error.response.data.message || 'Invalid request');
      } else {
        this.$toast.error('Failed to create report. Please try again.');
      }
    },

    resetNewReport() {
      this.newReport = {
        name: '',
        description: '',
        type: 'financial'
      };
    },

    async runReport(report) {
      try {
        // Find the report in our reactive array and update it
        let reportIndex = this.customReports.findIndex(r => r.id === report.id);
        if (reportIndex !== -1) {
          this.customReports[reportIndex].generating = true;
        }

        // Generate the report
        await this.$http.post(`/advanced-reporting/reports/${report.id}/generate`);

        // Download the generated report
        const downloadResponse = await this.$http.get(`/advanced-reporting/reports/${report.id}/download`, {
          responseType: 'blob'
        });

        const blob = new Blob([downloadResponse.data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${report.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$toast.success(`"${report.name}" downloaded successfully!`);

        // Update the report's generation count in our reactive array
        reportIndex = this.customReports.findIndex(r => r.id === report.id);
        if (reportIndex !== -1) {
          this.customReports[reportIndex].generations_count = (this.customReports[reportIndex].generations_count || 0) + 1;
        }

        // Refresh stats
        const statsResponse = await this.$http.get('/advanced-reporting/stats');
        this.stats = statsResponse.data;
      } catch (error) {
        console.error('Error running report:', error);
        if (error.response?.status === 403) {
          this.$toast.error('You need to upgrade your package to generate reports');
        } else if (error.response?.status === 404) {
          this.$toast.error('Report not found');
        } else {
          this.$toast.error('Failed to generate report. Please try again.');
        }
      } finally {
        // Find the report in our reactive array and update it
        const finalReportIndex = this.customReports.findIndex(r => r.id === report.id);
        if (finalReportIndex !== -1) {
          this.customReports[finalReportIndex].generating = false;
        }
      }
    },

    async deleteReport(report) {
      const confirmed = await this.confirmDelete(report.name);
      if (!confirmed) return;

      try {
        await this.$http.delete(`/advanced-reporting/reports/${report.id}`);
        this.$toast.success(`"${report.name}" deleted successfully!`);
        await this.loadData();
      } catch (error) {
        console.error('Error deleting report:', error);
        if (error.response?.status === 403) {
          this.$toast.error('You do not have permission to delete this report');
        } else if (error.response?.status === 404) {
          this.$toast.error('Report not found');
        } else {
          this.$toast.error('Failed to delete report. Please try again.');
        }
      }
    },

    confirmDelete(reportName) {
      return new Promise((resolve) => {
        const confirmed = confirm(`Are you sure you want to delete "${reportName}"?\n\nThis action cannot be undone.`);
        resolve(confirmed);
      });
    },

    formatDate(dateString) {
      try {
        return new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        return 'Invalid date';
      }
    }
  }
};
</script>

<style scoped>
.advanced-reporting {
  min-height: 100vh;
}

/* RTL Support */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* Modal z-index fix */
.fixed.inset-0.z-50 {
  z-index: 9999 !important;
}

/* Ensure modal content is properly positioned */
.fixed.inset-0.z-50 .relative {
  position: relative;
  z-index: 10000;
}

/* Custom button hover effects */
.group:hover .group-hover\:shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus states for accessibility */
.focus\:ring-2:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* RTL-specific adjustments */
.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .text-left {
  text-align: right;
}

.rtl .text-right {
  text-align: left;
}

/* Ensure proper text alignment for Arabic */
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  text-align: right;
}

.rtl p, .rtl span {
  text-align: right;
}

/* Button text alignment */
.rtl button {
  text-align: center;
}

/* Form inputs RTL */
.rtl input, .rtl textarea, .rtl select {
  text-align: right;
}
</style>
