<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON>er -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <router-link 
                    to="/admin/expenses" 
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {{ $t('expenses') }}
                  </router-link>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500">{{ $t('edit_expense') }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('edit_expense') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('edit_expense_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <router-link
              to="/admin/expenses"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('back_to_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-center py-12">
          <svg class="animate-spin w-8 h-8 text-blue-600" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="ml-3 text-gray-600">{{ $t('loading_expense') }}</span>
        </div>
      </div>

      <div v-else-if="expense" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('expense_details') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('edit_expense_details_description') }}</p>
        </div>
        
        <div class="p-6">
          <expense-form
            :expense="expense"
            :is-edit="true"
            @success="handleSuccess"
            @error="handleError"
            @cancel="handleCancel"
          />
        </div>
      </div>

      <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('expense_not_found') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('expense_not_found_description') }}</p>
          <div class="mt-6">
            <router-link
              to="/admin/expenses"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              {{ $t('back_to_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </div>
</template>

<script>
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    ExpenseForm,
    Notification
  },
  data() {
    return {
      loading: true,
      expense: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async created() {
    await this.loadExpense();
  },
  methods: {
    async loadExpense() {
      this.loading = true;
      try {
        const expenseId = this.$route.params.id;
        const response = await this.$axios.get(`/expenses/${expenseId}`);
        this.expense = response.data;
      } catch (error) {
        console.error('Error loading expense:', error);
        if (error.response?.status === 404) {
          this.expense = null;
        } else {
          this.showError('Loading Failed', 'Failed to load expense details');
        }
      } finally {
        this.loading = false;
      }
    },
    handleSuccess() {
      this.showSuccess('Expense Updated', 'The expense has been updated successfully.');
      setTimeout(() => {
        this.$router.push('/admin/expenses');
      }, 1500);
    },
    handleError(message) {
      this.showError('Update Failed', message);
    },
    handleCancel() {
      this.$router.push('/admin/expenses');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>