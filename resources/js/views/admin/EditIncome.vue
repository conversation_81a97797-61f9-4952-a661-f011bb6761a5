<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <router-link
                    to="/admin/incomes"
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v0M8 5a2 2 0 012-2h2a2 2 0 012 2v0" />
                    </svg>
                    {{ $t('incomes') }}
                  </router-link>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ $t('edit_income') }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('edit_income') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('edit_income_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <router-link
              to="/admin/incomes"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('back_to_incomes') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading" class="text-center py-4">
        <p>{{ $t('loading_income') }}</p>
      </div>

      <div v-else-if="income" class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('income_details') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('edit_income_details_description') }}</p>
        </div>
        
        <div class="p-6">
          <income-form
            :income="income"
            :is-edit="true"
            @success="handleSuccess"
            @error="handleError"
            @cancel="handleCancel"
          />
        </div>
      </div>

      <div v-else class="text-center py-8">
        <p class="text-red-500">{{ $t('income_not_found') }}</p>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </div>
</template>

<script>
import IncomeForm from '../../components/IncomeForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    IncomeForm,
    Notification
  },
  data() {
    return {
      loading: true,
      income: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async created() {
    await this.loadIncome();
  },
  methods: {
    async loadIncome() {
      this.loading = true;
      try {
        const incomeId = this.$route.params.id;
        const response = await this.$axios.get(`/incomes/${incomeId}`);
        this.income = response.data;
      } catch (error) {
        console.error('Error loading income:', error);
        if (error.response?.status === 404) {
          this.income = null;
        } else {
          this.showError(this.$t('loading_failed'), this.$t('failed_load_income'));
        }
      } finally {
        this.loading = false;
      }
    },
    handleSuccess() {
      this.showSuccess(this.$t('income_updated'), this.$t('income_updated_successfully'));
      setTimeout(() => {
        this.$router.push('/admin/incomes');
      }, 1500);
    },
    handleError(message) {
      this.showError(this.$t('update_failed'), message);
    },
    handleCancel() {
      this.$router.push('/admin/incomes');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
