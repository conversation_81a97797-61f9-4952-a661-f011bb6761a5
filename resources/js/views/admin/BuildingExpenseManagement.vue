<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('building_expense_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_building_expenses_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <router-link to="/admin/building-expenses/create"
              class="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_building_expense') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_building_expenses') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalBuildingExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('this_month') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ thisMonthBuildingExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>


      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('filters') }}</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('building_expense_type') }}
            </label>
            <select id="type-filter" v-model="filters.type" @change="loadBuildingExpenses"
              class="w-full py-2 px-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">{{ $t('all_types') }}</option>
              <option v-for="type in buildingExpenseTypes" :key="type.id" :value="type.name">
                {{ type.name }}
              </option>
            </select>
          </div>

          <div>
            <label for="month-filter" class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('month') }}
            </label>
            <select id="month-filter" v-model="filters.month" @change="loadBuildingExpenses"
              class="w-full py-2 px-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">{{ $t('all_months') }}</option>
              <option value="01">{{ $t('january') }}</option>
              <option value="02">{{ $t('february') }}</option>
              <option value="03">{{ $t('march') }}</option>
              <option value="04">{{ $t('april') }}</option>
              <option value="05">{{ $t('may') }}</option>
              <option value="06">{{ $t('june') }}</option>
              <option value="07">{{ $t('july') }}</option>
              <option value="08">{{ $t('august') }}</option>
              <option value="09">{{ $t('september') }}</option>
              <option value="10">{{ $t('october') }}</option>
              <option value="11">{{ $t('november') }}</option>
              <option value="12">{{ $t('december') }}</option>
            </select>
          </div>

          <div>
            <label for="year-filter" class="block text-sm font-medium text-gray-700 mb-2">
              {{ $t('year') }}
            </label>
            <select id="year-filter" v-model="filters.year" @change="loadBuildingExpenses"
              class="w-full py-2 px-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">{{ $t('all_years') }}</option>
              <option v-for="year in yearOptions" :key="year" :value="year">
                {{ year }}
              </option>
            </select>
          </div>

          <div class="flex items-end">
            <button @click="clearFilters"
              class="w-full px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
              {{ $t('clear_filters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Building Expenses Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h2 class="text-lg font-medium text-gray-900">{{ $t('building_expenses') }}</h2>
              <p class="mt-1 text-sm text-gray-500">{{ $t('building_expenses_description') }}</p>
            </div>
          </div>
        </div>

        <data-table
          :columns="columns"
          :items="buildingExpenses"
          :loading="loading"
          :pagination="pagination"
          @page-changed="handlePageChange"
        >
          <template #actions="{ item }">
            <div class="flex items-center space-x-2">
              <button @click="viewBuildingExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                {{ $t('view') }}
              </button>
              <button @click="editBuildingExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {{ $t('edit') }}
              </button>
              <button @click="deleteBuildingExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {{ $t('delete') }}
              </button>
            </div>
          </template>
        </data-table>
      </div>

      <!-- Empty State -->
      <div v-if="!loading && buildingExpenses.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_building_expenses') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('get_started_by_creating_building_expense') }}</p>
          <div class="mt-6">
            <router-link to="/admin/building-expenses/create"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_first_building_expense') }}
            </router-link>
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <notification :show="showNotification" :type="notificationType" :title="notificationTitle"
        :message="notificationMessage" @close="closeNotification" />
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import BuildingExpenseForm from '../../components/BuildingExpenseForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable,
    BuildingExpenseForm,
    Notification
  },
  data() {
    return {
      loading: false,
      buildingExpenses: [],
      buildingExpenseTypes: [],
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      filters: {
        type: '',
        month: '',
        year: new Date().getFullYear()
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: []
    };
  },
  computed: {
    totalBuildingExpenses() {
      return this.buildingExpenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    thisMonthBuildingExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      return this.buildingExpenses
        .filter(expense =>
          parseInt(expense.month) === currentMonth &&
          parseInt(expense.year) === currentYear
        )
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },

    yearOptions() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let year = currentYear - 5; year <= currentYear + 2; year++) {
        years.push(year);
      }
      return years;
    }
  },
  async created() {
    await this.initializeUser();
    this.initializeColumns();
    await this.loadBuildingExpenseTypes();
    await this.loadBuildingExpenses();
  },
  methods: {
    async initializeUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
      } catch (error) {
        console.error('Error loading user:', error);
        this.userLoaded = true;
      }
    },
    initializeColumns() {
      this.columns = [
        { key: 'building_expense_type.name', label: this.$t('type'), sortable: true },
        { key: 'amount', label: this.$t('amount'), sortable: true, type: 'currency' },
        { key: 'month', label: this.$t('month'), sortable: true },
        { key: 'year', label: this.$t('year'), sortable: true },
        { key: 'notes', label: this.$t('notes'), sortable: false },
        { key: 'created_at', label: this.$t('created_at'), sortable: true, type: 'datetime' },
      ];
    },
    async loadBuildingExpenseTypes() {
      try {
        const response = await this.$axios.get('/building-expense-types');
        this.buildingExpenseTypes = response.data;
      } catch (error) {
        console.error('Error loading building expense types:', error);
      }
    },
    async loadBuildingExpenses() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.currentPage,
          per_page: 25,
          ...this.filters
        };

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key];
          }
        });

        const response = await this.$axios.get('/building-expenses', { params });

        if (response.data.data) {
          this.buildingExpenses = response.data.data;
          this.pagination = {
            currentPage: response.data.current_page,
            lastPage: response.data.last_page,
            from: response.data.from,
            to: response.data.to,
            total: response.data.total
          };
        } else {
          this.buildingExpenses = response.data;
        }
      } catch (error) {
        console.error('Error loading building expenses:', error);
        this.showError(this.$t('error'), this.$t('failed_to_load_building_expenses'));
      } finally {
        this.loading = false;
      }
    },
    handlePageChange(page) {
      this.pagination.currentPage = page;
      this.loadBuildingExpenses();
    },
    viewBuildingExpense(buildingExpense) {
      this.$router.push(`/admin/building-expenses/${buildingExpense.id}`);
    },
    editBuildingExpense(buildingExpense) {
      this.$router.push(`/admin/building-expenses/${buildingExpense.id}/edit`);
    },
    async deleteBuildingExpense(buildingExpense) {
      if (confirm(this.$t('confirm_delete_building_expense'))) {
        try {
          await this.$axios.delete(`/building-expenses/${buildingExpense.id}`);
          this.showSuccess(this.$t('deleted'), this.$t('building_expense_deleted_successfully'));
          this.loadBuildingExpenses();
        } catch (error) {
          this.showError(this.$t('deletion_failed'), error.response?.data?.message || this.$t('failed_to_delete_building_expense'));
        }
      }
    },
    clearFilters() {
      this.filters = {
        type: '',
        month: '',
        year: new Date().getFullYear()
      };
      this.loadBuildingExpenses();
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
