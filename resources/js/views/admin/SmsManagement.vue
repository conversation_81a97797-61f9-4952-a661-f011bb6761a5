<template>
  <div class="sms-management">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">{{ $t('sms_management') }}</h1>
          <p class="text-sm text-gray-600 mt-1">{{ $t('manage_sms_notifications_and_settings') }}</p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="showBulkSmsModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
            :disabled="!smsSettings.sms_enabled"
          >
            {{ $t('send_bulk_sms') }}
          </button>
          <button
            @click="showTestSmsModal = true"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
            :disabled="!smsSettings.sms_enabled"
          >
            {{ $t('test_sms') }}
          </button>
        </div>
      </div>
    </div>

    <!-- SMS Status Banner -->
    <div v-if="!smsSettings.sms_enabled" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">{{ $t('sms_notifications_disabled') }}</h3>
          <p class="text-sm text-yellow-700 mt-1">{{ $t('enable_sms_in_settings_to_start_sending') }}</p>
        </div>
      </div>
    </div>

    <!-- SMS Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 p-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('sms_sent_this_month') }}</p>
            <p class="text-2xl font-semibold text-gray-900">
              {{ monthlyUsage.sent || 0 }}
              <span v-if="monthlyUsage.limit" class="text-sm text-gray-500">
                / {{ monthlyUsage.limit }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('delivery_rate') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ deliveryRate }}%</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('monthly_cost') }}</p>
            <p class="text-2xl font-semibold text-gray-900">${{ (monthlyUsage.cost || 0).toFixed(2) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('sms_provider') }}</p>
            <p class="text-lg font-semibold text-gray-900">{{ getProviderName(smsSettings.sms_provider) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200 px-6">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'settings'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'settings'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('sms_settings') }}
        </button>
        <button
          @click="activeTab = 'templates'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'templates'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('sms_templates') }}
        </button>
        <button
          @click="activeTab = 'logs'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'logs'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('delivery_logs') }}
        </button>
        <button
          @click="activeTab = 'analytics'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'analytics'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('analytics') }}
        </button>
      </nav>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- SMS Settings -->
      <div v-if="activeTab === 'settings'">
        <SmsSettings 
          :settings="smsSettings"
          :supported-providers="supportedProviders"
          @update="handleSettingsUpdate"
        />
      </div>

      <!-- SMS Templates -->
      <div v-if="activeTab === 'templates'">
        <SmsTemplatesList 
          :templates="smsTemplates"
          @create="showCreateTemplateModal = true"
          @refresh="loadSmsTemplates"
        />
      </div>

      <!-- Delivery Logs -->
      <div v-if="activeTab === 'logs'">
        <SmsDeliveryLogs 
          :logs="deliveryLogs"
          @refresh="loadDeliveryLogs"
        />
      </div>

      <!-- Analytics -->
      <div v-if="activeTab === 'analytics'">
        <SmsAnalytics 
          :statistics="statistics"
          @refresh="loadStatistics"
        />
      </div>
    </div>

    <!-- Bulk SMS Modal -->
    <BulkSmsModal
      v-if="showBulkSmsModal"
      @close="showBulkSmsModal = false"
      @success="handleBulkSmsSent"
    />

    <!-- Test SMS Modal -->
    <TestSmsModal
      v-if="showTestSmsModal"
      @close="showTestSmsModal = false"
      @success="handleTestSmsSent"
    />

    <!-- Create Template Modal -->
    <CreateSmsTemplateModal
      v-if="showCreateTemplateModal"
      @close="showCreateTemplateModal = false"
      @success="handleTemplateCreated"
    />
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import SmsSettings from '../../components/SmsSettings.vue';
import SmsTemplatesList from '../../components/SmsTemplatesList.vue';
import SmsDeliveryLogs from '../../components/SmsDeliveryLogs.vue';
import SmsAnalytics from '../../components/SmsAnalytics.vue';
import BulkSmsModal from '../../components/BulkSmsModal.vue';
import TestSmsModal from '../../components/TestSmsModal.vue';
import CreateSmsTemplateModal from '../../components/CreateSmsTemplateModal.vue';

export default {
  name: 'SmsManagement',
  mixins: [i18nMixin],
  components: {
    SmsSettings,
    SmsTemplatesList,
    SmsDeliveryLogs,
    SmsAnalytics,
    BulkSmsModal,
    TestSmsModal,
    CreateSmsTemplateModal
  },
  data() {
    return {
      activeTab: 'settings',
      smsSettings: {},
      supportedProviders: {},
      statistics: {},
      smsTemplates: [],
      deliveryLogs: [],
      monthlyUsage: {},
      deliveryRate: 0,
      showBulkSmsModal: false,
      showTestSmsModal: false,
      showCreateTemplateModal: false,
      loading: true
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadSmsSettings(),
        this.loadSmsTemplates(),
        this.loadDeliveryLogs(),
        this.loadStatistics()
      ]);
    },

    async loadSmsSettings() {
      try {
        const response = await this.$http.get('/sms/settings');
        this.smsSettings = response.data.settings;
        this.supportedProviders = response.data.supported_providers;
        this.monthlyUsage = response.data.statistics.monthly_usage;
        this.deliveryRate = response.data.statistics.delivery_rate;
      } catch (error) {
        console.error('Error loading SMS settings:', error);
        this.$toast.error(this.$t('error_loading_sms_settings'));
      }
    },

    async loadSmsTemplates() {
      try {
        const response = await this.$http.get('/sms/templates');
        this.smsTemplates = response.data.templates;
      } catch (error) {
        console.error('Error loading SMS templates:', error);
        this.$toast.error(this.$t('error_loading_sms_templates'));
      }
    },

    async loadDeliveryLogs() {
      try {
        const response = await this.$http.get('/sms/delivery-logs?per_page=20');
        this.deliveryLogs = response.data.data;
      } catch (error) {
        console.error('Error loading delivery logs:', error);
        this.$toast.error(this.$t('error_loading_delivery_logs'));
      }
    },

    async loadStatistics() {
      try {
        const response = await this.$http.get('/sms/statistics');
        this.statistics = response.data;
      } catch (error) {
        console.error('Error loading SMS statistics:', error);
        this.$toast.error(this.$t('error_loading_sms_statistics'));
      }
    },

    async handleSettingsUpdate(settings) {
      try {
        await this.$http.put('/sms/settings', settings);
        await this.loadSmsSettings();
        this.$toast.success(this.$t('sms_settings_updated'));
      } catch (error) {
        console.error('Error updating SMS settings:', error);
        this.$toast.error(this.$t('error_updating_sms_settings'));
      }
    },

    async handleBulkSmsSent() {
      this.showBulkSmsModal = false;
      await this.loadDeliveryLogs();
      await this.loadStatistics();
      this.$toast.success(this.$t('bulk_sms_sent_successfully'));
    },

    async handleTestSmsSent() {
      this.showTestSmsModal = false;
      await this.loadDeliveryLogs();
      this.$toast.success(this.$t('test_sms_sent_successfully'));
    },

    async handleTemplateCreated() {
      this.showCreateTemplateModal = false;
      await this.loadSmsTemplates();
      this.$toast.success(this.$t('sms_template_created_successfully'));
    },

    getProviderName(provider) {
      const providers = {
        'twilio': 'Twilio',
        'aws_sns': 'AWS SNS'
      };
      return providers[provider] || provider;
    }
  }
};
</script>

<style scoped>
.sms-management {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
