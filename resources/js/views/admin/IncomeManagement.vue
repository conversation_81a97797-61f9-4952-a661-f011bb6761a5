<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('income_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_building_incomes') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <router-link to="/admin/incomes/create"
              class="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('record_new_income') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_income') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalIncome.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('this_month') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ thisMonthIncome.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('last_30_days') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ last30DaysIncome.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('average_monthly') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ averageMonthlyIncome.toFixed(2) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('filters') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('filter_incomes_description') }}</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('from_date') }}</label>
              <input type="date" v-model="filters.date_from"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                :dir="$isRTL() ? 'rtl' : 'ltr'" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('to_date') }}</label>
              <input type="date" v-model="filters.date_to"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                :dir="$isRTL() ? 'rtl' : 'ltr'" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('year') }}</label>
              <input type="number" v-model="filters.year"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                min="2023" max="2030" placeholder="2025" />
            </div>

            <div class="flex items-end">
              <button @click="applyFilters"
                class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                {{ $t('apply_filters') }}
              </button>
            </div>
          </div>

          <!-- Active Filters Display -->
          <div v-if="hasActiveFilters" class="mt-4 flex flex-wrap gap-2">
            <span class="text-sm text-gray-500">{{ $t('active_filters') }}:</span>
            <span v-if="filters.date_from"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ $t('from') }}: {{ formatDate(filters.date_from) }}
              <button @click="filters.date_from = ''; applyFilters()" class="ml-1 text-blue-600 hover:text-blue-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <span v-if="filters.date_to"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {{ $t('to') }}: {{ formatDate(filters.date_to) }}
              <button @click="filters.date_to = ''; applyFilters()" class="ml-1 text-green-600 hover:text-green-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <span v-if="filters.year && filters.year !== new Date().getFullYear()"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {{ $t('year') }}: {{ filters.year }}
              <button @click="filters.year = new Date().getFullYear(); applyFilters()"
                class="ml-1 text-purple-600 hover:text-purple-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <button @click="clearAllFilters"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors">
              {{ $t('clear_all') }}
              <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Income Table -->
      <data-table
        :title="$t('income_records')"
        :columns="columns"
        :items="incomes"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex items-center space-x-2">
            <button @click="viewIncome(item)"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {{ $t('view') }}
            </button>
            <button @click="editIncome(item)"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {{ $t('edit') }}
            </button>
            <button @click="deleteIncome(item)"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {{ $t('delete') }}
            </button>
          </div>
        </template>
      </data-table>

      <!-- Pagination -->
      <div class="mt-6 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          {{ $t('showing_results') }} {{ pagination.from }} {{ $t('to') }} {{ pagination.to }} {{ $t('of') }} {{ pagination.total }} {{ $t('results') }}
        </div>
        <div class="flex space-x-2">
          <button
            @click="previousPage"
            :disabled="pagination.currentPage === 1"
            class="px-3 py-1 border rounded disabled:opacity-50"
          >
            {{ $t('previous') }}
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.currentPage === pagination.lastPage"
            class="px-3 py-1 border rounded disabled:opacity-50"
          >
            {{ $t('next') }}
          </button>
        </div>
      </div>



      <!-- Notifications -->
      <notification
        :show="showNotification"
        :type="notificationType"
        :title="notificationTitle"
        :message="notificationMessage"
        @close="closeNotification"
      />
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable,
    Notification
  },
  data() {
    return {
      loading: false,
      incomes: [],
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      filters: {
        date_from: '',
        date_to: '',
        year: new Date().getFullYear()
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: []
    };
  },
  computed: {
    totalIncome() {
      return this.incomes.reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    thisMonthIncome() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate.getMonth() + 1 === currentMonth &&
                 paymentDate.getFullYear() === currentYear;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    last30DaysIncome() {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return this.incomes
        .filter(income => {
          const paymentDate = new Date(income.payment_date);
          return paymentDate >= thirtyDaysAgo;
        })
        .reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    averageMonthlyIncome() {
      if (this.incomes.length === 0) return 0;

      // Group incomes by month/year
      const monthlyTotals = {};
      this.incomes.forEach(income => {
        const date = new Date(income.payment_date);
        const key = `${date.getFullYear()}-${date.getMonth() + 1}`;
        if (!monthlyTotals[key]) {
          monthlyTotals[key] = 0;
        }
        monthlyTotals[key] += parseFloat(income.amount);
      });

      const months = Object.keys(monthlyTotals);
      if (months.length === 0) return 0;

      const total = Object.values(monthlyTotals).reduce((sum, amount) => sum + amount, 0);
      return total / months.length;
    },
    hasActiveFilters() {
      return this.filters.date_from ||
             this.filters.date_to ||
             (this.filters.year && this.filters.year !== new Date().getFullYear());
    }
  },
  async created() {
    this.initializeColumns();
    this.initializeUserFromStorage();
    await this.fetchUser();
    this.loadIncomes();
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString( 'en-US');
    },
    clearAllFilters() {
      this.filters = {
        date_from: '',
        date_to: '',
        year: new Date().getFullYear()
      };
      this.applyFilters();
    },
    initializeColumns() {
      this.columns = [
        { key: 'user.name', label: this.$t('neighbor') },
        { key: 'user.apartment_number', label: this.$t('apartment') },
        { key: 'amount', label: this.$t('amount') },
        { key: 'payment_date', label: this.$t('payment_date') },
        { key: 'payment_method', label: this.$t('method') },
        { key: 'notes', label: this.$t('notes') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    },
    async loadIncomes() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/incomes', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });
        
        this.incomes = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError(this.$t('error_loading_incomes'));
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadIncomes();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadIncomes();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadIncomes();
      }
    },

    viewIncome(income) {
      this.$router.push(`/admin/incomes/${income.id}`);
    },
    editIncome(income) {
      this.$router.push(`/admin/incomes/${income.id}/edit`);
    },
    async deleteIncome(income) {
      if (confirm(this.$t('confirm_delete_income'))) {
        try {
          await this.$axios.delete(`/incomes/${income.id}`);
          this.showSuccess(this.$t('deleted'), this.$t('income_deleted'));
          this.loadIncomes();
        } catch (error) {
          this.showError(this.$t('delete_failed'), this.$t('failed_delete_income'));
        }
      }
    },

    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
