<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
              <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                  <router-link 
                    to="/admin/expenses" 
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    {{ $t('expenses') }}
                  </router-link>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500">{{ $t('view_expense') }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('expense_details') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('view_expense_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex gap-3">
            <router-link
              :to="`/admin/expenses/${expense.id}/edit`"
              v-if="expense"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {{ $t('edit_expense') }}
            </router-link>
            <router-link
              to="/admin/expenses"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {{ $t('back_to_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-sm text-gray-500">{{ $t('loading_expense') }}</p>
        </div>
      </div>

      <div v-else-if="expense" class="space-y-6">
        <!-- Basic Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('basic_information') }}</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('expense_type') }}</label>
                <p class="text-sm text-gray-900">{{ expense.expense_type?.name || '-' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('neighbor') }}</label>
                <p class="text-sm text-gray-900">{{ expense.user?.name || '-' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('amount') }}</label>
                <p class="text-lg font-semibold text-gray-900">{{ formatCurrency(expense.amount) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('month_year') }}</label>
                <p class="text-sm text-gray-900">{{ formatMonthYear(expense.month, expense.year) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('type') }}</label>
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  expense.is_automatic ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                ]">
                  {{ expense.is_automatic ? $t('automatic') : $t('manual') }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('created_at') }}</label>
                <p class="text-sm text-gray-900">{{ formatDate(expense.created_at) }}</p>
              </div>
            </div>
            <div v-if="expense.notes" class="mt-6">
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('notes') }}</label>
              <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{{ expense.notes }}</p>
            </div>
          </div>
        </div>

        <!-- Attachments Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('attachments') }}</h2>
          </div>
          <div class="p-6">
            <file-manager
              :attachable-type="'App\\Models\\Expense'"
              :attachable-id="expense.id"
              :auto-load="true"
              :can-manage="false"
              :show-upload="false"
            />
          </div>
        </div>
      </div>

      <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('expense_not_found') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('expense_not_found_description') }}</p>
          <div class="mt-6">
            <router-link
              to="/admin/expenses"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              {{ $t('back_to_expenses') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </div>
</template>

<script>
import FileManager from '../../components/FileManager.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    FileManager,
    Notification
  },
  data() {
    return {
      loading: true,
      expense: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  async created() {
    await this.loadExpense();
  },
  methods: {
    async loadExpense() {
      this.loading = true;
      try {
        const response = await this.$axios.get(`/expenses/${this.$route.params.id}`);
        this.expense = response.data;
      } catch (error) {
        console.error('Error loading expense:', error);
        if (error.response?.status === 404) {
          this.expense = null;
        } else {
          this.showError('Loading Failed', 'Failed to load expense details.');
        }
      } finally {
        this.loading = false;
      }
    },
    formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    },
    formatMonthYear(month, year) {
      if (!month || !year) return '-';
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      const monthIndex = parseInt(month) - 1;
      if (monthIndex < 0 || monthIndex >= 12) return '-';
      return `${monthNames[monthIndex]} ${year}`;
    },
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        return new Date(dateString).toLocaleDateString();
      } catch (error) {
        return '-';
      }
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
