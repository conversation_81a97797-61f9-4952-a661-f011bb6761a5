<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('notification_template_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_notification_templates') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="openCreateModal"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_template') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Template Statistics -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('active_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.active || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('email_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.email || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('sms_templates') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ templateStats.sms || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Templates Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('notification_templates') }}</h2>
            <div class="mt-4 sm:mt-0 flex space-x-3">
              <select v-model="filters.type" @change="loadTemplates" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option value="">{{ $t('all_types') }}</option>
                <option value="payment_reminder">{{ $t('payment_reminder') }}</option>
                <option value="expense_notification">{{ $t('expense_notification') }}</option>
                <option value="general_announcement">{{ $t('general_announcement') }}</option>
                <option value="overdue_notification">{{ $t('overdue_notification') }}</option>
              </select>
              <select v-model="filters.active" @change="loadTemplates" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option value="">{{ $t('all_statuses') }}</option>
                <option value="true">{{ $t('active_only') }}</option>
                <option value="false">{{ $t('inactive_only') }}</option>
              </select>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('name') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('type') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('priority') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('status') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('created_by') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('created_at') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('actions') }}</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="template in templates" :key="template.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ template.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ $t(template.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getPriorityClass(template.priority)">
                    {{ $t(template.priority || 'normal') }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                    {{ template.is_active ? $t('active') : $t('inactive') }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ template.creator?.name || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(template.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="previewTemplate(template)"
                      class="text-green-600 hover:text-green-900"
                    >
                      {{ $t('preview') }}
                    </button>
                    <button
                      @click="editTemplate(template)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      {{ $t('edit') }}
                    </button>
                    <button
                      @click="toggleTemplate(template)"
                      :class="template.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'"
                    >
                      {{ template.is_active ? $t('deactivate') : $t('activate') }}
                    </button>
                    <button
                      @click="deleteTemplate(template)"
                      class="text-red-600 hover:text-red-900"
                    >
                      {{ $t('delete') }}
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="templates.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_templates') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('no_notification_templates_description') }}</p>
        </div>
      </div>
    </div>

    <!-- Create/Edit Template Modal -->
    <NotificationTemplateModal
      v-if="showCreateModal || editingTemplate"
      :template="editingTemplate"
      @close="closeModal"
      @success="handleTemplateSuccess"
    />

    <!-- Preview Modal -->
    <NotificationTemplatePreviewModal
      v-if="previewingTemplate"
      :template="previewingTemplate"
      @close="previewingTemplate = null"
    />
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import NotificationTemplateModal from '../../components/NotificationTemplateModal.vue';
import NotificationTemplatePreviewModal from '../../components/NotificationTemplatePreviewModal.vue';

export default {
  name: 'NotificationTemplateManagement',
  mixins: [i18nMixin],
  components: {
    NotificationTemplateModal,
    NotificationTemplatePreviewModal
  },
  data() {
    return {
      loading: false,
      templates: [],
      templateStats: {},
      showCreateModal: false,
      editingTemplate: null,
      previewingTemplate: null,
      filters: {
        type: '',
        active: ''
      }
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadTemplates(),
        this.loadTemplateStats()
      ]);
    },

    async loadTemplates() {
      this.loading = true;
      try {
        const params = {};
        if (this.filters.type) params.type = this.filters.type;
        if (this.filters.active !== '') params.active = this.filters.active;

        const response = await this.$axios.get('/notification-templates', { params });
        this.templates = response.data.data || response.data || [];
      } catch (error) {
        console.error('Error loading templates:', error);
      } finally {
        this.loading = false;
      }
    },

    async loadTemplateStats() {
      try {
        const response = await this.$axios.get('/notification-templates/stats');
        this.templateStats = response.data;
      } catch (error) {
        console.error('Error loading template stats:', error);
      }
    },

    previewTemplate(template) {
      this.previewingTemplate = template;
    },

    editTemplate(template) {
      this.editingTemplate = template;
    },

    async toggleTemplate(template) {
      try {
        await this.$axios.put(`/notification-templates/${template.id}`, {
          is_active: !template.is_active
        });
        await this.loadData();
        this.$toast.success(this.$t('template_updated_successfully'));
      } catch (error) {
        console.error('Error toggling template:', error);
        this.$toast.error(this.$t('update_failed'));
      }
    },

    async deleteTemplate(template) {
      if (!confirm(this.$t('confirm_delete_template'))) return;

      try {
        await this.$axios.delete(`/notification-templates/${template.id}`);
        await this.loadData();
        this.$toast.success(this.$t('template_deleted_successfully'));
      } catch (error) {
        console.error('Error deleting template:', error);
        this.$toast.error(this.$t('delete_failed'));
      }
    },

    openCreateModal() {
      console.log('Opening create modal');
      this.editingTemplate = null;
      this.showCreateModal = true;
      console.log('showCreateModal is now:', this.showCreateModal);
    },

    closeModal() {
      console.log('Closing modal');
      this.showCreateModal = false;
      this.editingTemplate = null;
    },

    handleTemplateSuccess() {
      this.closeModal();
      this.loadData();
    },

    getPriorityClass(priority) {
      const classes = {
        'low': 'bg-gray-100 text-gray-800',
        'medium': 'bg-blue-100 text-blue-800',
        'high': 'bg-yellow-100 text-yellow-800'
      };
      return classes[priority] || classes['medium'];
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    }
  }
};
</script>
