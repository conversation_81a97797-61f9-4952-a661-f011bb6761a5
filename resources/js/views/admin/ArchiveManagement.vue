<template>
  <div class="archive-management">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">{{ $t('archive_management') }}</h1>
          <p class="text-sm text-gray-600 mt-1">{{ $t('manage_archived_records') }}</p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="showAutoArchiveModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
            :disabled="!hasArchiveFeature"
          >
            {{ $t('auto_archive') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Feature Not Available Notice -->
    <div v-if="!hasArchiveFeature" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">{{ $t('archive_feature_unavailable') }}</h3>
          <p class="text-sm text-yellow-700 mt-1">{{ $t('archive_feature_premium_only') }}</p>
        </div>
      </div>
    </div>

    <!-- Archive Statistics -->
    <div v-if="hasArchiveFeature" class="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('archived_expenses') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ archiveStats.archived_expenses || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('archived_incomes') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ archiveStats.archived_incomes || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('total_archived') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ archiveStats.total_archived || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div v-if="hasArchiveFeature" class="border-b border-gray-200 px-6">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'expenses'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'expenses'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('archived_expenses') }}
        </button>
        <button
          @click="activeTab = 'incomes'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'incomes'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('archived_incomes') }}
        </button>
      </nav>
    </div>

    <!-- Content -->
    <div v-if="hasArchiveFeature" class="p-6">
      <!-- Archived Expenses -->
      <div v-if="activeTab === 'expenses'">
        <ArchivedExpensesList 
          @unarchive="handleUnarchiveExpenses"
          @refresh="loadArchiveStats"
        />
      </div>

      <!-- Archived Incomes -->
      <div v-if="activeTab === 'incomes'">
        <ArchivedIncomesList 
          @unarchive="handleUnarchiveIncomes"
          @refresh="loadArchiveStats"
        />
      </div>
    </div>

    <!-- Auto Archive Modal -->
    <AutoArchiveModal
      v-if="showAutoArchiveModal"
      @close="showAutoArchiveModal = false"
      @success="handleAutoArchiveSuccess"
    />
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import ArchivedExpensesList from '../../components/ArchivedExpensesList.vue';
import ArchivedIncomesList from '../../components/ArchivedIncomesList.vue';
import AutoArchiveModal from '../../components/AutoArchiveModal.vue';

export default {
  name: 'ArchiveManagement',
  mixins: [i18nMixin],
  components: {
    ArchivedExpensesList,
    ArchivedIncomesList,
    AutoArchiveModal
  },
  data() {
    return {
      activeTab: 'expenses',
      archiveStats: {},
      hasArchiveFeature: false,
      showAutoArchiveModal: false,
      loading: true
    };
  },
  async mounted() {
    await this.loadArchiveStats();
  },
  methods: {
    async loadArchiveStats() {
      try {
        this.loading = true;
        const response = await this.$http.get('/api/archive/stats');
        this.archiveStats = response.data;
        this.hasArchiveFeature = response.data.has_feature;
      } catch (error) {
        console.error('Error loading archive stats:', error);
        this.$toast.error(this.$t('error_loading_archive_stats'));
      } finally {
        this.loading = false;
      }
    },
    async handleUnarchiveExpenses(expenseIds) {
      try {
        const response = await this.$http.post('/api/archive/expenses/unarchive', {
          expense_ids: expenseIds
        });
        
        this.$toast.success(response.data.message);
        await this.loadArchiveStats();
      } catch (error) {
        console.error('Error unarchiving expenses:', error);
        this.$toast.error(error.response?.data?.message || this.$t('error_unarchiving_expenses'));
      }
    },
    async handleUnarchiveIncomes(incomeIds) {
      try {
        const response = await this.$http.post('/api/archive/incomes/unarchive', {
          income_ids: incomeIds
        });
        
        this.$toast.success(response.data.message);
        await this.loadArchiveStats();
      } catch (error) {
        console.error('Error unarchiving incomes:', error);
        this.$toast.error(error.response?.data?.message || this.$t('error_unarchiving_incomes'));
      }
    },
    async handleAutoArchiveSuccess() {
      this.showAutoArchiveModal = false;
      await this.loadArchiveStats();
    }
  }
};
</script>

<style scoped>
.archive-management {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
