<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('building_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_buildings_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <router-link to="/admin/buildings/create"
              class="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('add_building') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div v-if="loading && !userLoaded" class="text-center py-4">
        <p>{{ $t('loading') }}</p>
      </div>

      <div v-else-if="isSuperAdmin">

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('total_buildings') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ totalBuildings }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('active_buildings') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ activeBuildings }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('average_monthly_fee') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ averageMonthlyFee.toFixed(2) }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-600">{{ $t('total_residents') }}</p>
                <p class="text-2xl font-bold text-gray-900">{{ totalResidents }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('filters') }}</h2>
            <p class="mt-1 text-sm text-gray-500">{{ $t('filter_buildings_description') }}</p>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('status') }}</label>
                <select v-model="filters.status"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">{{ $t('all_statuses') }}</option>
                  <option value="active">{{ $t('active') }}</option>
                  <option value="inactive">{{ $t('inactive') }}</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('monthly_fee_range') }}</label>
                <select v-model="filters.fee_range"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">{{ $t('all_ranges') }}</option>
                  <option value="0-100">0 - 100</option>
                  <option value="100-200">100 - 200</option>
                  <option value="200-300">200 - 300</option>
                  <option value="300+">300+</option>
                </select>
              </div>

              <div class="flex items-end">
                <button @click="applyFilters"
                  class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  {{ $t('apply_filters') }}
                </button>
              </div>
            </div>

            <!-- Active Filters Display -->
            <div v-if="hasActiveFilters" class="mt-4 flex flex-wrap gap-2">
              <span class="text-sm text-gray-500">{{ $t('active_filters') }}:</span>
              <span v-if="filters.status"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {{ $t('status') }}: {{ $t(filters.status) }}
                <button @click="filters.status = ''; applyFilters()" class="ml-1 text-blue-600 hover:text-blue-800">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
              <span v-if="filters.fee_range"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {{ $t('fee_range') }}: {{ filters.fee_range }}
                <button @click="filters.fee_range = ''; applyFilters()" class="ml-1 text-green-600 hover:text-green-800">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
              <button @click="clearAllFilters"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors">
                {{ $t('clear_all') }}
                <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Buildings Table -->
        <data-table
          :title="$t('buildings')"
          :columns="columns"
          :items="filteredBuildings"
          :loading="loading"
        >
          <template #actions="{ item }">
            <div class="flex space-x-2">
              <button
                @click="editBuilding(item)"
                class="text-indigo-600 hover:text-indigo-900"
              >
                {{ $t('edit') }}
              </button>
              <button
                @click="deleteBuilding(item)"
                class="text-red-600 hover:text-red-900"
              >
                {{ $t('delete') }}
              </button>
            </div>
          </template>
        </data-table>
      </div>

      <!-- Edit Modal -->
      <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center" @click="handleEditModalOutsideClick">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4" @click.stop>
          <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-900">{{ $t('edit_building') }}</h2>
            <button @click="handleEditCancel" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <building-form
              :building="selectedBuilding"
              :is-edit="true"
              :is-my-building="false"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="handleEditCancel"
            />
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <notification
        :show="showNotification"
        :type="notificationType"
        :title="notificationTitle"
        :message="notificationMessage"
        @close="closeNotification"
      />

      <div v-if="!isSuperAdmin && userLoaded" class="text-center py-8">
        <p class="text-red-500">{{ $t('no_permission') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';
import smartModalMixin from '../../mixins/smartModalMixin.js';

export default {
  mixins: [i18nMixin, smartModalMixin],
  components: {
    DataTable,
    BuildingForm,
    Notification
  },
  data() {
    return {
      loading: false,
      buildings: [],
      selectedBuilding: null,
      showEditModal: false,
      editFormData: null, // Track form data for change detection
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      columns: [],
      filters: {
        status: '',
        fee_range: ''
      }
    };
  },
  computed: {
    // Override smart modal mixin to track BuildingForm changes
    hasFormChanges() {
      if (!this.editFormData || !this.selectedBuilding) return false;

      // Compare current building data with original
      const originalData = {
        name: this.selectedBuilding.name || '',
        address: this.selectedBuilding.address || '',
        city: this.selectedBuilding.city || '',
        country: this.selectedBuilding.country || '',
        postal_code: this.selectedBuilding.postal_code || '',
        description: this.selectedBuilding.description || '',
        monthly_fee: this.selectedBuilding.monthly_fee || 70.00
      };

      return JSON.stringify(this.editFormData) !== JSON.stringify(originalData);
    },
    totalBuildings() {
      return this.buildings.length;
    },
    activeBuildings() {
      return this.buildings.filter(b => b.admin_count > 0).length;
    },
    averageMonthlyFee() {
      if (this.buildings.length === 0) return 0;
      const total = this.buildings.reduce((sum, building) =>
        sum + parseFloat(building.monthly_fee || 0), 0);
      return total / this.buildings.length;
    },
    totalResidents() {
      return this.buildings.reduce((total, building) =>
        total + (building.neighbor_count || 0), 0);
    },
    filteredBuildings() {
      let filtered = this.buildings;

      if (this.filters.status) {
        if (this.filters.status === 'active') {
          filtered = filtered.filter(building => building.admin_count > 0);
        } else if (this.filters.status === 'inactive') {
          filtered = filtered.filter(building => building.admin_count === 0);
        }
      }

      if (this.filters.fee_range) {
        filtered = filtered.filter(building => {
          const fee = parseFloat(building.monthly_fee || 0);
          switch (this.filters.fee_range) {
            case '0-100':
              return fee >= 0 && fee <= 100;
            case '100-200':
              return fee > 100 && fee <= 200;
            case '200-300':
              return fee > 200 && fee <= 300;
            case '300+':
              return fee > 300;
            default:
              return true;
          }
        });
      }

      return filtered;
    },
    hasActiveFilters() {
      return this.filters.status || this.filters.fee_range;
    }
  },
  async mounted() {
    this.initializeUserFromStorage();
    this.initializeColumns();
    await this.fetchUser();
    if (this.isSuperAdmin) {
      this.loadBuildings();
    }
  },
  methods: {
    initializeColumns() {
      this.columns = [
        { key: 'name', label: this.$t('building_name') },
        { key: 'city', label: this.$t('city') },
        { key: 'monthly_fee_display', label: this.$t('monthly_fee') },
        { key: 'admin_count', label: this.$t('admins') },
        { key: 'neighbor_count', label: this.$t('neighbors') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
        this.$router.push('/login');
      }
    },
    async loadBuildings() {
      this.loading = true;
      try {
        // Load buildings
        const buildingsResponse = await this.$axios.get('/buildings');

        // Load users to calculate counts
        const usersResponse = await this.$axios.get('/admin/users');
        const allUsers = usersResponse.data.data || usersResponse.data;

        this.buildings = buildingsResponse.data.map(building => ({
          ...building,
          monthly_fee_display: `${parseFloat(building.monthly_fee || 0).toFixed(2)}`,
          admin_count: allUsers.filter(user =>
            user.building_id === building.id && user.role === 'admin'
          ).length,
          neighbor_count: allUsers.filter(user =>
            user.building_id === building.id && user.role === 'neighbor'
          ).length
        }));
      } catch (error) {
        console.error('Error loading buildings:', error);
        this.showError(this.$t('error'), this.$t('failed_load_buildings'));
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      // Filters are applied automatically through computed property
    },
    clearAllFilters() {
      this.filters = {
        status: '',
        fee_range: ''
      };
    },
    editBuilding(building) {
      this.selectedBuilding = building;
      this.initializeEditFormTracking();
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedBuilding = null;
      this.editFormData = null;
    },

    handleEditModalOutsideClick() {
      if (!this.hasFormChanges) {
        this.closeEditModal();
      } else {
        this.showUnsavedChangesAlert();
      }
    },

    handleEditCancel() {
      if (!this.hasFormChanges) {
        this.closeEditModal();
      } else {
        this.showDiscardChangesConfirmation();
      }
    },

    initializeEditFormTracking() {
      if (this.selectedBuilding) {
        this.editFormData = {
          name: this.selectedBuilding.name || '',
          address: this.selectedBuilding.address || '',
          city: this.selectedBuilding.city || '',
          country: this.selectedBuilding.country || '',
          postal_code: this.selectedBuilding.postal_code || '',
          description: this.selectedBuilding.description || '',
          monthly_fee: this.selectedBuilding.monthly_fee || 70.00
        };
      }
    },
    handleEditSuccess(data) {
      this.showSuccess(this.$t('success'), this.$t('building_updated'));
      this.closeEditModal();
      this.loadBuildings();
    },
    handleEditError(message) {
      this.showError(this.$t('error'), message);
    },
    async deleteBuilding(building) {
      if (confirm(this.$t('confirm_delete_building', { name: building.name }))) {
        try {
          await this.$axios.delete(`/buildings/${building.id}`);
          this.showSuccess(this.$t('success'), this.$t('building_deleted'));
          this.loadBuildings();
        } catch (error) {
          this.showError(this.$t('error'), error.response?.data?.message || this.$t('failed_delete_building'));
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
