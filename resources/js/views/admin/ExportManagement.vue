<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('export_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_exports_and_reports') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="showCreateExportModal = true"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_export') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Statistics -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_exports') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ exportStats.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('completed_exports') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ exportStats.completed || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('pending_exports') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ exportStats.pending || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('monthly_quota') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ exportStats.monthly_used || 0 }}/{{ exportStats.monthly_limit || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Exports Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('export_history') }}</h2>
            <div class="mt-4 sm:mt-0 flex space-x-3">
              <select v-model="filters.type" @change="loadExports" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option value="">{{ $t('all_types') }}</option>
                <option value="financial_summary">{{ $t('financial_summary') }}</option>
                <option value="building_expense_report">{{ $t('building_expense_report') }}</option>
                <option value="neighbor_payments">{{ $t('neighbor_payments') }}</option>
                <option value="building_summary">{{ $t('building_summary') }}</option>
                <option value="expense_report">{{ $t('expense_report') }}</option>
                <option value="income_report">{{ $t('income_report') }}</option>
                <option value="neighbor_report">{{ $t('neighbor_report') }}</option>
              </select>
              <select v-model="filters.status" @change="loadExports" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <option value="">{{ $t('all_statuses') }}</option>
                <option value="pending">{{ $t('pending') }}</option>
                <option value="processing">{{ $t('processing') }}</option>
                <option value="completed">{{ $t('completed') }}</option>
                <option value="failed">{{ $t('failed') }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-600">{{ $t('loading') }}</span>
          </div>
        </div>

        <!-- Exports Table -->
        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('type') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('format') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('status') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('created_by') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('created_at') }}</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $t('actions') }}</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="exportItem in exports" :key="exportItem?.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ formatExportType(exportItem?.type) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="exportItem?.format === 'pdf' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'">
                    {{ exportItem.format?.toUpperCase() }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getStatusClass(exportItem?.status)">
                    {{ $t(exportItem?.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ exportItem.user?.name || $t('not_specified') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(exportItem?.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      v-if="exportItem?.status === 'completed' && exportItem?.file_exists"
                      @click="downloadExport(exportItem)"
                      class="text-blue-600 hover:text-blue-900 transition-colors"
                      :title="$t('download')"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </button>
                    <button
                      @click="deleteExport(exportItem)"
                      class="text-red-600 hover:text-red-900 transition-colors"
                      :title="$t('delete')"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && exports.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_exports') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('no_exports_description') }}</p>
          <div class="mt-6">
            <button
              @click="showCreateExportModal = true"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_export') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Export Modal -->
    <CreateExportModal
      v-if="showCreateExportModal"
      @close="showCreateExportModal = false"
      @success="handleExportCreated"
    />
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import CreateExportModal from '../../components/CreateExportModal.vue';

export default {
  name: 'ExportManagement',
  mixins: [i18nMixin],
  components: {
    CreateExportModal
  },
  data() {
    return {
      loading: false,
      exports: [],
      exportStats: {},
      showCreateExportModal: false,
      filters: {
        type: '',
        status: ''
      }
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadExports(),
        this.loadExportStats()
      ]);
    },

    async loadExports() {
      this.loading = true;
      try {
        const params = {};
        if (this.filters.type) params.type = this.filters.type;
        if (this.filters.status) params.status = this.filters.status;

        const response = await this.$axios.get('/exports', { params });
        this.exports = response.data.exports?.data || [];
      } catch (error) {
        console.error('Error loading exports:', error);
        this.$toast.error(this.$t('error_loading_exports'));
      } finally {
        this.loading = false;
      }
    },

    async loadExportStats() {
      try {
        const response = await this.$axios.get('/exports/stats');
        this.exportStats = response.data;
      } catch (error) {
        console.error('Error loading export stats:', error);
        this.$toast.error(this.$t('error_loading_export_stats'));
      }
    },

    async downloadExport(exportItem) {
      try {
        const response = await this.$axios.get(`/exports/${exportItem.id}/download`, {
          responseType: 'blob'
        });
        
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        
        // Use the filename from the Content-Disposition header or fallback to the export item's filename
        let filename = exportItem.file_name;
        const contentDisposition = response.headers['content-disposition'];

        if (contentDisposition) {
          // Try RFC 5987 (UTF-8 encoded)
          const utf8FilenameMatch = contentDisposition.match(/filename\*\=([^;]+)/);
          if (utf8FilenameMatch) {
            const encodedFilename = utf8FilenameMatch[1].trim();
            const parts = encodedFilename.split("''");
            if (parts.length === 2) {
              try {
                filename = decodeURIComponent(parts[1]);
              } catch (e) {
                console.error('Error decoding filename*:', e);
              }
            }
          } else {
            // Fallback to basic filename (but try to fix mojibake)
            const asciiFilenameMatch = contentDisposition.match(/filename="([^"]+)"/);
            if (asciiFilenameMatch) {
              const rawFilename = asciiFilenameMatch[1];
              try {
                // Re-decode assuming it's misinterpreted as Latin-1 but is really UTF-8
                const bytes = new Uint8Array([...rawFilename].map(c => c.charCodeAt(0)));
                filename = new TextDecoder('utf-8').decode(bytes);
              } catch (e) {
                console.warn('Failed to recover UTF-8 filename, using raw fallback.');
                filename = rawFilename;
              }
            }
          }
        }

        
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        
        this.$toast.success(this.$t('export_downloaded_successfully'));
      } catch (error) {
        console.error('Error downloading export:', error);
        this.$toast.error(this.$t('download_failed'));
      }
    },

    async deleteExport(exportItem) {
      if (!confirm(this.$t('confirm_delete_export'))) return;

      try {
        await this.$axios.delete(`/exports/${exportItem.id}`);
        await this.loadData();
        this.$toast.success(this.$t('export_deleted_successfully'));
      } catch (error) {
        console.error('Error deleting export:', error);
        this.$toast.error(this.$t('delete_failed'));
      }
    },

    handleExportCreated() {
      this.showCreateExportModal = false;
      this.loadData();
    },

    formatExportType(type) {
      const types = {
        'financial_summary': this.$t('financial_summary'),
        'building_expense_report': this.$t('building_expense_report'),
        'building_summary': this.$t('building_summary'),
        'neighbor_payments': this.$t('neighbor_payments'),
        'financial_overview': this.$t('financial_overview'),
        'expense_report': this.$t('expense_report'),
        'income_report': this.$t('income_report'),
        'neighbor_report': this.$t('neighbor_report')
      };
      return types[type] || type;
    },

    getStatusClass(status) {
      const classes = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'processing': 'bg-blue-100 text-blue-800',
        'completed': 'bg-green-100 text-green-800',
        'failed': 'bg-red-100 text-red-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    }
  }
};
</script>
