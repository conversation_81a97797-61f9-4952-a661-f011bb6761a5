<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('payment_gateway_settings') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('configure_payment_gateways') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="testConnection"
              :disabled="isTesting"
              class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              <svg v-if="isTesting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {{ isTesting ? $t('testing') : $t('test_connection') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Gateway Configuration -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Stripe Configuration -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-medium text-gray-900">Stripe</h2>
                  <p class="text-sm text-gray-500">{{ $t('stripe_description') }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" v-model="settings.stripe.enabled" class="sr-only peer" @change="saveSettings">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('publishable_key') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                v-model="settings.stripe.publishable_key"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('stripe_publishable_key_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('secret_key') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="password" 
                v-model="settings.stripe.secret_key"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('stripe_secret_key_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('webhook_secret') }}
              </label>
              <input 
                type="password" 
                v-model="settings.stripe.webhook_secret"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('stripe_webhook_secret_placeholder')"
              >
            </div>

            <div class="flex items-center">
              <input 
                type="checkbox" 
                v-model="settings.stripe.test_mode"
                @change="saveSettings"
                class="mr-2"
              >
              <span class="text-sm text-gray-700">{{ $t('test_mode') }}</span>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div class="flex">
                <svg class="h-5 w-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="text-sm">
                  <p class="text-blue-800 font-medium">{{ $t('webhook_url') }}</p>
                  <p class="text-blue-700 mt-1 font-mono text-xs">{{ webhookUrl }}/stripe</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- PayPal Configuration -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-medium text-gray-900">PayPal</h2>
                  <p class="text-sm text-gray-500">{{ $t('paypal_description') }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" v-model="settings.paypal.enabled" class="sr-only peer" @change="saveSettings">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('client_id') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                v-model="settings.paypal.client_id"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('paypal_client_id_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('client_secret') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="password" 
                v-model="settings.paypal.client_secret"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('paypal_client_secret_placeholder')"
              >
            </div>

            <div class="flex items-center">
              <input 
                type="checkbox" 
                v-model="settings.paypal.sandbox_mode"
                @change="saveSettings"
                class="mr-2"
              >
              <span class="text-sm text-gray-700">{{ $t('sandbox_mode') }}</span>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div class="flex">
                <svg class="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="text-sm">
                  <p class="text-yellow-800 font-medium">{{ $t('webhook_url') }}</p>
                  <p class="text-yellow-700 mt-1 font-mono text-xs">{{ webhookUrl }}/paypal</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bank Transfer Configuration -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-medium text-gray-900">{{ $t('bank_transfer') }}</h2>
                  <p class="text-sm text-gray-500">{{ $t('bank_transfer_description') }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" v-model="settings.bank_transfer.enabled" class="sr-only peer" @change="saveSettings">
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('bank_name') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                v-model="settings.bank_transfer.bank_name"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('bank_name_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('account_number') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                v-model="settings.bank_transfer.account_number"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('account_number_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('account_holder_name') }} <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                v-model="settings.bank_transfer.account_holder"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('account_holder_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('iban') }}
              </label>
              <input 
                type="text" 
                v-model="settings.bank_transfer.iban"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('iban_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('swift_code') }}
              </label>
              <input 
                type="text" 
                v-model="settings.bank_transfer.swift_code"
                @blur="saveSettings"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('swift_code_placeholder')"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('instructions') }}
              </label>
              <textarea 
                v-model="settings.bank_transfer.instructions"
                @blur="saveSettings"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('bank_transfer_instructions_placeholder')"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- General Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">{{ $t('general_settings') }}</h2>
            <p class="text-sm text-gray-500">{{ $t('general_payment_settings') }}</p>
          </div>

          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('default_currency') }}
              </label>
              <select v-model="settings.general.default_currency" @change="saveSettings" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="USD">USD - US Dollar</option>
                <option value="JOD">JOD - Jordanian Dinar</option>
                <option value="ILS">ILS - Israeli Shekel</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ $t('payment_timeout') }} ({{ $t('minutes') }})
              </label>
              <input 
                type="number" 
                v-model="settings.general.payment_timeout"
                @blur="saveSettings"
                min="5"
                max="60"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
            </div>

            <div class="flex items-center">
              <input 
                type="checkbox" 
                v-model="settings.general.auto_confirm_payments"
                @change="saveSettings"
                class="mr-2"
              >
              <span class="text-sm text-gray-700">{{ $t('auto_confirm_payments') }}</span>
            </div>

            <div class="flex items-center">
              <input 
                type="checkbox" 
                v-model="settings.general.send_payment_receipts"
                @change="saveSettings"
                class="mr-2"
              >
              <span class="text-sm text-gray-700">{{ $t('send_payment_receipts') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  name: 'PaymentGatewaySettings',
  mixins: [i18nMixin],
  data() {
    return {
      loading: false,
      isTesting: false,
      settings: {
        stripe: {
          enabled: false,
          publishable_key: '',
          secret_key: '',
          webhook_secret: '',
          test_mode: true
        },
        paypal: {
          enabled: false,
          client_id: '',
          client_secret: '',
          sandbox_mode: true
        },
        bank_transfer: {
          enabled: false,
          bank_name: '',
          account_number: '',
          account_holder: '',
          iban: '',
          swift_code: '',
          instructions: ''
        },
        general: {
          default_currency: 'USD',
          payment_timeout: 30,
          auto_confirm_payments: false,
          send_payment_receipts: true
        }
      }
    };
  },
  computed: {
    webhookUrl() {
      return `${window.location.origin}/api/webhooks`;
    }
  },
  async mounted() {
    await this.loadSettings();
  },
  methods: {
    async loadSettings() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/payment-gateway-settings');
        this.settings = { ...this.settings, ...response.data };
      } catch (error) {
        console.error('Error loading settings:', error);
      } finally {
        this.loading = false;
      }
    },

    async saveSettings() {
      try {
        await this.$axios.put('/payment-gateway-settings', this.settings);
      } catch (error) {
        console.error('Error saving settings:', error);
        this.$toast.error(this.$t('save_failed'));
      }
    },

    async testConnection() {
      this.isTesting = true;
      try {
        const response = await this.$axios.post('/payment-gateway-settings/test');
        this.$toast.success(this.$t('connection_test_successful'));
      } catch (error) {
        console.error('Error testing connection:', error);
        this.$toast.error(error.response?.data?.message || this.$t('connection_test_failed'));
      } finally {
        this.isTesting = false;
      }
    }
  }
};
</script>
