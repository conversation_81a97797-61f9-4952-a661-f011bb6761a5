<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="px-4 py-5 sm:p-6">
        <h1 class="text-2xl font-bold text-gray-900">{{ $t('building_settings') }}</h1>
        <p class="mt-1 text-sm text-gray-600">{{ $t('building_settings_description') }}</p>
      </div>
    </div>

    <!-- Building Information Form -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('building_information') }}</h2>
        
        <form @submit.prevent="updateBuilding" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Building Name -->
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">
                {{ $t('building_name') }}
              </label>
              <input
                type="text"
                id="name"
                v-model="buildingData.name"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Currency Selection -->
            <div>
              <label for="currency" class="block text-sm font-medium text-gray-700">
                {{ $t('currency') }}
              </label>
              <select
                id="currency"
                v-model="buildingData.currency"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="JOD">JOD - Jordanian Dinar</option>
                <option value="ILS">ILS - Israeli Shekel</option>
              </select>
              <p class="mt-1 text-sm text-gray-500">{{ $t('currency_display_note') }}</p>
            </div>

            <!-- Address -->
            <div class="md:col-span-2">
              <label for="address" class="block text-sm font-medium text-gray-700">
                {{ $t('building_address') }}
              </label>
              <input
                type="text"
                id="address"
                v-model="buildingData.address"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- City -->
            <div>
              <label for="city" class="block text-sm font-medium text-gray-700">
                {{ $t('city') }}
              </label>
              <input
                type="text"
                id="city"
                v-model="buildingData.city"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Postal Code -->
            <div>
              <label for="postal_code" class="block text-sm font-medium text-gray-700">
                {{ $t('postal_code') }}
              </label>
              <input
                type="text"
                id="postal_code"
                v-model="buildingData.postal_code"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <!-- Monthly Fee -->
            <div>
              <label for="monthly_fee" class="block text-sm font-medium text-gray-700">
                {{ $t('monthly_fee_per_neighbor') }}
              </label>
              <div class="mt-1 relative rounded-md shadow-sm">
                <input
                  type="number"
                  id="monthly_fee"
                  v-model="buildingData.monthly_fee"
                  step="0.01"
                  min="0"
                  class="block w-full pr-12 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">{{ buildingData.currency }}</span>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">{{ $t('monthly_fee_help') }}</p>
            </div>

            <!-- Description -->
            <div class="md:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700">
                {{ $t('description') }}
              </label>
              <textarea
                id="description"
                v-model="buildingData.description"
                rows="3"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                :placeholder="$t('building_description_placeholder')"
              ></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="loading"
              class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {{ loading ? $t('saving') : $t('save_changes') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Payment Information (Read-only) -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('payment_information') }}</h2>
        <div class="bg-gray-50 p-4 rounded-md">
          <p class="text-sm text-gray-600 mb-2">{{ $t('payment_currency_note') }}</p>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>{{ $t('setup_fee') }}:</span>
              <span class="font-medium">70.00 USD</span>
            </div>
            <div class="flex justify-between">
              <span>{{ $t('monthly_subscription') }}:</span>
              <span class="font-medium">7.00 USD</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  name: 'BuildingSettings',
  mixins: [i18nMixin],
  data() {
    return {
      buildingData: {
        name: '',
        address: '',
        city: '',
        postal_code: '',
        description: '',
        monthly_fee: 0,
        currency: 'USD'
      },
      loading: false
    };
  },
  async mounted() {
    document.title = `${this.$t('building_settings')} - ${this.$t('app_name')}`;
    await this.loadBuildingData();
  },
  methods: {
    async loadBuildingData() {
      try {
        const response = await this.$axios.get('/buildings/current');
        if (response.data) {
          this.buildingData = {
            name: response.data.name || '',
            address: response.data.address || '',
            city: response.data.city || '',
            postal_code: response.data.postal_code || '',
            description: response.data.description || '',
            monthly_fee: response.data.monthly_fee || 0,
            currency: response.data.currency || 'USD'
          };
        }
      } catch (error) {
        console.error('Error loading building data:', error);
        this.$toast.error(this.$t('error_loading_building_data'));
      }
    },
    async updateBuilding() {
      this.loading = true;
      try {
        const response = await this.$axios.put('/buildings/current', this.buildingData);
        if (response.data.success) {
          this.$toast.success(this.$t('building_updated_successfully'));
        }
      } catch (error) {
        console.error('Error updating building:', error);
        this.$toast.error(this.$t('error_updating_building'));
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
