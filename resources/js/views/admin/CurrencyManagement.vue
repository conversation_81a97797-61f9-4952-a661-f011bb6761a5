<template>
  <div class="currency-management">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">{{ $t('currency_management') }}</h1>
          <p class="text-sm text-gray-600 mt-1">{{ $t('manage_currencies_and_exchange_rates') }}</p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="refreshRates"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
            :disabled="loading"
          >
            {{ $t('refresh_rates') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Currency Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 p-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('primary_currency') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ currencySettings.primary_currency || 'USD' }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('accepted_currencies') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ acceptedCurrenciesCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('conversions_this_month') }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ conversionStats.total_conversions || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ $t('exchange_provider') }}</p>
            <p class="text-lg font-semibold text-gray-900">{{ getProviderName(currencySettings.exchange_rate_provider) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200 px-6">
      <nav class="-mb-px flex space-x-8">
        <button
          @click="activeTab = 'settings'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'settings'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('currency_settings') }}
        </button>
        <button
          @click="activeTab = 'rates'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'rates'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('exchange_rates') }}
        </button>
        <button
          @click="activeTab = 'converter'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'converter'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('currency_converter') }}
        </button>
        <button
          @click="activeTab = 'analytics'"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'analytics'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ $t('analytics') }}
        </button>
      </nav>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Currency Settings -->
      <div v-if="activeTab === 'settings'">
        <CurrencySettings 
          :settings="currencySettings"
          :supported-currencies="supportedCurrencies"
          :available-providers="availableProviders"
          @update="handleSettingsUpdate"
        />
      </div>

      <!-- Exchange Rates -->
      <div v-if="activeTab === 'rates'">
        <ExchangeRatesTable 
          :rates="exchangeRates"
          :primary-currency="currencySettings.primary_currency"
          @refresh="loadExchangeRates"
          @update="handleRateUpdate"
        />
      </div>

      <!-- Currency Converter -->
      <div v-if="activeTab === 'converter'">
        <CurrencyConverter 
          :supported-currencies="supportedCurrencies"
          :primary-currency="currencySettings.primary_currency"
        />
      </div>

      <!-- Analytics -->
      <div v-if="activeTab === 'analytics'">
        <CurrencyAnalytics 
          :stats="conversionStats"
          @refresh="loadConversionStats"
        />
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import CurrencySettings from '../../components/CurrencySettings.vue';
import ExchangeRatesTable from '../../components/ExchangeRatesTable.vue';
import CurrencyConverter from '../../components/CurrencyConverter.vue';
import CurrencyAnalytics from '../../components/CurrencyAnalytics.vue';

export default {
  name: 'CurrencyManagement',
  mixins: [i18nMixin],
  components: {
    CurrencySettings,
    ExchangeRatesTable,
    CurrencyConverter,
    CurrencyAnalytics
  },
  data() {
    return {
      activeTab: 'settings',
      currencySettings: {},
      supportedCurrencies: {},
      availableProviders: {},
      exchangeRates: [],
      conversionStats: {},
      loading: false
    };
  },
  computed: {
    acceptedCurrenciesCount() {
      return this.currencySettings.accepted_currencies?.length || 1;
    }
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      await Promise.all([
        this.loadCurrencySettings(),
        this.loadExchangeRates(),
        this.loadConversionStats()
      ]);
    },

    async loadCurrencySettings() {
      try {
        const response = await this.$http.get('/api/currency/settings');
        this.currencySettings = response.data.settings;
        this.supportedCurrencies = response.data.supported_currencies;
        this.availableProviders = response.data.available_providers;
      } catch (error) {
        console.error('Error loading currency settings:', error);
        this.$toast.error(this.$t('error_loading_currency_settings'));
      }
    },

    async loadExchangeRates() {
      try {
        const response = await this.$http.get('/api/currency/exchange-rates');
        this.exchangeRates = response.data.exchange_rates;
      } catch (error) {
        console.error('Error loading exchange rates:', error);
        this.$toast.error(this.$t('error_loading_exchange_rates'));
      }
    },

    async loadConversionStats() {
      try {
        const response = await this.$http.get('/api/currency/conversion-stats');
        this.conversionStats = response.data;
      } catch (error) {
        console.error('Error loading conversion stats:', error);
        this.$toast.error(this.$t('error_loading_conversion_stats'));
      }
    },

    async refreshRates() {
      this.loading = true;
      try {
        await this.loadExchangeRates();
        this.$toast.success(this.$t('exchange_rates_refreshed'));
      } catch (error) {
        this.$toast.error(this.$t('error_refreshing_rates'));
      } finally {
        this.loading = false;
      }
    },

    async handleSettingsUpdate(settings) {
      try {
        await this.$http.put('/api/currency/settings', settings);
        await this.loadCurrencySettings();
        this.$toast.success(this.$t('currency_settings_updated'));
      } catch (error) {
        console.error('Error updating currency settings:', error);
        this.$toast.error(this.$t('error_updating_currency_settings'));
      }
    },

    async handleRateUpdate(rates) {
      try {
        await this.$http.post('/api/currency/exchange-rates', { rates });
        await this.loadExchangeRates();
        this.$toast.success(this.$t('exchange_rates_updated'));
      } catch (error) {
        console.error('Error updating exchange rates:', error);
        this.$toast.error(this.$t('error_updating_exchange_rates'));
      }
    },

    getProviderName(provider) {
      const providers = {
        'manual': 'Manual',
        'fixer': 'Fixer.io',
        'exchangerate': 'ExchangeRate-API',
        'currencylayer': 'CurrencyLayer'
      };
      return providers[provider] || provider;
    }
  }
};
</script>

<style scoped>
.currency-management {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
