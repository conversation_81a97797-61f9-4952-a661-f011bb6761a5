<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl w-full">
      <!-- Header with Logo -->
      <div class="text-center mb-8">
        <router-link to="/" class="inline-flex items-center space-x-2 mb-6">
          <img src="/images/logo.png" alt="Logo" class="h-12 w-auto" />
          <span class="text-2xl font-bold text-gray-900">{{ $t('app_name') }}</span>
        </router-link>
      </div>

      <!-- Main Success Card -->
      <div class="bg-white rounded-3xl shadow-2xl overflow-hidden">
        <!-- Success Header -->
        <div class="bg-gradient-to-r from-green-500 to-emerald-600 px-8 py-12 text-center">
          <!-- Animated Success Icon -->
          <div class="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-white/20 backdrop-blur-sm mb-6 animate-bounce">
            <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>

          <h1 class="text-4xl font-bold text-white mb-4">
            {{ $t('welcome_aboard') }}!
          </h1>
          <p class="text-xl text-green-100">
            {{ $t('registration_successful_message') }}
          </p>
        </div>

        <!-- User Information Card -->
        <div class="px-8 py-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              {{ $t('welcome_message') }}, <span class="text-blue-600">{{ user?.name }}</span>!
            </h2>
            <p class="text-gray-600">
              {{ $t('account_created_successfully') }}
            </p>
          </div>

          <!-- Account Details -->
          <div class="bg-gray-50 rounded-2xl p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
              </svg>
              {{ $t('account_details') }}
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-500">{{ $t('email') }}</span>
                  <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                </div>
                <p class="text-lg font-semibold text-gray-900 mt-1">{{ user?.email }}</p>
              </div>

              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-500">{{ $t('role') }}</span>
                  <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-lg font-semibold text-gray-900 mt-1 capitalize">
                  {{ user?.role === 'admin' ? $t('building_admin') : $t('neighbor') }}
                </p>
              </div>
            </div>
          </div>

          <!-- Email Verification Notice -->
          <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
              {{ $t('verify_your_email') }}
            </h3>
            <div class="space-y-3">
              <p class="text-gray-700">
                {{ $t('verify_email_message') }}
              </p>
              <div class="flex items-center space-x-2 text-sm text-gray-600">
                <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <span>{{ $t('click_verification_link_message') }}</span>
              </div>
              <button
                @click="resendVerificationEmail"
                :disabled="isResending"
                class="inline-flex items-center px-4 py-2 border border-yellow-300 rounded-lg text-sm font-medium text-yellow-700 bg-yellow-50 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg v-if="isResending" class="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-700" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isResending ? $t('sending') : $t('resend_verification_email') }}
              </button>
            </div>
          </div>

          <!-- Next Steps -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
              {{ $t('whats_next') }}
            </h3>

            <div class="space-y-3">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700">{{ $t('account_created_successfully') }}</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <span class="text-xs font-bold text-white">2</span>
                </div>
                <p class="text-gray-700">{{ $t('access_dashboard_now') }}</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center mt-0.5">
                  <span class="text-xs font-bold text-white">3</span>
                </div>
                <p class="text-gray-700">
                  <span v-if="user?.role === 'admin'">{{ $t('manage_building_expenses') }}</span>
                  <span v-else>{{ $t('view_expenses_make_payments') }}</span>
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <button
              @click="goToDashboard"
              :disabled="isLoading"
              class="flex-1 flex justify-center items-center py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <svg v-if="!isLoading" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoading ? $t('loading') : $t('continue_to_dashboard') }}
              <svg v-if="!isLoading" class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </button>

            <button
              @click="logout"
              :disabled="isLoading"
              class="flex justify-center items-center py-4 px-6 border-2 border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-medium rounded-xl bg-white hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="!isLoading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
              </svg>
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ $t('logout') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center mt-8">
        <p class="text-sm text-gray-500">
          {{ $t('need_help') }}
          <router-link to="/contact" class="text-blue-600 hover:text-blue-700 font-medium">
            {{ $t('contact_support') }}
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'RegistrationSuccess',
  mixins: [i18nMixin],
  data() {
    return {
      user: null,
      building: null,
      isLoading: false,
      isResending: false
    };
  },
  created() {
    // Get user and building data from localStorage
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
    this.building = JSON.parse(localStorage.getItem('building') || 'null');

    // If no user data, redirect to login
    if (!this.user) {
      this.$router.push('/login');
      return;
    }

    // Set page title
    document.title = `${this.$t('registration_successful')} - ${this.$t('app_name')}`;
  },
  methods: {
    async goToDashboard() {
      this.isLoading = true;

      try {
        // Small delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500));

        if (this.user?.role === 'admin' || this.user?.role === 'super_admin') {
          this.$router.push({ name: 'AdminDashboard' });
        } else {
          this.$router.push({ name: 'NeighborDashboard' });
        }
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async logout() {
      this.isLoading = true;

      try {
        // Try to logout from server
        await this.$axios.post('/logout');
      } catch (error) {
        console.error('Logout error:', error);
        // Continue with logout even if server request fails
      } finally {
        // Clear all stored data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('building');

        // Dispatch auth state change event
        window.dispatchEvent(new CustomEvent('auth-state-changed'));

        // Redirect to login
        this.$router.push('/login');
        this.isLoading = false;
      }
    },

    async resendVerificationEmail() {
      if (!this.user) return;

      this.isResending = true;

      try {
        await this.$axios.post('/email/verification-notification');

        // Show success message
        this.$toast.success(this.$t('verification_email_sent_successfully'));

      } catch (error) {
        console.error('Failed to resend verification email:', error);
        this.$toast.error(this.$t('failed_to_send_verification_email'));
      } finally {
        this.isResending = false;
      }
    }
  }
};
</script>

<style scoped>
/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Apply animations */
.bg-white {
  animation: fadeInUp 0.6s ease-out;
}

.bg-gradient-to-r.from-green-500 {
  animation: slideInLeft 0.8s ease-out;
}

.bg-gray-50.rounded-2xl {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.bg-gradient-to-r.from-blue-50 {
  animation: fadeInUp 1s ease-out 0.4s both;
}

.flex.flex-col.sm\\:flex-row {
  animation: fadeInUp 1.2s ease-out 0.6s both;
}

/* Button hover effects */
.bg-gradient-to-r.from-blue-600:hover {
  animation: pulse 0.3s ease-in-out;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .text-4xl {
    font-size: 2.5rem;
  }

  .text-2xl {
    font-size: 1.75rem;
  }
}
</style>
