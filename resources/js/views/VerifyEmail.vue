<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <router-link to="/" class="inline-flex items-center space-x-2 mb-8">
          <img src="images/logo.png" alt="Logo" class="h-10 w-auto" />
          <span class="text-2xl font-bold text-gray-900">{{ $t('app_name') }}</span>
        </router-link>

        <div class="mb-6">
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
            <svg v-if="!isVerifying && !isVerified && !hasError" class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <svg v-else-if="isVerifying" class="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else-if="isVerified" class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            <svg v-else-if="hasError" class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
        </div>

        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          {{ getTitle() }}
        </h2>
        <p class="text-gray-600">
          {{ getMessage() }}
        </p>
      </div>

      <!-- Content -->
      <div class="bg-white py-8 px-6 shadow-xl rounded-xl border border-gray-100">
        <!-- Success State -->
        <div v-if="isVerified" class="text-center space-y-4">
          <div class="text-green-600 font-medium">
            {{ $t('email_verified_successfully') }}
          </div>
          <div class="space-y-3">
            <button
              @click="redirectToDashboard"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {{ $t('continue_to_dashboard') }}
            </button>
            <router-link
              to="/login"
              class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {{ $t('go_to_login') }}
            </router-link>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="hasError" class="text-center space-y-4">
          <div class="text-red-600 font-medium">
            {{ errorMessage }}
          </div>
          <div class="space-y-3">
            <button
              v-if="canResend"
              @click="resendVerification"
              :disabled="isResending"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <svg v-if="isResending" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isResending ? $t('sending') : $t('resend_verification_email') }}
            </button>
            <router-link
              to="/login"
              class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {{ $t('back_to_login') }}
            </router-link>
          </div>
        </div>

        <!-- Loading State -->
        <div v-else-if="isVerifying" class="text-center space-y-4">
          <div class="text-gray-600">
            {{ $t('verifying_email') }}
          </div>
        </div>

        <!-- Initial State -->
        <div v-else class="text-center space-y-4">
          <div class="text-gray-600">
            {{ $t('click_verification_link_message') }}
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center">
        <p class="text-sm text-gray-500">
          {{ $t('need_help') }}
          <a href="/contact" class="font-medium text-blue-600 hover:text-blue-500">
            {{ $t('contact_support') }}
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'VerifyEmail',
  mixins: [i18nMixin],
  data() {
    return {
      isVerifying: false,
      isVerified: false,
      hasError: false,
      errorMessage: '',
      isResending: false,
      canResend: false,
      email: '',
      token: ''
    };
  },
  async mounted() {
    // Get token and email from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    this.token = urlParams.get('token');
    this.email = urlParams.get('email');

    if (this.token && this.email) {
      await this.verifyEmail();
    } else {
      this.hasError = true;
      this.errorMessage = this.$t('invalid_verification_link');
    }
  },
  methods: {
    async verifyEmail() {
      this.isVerifying = true;
      this.hasError = false;

      try {
        const response = await this.$axios.post('/email/verify', {
          token: this.token,
          email: this.email
        });

        this.isVerified = true;
        this.isVerifying = false;

        // Optional: Auto-login the user
        if (response.data.user) {
          // You might want to generate a token here or redirect to login
        }

      } catch (error) {
        this.isVerifying = false;
        this.hasError = true;
        
        if (error.response?.status === 400) {
          this.errorMessage = error.response.data.message;
          // Check if it's an expired token error to show resend option
          if (this.errorMessage.includes('expired')) {
            this.canResend = true;
          }
        } else {
          this.errorMessage = this.$t('verification_failed');
        }
      }
    },

    async resendVerification() {
      if (!this.email) return;

      this.isResending = true;

      try {
        // This would require the user to be logged in, so we might need to handle this differently
        // For now, we'll redirect to login with a message
        this.$router.push({
          name: 'Login',
          query: { 
            message: 'Please log in to resend verification email',
            email: this.email 
          }
        });
      } catch (error) {
        console.error('Failed to resend verification:', error);
      } finally {
        this.isResending = false;
      }
    },

    redirectToDashboard() {
      // Redirect to login since we don't have auth token
      this.$router.push('/login');
    },

    getTitle() {
      if (this.isVerifying) return this.$t('verifying_email');
      if (this.isVerified) return this.$t('email_verified');
      if (this.hasError) return this.$t('verification_failed');
      return this.$t('verify_your_email');
    },

    getMessage() {
      if (this.isVerifying) return this.$t('please_wait_verifying');
      if (this.isVerified) return this.$t('email_verified_message');
      if (this.hasError) return this.$t('verification_error_message');
      return this.$t('verify_email_message');
    }
  }
};
</script>
