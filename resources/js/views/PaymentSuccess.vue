<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div v-if="processing" class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p class="mt-4 text-sm text-gray-600">{{ $t('processing_payment') }}</p>
        </div>

        <div v-else-if="paymentSuccess" class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="mt-4 text-lg font-medium text-gray-900">{{ $t('payment_successful') }}</h2>
          <p class="mt-2 text-sm text-gray-600">{{ $t('setup_fee_paid_successfully') }}</p>
          
          <div class="mt-6">
            <router-link
              to="/signup-success"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ $t('continue_to_dashboard') }}
            </router-link>
          </div>
        </div>

        <div v-else class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h2 class="mt-4 text-lg font-medium text-gray-900">{{ $t('payment_failed') }}</h2>
          <p class="mt-2 text-sm text-gray-600">{{ errorMessage || $t('payment_processing_error') }}</p>
          
          <div class="mt-6 space-y-3">
            <button
              @click="retryPayment"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ $t('retry_payment') }}
            </button>
            <router-link
              to="/contact"
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ $t('contact_support') }}
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'PaymentSuccess',
  mixins: [i18nMixin],
  data() {
    return {
      processing: true,
      paymentSuccess: false,
      errorMessage: ''
    };
  },
  async mounted() {
    document.title = `${this.$t('payment_processing')} - ${this.$t('app_name')}`;
    await this.processPayment();
  },
  methods: {
    async processPayment() {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const paymentId = urlParams.get('paymentId');
        const payerId = urlParams.get('PayerID');

        if (!paymentId || !payerId) {
          throw new Error('Missing payment parameters');
        }

        const response = await this.$axios.get('/payments/success', {
          params: { paymentId, PayerID: payerId }
        });

        if (response.data.success) {
          this.paymentSuccess = true;
          // Update the stored signup data to indicate payment is complete
          const signupData = JSON.parse(localStorage.getItem('signup_data') || '{}');
          signupData.paymentCompleted = true;
          localStorage.setItem('signup_data', JSON.stringify(signupData));
        } else {
          throw new Error(response.data.message || 'Payment processing failed');
        }
      } catch (error) {
        console.error('Payment processing error:', error);
        this.errorMessage = error.message || 'Payment processing failed';
        this.paymentSuccess = false;
      } finally {
        this.processing = false;
      }
    },
    async retryPayment() {
      try {
        const signupData = JSON.parse(localStorage.getItem('signup_data') || '{}');
        if (!signupData.building) {
          this.$router.push('/signup');
          return;
        }

        const paymentResponse = await this.$axios.post('/payments/setup-fee', {
          amount: signupData.building.setup_fee || 100.00, // Use building's setup fee or default
          currency: 'USD', // Payment always in USD
          building_id: signupData.building.id,
          country_code: 'JO'
        });

        if (paymentResponse.data.success) {
          window.location.href = paymentResponse.data.approval_url;
        } else {
          throw new Error(paymentResponse.data.message || 'Failed to create payment');
        }
      } catch (error) {
        console.error('Retry payment error:', error);
        this.errorMessage = error.message || 'Failed to retry payment';
      }
    }
  }
};
</script>
