<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <!-- Success Icon -->
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-6">
          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <!-- Success Message -->
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">
            {{ $t('account_created_successfully') }}
          </h2>
          <p class="text-gray-600 mb-8">
            {{ $t('signup_success_message') }}
          </p>
        </div>

        <!-- Next Steps -->
        <div class="space-y-6">
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 class="text-lg font-medium text-blue-900 mb-3">{{ $t('next_steps') }}</h3>
            <ol class="list-decimal list-inside space-y-2 text-sm text-blue-800">
              <li>{{ $t('step_1_verify_email') }}</li>
              <li>{{ $t('step_2_setup_fee') }}</li>
              <li>{{ $t('step_3_add_neighbors') }}</li>
              <li>{{ $t('step_4_start_managing') }}</li>
            </ol>
          </div>

          <!-- Pricing Reminder -->
          <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-2">{{ $t('pricing_reminder') }}</h4>
            <div class="space-y-1 text-sm text-gray-600">
              <div class="flex justify-between">
                <span>{{ $t('setup_fee') }} ({{ $t('one_time') }}):</span>
                <span class="font-medium">{{ $t('contact_for_pricing') }}</span>
              </div>
              <div class="flex justify-between">
                <span>{{ $t('monthly_subscription') }}:</span>
                <span class="font-medium">{{ $t('based_on_selected_package') }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              {{ $t('billing_info') }}
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="space-y-3">
            <router-link
              to="/login"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ $t('sign_in_to_dashboard') }}
            </router-link>
            
            <router-link
              to="/contact"
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ $t('need_help') }}
            </router-link>
          </div>
        </div>

        <!-- Support Information -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <div class="text-center">
            <p class="text-sm text-gray-600 mb-2">
              {{ $t('questions_or_issues') }}
            </p>
            <div class="space-y-1 text-sm">
              <p class="text-gray-600">
                {{ $t('email') }}: 
                <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-500">
                  <EMAIL>
                </a>
              </p>
              <p class="text-gray-600">
                {{ $t('phone') }}: 
                <a href="tel:+972-XX-XXX-XXXX" class="text-indigo-600 hover:text-indigo-500">
                  +972-XX-XXX-XXXX
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Resources -->
    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-6 px-4 shadow sm:rounded-lg sm:px-10">
        <h3 class="text-lg font-medium text-gray-900 mb-4 text-center">
          {{ $t('helpful_resources') }}
        </h3>
        
        <div class="space-y-3">
          <a
            href="#"
            class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ $t('user_guide') }}</p>
              <p class="text-xs text-gray-500">{{ $t('user_guide_description') }}</p>
            </div>
          </a>

          <a
            href="#"
            class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ $t('video_tutorials') }}</p>
              <p class="text-xs text-gray-500">{{ $t('video_tutorials_description') }}</p>
            </div>
          </a>

          <a
            href="#"
            class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
            </svg>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ $t('community_forum') }}</p>
              <p class="text-xs text-gray-500">{{ $t('community_forum_description') }}</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'SignUpSuccess',
  mixins: [i18nMixin],
  mounted() {
    document.title = `${this.$t('registration_successful')} - ${this.$t('app_name')}`;
  }
};
</script>
