<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Expense Type Management</h1>
            <p class="mt-1 text-sm text-gray-500">Manage expense types used throughout the system</p>
          </div>
          <div class="mt-4 sm:mt-0 flex space-x-3">
            <button
              @click="loadStatistics"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Statistics
            </button>
            <button
              @click="openCreateModal"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Create Expense Type
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              v-model="filters.search"
              @input="debounceSearch"
              type="text"
              placeholder="Search expense types..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Per Page</label>
            <select
              v-model="filters.per_page"
              @change="loadExpenseTypes"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option :value="15">15 per page</option>
              <option :value="25">25 per page</option>
              <option :value="50">50 per page</option>
              <option :value="100">100 per page</option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              v-if="selectedItems.length > 0"
              @click="confirmBulkDelete"
              class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete Selected ({{ selectedItems.length }})
            </button>
          </div>
        </div>
      </div>

      <!-- Statistics Cards (if loaded) -->
      <div v-if="statistics" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Types</p>
              <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_expense_types }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">In Use</p>
              <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_expense_types - statistics.unused }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Unused</p>
              <p class="text-2xl font-semibold text-gray-900">{{ statistics.unused }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Most Used</p>
              <p class="text-lg font-semibold text-gray-900">{{ statistics.most_used?.[0]?.name || 'N/A' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Expense Types</h3>
            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-500">Select All</span>
            </div>
          </div>
        </div>

        <div v-if="loading" class="p-8 text-center">
          <div class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading expense types...
          </div>
        </div>

        <div v-else-if="expenseTypes.data && expenseTypes.data.length === 0" class="p-8 text-center text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No expense types found</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating a new expense type.</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    :checked="isAllSelected"
                    @change="toggleSelectAll"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage Count</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="expenseType in expenseTypes.data" :key="expenseType.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    :value="expenseType.id"
                    v-model="selectedItems"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ expenseType.name }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500">{{ expenseType.description || 'No description' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="expenseType.expenses_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                    {{ expenseType.expenses_count }} expenses
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(expenseType.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <button
                      @click="viewExpenseType(expenseType)"
                      class="text-blue-600 hover:text-blue-900 transition-colors"
                      title="View Details"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                    <button
                      @click="editExpenseType(expenseType)"
                      class="text-indigo-600 hover:text-indigo-900 transition-colors"
                      title="Edit"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      @click="confirmDelete(expenseType)"
                      class="text-red-600 hover:text-red-900 transition-colors"
                      title="Delete"
                      :disabled="expenseType.expenses_count > 0"
                      :class="{ 'opacity-50 cursor-not-allowed': expenseType.expenses_count > 0 }"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="expenseTypes.data && expenseTypes.data.length > 0" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <button
                @click="changePage(expenseTypes.current_page - 1)"
                :disabled="expenseTypes.current_page <= 1"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                @click="changePage(expenseTypes.current_page + 1)"
                :disabled="expenseTypes.current_page >= expenseTypes.last_page"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium">{{ expenseTypes.from }}</span>
                  to
                  <span class="font-medium">{{ expenseTypes.to }}</span>
                  of
                  <span class="font-medium">{{ expenseTypes.total }}</span>
                  results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    @click="changePage(expenseTypes.current_page - 1)"
                    :disabled="expenseTypes.current_page <= 1"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>

                  <template v-for="page in getVisiblePages()" :key="page">
                    <button
                      v-if="page !== '...'"
                      @click="changePage(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                        page === expenseTypes.current_page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      {{ page }}
                    </button>
                    <span
                      v-else
                      class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                    >
                      ...
                    </span>
                  </template>

                  <button
                    @click="changePage(expenseTypes.current_page + 1)"
                    :disabled="expenseTypes.current_page >= expenseTypes.last_page"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleModalOutsideClick">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              {{ isEditing ? 'Edit Expense Type' : 'Create New Expense Type' }}
            </h3>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="submitForm" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
              <input
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'border-red-300': errors.name }"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name[0] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'border-red-300': errors.description }"
              ></textarea>
              <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description[0] }}</p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="submitting"
                class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="submitting" class="inline-flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isEditing ? 'Updating...' : 'Creating...' }}
                </span>
                <span v-else>
                  {{ isEditing ? 'Update' : 'Create' }}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- View Details Modal -->
    <div v-if="showViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeViewModal">
      <div class="relative top-20 mx-auto p-5 border w-2xl max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Expense Type Details</h3>
            <button @click="closeViewModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div v-if="selectedExpenseType" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedExpenseType.name }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Usage Count</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedExpenseType.expenses_count }} expenses</p>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Description</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedExpenseType.description || 'No description provided' }}</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Created At</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(selectedExpenseType.created_at) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Updated At</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(selectedExpenseType.updated_at) }}</p>
              </div>
            </div>

            <div v-if="selectedExpenseType.expenses && selectedExpenseType.expenses.length > 0">
              <label class="block text-sm font-medium text-gray-700 mb-2">Recent Expenses</label>
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="space-y-2">
                  <div v-for="expense in selectedExpenseType.expenses" :key="expense.id" class="flex justify-between items-center text-sm">
                    <span>{{ expense.building?.name || 'Unknown Building' }}</span>
                    <span class="text-gray-500">{{ expense.user?.name || 'System' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end pt-4">
            <button
              @click="closeViewModal"
              class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'ExpenseTypeManagement',
  setup() {
    const { proxy } = getCurrentInstance()
    const toast = proxy.$toast

    // Reactive data
    const loading = ref(false)
    const submitting = ref(false)
    const showModal = ref(false)
    const showViewModal = ref(false)
    const isEditing = ref(false)
    const selectedExpenseType = ref(null)
    const selectedItems = ref([])
    const statistics = ref(null)

    const expenseTypes = ref({
      data: [],
      current_page: 1,
      last_page: 1,
      from: 0,
      to: 0,
      total: 0
    })

    const filters = reactive({
      search: '',
      per_page: 15
    })

    const form = reactive({
      name: '',
      description: ''
    })

    const errors = ref({})

    // Computed properties
    const isAllSelected = computed(() => {
      return expenseTypes.value.data.length > 0 &&
             selectedItems.value.length === expenseTypes.value.data.length
    })

    // Debounced search
    let searchTimeout = null
    const debounceSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        loadExpenseTypes()
      }, 500)
    }

    // Methods
    const loadExpenseTypes = async (page = 1) => {
      loading.value = true
      try {
        const params = {
          page,
          per_page: filters.per_page,
          ...(filters.search && { search: filters.search })
        }

        const response = await axios.get('/super-admin/expense-types', { params })
        expenseTypes.value = response.data
      } catch (error) {
        console.error('Error loading expense types:', error)
        toast.error('Failed to load expense types')
      } finally {
        loading.value = false
      }
    }

    const loadStatistics = async () => {
      try {
        const response = await axios.get('/super-admin/expense-types/statistics')
        statistics.value = response.data
        toast.success('Statistics loaded successfully')
      } catch (error) {
        console.error('Error loading statistics:', error)
        toast.error('Failed to load statistics')
      }
    }

    const openCreateModal = () => {
      isEditing.value = false
      selectedExpenseType.value = null
      form.name = ''
      form.description = ''
      errors.value = {}
      showModal.value = true
    }

    const editExpenseType = (expenseType) => {
      isEditing.value = true
      selectedExpenseType.value = expenseType
      form.name = expenseType.name
      form.description = expenseType.description || ''
      errors.value = {}
      showModal.value = true
    }

    const viewExpenseType = async (expenseType) => {
      try {
        const response = await axios.get(`/super-admin/expense-types/${expenseType.id}`)
        selectedExpenseType.value = response.data
        showViewModal.value = true
      } catch (error) {
        console.error('Error loading expense type details:', error)
        toast.error('Failed to load expense type details')
      }
    }

    const submitForm = async () => {
      submitting.value = true
      errors.value = {}

      try {
        const data = {
          name: form.name,
          description: form.description
        }

        if (isEditing.value) {
          await axios.put(`/super-admin/expense-types/${selectedExpenseType.value.id}`, data)
          toast.success('Expense type updated successfully')
        } else {
          await axios.post('/super-admin/expense-types', data)
          toast.success('Expense type created successfully')
        }

        closeModal()
        loadExpenseTypes(expenseTypes.value.current_page)

        // Reload statistics if they were loaded
        if (statistics.value) {
          loadStatistics()
        }
      } catch (error) {
        if (error.response?.status === 422) {
          errors.value = error.response.data.errors || {}
        } else {
          console.error('Error submitting form:', error)
          toast.error(error.response?.data?.message || 'An error occurred')
        }
      } finally {
        submitting.value = false
      }
    }

    const confirmDelete = (expenseType) => {
      if (expenseType.expenses_count > 0) {
        toast.error(`Cannot delete expense type. It is being used by ${expenseType.expenses_count} expense(s).`)
        return
      }

      if (confirm(`Are you sure you want to delete "${expenseType.name}"? This action cannot be undone.`)) {
        deleteExpenseType(expenseType)
      }
    }

    const deleteExpenseType = async (expenseType) => {
      try {
        await axios.delete(`/super-admin/expense-types/${expenseType.id}`)
        toast.success('Expense type deleted successfully')
        loadExpenseTypes(expenseTypes.value.current_page)

        // Reload statistics if they were loaded
        if (statistics.value) {
          loadStatistics()
        }
      } catch (error) {
        console.error('Error deleting expense type:', error)
        toast.error(error.response?.data?.message || 'Failed to delete expense type')
      }
    }

    const confirmBulkDelete = () => {
      if (selectedItems.value.length === 0) return

      if (confirm(`Are you sure you want to delete ${selectedItems.value.length} expense type(s)? This action cannot be undone.`)) {
        bulkDelete()
      }
    }

    const bulkDelete = async () => {
      try {
        const response = await axios.post('/super-admin/expense-types/bulk-delete', {
          ids: selectedItems.value
        })

        if (response.data.errors && response.data.errors.length > 0) {
          response.data.errors.forEach(error => toast.warning(error))
        }

        toast.success(response.data.message)
        selectedItems.value = []
        loadExpenseTypes(expenseTypes.value.current_page)

        // Reload statistics if they were loaded
        if (statistics.value) {
          loadStatistics()
        }
      } catch (error) {
        console.error('Error bulk deleting expense types:', error)
        toast.error(error.response?.data?.message || 'Failed to delete expense types')
      }
    }

    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedItems.value = []
      } else {
        selectedItems.value = expenseTypes.value.data.map(item => item.id)
      }
    }

    const changePage = (page) => {
      if (page >= 1 && page <= expenseTypes.value.last_page) {
        loadExpenseTypes(page)
      }
    }

    const getVisiblePages = () => {
      const current = expenseTypes.value.current_page
      const last = expenseTypes.value.last_page
      const pages = []

      if (last <= 7) {
        for (let i = 1; i <= last; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(last)
        } else if (current >= last - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = last - 4; i <= last; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(last)
        }
      }

      return pages
    }

    const closeModal = () => {
      showModal.value = false
      isEditing.value = false
      selectedExpenseType.value = null
      form.name = ''
      form.description = ''
      errors.value = {}
    }

    const closeViewModal = () => {
      showViewModal.value = false
      selectedExpenseType.value = null
    }

    const handleModalOutsideClick = (event) => {
      if (event.target === event.currentTarget) {
        closeModal()
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return 'N/A'
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // Lifecycle
    onMounted(() => {
      loadExpenseTypes()
    })

    // Watch for filter changes
    watch(() => filters.per_page, () => {
      loadExpenseTypes()
    })

    return {
      // Data
      loading,
      submitting,
      showModal,
      showViewModal,
      isEditing,
      selectedExpenseType,
      selectedItems,
      statistics,
      expenseTypes,
      filters,
      form,
      errors,

      // Computed
      isAllSelected,

      // Methods
      loadExpenseTypes,
      loadStatistics,
      openCreateModal,
      editExpenseType,
      viewExpenseType,
      submitForm,
      confirmDelete,
      deleteExpenseType,
      confirmBulkDelete,
      bulkDelete,
      toggleSelectAll,
      changePage,
      getVisiblePages,
      closeModal,
      closeViewModal,
      handleModalOutsideClick,
      formatDate,
      debounceSearch
    }
  }
}
</script>