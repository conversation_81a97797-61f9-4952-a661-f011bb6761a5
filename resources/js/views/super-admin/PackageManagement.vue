<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('package_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('package_management_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="openCreateModal"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_package') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('search') }}</label>
            <input
              v-model="filters.search"
              @input="debounceSearch"
              type="text"
              :placeholder="$t('search_packages')"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('status') }}</label>
            <select
              v-model="filters.active_only"
              @change="loadPackages"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option :value="null">{{ $t('all_packages') }}</option>
              <option :value="true">{{ $t('active_only') }}</option>
              <option :value="false">{{ $t('inactive_only') }}</option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              @click="clearFilters"
              class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {{ $t('clear_filters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Packages Grid -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="packages.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">{{ $t('no_packages_found') }}</h3>
        <p class="mt-2 text-sm text-gray-500">{{ $t('create_first_package') }}</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="pkg in packages"
          :key="pkg.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
        >
          <!-- Package Header -->
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between mb-2">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ pkg.name }}</h3>
                <p v-if="pkg.name_en" class="text-sm text-gray-500">{{ pkg.name_en }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  v-if="pkg.is_popular"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800"
                >
                  {{ $t('popular') }}
                </span>
                <span
                  :class="pkg.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                >
                  {{ pkg.is_active ? $t('active') : $t('inactive') }}
                </span>
              </div>
            </div>
            <div class="space-y-1">
              <p class="text-sm text-gray-600">{{ pkg.description }}</p>
              <p v-if="pkg.description_en" class="text-sm text-gray-500">{{ pkg.description_en }}</p>
            </div>

            <!-- Pricing -->
            <div class="flex items-baseline space-x-2">
              <span class="text-2xl font-bold text-gray-900">{{ formatCurrency(pkg.price) }}</span>
              <span class="text-sm text-gray-500">/{{ $t('month') }}</span>
              <span v-if="pkg.annual_price" class="text-sm text-gray-500">
                ({{ formatCurrency(pkg.annual_price) }}/{{ $t('year') }})
              </span>
            </div>
          </div>

          <!-- Package Features -->
          <div class="p-6">
            <div class="space-y-3">
              <div v-if="pkg.max_neighbors" class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                {{ $t('max_neighbors') }}: {{ pkg.max_neighbors }}
              </div>

              <div v-if="pkg.storage_limit_gb" class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                {{ $t('storage_limit') }}: {{ pkg.storage_limit_gb }}GB
              </div>

              <div v-if="pkg.email_notifications_enabled" class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                {{ $t('email_notifications') }}
              </div>

              <div v-if="pkg.priority_support" class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                {{ $t('priority_support') }}
              </div>
            </div>
          </div>

          <!-- Package Actions -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-500">
                {{ $t('subscriptions') }}: {{ pkg.subscription_count || 0 }}
              </div>
              <div class="flex space-x-2">
                <button
                  @click="editPackage(pkg)"
                  class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                >
                  {{ $t('edit') }}
                </button>
                <button
                  @click="duplicatePackage(pkg)"
                  class="text-green-600 hover:text-green-900 text-sm font-medium"
                >
                  {{ $t('duplicate') }}
                </button>
                <button
                  @click="togglePackageStatus(pkg)"
                  :class="pkg.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                  class="text-sm font-medium"
                >
                  {{ pkg.is_active ? $t('deactivate') : $t('activate') }}
                </button>
                <button
                  @click="deletePackage(pkg)"
                  class="text-red-600 hover:text-red-900 text-sm font-medium"
                >
                  {{ $t('delete') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Package Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleModalOutsideClick">
      <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? $t('create_new_package') : $t('edit_package') }}
          </h3>
          
          <form @submit.prevent="showCreateModal ? createPackage() : updatePackage()" class="space-y-4">
            <!-- Package Names -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('package_name') }} ({{ $t('arabic') }})</label>
                <input
                  v-model="packageForm.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_package_name')"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('package_name') }} ({{ $t('english') }})</label>
                <input
                  v-model="packageForm.name_en"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_package_name_english')"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('slug') }}</label>
              <input
                v-model="packageForm.slug"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('auto_generated')"
              />
            </div>

            <!-- Package Descriptions -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('description') }} ({{ $t('arabic') }})</label>
                <textarea
                  v-model="packageForm.description"
                  rows="3"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_package_description')"
                ></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('description') }} ({{ $t('english') }})</label>
                <textarea
                  v-model="packageForm.description_en"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_package_description_english')"
                ></textarea>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('monthly_price') }}</label>
                <input
                  v-model.number="packageForm.price"
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('enter_monthly_price')"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('annual_price') }}</label>
                <input
                  v-model.number="packageForm.annual_price"
                  type="number"
                  step="0.01"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('optional_annual_price')"
                />
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('max_neighbors') }}</label>
                <input
                  v-model.number="packageForm.max_neighbors"
                  type="number"
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('unlimited')"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('storage_limit_gb') }}</label>
                <input
                  v-model.number="packageForm.storage_limit_gb"
                  type="number"
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('unlimited')"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('max_admins') }}</label>
                <input
                  v-model.number="packageForm.max_admins"
                  type="number"
                  min="1"
                  max="50"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('max_admins_placeholder')"
                />
              </div>


              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('trial_days') }}</label>
                <input
                  v-model.number="packageForm.trial_days"
                  type="number"
                  min="0"
                  max="365"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="$t('no_trial')"
                />
              </div>
            </div>

            <!-- Feature Checkboxes -->
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <label class="flex items-center">
                <input
                  v-model="packageForm.email_notifications_enabled"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('email_notifications') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.sms_notifications_enabled"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('sms_notifications') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.priority_support"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('priority_support') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.exports_enabled"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('exports_enabled') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.is_active"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('active') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.multi_admin_enabled"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('multi_admin_support') }}</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="packageForm.is_popular"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">{{ $t('popular') }}</span>
              </label>
            </div>

            <!-- Email Settings Section -->
            <div class="border-t pt-4 bg-gray-50 p-4 rounded-lg">
              <div class="flex items-center mb-3">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h4 class="text-md font-medium text-gray-900">{{ $t('email_settings') }}</h4>
              </div>
              <p class="text-sm text-gray-600 mb-4">{{ $t('configure_email_limitations_for_package') }}</p>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('email_limit_per_month') }}</label>
                  <input
                    v-model.number="packageForm.email_limit_per_month"
                    type="number"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$t('unlimited')"
                  />
                  <p class="text-xs text-gray-500 mt-1">{{ $t('leave_empty_for_unlimited') }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('email_limit_per_day') }}</label>
                  <input
                    v-model.number="packageForm.email_limit_per_day"
                    type="number"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$t('unlimited')"
                  />
                  <p class="text-xs text-gray-500 mt-1">{{ $t('leave_empty_for_unlimited') }}</p>
                </div>

                <div class="flex items-end">
                  <label class="flex items-center">
                    <input
                      v-model="packageForm.email_quota_warnings_enabled"
                      type="checkbox"
                      class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <span class="ml-2 text-sm text-gray-700">{{ $t('email_quota_warnings') }}</span>
                  </label>
                </div>
              </div>
            </div>



            <!-- Export Settings (conditional) -->
            <div v-if="packageForm.exports_enabled" class="border-t pt-4 bg-gray-50 p-4 rounded-lg">
              <div class="flex items-center mb-3">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h4 class="text-md font-medium text-gray-900">{{ $t('export_settings') }}</h4>
              </div>
              <p class="text-sm text-gray-600 mb-4">{{ $t('configure_export_limitations_for_package') }}</p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('exports_per_month') }}</label>
                  <input
                    v-model.number="packageForm.exports_per_month"
                    type="number"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$t('unlimited')"
                  />
                  <p class="text-xs text-gray-500 mt-1">{{ $t('leave_empty_for_unlimited') }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('max_records_per_export') }}</label>
                  <input
                    v-model.number="packageForm.max_records_per_export"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="$t('default_1000')"
                  />
                </div>
              </div>

              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('export_formats') }}</label>
                <p class="text-xs text-gray-500 mb-3">{{ $t('select_allowed_export_formats') }}</p>
                <div class="grid grid-cols-2 gap-3">
                  <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      v-model="packageForm.export_formats"
                      type="checkbox"
                      value="pdf"
                      class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <div class="ml-3">
                      <span class="text-sm font-medium text-gray-700">PDF</span>
                      <p class="text-xs text-gray-500">{{ $t('portable_document_format') }}</p>
                    </div>
                  </label>
                  <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      v-model="packageForm.export_formats"
                      type="checkbox"
                      value="excel"
                      class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <div class="ml-3">
                      <span class="text-sm font-medium text-gray-700">Excel</span>
                      <p class="text-xs text-gray-500">{{ $t('spreadsheet_format') }}</p>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="handleCancel"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                {{ $t('cancel') }}
              </button>
              <button
                type="submit"
                :disabled="isSubmitting"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isSubmitting">{{ showCreateModal ? $t('creating') : $t('updating') }}...</span>
                <span v-else>{{ showCreateModal ? $t('create_package') : $t('update_package') }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';
import smartModalMixin from '../../mixins/smartModalMixin.js';

export default {
  name: 'PackageManagement',
  mixins: [i18nMixin, smartModalMixin],
  data() {
    return {
      loading: false,
      showCreateModal: false,
      showEditModal: false,
      isSubmitting: false,
      packages: [],
      filters: {
        search: '',
        active_only: null
      },
      packageForm: {
        name: '',
        name_en: '',
        slug: '',
        description: '',
        description_en: '',
        price: null,
        annual_price: null,
        max_neighbors: null,
        email_notifications_enabled: true,
        email_limit_per_month: null,
        email_limit_per_day: null,
        email_quota_warnings_enabled: true,
        sms_notifications_enabled: false,
        priority_support: false,
        exports_enabled: false,
        exports_per_month: null,
        max_records_per_export: 1000,
        export_formats: [],
        file_attachments_enabled: true,
        storage_limit_gb: null,
        multi_admin_enabled: false,
        max_admins: 1,
        trial_days: null,
        is_active: true,
        is_popular: false
      },
      editingPackage: null,
      originalPackageForm: {}, // Track original form state
      searchTimeout: null
    };
  },
  computed: {
    // Override smart modal mixin to track packageForm changes
    hasFormChanges() {
      return JSON.stringify(this.packageForm) !== JSON.stringify(this.originalPackageForm);
    }
  },
  async mounted() {
    await this.loadPackages();
  },
  methods: {
    async loadPackages() {
      this.loading = true;
      try {
        const params = {};
        
        if (this.filters.search) {
          params.search = this.filters.search;
        }
        
        if (this.filters.active_only !== null) {
          params.active_only = this.filters.active_only;
        }

        const response = await this.$axios.get('/super-admin/packages', { params });
        this.packages = response.data;
      } catch (error) {
        console.error('Error loading packages:', error);
        this.$toast.error(this.$t('failed_to_load_packages'));
      } finally {
        this.loading = false;
      }
    },

    async createPackage() {
      this.isSubmitting = true;
      try {
        await this.$axios.post('/super-admin/packages', this.packageForm);
        
        await this.loadPackages();
        this.$toast.success(this.$t('package_created_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error creating package:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_create_package'));
      } finally {
        this.isSubmitting = false;
      }
    },

    async updatePackage() {
      this.isSubmitting = true;
      try {
        await this.$axios.put(`/super-admin/packages/${this.editingPackage.id}`, this.packageForm);
        
        await this.loadPackages();
        this.$toast.success(this.$t('package_updated_successfully'));
        this.handleFormSuccess();
      } catch (error) {
        console.error('Error updating package:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_update_package'));
      } finally {
        this.isSubmitting = false;
      }
    },

    openCreateModal() {
      this.resetPackageForm();
      this.initializeFormTracking();
      this.showCreateModal = true;
    },

    editPackage(packageData) {
      this.editingPackage = packageData;
      this.packageForm = { ...packageData };
      this.initializeFormTracking();
      this.showEditModal = true;
    },

    async duplicatePackage(packageData) {
      const name = prompt(this.$t('enter_new_package_name'), packageData.name + ' (Copy)');
      if (!name) return;

      try {
        await this.$axios.post(`/super-admin/packages/${packageData.id}/duplicate`, { name });
        await this.loadPackages();
        this.$toast.success(this.$t('package_duplicated_successfully'));
      } catch (error) {
        console.error('Error duplicating package:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_duplicate_package'));
      }
    },

    async togglePackageStatus(packageData) {
      const action = packageData.is_active ? 'deactivate' : 'activate';
      if (!confirm(this.$t(`confirm_${action}_package`))) return;

      try {
        await this.$axios.post(`/super-admin/packages/${packageData.id}/toggle-status`);
        await this.loadPackages();
        this.$toast.success(this.$t('package_status_updated_successfully'));
      } catch (error) {
        console.error('Error toggling package status:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_update_package_status'));
      }
    },

    async deletePackage(packageData) {
      if (!confirm(this.$t('confirm_delete_package'))) return;

      try {
        await this.$axios.delete(`/super-admin/packages/${packageData.id}`);
        await this.loadPackages();
        this.$toast.success(this.$t('package_deleted_successfully'));
      } catch (error) {
        console.error('Error deleting package:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_delete_package'));
      }
    },

    closeModal() {
      this.showCreateModal = false;
      this.showEditModal = false;
      this.editingPackage = null;
      this.resetPackageForm();
    },

    resetPackageForm() {
      this.packageForm = {
        name: '',
        name_en: '',
        slug: '',
        description: '',
        description_en: '',
        price: null,
        annual_price: null,
        max_neighbors: null,
        email_notifications_enabled: true,
        email_limit_per_month: null,
        email_limit_per_day: null,
        email_quota_warnings_enabled: true,
        sms_notifications_enabled: false,
        priority_support: false,
        exports_enabled: false,
        exports_per_month: null,
        max_records_per_export: 1000,
        export_formats: [],
        file_attachments_enabled: true,
        storage_limit_gb: null,
        multi_admin_enabled: false,
        max_admins: 1,
        trial_days: null,
        is_active: true,
        is_popular: false
      };
      this.originalPackageForm = { ...this.packageForm };
    },

    handleModalOutsideClick() {
      if (!this.hasFormChanges) {
        this.closeModal();
      } else {
        this.showUnsavedChangesAlert();
      }
    },

    handleCancel() {
      if (!this.hasFormChanges) {
        this.closeModal();
      } else {
        this.showDiscardChangesConfirmation();
      }
    },

    initializeFormTracking() {
      this.originalPackageForm = JSON.parse(JSON.stringify(this.packageForm));
    },

    clearFilters() {
      this.filters = {
        search: '',
        active_only: null
      };
      this.loadPackages();
    },

    debounceSearch() {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.loadPackages();
      }, 500);
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    }
  }
};
</script>