<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('subscription_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('subscription_management_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <button
              @click="initializeCreateForm"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_subscription') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Statistics Cards -->
      <div v-if="!loadingStats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('active_subscriptions') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.active_subscriptions || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z"/>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('trial_subscriptions') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.trial_subscriptions || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_revenue') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(statistics.total_revenue || 0) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ $t('cancelled_subscriptions') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.cancelled_subscriptions || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('status') }}</label>
            <select
              v-model="filters.status"
              @change="loadSubscriptions"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{{ $t('all_statuses') }}</option>
              <option value="active">{{ $t('active') }}</option>
              <option value="trial">{{ $t('trial') }}</option>
              <option value="cancelled">{{ $t('cancelled') }}</option>
              <option value="expired">{{ $t('expired') }}</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('package') }}</label>
            <select
              v-model="filters.package_id"
              @change="loadSubscriptions"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{{ $t('all_packages') }}</option>
              <option v-for="pkg in packages" :key="pkg.id" :value="pkg.id">
                {{ getPackageName(pkg) }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('search') }}</label>
            <input
              v-model="filters.search"
              @input="debounceSearch"
              type="text"
              :placeholder="$t('search_buildings')"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div class="flex items-end">
            <button
              @click="clearFilters"
              class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {{ $t('clear_filters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Subscriptions Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('subscriptions') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('manage_all_building_subscriptions') }}</p>
        </div>
        
        <div v-if="loading" class="p-6">
          <div class="flex justify-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>

        <div v-else-if="subscriptions.length === 0" class="p-6 text-center text-gray-500">
          {{ $t('no_subscriptions_found') }}
        </div>

        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('building') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('package') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('status') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('billing_cycle') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('amount') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('expires_at') }}
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('actions') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="subscription in subscriptions" :key="subscription.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ subscription.building?.name }}</div>
                    <div class="text-sm text-gray-500">{{ subscription.building?.city }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ getPackageName(subscription.package) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(subscription.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ $t(subscription.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ $t(subscription.billing_cycle) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(subscription.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(subscription.ends_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                  <button
                    @click="editSubscription(subscription)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    {{ $t('edit') }}
                  </button>
                  <button
                    v-if="(subscription.status === 'active' || subscription.status === 'trial') && shouldShowRenewButton(subscription)"
                    @click="renewSubscription(subscription)"
                    class="text-green-600 hover:text-green-900"
                  >
                    {{ $t('renew') }}
                  </button>
                  <button
                    v-if="subscription.status !== 'cancelled'"
                    @click="cancelSubscription(subscription)"
                    class="text-red-600 hover:text-red-900"
                  >
                    {{ $t('cancel') }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.total > pagination.per_page" class="px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              {{ $t('showing') }} {{ pagination.from }} {{ $t('to') }} {{ pagination.to }} {{ $t('of') }} {{ pagination.total }} {{ $t('results') }}
            </div>
            <div class="flex space-x-2">
              <button
                @click="changePage(pagination.current_page - 1)"
                :disabled="pagination.current_page <= 1"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ $t('previous') }}
              </button>
              <button
                @click="changePage(pagination.current_page + 1)"
                :disabled="pagination.current_page >= pagination.last_page"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ $t('next') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Subscription Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleCreateModalOutsideClick">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('create_new_subscription') }}</h3>
          
          <form @submit.prevent="createSubscription" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('building') }}</label>
              <select
                v-model="createForm.building_id"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{{ $t('select_building') }}</option>
                <option v-for="building in buildings" :key="building.id" :value="building.id">
                  {{ building.name }} - {{ building.city }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('package') }}</label>
              <select
                v-model="createForm.package_id"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{{ $t('select_package') }}</option>
                <option v-for="pkg in packages" :key="pkg.id" :value="pkg.id">
                  {{ getPackageName(pkg) }} - {{ formatCurrency(pkg.price) }}/{{ $t('month') }}
                  <span v-if="!pkg.is_active"> ({{ $t('inactive') }})</span>
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('billing_cycle') }}</label>
              <select
                v-model="createForm.billing_cycle"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="monthly">{{ $t('monthly') }}</option>
                <option value="annual">{{ $t('annual') }}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('trial_days') }}</label>
              <input
                v-model.number="createForm.trial_days"
                type="number"
                min="0"
                max="365"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('optional_trial_days')"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('notes') }}</label>
              <textarea
                v-model="createForm.notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('optional_notes')"
              ></textarea>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeCreateModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                {{ $t('cancel') }}
              </button>
              <button
                type="submit"
                :disabled="isSubmitting"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isSubmitting">{{ $t('creating') }}...</span>
                <span v-else>{{ $t('create_subscription') }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Edit Subscription Modal -->
    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleEditModalOutsideClick">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('edit_subscription') }}</h3>

          <form @submit.prevent="updateSubscription" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('package') }}</label>
              <select
                v-model="editForm.package_id"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">{{ $t('select_package') }}</option>
                <option v-for="pkg in packages" :key="pkg.id" :value="pkg.id">
                  {{ getPackageName(pkg) }} - ${{ pkg.price }}/{{ $t('month') }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('billing_cycle') }}</label>
              <select
                v-model="editForm.billing_cycle"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="monthly">{{ $t('monthly') }}</option>
                <option value="annual">{{ $t('annual') }}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('status') }}</label>
              <select
                v-model="editForm.status"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="active">{{ $t('active') }}</option>
                <option value="inactive">{{ $t('inactive') }}</option>
                <option value="trial">{{ $t('trial') }}</option>
                <option value="cancelled">{{ $t('cancelled') }}</option>
                <option value="expired">{{ $t('expired') }}</option>
              </select>
            </div>

            <div>
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="editForm.auto_renew"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                >
                <span class="ml-2 text-sm text-gray-700">{{ $t('auto_renew') }}</span>
              </label>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('expires_at') }}</label>
              <input
                type="date"
                v-model="editForm.ends_at"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('notes') }}</label>
              <textarea
                v-model="editForm.notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="$t('optional_notes')"
              ></textarea>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeEditModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                {{ $t('cancel') }}
              </button>
              <button
                type="submit"
                :disabled="isSubmitting"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isSubmitting">{{ $t('updating') }}...</span>
                <span v-else>{{ $t('update_subscription') }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  name: 'SubscriptionManagement',
  mixins: [i18nMixin],
  data() {
    return {
      loading: false,
      loadingStats: false,
      showCreateModal: false,
      showEditModal: false,
      isSubmitting: false,
      subscriptions: [],
      statistics: {},
      packages: [],
      buildings: [],
      filters: {
        status: '',
        package_id: '',
        search: ''
      },
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 0,
        last_page: 1,
        from: 0,
        to: 0
      },
      createForm: {
        building_id: '',
        package_id: '',
        billing_cycle: 'monthly',
        trial_days: null,
        notes: ''
      },
      editForm: {
        id: null,
        package_id: '',
        billing_cycle: 'monthly',
        status: 'active',
        auto_renew: true,
        ends_at: '',
        notes: ''
      },
      editingSubscription: null,
      searchTimeout: null,
      originalCreateForm: {},
      originalEditForm: {},
      createFormModified: false,
      editFormModified: false
    };
  },
  computed: {
    hasCreateFormChanges() {
      return JSON.stringify(this.createForm) !== JSON.stringify(this.originalCreateForm);
    },
    hasEditFormChanges() {
      return JSON.stringify(this.editForm) !== JSON.stringify(this.originalEditForm);
    }
  },
  async mounted() {
    await Promise.all([
      this.loadSubscriptions(),
      this.loadStatistics(),
      this.loadPackages(),
      this.loadBuildings()
    ]);
  },
  methods: {
    async loadSubscriptions(page = 1) {
      this.loading = true;
      try {
        const params = {
          page,
          per_page: this.pagination.per_page,
          ...this.filters
        };

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null) {
            delete params[key];
          }
        });

        const response = await this.$axios.get('/super-admin/subscriptions', { params });
        
        this.subscriptions = response.data.data;
        this.pagination = {
          current_page: response.data.current_page,
          per_page: response.data.per_page,
          total: response.data.total,
          last_page: response.data.last_page,
          from: response.data.from,
          to: response.data.to
        };
      } catch (error) {
        console.error('Error loading subscriptions:', error);
        this.$toast.error(this.$t('failed_to_load_subscriptions'));
      } finally {
        this.loading = false;
      }
    },

    async loadStatistics() {
      this.loadingStats = true;
      try {
        const response = await this.$axios.get('/super-admin/subscriptions/statistics');
        this.statistics = response.data;
      } catch (error) {
        console.error('Error loading statistics:', error);
      } finally {
        this.loadingStats = false;
      }
    },

    async loadPackages() {
      try {
        // Use super admin packages endpoint to get all packages (including inactive ones)
        const response = await this.$axios.get('/super-admin/packages');
        this.packages = response.data;
      } catch (error) {
        console.error('Error loading packages:', error);
      }
    },

    async loadBuildings() {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (error) {
        console.error('Error loading buildings:', error);
      }
    },

    async createSubscription() {
      this.isSubmitting = true;
      try {
        await this.$axios.post('/super-admin/subscriptions', this.createForm);
        
        // Reset form and close modal
        this.closeCreateModal();
        
        // Reload data
        await Promise.all([
          this.loadSubscriptions(),
          this.loadStatistics()
        ]);
        
        this.$toast.success(this.$t('subscription_created_successfully'));
      } catch (error) {
        console.error('Error creating subscription:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_create_subscription'));
      } finally {
        this.isSubmitting = false;
      }
    },

    async renewSubscription(subscription) {
      if (!confirm(this.$t('confirm_renew_subscription'))) return;
      
      try {
        await this.$axios.post(`/super-admin/subscriptions/${subscription.id}/renew`);
        await this.loadSubscriptions();
        this.$toast.success(this.$t('subscription_renewed_successfully'));
      } catch (error) {
        console.error('Error renewing subscription:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_renew_subscription'));
      }
    },

    async cancelSubscription(subscription) {
      if (!confirm(this.$t('confirm_cancel_subscription'))) return;
      
      try {
        await this.$axios.post(`/super-admin/subscriptions/${subscription.id}/cancel`);
        await this.loadSubscriptions();
        this.$toast.success(this.$t('subscription_cancelled_successfully'));
      } catch (error) {
        console.error('Error cancelling subscription:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_cancel_subscription'));
      }
    },

    editSubscription(subscription) {
      this.initializeEditForm(subscription);
    },

    async updateSubscription() {
      this.isSubmitting = true;
      try {
        await this.$axios.put(`/super-admin/subscriptions/${this.editForm.id}`, {
          package_id: this.editForm.package_id,
          billing_cycle: this.editForm.billing_cycle,
          status: this.editForm.status,
          auto_renew: this.editForm.auto_renew,
          ends_at: this.editForm.ends_at,
          notes: this.editForm.notes
        });

        // Reset form and close modal
        this.closeEditModal();

        // Reload data
        await Promise.all([
          this.loadSubscriptions(),
          this.loadStatistics()
        ]);

        this.$toast.success(this.$t('subscription_updated_successfully'));
      } catch (error) {
        console.error('Error updating subscription:', error);
        this.$toast.error(error.response?.data?.message || this.$t('failed_to_update_subscription'));
      } finally {
        this.isSubmitting = false;
      }
    },

    changePage(page) {
      if (page >= 1 && page <= this.pagination.last_page) {
        this.loadSubscriptions(page);
      }
    },

    clearFilters() {
      this.filters = {
        status: '',
        package_id: '',
        search: ''
      };
      this.loadSubscriptions();
    },

    debounceSearch() {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.loadSubscriptions();
      }, 500);
    },

    getStatusClass(status) {
      const classes = {
        active: 'bg-green-100 text-green-800',
        trial: 'bg-blue-100 text-blue-800',
        cancelled: 'bg-red-100 text-red-800',
        expired: 'bg-gray-100 text-gray-800',
        inactive: 'bg-yellow-100 text-yellow-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    },

    formatCurrency(amount) {
      // Force reactivity by accessing localeUpdateKey
      this.localeUpdateKey;
      const locale = this.$locale() === 'ar' ? 'ar-SA' : 'en-US';
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    },

    formatDate(dateString) {
      // Force reactivity by accessing localeUpdateKey
      this.localeUpdateKey;
      const locale = 'en-US';
      return new Date(dateString).toLocaleDateString(locale);
    },

    getPackageName(pkg) {
      if (!pkg) return '';
      // Force reactivity by accessing localeUpdateKey
      this.localeUpdateKey;
      if (this.$locale() === 'en' && pkg.name_en) {
        return pkg.name_en;
      }
      return pkg.name;
    },

    shouldShowRenewButton(subscription) {
      if (!subscription.ends_at) {
        return true; // Show renew button if no expiration date is set
      }

      const today = new Date();
      const expirationDate = new Date(subscription.ends_at);

      // Only show renew button if subscription has expired or expires today
      return expirationDate <= today;
    },

    handleCreateModalOutsideClick() {
      if (!this.hasCreateFormChanges) {
        this.closeCreateModal();
      } else {
        this.$toast.warning(this.$t('unsaved_changes_warning'));
      }
    },

    handleEditModalOutsideClick() {
      if (!this.hasEditFormChanges) {
        this.closeEditModal();
      } else {
        this.$toast.warning(this.$t('unsaved_changes_warning'));
      }
    },

    closeCreateModal() {
      if (this.hasCreateFormChanges && !confirm(this.$t('confirm_discard_changes'))) {
        return;
      }
      this.showCreateModal = false;
      this.resetCreateForm();
    },

    closeEditModal() {
      if (this.hasEditFormChanges && !confirm(this.$t('confirm_discard_changes'))) {
        return;
      }
      this.showEditModal = false;
      this.resetEditForm();
    },

    resetCreateForm() {
      this.createForm = {
        building_id: '',
        package_id: '',
        billing_cycle: 'monthly',
        trial_days: null,
        notes: ''
      };
      this.originalCreateForm = { ...this.createForm };
    },

    resetEditForm() {
      this.editForm = {
        id: null,
        package_id: '',
        billing_cycle: 'monthly',
        status: 'active',
        auto_renew: true,
        ends_at: '',
        notes: ''
      };
      this.editingSubscription = null;
      this.originalEditForm = { ...this.editForm };
    },

    initializeCreateForm() {
      this.resetCreateForm();
      this.showCreateModal = true;
    },

    initializeEditForm(subscription) {
      this.editingSubscription = subscription;
      this.editForm = {
        id: subscription.id,
        package_id: subscription.package_id,
        billing_cycle: subscription.billing_cycle,
        status: subscription.status,
        auto_renew: subscription.auto_renew,
        ends_at: subscription.ends_at ? subscription.ends_at.split('T')[0] : '', // Format for date input
        notes: subscription.notes || ''
      };
      this.originalEditForm = { ...this.editForm };
      this.showEditModal = true;
    }
  }
};
</script>
