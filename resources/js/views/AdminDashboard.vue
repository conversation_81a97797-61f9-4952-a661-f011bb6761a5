<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              {{ isSuperAdmin ? $t('super_admin_dashboard') : $t('admin_dashboard') }}
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              {{ isSuperAdmin ? $t('super_admin_dashboard_description') : $t('admin_dashboard_description') }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">



      <!-- Enhanced Financial Reports Section (Admin only) -->
      <div v-if="!isSuperAdmin && !loading" class="space-y-6 mb-8">
        <!-- Financial Overview with Charts -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 class="text-lg font-medium text-gray-900">{{ $t('financial_overview') }}</h2>
                <p class="mt-1 text-sm text-gray-500">{{ $t('financial_overview_description') }}</p>
              </div>
              <div class="mt-4 sm:mt-0 flex space-x-2">
                <select v-model="selectedTimeRange" @change="updateFinancialData"
                  class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="30">{{ $t('last_30_days') }}</option>
                  <option value="90">{{ $t('last_3_months') }}</option>
                  <option value="180">{{ $t('last_6_months') }}</option>
                  <option value="365">{{ $t('last_year') }}</option>
                </select>
                <button @click="showAdvancedFilters = !showAdvancedFilters"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  {{ $t('filters') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Advanced Filters Panel -->
          <div v-if="showAdvancedFilters" class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('from_date') }}</label>
                <input type="date" v-model="financialFilters.fromDate" @change="updateFinancialData"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('to_date') }}</label>
                <input type="date" v-model="financialFilters.toDate" @change="updateFinancialData"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('comparison_period') }}</label>
                <select v-model="financialFilters.comparisonPeriod" @change="updateFinancialData"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">{{ $t('no_comparison') }}</option>
                  <option value="previous_month">{{ $t('previous_month') }}</option>
                  <option value="same_month_last_year">{{ $t('same_month_last_year') }}</option>
                  <option value="previous_period">{{ $t('previous_period') }}</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Financial Charts -->
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Expenses vs Income Chart -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 mb-4">{{ $t('expenses_vs_income_trend') }}</h3>
                <div class="h-64 flex items-center justify-center">
                  <canvas ref="expenseIncomeChart" class="max-w-full max-h-full"></canvas>
                </div>
              </div>

              <!-- Monthly Comparison Chart -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 mb-4">{{ $t('monthly_comparison') }}</h3>
                <div class="h-64 flex items-center justify-center">
                  <canvas ref="monthlyComparisonChart" class="max-w-full max-h-full"></canvas>
                </div>
              </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
              <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-blue-900">{{ $t('total_expenses') }}</p>
                    <p class="text-lg font-bold text-blue-900">{{ formatCurrency(financialSummary.totalExpenses) }}</p>
                    <p v-if="financialSummary.expensesChange" class="text-xs" :class="financialSummary.expensesChange > 0 ? 'text-red-600' : 'text-green-600'">
                      {{ financialSummary.expensesChange > 0 ? '+' : '' }}{{ financialSummary.expensesChange.toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>

              <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-900">{{ $t('total_income') }}</p>
                    <p class="text-lg font-bold text-green-900">{{ formatCurrency(financialSummary.totalIncome) }}</p>
                    <p v-if="financialSummary.incomeChange" class="text-xs" :class="financialSummary.incomeChange > 0 ? 'text-green-600' : 'text-red-600'">
                      {{ financialSummary.incomeChange > 0 ? '+' : '' }}{{ financialSummary.incomeChange.toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>

              <div class="bg-purple-50 rounded-lg p-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-purple-900">{{ $t('net_balance') }}</p>
                    <p class="text-lg font-bold" :class="financialSummary.netBalance >= 0 ? 'text-green-900' : 'text-red-900'">
                      {{ formatCurrency(financialSummary.netBalance) }}
                    </p>
                    <p v-if="financialSummary.balanceChange" class="text-xs" :class="financialSummary.balanceChange > 0 ? 'text-green-600' : 'text-red-600'">
                      {{ financialSummary.balanceChange > 0 ? '+' : '' }}{{ financialSummary.balanceChange.toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>

              <div class="bg-orange-50 rounded-lg p-4">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-orange-900">{{ $t('avg_monthly_expense') }}</p>
                    <p class="text-lg font-bold text-orange-900">{{ formatCurrency(financialSummary.avgMonthlyExpense).toFixed(1) }}</p>
                    <p v-if="financialSummary.avgExpenseChange" class="text-xs" :class="financialSummary.avgExpenseChange > 0 ? 'text-red-600' : 'text-green-600'">
                      {{ financialSummary.avgExpenseChange > 0 ? '+' : '' }}{{ financialSummary.avgExpenseChange.toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Super Admin Tables -->
      <div v-if="isSuperAdmin && !loading" class="space-y-6">
        <!-- Buildings Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 class="text-lg font-medium text-gray-900">{{ $t('buildings') }}</h2>
                <p class="mt-1 text-sm text-gray-500">{{ $t('buildings_description') }}</p>
              </div>
              <div class="mt-4 sm:mt-0">
                <router-link to="/admin/buildings/create"
                  class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  {{ $t('add_building') }}
                </router-link>
              </div>
            </div>
          </div>

          <data-table :columns="buildingColumns" :items="buildings" :loading="loading" :show-header="false">
            <template #actions="{ item }">
              <div class="flex items-center space-x-2" v-if="item && item.id">
                <router-link :to="`/admin/buildings/${item.id}/edit`"
                  class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  {{ $t('edit') }}
                </router-link>
                <button @click="deleteBuilding(item)"
                  class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  {{ $t('delete') }}
                </button>
              </div>
            </template>
          </data-table>
        </div>

        <!-- Admins Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 class="text-lg font-medium text-gray-900">{{ $t('admins') }}</h2>
                <p class="mt-1 text-sm text-gray-500">{{ $t('admins_description') }}</p>
              </div>
              <div class="mt-4 sm:mt-0">
                <router-link to="/admin/users/create"
                  class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  {{ $t('add_admin') }}
                </router-link>
              </div>
            </div>
          </div>

          <data-table :columns="adminColumns" :items="admins" :loading="loading" :show-header="false">
            <template #actions="{ item }">
              <div class="flex items-center space-x-2" v-if="item && item.id">
                <button @click="editAdmin(item)"
                  class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  {{ $t('edit') }}
                </button>
                <button @click="deleteAdmin(item)"
                  class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  {{ $t('delete') }}
                </button>
              </div>
            </template>
          </data-table>
        </div>
      </div>

      <!-- Regular Admin Table -->
      <div v-else-if="!loading">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 class="text-lg font-medium text-gray-900">{{ $t('neighbor_financial_summary') }}</h2>
                <p class="mt-1 text-sm text-gray-500">{{ $t('neighbor_financial_summary_description') }}</p>
              </div>
              <div class="mt-4 sm:mt-0">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ neighborSummary.length }} {{ $t('neighbors') }}
                </span>
              </div>
            </div>
          </div>

          <data-table :columns="neighborColumns" :items="neighborSummary" :loading="loading" :show-header="false" />
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2 text-sm text-gray-500">{{ $t('loading_dashboard_data') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DataTable from '../components/DataTable.vue';
import i18nMixin from '../mixins/i18nMixin.js';
import Chart from 'chart.js/auto';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable
  },
  data() {
    return {
      loading: true, // Start with loading true
      totalExpenses: 0,
      totalIncome: 0,
      totalUsers: 0,
      totalBuildings: 0,
      totalAdmins: 0,
      totalNeighbors: 0,
      activeBuildings: 0,
      allExpenses: [],
      allIncomes: [],
      neighborSummaryData: [],
      buildings: [],
      admins: [],
      neighborColumns: [],
      buildingColumns: [],
      adminColumns: [],
      isSuperAdmin: false,
      userLoaded: false,
      // Enhanced financial reporting data
      selectedTimeRange: '365',
      showAdvancedFilters: false,
      financialFilters: {
        fromDate: '',
        toDate: '',
        comparisonPeriod: ''
      },
      financialSummary: {
        totalExpenses: 0,
        totalIncome: 0,
        netBalance: 0,
        avgMonthlyExpense: 0,
        expensesChange: null,
        incomeChange: null,
        balanceChange: null,
        avgExpenseChange: null
      },
      expenseIncomeChart: null,
      monthlyComparisonChart: null
    };
  },
  computed: {
    outstandingBalance() {
      return this.totalExpenses - this.totalIncome;
    },
    neighborSummary() {
      // Return the data from the API directly since it's already processed
      return this.neighborSummaryData || [];
    }
  },
  async created() {
    // First try to get user data from localStorage for immediate role determination
    this.initializeUserFromStorage();
    this.initializeColumns();
    await this.loadDashboardData();

    // Initialize financial data for admin users
    if (!this.isSuperAdmin) {
      await this.updateFinancialData();
    }
  },
  methods: {
    initializeColumns() {
      this.neighborColumns = [
        { key: 'neighbor_name', label: this.$t('neighbor') },
        { key: 'apartment_number', label: this.$t('apartment') },
        { key: 'expenses', label: this.$t('total_expenses') },
        { key: 'incomes', label: this.$t('total_income') },
        { key: 'outstanding_balance', label: this.$t('outstanding_balance') }
      ];
      this.buildingColumns = [
        { key: 'name', label: this.$t('building_name') },
        { key: 'city', label: this.$t('city') },
        { key: 'monthly_fee', label: this.$t('monthly_fee') },
        { key: 'admin_count', label: this.$t('admins') },
        { key: 'neighbor_count', label: this.$t('neighbors') }
      ];
      this.adminColumns = [
        { key: 'name', label: this.$t('name') },
        { key: 'email', label: this.$t('email') },
        { key: 'building.name', label: this.$t('building') },
        { key: 'created_at', label: this.$t('created') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async loadDashboardData() {
      this.loading = true;
      try {
        // Load user data to check role (and update localStorage if needed)
        const userResponse = await this.$axios.get('/user');
        if (userResponse.data) {
          this.isSuperAdmin = userResponse.data.role === 'super_admin';
          this.userLoaded = true;
          // Update localStorage with fresh user data
          localStorage.setItem('user', JSON.stringify(userResponse.data));
        }

        if (this.isSuperAdmin) {
          // Load buildings data for super admin
          const buildingsResponse = await this.$axios.get('/buildings');
          if (buildingsResponse.data && Array.isArray(buildingsResponse.data)) {
            this.buildings = buildingsResponse.data.filter(building => building && building.id).map(building => ({
              ...building,
              monthly_fee: `${parseFloat(building.monthly_fee || 0).toFixed(2)}`,
              admin_count: 0, // Will be calculated from users
              neighbor_count: 0 // Will be calculated from users
            }));
            this.totalBuildings = this.buildings.length;
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          } else {
            this.buildings = [];
          }

          // Load all users data for super admin
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data && Array.isArray(usersResponse.data.data)) {
            const allUsers = usersResponse.data.data.filter(user => user && user.id);

            // Filter admins
            this.admins = allUsers.filter(user => user.role === 'admin');
            this.totalAdmins = this.admins.length;

            // Count neighbors
            this.totalNeighbors = allUsers.filter(user => user.role === 'neighbor').length;

            // Calculate building user counts
            this.buildings.forEach(building => {
              building.admin_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'admin'
              ).length;
              building.neighbor_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'neighbor'
              ).length;
            });

            // Update active buildings count
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          }
        } else {
          // Load regular admin data

          // Load neighbor financial summary data
          const neighborSummaryResponse = await this.$axios.get('/admin/neighbor-financial-summary');
          if (neighborSummaryResponse.data && Array.isArray(neighborSummaryResponse.data)) {
            this.neighborSummaryData = neighborSummaryResponse.data;
          } else {
            this.neighborSummaryData = [];
          }

          // Load users data
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data) {
            this.totalUsers = usersResponse.data.data.length;
          }
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        this.loading = false;
      }
    },
    async deleteBuilding(building) {
      if (confirm(this.$t('confirm_delete_building', { name: building.name }))) {
        try {
          await this.$axios.delete(`/buildings/${building.id}`);
          this.buildings = this.buildings.filter(b => b.id !== building.id);
          this.totalBuildings = this.buildings.length;
        } catch (error) {
          console.error('Error deleting building:', error);
          this.$toast.error(this.$t('failed_delete_building'));
        }
      }
    },
    editAdmin(admin) {
      // Navigate to user edit page or open modal
      this.$router.push(`/admin/users/${admin.id}/edit`);
    },
    async deleteAdmin(admin) {
      if (confirm(this.$t('confirm_delete_admin', { name: admin.name }))) {
        try {
          await this.$axios.delete(`/admin/users/${admin.id}`);
          this.admins = this.admins.filter(a => a.id !== admin.id);
          this.totalAdmins = this.admins.length;
        } catch (error) {
          console.error('Error deleting admin:', error);
          this.$toast.error(this.$t('failed_delete_admin'));
        }
      }
    },

    // Enhanced Financial Reporting Methods
    async updateFinancialData() {
      if (this.isSuperAdmin) return;

      try {
        const params = {
          days: this.selectedTimeRange,
          from_date: this.financialFilters.fromDate,
          to_date: this.financialFilters.toDate,
          comparison_period: this.financialFilters.comparisonPeriod
        };

        const response = await this.$axios.get('/admin/financial-summary', { params });
        this.financialSummary = response.data;

        // Update charts
        this.updateExpenseIncomeChart(response.data.chartData);
        this.updateMonthlyComparisonChart(response.data.monthlyData);
      } catch (error) {
        console.error('Error loading financial data:', error);
      }
    },

    updateExpenseIncomeChart(data) {
      if (!this.$refs.expenseIncomeChart) return;

      const ctx = this.$refs.expenseIncomeChart.getContext('2d');

      if (this.expenseIncomeChart) {
        this.expenseIncomeChart.destroy();
      }

      this.expenseIncomeChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.labels || [],
          datasets: [
            {
              label: this.$t('expenses'),
              data: data.expenses || [],
              borderColor: 'rgb(239, 68, 68)',
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              tension: 0.4
            },
            {
              label: this.$t('income'),
              data: data.income || [],
              borderColor: 'rgb(34, 197, 94)',
              backgroundColor: 'rgba(34, 197, 94, 0.1)',
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return value;
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.dataset.label + ': ' + context.parsed.y;
                }
              }
            }
          }
        }
      });
    },

    updateMonthlyComparisonChart(data) {
      if (!this.$refs.monthlyComparisonChart) return;

      const ctx = this.$refs.monthlyComparisonChart.getContext('2d');

      if (this.monthlyComparisonChart) {
        this.monthlyComparisonChart.destroy();
      }

      this.monthlyComparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: data.labels || [],
          datasets: [
            {
              label: this.$t('current_period'),
              data: data.current || [],
              backgroundColor: 'rgba(59, 130, 246, 0.8)',
              borderColor: 'rgb(59, 130, 246)',
              borderWidth: 1
            },
            {
              label: this.$t('comparison_period'),
              data: data.comparison || [],
              backgroundColor: 'rgba(156, 163, 175, 0.8)',
              borderColor: 'rgb(156, 163, 175)',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return value;
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.dataset.label + ': ' + context.parsed.y;
                }
              }
            }
          }
        }
      });
    },

    formatCurrency(amount) {
      return amount || 0;
    }
  }
};
</script>