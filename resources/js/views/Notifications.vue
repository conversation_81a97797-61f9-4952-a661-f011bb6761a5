<template>
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">{{ $t('notifications') }}</h1>
      <p class="text-gray-600 mt-1">{{ $t('manage_your_notifications') }}</p>
    </div>

    <!-- Actions Bar -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <!-- Filters -->
        <div class="flex items-center space-x-4">
          <div class="flex space-x-2">
            <button
              @click="currentFilter = 'all'"
              :class="filterButtonClass('all')"
              class="px-4 py-2 text-sm rounded-lg font-medium"
            >
              {{ $t('all') }} ({{ totalCount }})
            </button>
            <button
              @click="currentFilter = 'unread'"
              :class="filterButtonClass('unread')"
              class="px-4 py-2 text-sm rounded-lg font-medium"
            >
              {{ $t('unread') }} ({{ unreadCount }})
            </button>
          </div>
          
          <!-- Type Filter -->
          <select
            v-model="typeFilter"
            @change="fetchNotifications"
            class="border border-gray-300 rounded-lg px-3 py-2 text-sm"
          >
            <option value="">{{ $t('all_types') }}</option>
            <option value="payment_reminder">{{ $t('payment_reminders') }}</option>
            <option value="expense_created">{{ $t('expense_notifications') }}</option>
            <option value="income_received">{{ $t('income_notifications') }}</option>
            <option value="general_announcement">{{ $t('announcements') }}</option>
            <option value="payment_received">{{ $t('payment_confirmations') }}</option>
            <option value="overdue_payment">{{ $t('overdue_notifications') }}</option>
          </select>
        </div>

        <!-- Actions -->
        <div class="flex items-center space-x-3">
          <button
            v-if="unreadCount > 0"
            @click="markAllAsRead"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
          >
            {{ $t('mark_all_read') }}
          </button>
          <button
            @click="fetchNotifications"
            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium"
          >
            {{ $t('refresh') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Loading State -->
      <div v-if="loading" class="p-8 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-500 mt-2">{{ $t('loading') }}...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="notifications.length === 0" class="p-8 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $t('no_notifications') }}</h3>
        <p class="text-gray-500">{{ $t('no_notifications_description') }}</p>
      </div>

      <!-- Notifications -->
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="p-6 hover:bg-gray-50 transition-colors"
          :class="{ 'bg-blue-50': !notification.read_at }"
        >
          <div class="flex items-start space-x-4">
            <!-- Icon -->
            <div class="flex-shrink-0">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center"
                :class="getNotificationIconClass(notification.type)"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path :d="getNotificationIconPath(notification.type)" />
                </svg>
              </div>
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-medium text-gray-900">
                  {{ notification.title }}
                </h3>
                <div class="flex items-center space-x-2">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getPriorityClass(notification.priority)"
                  >
                    {{ $t(notification.priority) }}
                  </span>
                  <span
                    v-if="!notification.read_at"
                    class="w-3 h-3 bg-blue-500 rounded-full"
                  ></span>
                </div>
              </div>

              <p class="text-gray-700 mb-3">{{ notification.message }}</p>

              <!-- Additional Details -->
              <div v-if="notification.data && Object.keys(notification.data).length > 0" 
                   class="bg-gray-50 rounded-lg p-3 mb-3">
                <h4 class="text-sm font-medium text-gray-900 mb-2">{{ $t('details') }}:</h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                  <div v-if="notification.data.amount" class="flex justify-between">
                    <span class="text-gray-600">{{ $t('amount') }}:</span>
                    <span class="font-medium">{{ formatCurrency(notification.data.amount) }}</span>
                  </div>
                  <div v-if="notification.data.due_date" class="flex justify-between">
                    <span class="text-gray-600">{{ $t('due_date') }}:</span>
                    <span class="font-medium">{{ formatDate(notification.data.due_date) }}</span>
                  </div>
                  <div v-if="notification.data.payment_date" class="flex justify-between">
                    <span class="text-gray-600">{{ $t('payment_date') }}:</span>
                    <span class="font-medium">{{ formatDate(notification.data.payment_date) }}</span>
                  </div>
                  <div v-if="notification.data.payment_method" class="flex justify-between">
                    <span class="text-gray-600">{{ $t('payment_method') }}:</span>
                    <span class="font-medium">{{ $t(notification.data.payment_method) }}</span>
                  </div>
                  <div v-if="notification.data.days_overdue" class="flex justify-between">
                    <span class="text-gray-600">{{ $t('days_overdue') }}:</span>
                    <span class="font-medium text-red-600">{{ notification.data.days_overdue }}</span>
                  </div>
                </div>
              </div>

              <!-- Footer -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                  <span>{{ formatDateTime(notification.created_at) }}</span>
                  <span v-if="notification.building" class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    {{ notification.building.name }}
                  </span>
                </div>

                <div class="flex items-center space-x-2">
                  <button
                    v-if="!notification.read_at"
                    @click="markAsRead(notification)"
                    class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {{ $t('mark_read') }}
                  </button>
                  <button
                    v-else
                    @click="markAsUnread(notification)"
                    class="text-sm text-gray-600 hover:text-gray-800 font-medium"
                  >
                    {{ $t('mark_unread') }}
                  </button>
                  <button
                    @click="deleteNotification(notification)"
                    class="text-sm text-red-600 hover:text-red-800 font-medium"
                  >
                    {{ $t('delete') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="pagination && pagination.last_page > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            {{ $t('showing') }} {{ pagination.from }} {{ $t('to') }} {{ pagination.to }} 
            {{ $t('of') }} {{ pagination.total }} {{ $t('results') }}
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(pagination.current_page - 1)"
              :disabled="pagination.current_page <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              {{ $t('previous') }}
            </button>
            <span class="px-3 py-2 text-sm">
              {{ pagination.current_page }} / {{ pagination.last_page }}
            </span>
            <button
              @click="changePage(pagination.current_page + 1)"
              :disabled="pagination.current_page >= pagination.last_page"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              {{ $t('next') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Notifications',
  mixins: [i18nMixin],
  data() {
    return {
      notifications: [],
      loading: false,
      currentFilter: 'all',
      typeFilter: '',
      unreadCount: 0,
      totalCount: 0,
      pagination: null,
      currentPage: 1,
    };
  },
  computed: {
    isAuthenticated() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      return !!token && !!user && user !== 'null';
    },
  },
  mounted() {
    if (this.isAuthenticated) {
      this.fetchNotifications();
    }
  },
  methods: {
    async fetchNotifications(page = 1) {
      if (!this.isAuthenticated) {
        return;
      }

      this.loading = true;
      try {
        const params = {
          page,
          per_page: 20,
        };

        if (this.currentFilter === 'unread') {
          params.unread_only = true;
        }

        if (this.typeFilter) {
          params.type = this.typeFilter;
        }

        const response = await this.$axios.get('/notifications', { params });
        this.notifications = response.data.notifications.data;
        this.pagination = {
          current_page: response.data.notifications.current_page,
          last_page: response.data.notifications.last_page,
          from: response.data.notifications.from,
          to: response.data.notifications.to,
          total: response.data.notifications.total,
        };
        this.unreadCount = response.data.unread_count;
        this.totalCount = response.data.notifications.total;
        this.currentPage = page;
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
        // If authentication fails, redirect to login
        if (error.response?.status === 401) {
          this.$router.push('/login');
        }
      } finally {
        this.loading = false;
      }
    },

    async markAsRead(notification) {
      if (notification.read_at || !this.isAuthenticated) return;

      try {
        await this.$axios.put(`/notifications/${notification.id}/mark-as-read`);
        notification.read_at = new Date().toISOString();
        this.unreadCount = Math.max(0, this.unreadCount - 1);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },

    async markAsUnread(notification) {
      if (!notification.read_at || !this.isAuthenticated) return;

      try {
        await this.$axios.put(`/notifications/${notification.id}/mark-as-unread`);
        notification.read_at = null;
        this.unreadCount += 1;
      } catch (error) {
        console.error('Failed to mark notification as unread:', error);
      }
    },

    async markAllAsRead() {
      if (!this.isAuthenticated) return;

      try {
        await this.$axios.put('/notifications/mark-all-as-read');
        this.notifications.forEach(n => {
          if (!n.read_at) {
            n.read_at = new Date().toISOString();
          }
        });
        this.unreadCount = 0;
      } catch (error) {
        console.error('Failed to mark all notifications as read:', error);
      }
    },

    async deleteNotification(notification) {
      if (!this.isAuthenticated || !confirm(this.$t('confirm_delete_notification'))) return;

      try {
        await this.$axios.delete(`/notifications/${notification.id}`);
        this.notifications = this.notifications.filter(n => n.id !== notification.id);
        if (!notification.read_at) {
          this.unreadCount = Math.max(0, this.unreadCount - 1);
        }
        this.totalCount = Math.max(0, this.totalCount - 1);
      } catch (error) {
        console.error('Failed to delete notification:', error);
      }
    },

    changePage(page) {
      if (page >= 1 && page <= this.pagination.last_page) {
        this.fetchNotifications(page);
      }
    },

    filterButtonClass(filter) {
      return this.currentFilter === filter
        ? 'bg-blue-100 text-blue-800 border-blue-200'
        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border-gray-200';
    },

    getNotificationIconClass(type) {
      const classes = {
        payment_reminder: 'bg-yellow-100 text-yellow-600',
        expense_created: 'bg-blue-100 text-blue-600',
        income_received: 'bg-green-100 text-green-600',
        general_announcement: 'bg-gray-100 text-gray-600',
        payment_received: 'bg-green-100 text-green-600',
        overdue_payment: 'bg-red-100 text-red-600',
      };
      return classes[type] || 'bg-gray-100 text-gray-600';
    },

    getNotificationIconPath(type) {
      const paths = {
        payment_reminder: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        expense_created: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        income_received: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        general_announcement: 'M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z',
        payment_received: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        overdue_payment: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z',
      };
      return paths[type] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    },

    getPriorityClass(priority) {
      const classes = {
        high: 'bg-red-100 text-red-800',
        medium: 'bg-yellow-100 text-yellow-800',
        low: 'bg-green-100 text-green-800',
      };
      return classes[priority] || 'bg-gray-100 text-gray-800';
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    },

    formatDateTime(dateString) {
      return new Date(dateString).toLocaleString();
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    },
  },

  watch: {
    currentFilter() {
      this.fetchNotifications();
    },
  },
};
</script>
