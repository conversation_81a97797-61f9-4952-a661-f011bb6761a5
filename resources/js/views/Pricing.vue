<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
          <defs>
            <pattern id="pricing-grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#pricing-grid)" />
        </svg>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        <div class="text-center">
          <div class="mb-6">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-4">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
              </svg>
              {{ $t('transparent_pricing') }}
            </span>
          </div>

          <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            {{ $t('simple_transparent_pricing') }}
          </h1>

          <p class="text-xl lg:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed mb-12">
            {{ $t('pricing_subtitle') }}
          </p>

          <!-- Value Proposition -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 mt-12 sm:mt-16">
            <div class="text-center py-4">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="text-white font-semibold mb-2">{{ $t('no_hidden_fees') }}</div>
              <div class="text-blue-200 text-sm">{{ $t('what_you_see_is_what_you_pay') }}</div>
            </div>

            <div class="text-center py-4">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <div class="text-white font-semibold mb-2">{{ $t('instant_setup') }}</div>
              <div class="text-blue-200 text-sm">{{ $t('get_started_in_minutes') }}</div>
            </div>

            <div class="text-center py-4">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                </svg>
              </div>
              <div class="text-white font-semibold mb-2">{{ $t('money_back_guarantee') }}</div>
              <div class="text-blue-200 text-sm">{{ $t('30_day_guarantee') }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Billing Cycle Toggle -->
    <section class="py-12 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {{ $t('choose_your_plan') }}
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            {{ $t('pricing_plans_description') }}
          </p>

          <!-- Billing Cycle Toggle -->
          <div class="flex items-center justify-center mb-8">
            <span :class="billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'">
              {{ $t('monthly') }}
            </span>
            <button
              @click="toggleBillingCycle"
              :class="[
                'mx-4 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                billingCycle === 'annual' ? 'bg-blue-600' : 'bg-gray-200'
              ]"
            >
              <span
                :class="[
                  'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                  billingCycle === 'annual' ? 'translate-x-5' : 'translate-x-0'
                ]"
              />
            </button>
            <span :class="billingCycle === 'annual' ? 'text-gray-900 font-medium' : 'text-gray-500'">
              {{ $t('annual') }}
              <span class="ml-1 text-green-600 text-sm font-medium">{{ $t('annual_discount_20') }}</span>
            </span>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-2 text-gray-600">{{ $t('loading_packages') }}</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <p class="text-red-600">{{ error }}</p>
        </div>

        <!-- Package Cards -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div
            v-for="pkg in filteredPackages"
            :key="pkg.id"
            :class="[
              'relative bg-white rounded-2xl shadow-xl border-2 p-8 hover:shadow-2xl transition-all duration-300',
              pkg.is_popular ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20' : 'border-gray-200'
            ]"
          >
            <!-- Popular Badge -->
            <div v-if="pkg.is_popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                {{ $t('most_popular') }}
              </span>
            </div>

            <div class="text-center">
              <!-- Package Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6',
                getPackageIconClass(pkg.slug)
              ]">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getPackageIconPath(pkg.slug)"/>
                </svg>
              </div>

              <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ getPackageName(pkg) }}</h3>

              <!-- Pricing -->
              <div class="mb-6">
                <div v-if="billingCycle === 'monthly'">
                  <span class="text-5xl lg:text-6xl font-bold text-blue-600">${{ pkg.price }}</span>
                  <div class="text-gray-600 mt-2">{{ $t('per_month') }}</div>
                </div>
                <div v-else-if="pkg.annual_price">
                  <span class="text-5xl lg:text-6xl font-bold text-blue-600">${{ pkg.annual_price }}</span>
                  <div class="text-gray-600 mt-2">{{ $t('per_year') }}</div>
                  <div class="text-sm text-green-600 mt-1">
                    {{ $t('save') }} ${{ (pkg.price * 12 - pkg.annual_price).toFixed(2) }} (20%)
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    ${{ (pkg.annual_price / 12).toFixed(2) }} {{ $t('per_month') }}
                  </div>
                </div>
              </div>

              <p class="text-gray-600 mb-8 leading-relaxed">{{ getPackageDescription(pkg) }}</p>

              <!-- Features List -->
              <ul class="text-left space-y-3 mb-8">
                <li v-for="feature in getPackageFeatures(pkg)" :key="feature" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t(feature) }}</span>
                </li>

                <!-- Package Limits -->
                <li v-if="pkg.max_neighbors" class="flex items-start">
                  <svg class="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t('max_neighbors_count', { count: pkg.max_neighbors }) }}</span>
                </li>
                <li v-else class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t('unlimited_neighbors') }}</span>
                </li>

                <li v-if="pkg.storage_limit_gb" class="flex items-start">
                  <svg class="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t('storage_limit_gb', { gb: pkg.storage_limit_gb }) }}</span>
                </li>
                <li v-else-if="pkg.slug !== 'addons'" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t('unlimited_storage') }}</span>
                </li>

                <!-- Trial Days -->
                <li v-if="pkg.trial_days > 0" class="flex items-start">
                  <svg class="w-5 h-5 text-purple-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-gray-700">{{ $t('free_trial_days', { days: pkg.trial_days }) }}</span>
                </li>
              </ul>

              <!-- Action Button -->
              <div class="mt-8">
                <router-link
                  to="/signup"
                  :class="[
                    'w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-xl transition-all duration-200',
                    pkg.is_popular
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                  ]"
                >
                  {{ getPackageButtonText(pkg) }}
                </router-link>
              </div>
            </div>
          </div>
        </div>


      </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h2 class="text-3xl sm:text-4xl font-bold mb-4">
            {{ $t('ready_to_get_started') }}
          </h2>
          <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            {{ $t('pricing_final_cta_description') }}
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link
              to="/signup"
              class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
            >
              {{ $t('get_started_now') }}
              <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
              </svg>
            </router-link>

            <router-link
              to="/contact"
              class="inline-flex group-hover:text-blue-700 items-center justify-center px-8 py-4 bg-transparent hover:bg-white hover:bg-opacity-10 text-white font-semibold border-2 border-white rounded-xl transition-all duration-300 text-lg min-h-[56px] w-full sm:w-auto"
            >
              {{ $t('schedule_demo') }}
            </router-link>
          </div>

          <p class="text-sm text-blue-200 mt-6">
            {{ $t('join_satisfied_customers') }}
          </p>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {{ $t('pricing_faqs') }}
          </h2>
          <p class="text-lg text-gray-600">
            {{ $t('pricing_faqs_description') }}
          </p>
        </div>

        <div class="space-y-6">
          <!-- FAQ Item 1 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('pricing_faq_1_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('pricing_faq_1_answer') }}</p>
          </div>

          <!-- FAQ Item 2 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('pricing_faq_2_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('pricing_faq_2_answer') }}</p>
          </div>

          <!-- FAQ Item 3 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('pricing_faq_3_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('pricing_faq_3_answer') }}</p>
          </div>

          <!-- FAQ Item 4 -->
          <div class="bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors duration-300">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $t('pricing_faq_4_question') }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ $t('pricing_faq_4_answer') }}</p>
          </div>
        </div>

        <div class="text-center mt-12">
          <p class="text-gray-600 mb-6">{{ $t('more_questions') }}</p>
          <router-link
            to="/contact"
            class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-colors duration-300"
          >
            {{ $t('contact_us') }}
          </router-link>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl sm:text-4xl font-bold text-white mb-6">
          {{ $t('transform_building_management') }}
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
          {{ $t('pricing_final_cta_description') }}
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/signup"
            class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-lg min-h-[56px] w-full sm:w-auto"
          >
            {{ $t('get_started_now') }}
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </router-link>

          <router-link
            to="/contact"
            class="inline-flex items-center justify-center px-8 py-4 bg-transparent hover:bg-white hover:bg-opacity-10 text-white font-semibold border-2 border-white rounded-xl transition-all duration-300 text-lg min-h-[56px] w-full sm:w-auto"
          >
            {{ $t('schedule_demo') }}
          </router-link>
        </div>

        <p class="text-sm text-blue-200 mt-6">
          {{ $t('join_satisfied_customers') }}
        </p>
      </div>
    </section>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Pricing',
  mixins: [i18nMixin],
  data() {
    return {
      packages: [],
      loading: true,
      error: null,
      billingCycle: 'monthly'
    };
  },
  computed: {
    filteredPackages() {
      // Filter out add-ons package from main pricing display
      return this.packages.filter(pkg => pkg.slug !== 'addons');
    }
  },
  mounted() {
    document.title = `${this.$t('pricing')} - ${this.$t('app_name')}`;
    this.loadPackages();
  },
  methods: {
    async loadPackages() {
      this.loading = true;
      this.error = null;

      try {
        // Use the public packages endpoint for the pricing page
        const response = await fetch('/api/public/packages', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load packages');
        }

        const data = await response.json();
        this.packages = data.packages || data;
      } catch (error) {
        console.error('Error loading packages:', error);
        this.error = this.$t('error_loading_packages');
        // Fallback to hardcoded packages for demo
        this.loadFallbackPackages();
      } finally {
        this.loading = false;
      }
    },

    loadFallbackPackages() {
      // Fallback packages based on the new structure
      this.packages = [
        {
          id: 1,
          name: 'الباقة الأساسية',
          name_en: 'Basic Package',
          slug: 'basic',
          description: 'أداة إدارة الأساسية للمباني الصغيرة',
          description_en: 'Core admin tool for small buildings',
          price: 9.99,
          annual_price: 99.90,
          max_neighbors: 50,
          storage_limit_gb: 5,
          trial_days: 14,
          is_popular: false,
          features: ['core_saas_admin_tool', 'expense_tracking', 'income_tracking', 'user_management', 'basic_reports', 'email_notifications', 'file_attachments']
        },
        {
          id: 2,
          name: 'الباقة القياسية',
          name_en: 'Standard Package',
          slug: 'standard',
          description: 'باقة قياسية مع تصدير التقارير ومتعدد المشرفين',
          description_en: 'Standard package with reports export and multi-admin support',
          price: 14.99,
          annual_price: 149.90,
          max_neighbors: null,
          storage_limit_gb: 25,
          trial_days: 14,
          is_popular: true,
          features: ['core_saas_admin_tool', 'reports_export', 'multi_admin_support', 'unlimited_neighbors', 'advanced_reports', 'email_notifications', 'file_attachments', 'expense_tracking', 'income_tracking', 'user_management', 'payment_reminders']
        },
        {
          id: 3,
          name: 'الباقة الاحترافية',
          name_en: 'Professional Package',
          slug: 'pro',
          description: 'باقة احترافية مع الدعم الهاتفي والأرشفة وحزمة SMS صغيرة',
          description_en: 'Pro package with phone support, archive, and small SMS package',
          price: 24.99,
          annual_price: 249.90,
          max_neighbors: null,
          storage_limit_gb: null,
          trial_days: 30,
          is_popular: false,
          features: ['core_saas_admin_tool', 'reports_export', 'multi_admin_support', 'phone_support', 'archive_feature', 'small_sms_package', 'unlimited_neighbors', 'unlimited_storage', 'advanced_reports', 'email_notifications', 'sms_notifications', 'file_attachments', 'large_file_support', 'expense_tracking', 'income_tracking', 'user_management', 'payment_reminders', 'priority_support', 'data_archiving']
        }
      ];
    },

    toggleBillingCycle() {
      this.billingCycle = this.billingCycle === 'monthly' ? 'annual' : 'monthly';
    },

    getPackageName(pkg) {
      if (this.$locale() === 'en' && pkg.name_en) {
        return pkg.name_en;
      }
      return pkg.name;
    },

    getPackageDescription(pkg) {
      if (this.$locale() === 'en' && pkg.description_en) {
        return pkg.description_en;
      }
      return pkg.description;
    },

    getPackageFeatures(pkg) {
      if (!pkg.features) return [];
      // Return first 5 features for display
      return pkg.features.slice(0, 5);
    },

    getPackageIconClass(slug) {
      const classes = {
        'basic': 'bg-green-100 text-green-600',
        'standard': 'bg-blue-100 text-blue-600',
        'pro': 'bg-purple-100 text-purple-600',
        'addons': 'bg-orange-100 text-orange-600'
      };
      return classes[slug] || 'bg-gray-100 text-gray-600';
    },

    getPackageIconPath(slug) {
      const paths = {
        'basic': 'M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z',
        'standard': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
        'pro': 'M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z',
        'addons': 'M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4'
      };
      return paths[slug] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    },

    getPackageButtonText(pkg) {
      if (pkg.slug === 'addons') {
        return this.$t('contact_sales');
      }
      return this.$t('get_started');
    }
  }
};
</script>
