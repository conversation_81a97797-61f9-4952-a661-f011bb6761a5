<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير مصروفات المبنى</title>
    <style>
        body {
            font-family: "DejaVu Sans", sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
        }

        /* Force Arabic text rendering */
        * {
            font-family: "DejaVu Sans", sans-serif !important;
        }

        .arabic-text, .arabic, h1, h2, h3, p, td, th, div, span {
            direction: rtl !important;
            text-align: right !important;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .info-section {
            margin-bottom: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            color: #333;
        }
        
        .info-value {
            color: #666;
        }
        
        .summary-section {
            margin-bottom: 25px;
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-right: 4px solid #2196f3;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1976d2;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px 0;
        }
        
        .summary-label {
            font-weight: bold;
        }
        
        .summary-value {
            font-weight: bold;
            color: #1976d2;
        }
        
        .table-section {
            margin-top: 25px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .amount {
            font-weight: bold;
            color: #d32f2f;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }

        /* Additional Arabic text support */
        h1, h2, h3, p, span, div, td, th {
            font-family: 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', Arial, sans-serif;
        }

        /* Specific Arabic character support */
        * {
            font-family: 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', Arial, sans-serif !important;
        }

        /* Ensure Arabic text is properly rendered */
        [lang="ar"], .arabic {
            font-family: 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 style="font-family: 'DejaVu Sans', sans-serif; direction: rtl; text-align: right;">تقرير مصروفات المبنى</h1>
        <h2 style="font-family: 'DejaVu Sans', sans-serif; direction: rtl; text-align: right;">{{ $data['building']->name }}</h2>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span class="info-label" style="font-family: 'DejaVu Sans', sans-serif;">اسم المبنى:</span>
            <span class="info-value">{{ $data['building']->name }}</span>
        </div>
        <div class="info-row">
            <span class="info-label" style="font-family: 'DejaVu Sans', sans-serif;">عنوان المبنى:</span>
            <span class="info-value">{{ $data['building']->address ?? 'غير محدد' }}</span>
        </div>
        <div class="info-row">
            <span class="info-label" style="font-family: 'DejaVu Sans', sans-serif;">فترة التقرير:</span>
            <span class="info-value">من {{ $data['date_from']->format('Y-m-d') }} إلى {{ $data['date_to']->format('Y-m-d') }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">تاريخ إنشاء التقرير:</span>
            <span class="info-value">{{ now()->format('Y-m-d H:i') }}</span>
        </div>
    </div>

    <div class="summary-section">
        <div class="summary-title">ملخص مصروفات المبنى</div>
        <div class="summary-item">
            <span class="summary-label">إجمالي المصروفات:</span>
            <span class="summary-value amount">{{ number_format($data['total_amount'], 2) }} دينار</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">عدد المصروفات:</span>
            <span class="summary-value">{{ $data['record_count'] }}</span>
        </div>
    </div>

    <div class="table-section">
        <div class="section-title">تفاصيل مصروفات المبنى</div>
        
        @if($data['building_expenses']->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>نوع المصروف</th>
                        <th>المبلغ</th>
                        <th>الشهر/السنة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>العملة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['building_expenses'] as $expense)
                        <tr>
                            <td>{{ $expense->created_at->format('Y-m-d') }}</td>
                            <td>{{ $expense->buildingExpenseType->name ?? 'غير محدد' }}</td>
                            <td class="amount">{{ number_format($expense->amount, 2) }}</td>
                            <td>{{ $expense->month }}/{{ $expense->year }}</td>
                            <td>{{ $expense->due_date ? $expense->due_date->format('Y-m-d') : 'غير محدد' }}</td>
                            <td>{{ $expense->currency ?? 'USD' }}</td>
                            <td>{{ $expense->notes ?? '-' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <p style="font-family: 'DejaVu Sans', sans-serif; direction: rtl; text-align: right;">
                لا توجد مصروفات مبنى في الفترة المحددة
            </p>
        @endif
    </div>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المباني - {{ now()->format('Y-m-d H:i:s') }}</p>
    </div>
</body>
</html>
