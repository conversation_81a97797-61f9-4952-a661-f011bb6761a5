<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Summary Report</title>
    <style>
        body {
            font-family: "DejaVu Sans", sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.6;
            unicode-bidi: embed;
        }
        
        /* Ensure proper Arabic text rendering */
        * {
            unicode-bidi: inherit;
        }
        
        /* Force RTL for Arabic text */
        .arabic-text {
            direction: rtl;
            text-align: right;
            unicode-bidi: bidi-override;
            font-family: 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', Arial, sans-serif;
            font-weight: normal;
        }
        
        /* Ensure numbers are properly aligned */
        .amount {
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
            unicode-bidi: bidi-override;
        }
        
        /* Additional Arabic text support */
        h1, h2, h3, p, span, div, td, th {
            font-family: "DejaVu Sans", sans-serif;
        }

        /* Specific Arabic character support */
        * {
            font-family: "DejaVu Sans", sans-serif !important;
        }

        /* Ensure Arabic text is properly rendered */
        [lang="ar"], .arabic, .arabic-text {
            font-family: "DejaVu Sans", sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        /* Fix English text direction */
        .english-text {
            direction: ltr;
            text-align: left;
            unicode-bidi: bidi-override;
        }
        
        /* Ensure mixed content displays properly */
        .mixed-content {
            unicode-bidi: embed;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            color: #2c5aa0;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            color: #666;
        }
        .building-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 5px solid #2c5aa0;
        }
        .building-info h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
            color: #2c5aa0;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .summary-card {
            display: table-cell;
            width: 25%;
            padding: 15px;
            margin: 0 5px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        .summary-card.expenses {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .summary-card.building-expenses {
            border-color: #fd7e14;
            background-color: #fff3cd;
        }
        .summary-card.incomes {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .summary-card.balance {
            border-color: #2c5aa0;
            background-color: #d1ecf1;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: bold;
        }
        .summary-card.expenses h3 {
            color: #721c24;
        }
        .summary-card.building-expenses h3 {
            color: #b45309;
        }
        .summary-card.incomes h3 {
            color: #155724;
        }
        .summary-card.balance h3 {
            color: #0c5460;
        }
        .summary-card .amount {
            font-size: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        .summary-card.expenses .amount {
            color: #dc3545;
        }
        .summary-card.building-expenses .amount {
            color: #fd7e14;
        }
        .summary-card.incomes .amount {
            color: #28a745;
        }
        .summary-card.balance .amount {
            color: #2c5aa0;
        }
        .section {
            margin-bottom: 35px;
        }
        .section h3 {
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            padding: 12px 15px;
            margin: 0 0 20px 0;
            font-size: 16px;
            border-radius: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 10px 8px;
            text-align: right;
        }
        .data-table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: bold;
            color: #495057;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        .positive {
            color: #28a745;
        }
        .negative {
            color: #dc3545;
        }
        .chart-placeholder {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin: 20px 0;
        }
        .footer {
            position: fixed;
            bottom: 15px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            .summary-cards {
                display: block;
            }
            .summary-card {
                display: block;
                width: 100%;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="arabic-text">التقرير المالي الشهري</h1>
        <div class="subtitle english-text">Monthly Financial Summary Report</div>
        <div class="subtitle">تم إنشاؤه في {{ now()->format('Y-m-d H:i:s') }}</div>
    </div>

    <div class="building-info">
        <h2 class="arabic-text">{{ $building->name }}</h2>
        @if($building->address)
            <p><strong class="arabic-text">العنوان / <span class="english-text">Address:</span></strong> <span class="arabic-text">{{ $building->address }}</span></p>
        @endif
        @if($building->city)
            <p><strong class="arabic-text">المدينة / <span class="english-text">City:</span></strong> <span class="arabic-text">{{ $building->city }}</span></p>
        @endif
        <p><strong class="arabic-text">فترة التقرير / <span class="english-text">Period:</span></strong> من {{ $data['date_from']->format('Y-m-d') }} إلى {{ $data['date_to']->format('Y-m-d') }}</p>
        <p><strong class="arabic-text">عدد الأيام / <span class="english-text">Days:</span></strong> {{ $data['date_from']->diffInDays($data['date_to']) + 1 }} يوم</p>
    </div>

    <div class="summary-cards">
        <div class="summary-card expenses">
            <h3 class="arabic-text">مصروفات الجيران<br><span class="english-text">Neighbor Expenses</span></h3>
            <div class="amount">{{ number_format($data['total_expenses'], 2) }}</div>
            <p class="arabic-text">{{ $data['expenses']->count() }} معاملة</p>
        </div>

        <div class="summary-card building-expenses">
            <h3 class="arabic-text">مصروفات المبنى<br><span class="english-text">Building Expenses</span></h3>
            <div class="amount">{{ number_format($data['total_building_expenses'] ?? 0, 2) }}</div>
            <p class="arabic-text">{{ isset($data['building_expenses']) ? $data['building_expenses']->count() : 0 }} معاملة</p>
        </div>

        <div class="summary-card incomes">
            <h3 class="arabic-text">إجمالي الإيرادات<br><span class="english-text">Total Incomes</span></h3>
            <div class="amount">{{ number_format($data['total_incomes'], 2) }}</div>
            <p class="arabic-text">{{ $data['incomes']->count() }} معاملة</p>
        </div>
        
        <div class="summary-card balance">
            <h3 class="arabic-text">الرصيد الصافي<br><span class="english-text">Net Balance</span></h3>
            <div class="amount {{ $data['net_balance'] >= 0 ? 'positive' : 'negative' }}">
                {{ number_format($data['net_balance'], 2) }}
            </div>
            <p class="arabic-text">
                @if($data['net_balance'] > 0)
                    فائض / <span class="english-text">Surplus</span>
                @elseif($data['net_balance'] < 0)
                    عجز / <span class="english-text">Deficit</span>
                @else
                    متوازن / <span class="english-text">Balanced</span>
                @endif
            </p>
        </div>
    </div>

    @if($data['expenses']->count() > 0)
    <div class="section">
        <h3 class="arabic-text">تفاصيل المصروفات / <span class="english-text">Expense Details</span></h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th class="arabic-text">التاريخ<br><span class="english-text">Date</span></th>
                    <th class="arabic-text">نوع المصروف<br><span class="english-text">Expense Type</span></th>
                    <th class="arabic-text">المبلغ <br><span class="english-text">Amount</span></th>
                    <th class="arabic-text">المستخدم<br><span class="english-text">User</span></th>
                    <th class="arabic-text">الوصف<br><span class="english-text">Description</span></th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['expenses'] as $expense)
                <tr>
                    <td>{{ $expense->created_at->format('Y-m-d') }}</td>
                    <td class="arabic-text">{{ $expense->expenseType->name ?? 'غير محدد' }}</td>
                    <td class="amount negative">{{ number_format($expense->amount, 2) }}</td>
                    <td class="arabic-text">{{ $expense->user->name ?? 'غير محدد' }}</td>
                    <td class="arabic-text">{{ $expense->description ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr style="background-color: #f1f3f4; font-weight: bold;">
                    <td colspan="2" class="arabic-text">الإجمالي / <span class="english-text">Total</span></td>
                    <td class="amount negative">{{ number_format($data['total_expenses'], 2) }}</td>
                    <td colspan="2" class="arabic-text">{{ $data['expenses']->count() }} معاملة</td>
                </tr>
            </tfoot>
        </table>
    </div>
    @else
    <div class="section">
        <h3 class="arabic-text">تفاصيل المصروفات / <span class="english-text">Expense Details</span></h3>
        <div class="no-data"><span class="arabic-text">لا توجد مصروفات</span> / <span class="english-text">No expenses found for this period</span></div>
    </div>
    @endif

    @if($data['incomes']->count() > 0)
    <div class="section page-break">
        <h3 class="arabic-text">تفاصيل الإيرادات / <span class="english-text">Income Details</span></h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th class="arabic-text">التاريخ<br><span class="english-text">Date</span></th>
                    <th class="arabic-text">نوع الإيراد<br><span class="english-text">Income Type</span></th>
                    <th class="arabic-text">المبلغ <br><span class="english-text">Amount</span></th>
                    <th class="arabic-text">المستخدم<br><span class="english-text">User</span></th>
                    <th class="arabic-text">الوصف<br><span class="english-text">Description</span></th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['incomes'] as $income)
                <tr>
                    <td>{{ $income->created_at->format('Y-m-d') }}</td>
                    <td class="arabic-text">{{ $income->incomeType->name ?? 'غير محدد' }}</td>
                    <td class="amount positive">{{ number_format($income->amount, 2) }}</td>
                    <td class="arabic-text">{{ $income->user->name ?? 'غير محدد' }}</td>
                    <td class="arabic-text">{{ $income->description ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr style="background-color: #f1f3f4; font-weight: bold;">
                    <td colspan="2" class="arabic-text">الإجمالي / <span class="english-text">Total</span></td>
                    <td class="amount positive">{{ number_format($data['total_incomes'], 2) }}</td>
                    <td colspan="2" class="arabic-text">{{ $data['incomes']->count() }} معاملة</td>
                </tr>
            </tfoot>
        </table>
    </div>
    @else
    <div class="section">
        <h3 class="arabic-text">تفاصيل الإيرادات / <span class="english-text">Income Details</span></h3>
        <div class="no-data"><span class="arabic-text">لا توجد إيرادات </span> / <span class="english-text">No incomes found for this period</span></div>
    </div>
    @endif

    @if(isset($data['building_expenses']) && $data['building_expenses']->count() > 0)
    <div class="section">
        <h3 class="arabic-text">تفاصيل مصروفات المبنى / <span class="english-text">Building Expense Details</span></h3>
        <table>
            <thead>
                <tr>
                    <th class="arabic-text">التاريخ / <span class="english-text">Date</span></th>
                    <th class="arabic-text">النوع / <span class="english-text">Type</span></th>
                    <th class="arabic-text">المبلغ / <span class="english-text">Amount</span></th>
                    <th class="arabic-text">الشهر/السنة / <span class="english-text">Month/Year</span></th>
                    <th class="arabic-text">ملاحظات / <span class="english-text">Notes</span></th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['building_expenses'] as $expense)
                <tr>
                    <td class="english-text">{{ $expense->created_at->format('Y-m-d') }}</td>
                    <td class="arabic-text">{{ $expense->buildingExpenseType->name ?? 'غير محدد' }}</td>
                    <td class="amount negative">{{ number_format($expense->amount, 2) }}</td>
                    <td class="english-text">{{ $expense->month }}/{{ $expense->year }}</td>
                    <td class="arabic-text">{{ $expense->notes ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr style="background-color: #f1f3f4; font-weight: bold;">
                    <td colspan="2" class="arabic-text">الإجمالي / <span class="english-text">Total</span></td>
                    <td class="amount negative">{{ number_format($data['total_building_expenses'], 2) }}</td>
                    <td colspan="2" class="arabic-text">{{ $data['building_expenses']->count() }} معاملة</td>
                </tr>
            </tfoot>
        </table>
    </div>
    @else
    <div class="section">
        <h3 class="arabic-text">تفاصيل مصروفات المبنى / <span class="english-text">Building Expense Details</span></h3>
        <div class="no-data"><span class="arabic-text">لا توجد مصروفات </span> / <span class="english-text">No building expenses found for this period</span></div>
    </div>
    @endif

    <div class="section">
        <h3 class="arabic-text">ملخص التحليل المالي / <span class="english-text">Financial Analysis Summary</span></h3>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 5px solid #2c5aa0;">
            <ul style="margin: 0; padding-right: 20px;">
                <li class="arabic-text"><strong>متوسط المصروفات اليومية:</strong> {{ number_format($data['total_expenses'] / max(1, $data['date_from']->diffInDays($data['date_to']) + 1), 2) }}</li>
                <li class="arabic-text"><strong>متوسط الإيرادات اليومية:</strong> {{ number_format($data['total_incomes'] / max(1, $data['date_from']->diffInDays($data['date_to']) + 1), 2) }}</li>
                <li class="arabic-text"><strong>نسبة الإيرادات إلى المصروفات:</strong> 
                    @if($data['total_expenses'] > 0)
                        {{ number_format(($data['total_incomes'] / $data['total_expenses']) * 100, 1) }}%
                    @else
                        غير محدد
                    @endif
                </li>
                <li class="arabic-text"><strong>حالة الميزانية:</strong> 
                    @if($data['net_balance'] > 0)
                        <span class="positive arabic-text">فائض بقيمة {{ number_format($data['net_balance'], 2) }}</span>
                    @elseif($data['net_balance'] < 0)
                        <span class="negative arabic-text">عجز بقيمة {{ number_format(abs($data['net_balance']), 2) }}</span>
                    @else
                        متوازنة
                    @endif
                </li>
            </ul>
        </div>
    </div>

    <div class="footer">
        <p class="arabic-text">تم إنشاء هذا التقرير بواسطة نظام إدارة عمارتنا</p>
        <p class="english-text">This report was generated by Amaretna Management System</p>
        <p class="arabic-text">{{ now()->format('Y-m-d H:i:s') }} - جميع المبالغ بالعملة المحلية</p>
    </div>
</body>
</html>
