<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ ucfirst(str_replace('_', ' ', $type)) }} Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.6;
            unicode-bidi: embed;
        }
        
        /* Ensure proper Arabic text rendering */
        * {
            unicode-bidi: inherit;
        }
        
        /* Force RTL for Arabic text */
        .arabic-text {
            direction: rtl;
            text-align: right;
            unicode-bidi: bidi-override;
            font-family: Arial, sans-serif;
        }
        
        /* Ensure numbers are properly aligned */
        .amount {
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
            unicode-bidi: bidi-override;
        }
        
        /* Additional Arabic text support */
        h1, h2, h3, p, span, div {
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 14px;
            color: #666;
        }
        .building-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .building-info h2 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #333;
        }
        .summary-section {
            margin-bottom: 30px;
        }
        .summary-section h3 {
            background-color: #e0e0e0;
            padding: 10px;
            margin: 0 0 15px 0;
            font-size: 14px;
            color: #333;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-row {
            display: table-row;
        }
        .summary-cell {
            display: table-cell;
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }
        .summary-cell.label {
            font-weight: bold;
            width: 40%;
        }
        .summary-cell.value {
            width: 60%;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left;
        }
        .amount {
            font-weight: bold;
            color: #2c5aa0;
        }
        .positive {
            color: #28a745;
        }
        .negative {
            color: #dc3545;
        }
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ ucfirst(str_replace('_', ' ', $type)) }} Report</h1>
        <div class="subtitle">تقرير {{ ucfirst(str_replace('_', ' ', $type)) }}</div>
        <div class="subtitle">Generated on {{ now()->format('Y-m-d H:i:s') }}</div>
    </div>

    <div class="building-info">
        <h2 >{{ $building->name }}</h2>
        @if($building->address)
            <p><strong >العنوان / Address:</strong> <span >{{ $building->address }}</span></p>
        @endif
        @if($building->city)
            <p><strong >المدينة / City:</strong> <span >{{ $building->city }}</span></p>
        @endif
        @if(isset($data['date_from']) && isset($data['date_to']))
            <p><strong >الفترة / Period:</strong> {{ $data['date_from']->format('Y-m-d') }} إلى {{ $data['date_to']->format('Y-m-d') }}</p>
        @endif
    </div>

    @if(isset($data['total_expenses']) || isset($data['total_incomes']) || isset($data['net_balance']))
    <div class="summary-section">
        <h3>الملخص المالي / Financial Summary</h3>
        <div class="summary-grid">
            @if(isset($data['total_expenses']))
            <div class="summary-row">
                <div class="summary-cell label">إجمالي المصروفات / Total Expenses:</div>
                <div class="summary-cell value amount">{{ number_format($data['total_expenses'], 2) }}</div>
            </div>
            @endif
            
            @if(isset($data['total_incomes']))
            <div class="summary-row">
                <div class="summary-cell label">إجمالي الإيرادات / Total Incomes:</div>
                <div class="summary-cell value amount positive">{{ number_format($data['total_incomes'], 2) }}</div>
            </div>
            @endif
            
            @if(isset($data['net_balance']))
            <div class="summary-row">
                <div class="summary-cell label arabic-text">الرصيد الصافي / Net Balance:</div>
                <div class="summary-cell value amount {{ $data['net_balance'] >= 0 ? 'positive' : 'negative' }}">
                    {{ number_format($data['net_balance'], 2) }}
                </div>
            </div>
            @endif
            
            @if(isset($data['record_count']))
            <div class="summary-row">
                <div class="summary-cell label">عدد السجلات / Record Count:</div>
                <div class="summary-cell value">{{ $data['record_count'] }}</div>
            </div>
            @endif
        </div>
    </div>
    @endif

    @if(isset($data['expenses']) && $data['expenses']->count() > 0)
    <div class="summary-section">
        <h3>المصروفات / Expenses</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>التاريخ / Date</th>
                    <th>النوع / Type</th>
                    <th>المبلغ / Amount</th>
                    <th>المستخدم / User</th>
                    @if(isset($data['expenses']->first()->due_date))
                    <th>تاريخ الاستحقاق / Due Date</th>
                    @endif
                </tr>
            </thead>
            <tbody>
                @foreach($data['expenses'] as $expense)
                <tr>
                    <td>{{ $expense->created_at->format('Y-m-d') }}</td>
                    <td>{{ $expense->expenseType->name ?? 'N/A' }}</td>
                    <td class="amount">{{ number_format($expense->amount, 2) }}</td>
                    <td>{{ $expense->user->name ?? 'N/A' }}</td>
                    @if(isset($expense->due_date))
                    <td>{{ $expense->due_date ? $expense->due_date->format('Y-m-d') : 'N/A' }}</td>
                    @endif
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($data['incomes']) && $data['incomes']->count() > 0)
    <div class="summary-section">
        <h3>الإيرادات / Incomes</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>التاريخ / Date</th>
                    <th>النوع / Type</th>
                    <th>المبلغ / Amount</th>
                    <th>المستخدم / User</th>
                    <th>الوصف / Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['incomes'] as $income)
                <tr>
                    <td>{{ $income->created_at->format('Y-m-d') }}</td>
                    <td>{{ $income->incomeType->name ?? 'N/A' }}</td>
                    <td class="amount positive">{{ number_format($income->amount, 2) }}</td>
                    <td>{{ $income->user->name ?? 'N/A' }}</td>
                    <td>{{ $income->description ?? '' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($data['payments']) && $data['payments']->count() > 0)
    <div class="summary-section">
        <h3>المدفوعات / Payments</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>التاريخ / Date</th>
                    <th>الجار / Neighbor</th>
                    <th>المبلغ / Amount</th>
                    <th>الحالة / Status</th>
                    <th>طريقة الدفع / Method</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['payments'] as $payment)
                <tr>
                    <td>{{ $payment->created_at->format('Y-m-d') }}</td>
                    <td>{{ $payment->user->name ?? 'N/A' }}</td>
                    <td class="amount">{{ number_format($payment->amount, 2) }}</td>
                    <td>{{ ucfirst($payment->status) }}</td>
                    <td>{{ ucfirst($payment->payment_method ?? 'N/A') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p class="arabic-text">تم إنشاء هذا التقرير بواسطة نظام إدارة عمارتنا</p>
        <p class="english-text">This report was generated by Amaretna Management System</p>
        <p class="arabic-text">{{ now()->format('Y-m-d H:i:s') }} - جميع المبالغ بالعملة المحلية</p>
    </div>
</body>
</html>
