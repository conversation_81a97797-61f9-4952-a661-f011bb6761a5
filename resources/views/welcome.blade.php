<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
        <title>عمارتنا - نظام إدارة المباني</title>

        <!-- SEO Meta Tags -->
        <meta name="description" content="نظام شامل لإدارة المباني للمصروفات والإيرادات وإدارة الجيران. Complete building management system for expenses, incomes, and neighbor management">
        <meta name="keywords" content="عمارتنا, إدارة المباني, المصروفات, الإيرادات, الجيران, building, expenses, income, neighbors">
        <meta name="author" content="عمارتنا">
        <meta name="robots" content="index, follow">
        
        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="عمارتنا - نظام إدارة المباني">
        <meta property="og:description" content="نظام شامل لإدارة المباني للمصروفات والإيرادات وإدارة الجيران">
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ url('/') }}">
        <meta property="og:image" content="{{ url('/images/android-chrome-512x512.png') }}">
        <meta property="og:site_name" content="عمارتنا">
        <meta property="og:locale" content="ar_SA">
        
        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="عمارتنا - نظام إدارة المباني">
        <meta name="twitter:description" content="نظام شامل لإدارة المباني للمصروفات والإيرادات وإدارة الجيران">
        <meta name="twitter:image" content="{{ url('/images/android-chrome-512x512.png') }}">

        <!-- PWA Meta Tags -->
        <meta name="application-name" content="عمارتنا">
        <meta name="theme-color" content="#2563eb">
        <meta name="background-color" content="#ffffff">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="عمارتنا">
        <meta name="msapplication-TileColor" content="#2563eb">
        <meta name="msapplication-TileImage" content="/images/android-chrome-192x192.png">
        <meta name="msapplication-config" content="/browserconfig.xml">
        <meta name="msapplication-tap-highlight" content="no">

        <!-- PWA Manifest -->
        <link rel="manifest" href="/images/site.webmanifest">

        <!-- Favicon and Icons -->
        <link rel="icon" type="image/x-icon" href="/favicon.ico">
        <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
        <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
        
        <!-- Additional PWA Icons -->
        <link rel="icon" type="image/png" sizes="192x192" href="/images/android-chrome-192x192.png">
        <link rel="icon" type="image/png" sizes="512x512" href="/images/android-chrome-512x512.png">

        <!-- Preload critical resources -->
        <link rel="preload" href="/build/assets/app.css" as="style">
        <link rel="preload" href="/build/assets/app.js" as="script">

        <!-- DNS prefetch for external resources -->
        <link rel="dns-prefetch" href="//fonts.googleapis.com">

        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body>
        <div id="app"></div>

        <!-- PWA Install Prompt -->
        <div id="pwa-install-prompt" class="hidden fixed bottom-4 left-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50" style="display:none">
            <div class="flex items-center justify-between">
                <div>
                    <p class="font-semibold">Install App</p>
                    <p class="text-sm opacity-90">Install Amaretna for a better experience</p>
                </div>
                <div class="flex space-x-2">
                    <button id="pwa-install-btn" class="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium">
                        Install
                    </button>
                    <button id="pwa-dismiss-btn" class="text-white opacity-75 hover:opacity-100">
                        ✕
                    </button>
                </div>
            </div>
        </div>

        <script>
            // PWA Install Prompt
            let deferredPrompt;
            const installPrompt = document.getElementById('pwa-install-prompt');
            const installBtn = document.getElementById('pwa-install-btn');
            const dismissBtn = document.getElementById('pwa-dismiss-btn');

            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                installPrompt.classList.remove('hidden');
            });

            installBtn.addEventListener('click', async () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    const { outcome } = await deferredPrompt.userChoice;
                    console.log(`User response to the install prompt: ${outcome}`);
                    deferredPrompt = null;
                    installPrompt.classList.add('hidden');
                }
            });

            dismissBtn.addEventListener('click', () => {
                installPrompt.classList.add('hidden');
                localStorage.setItem('pwa-install-dismissed', 'true');
            });

            // Hide prompt if previously dismissed
            if (localStorage.getItem('pwa-install-dismissed') === 'true') {
                installPrompt.classList.add('hidden');
            }

            // Handle app installed
            window.addEventListener('appinstalled', () => {
                console.log('PWA was installed');
                installPrompt.classList.add('hidden');
            });
        </script>
    </body>
</html>
