<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        @if($changeType === 'upgrade')
            تأكيد ترقية الباقة - Package Upgrade Confirmation
        @elseif($changeType === 'downgrade')
            تأكيد تخفيض الباقة - Package Downgrade Confirmation
        @else
            تأكيد تغيير الباقة - Package Change Confirmation
        @endif
    </title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 650px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            @if($changeType === 'upgrade')
                background: linear-gradient(135deg, #10b981, #059669);
            @elseif($changeType === 'downgrade')
                background: linear-gradient(135deg, #f59e0b, #d97706);
            @else
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            @endif
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .building-info {
            background-color: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .package-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            margin: 30px 0;
            align-items: center;
        }
        .package-card {
            background-color: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .package-card.old {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        .package-card.new {
            @if($changeType === 'upgrade')
                border-color: #10b981;
                background-color: #f0fdf4;
            @elseif($changeType === 'downgrade')
                border-color: #f59e0b;
                background-color: #fffbeb;
            @else
                border-color: #3b82f6;
                background-color: #eff6ff;
            @endif
        }
        .package-card h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .package-card.old h3 {
            color: #991b1b;
        }
        .package-card.new h3 {
            @if($changeType === 'upgrade')
                color: #065f46;
            @elseif($changeType === 'downgrade')
                color: #92400e;
            @else
                color: #1e40af;
            @endif
        }
        .package-price {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .package-card.old .package-price {
            color: #ef4444;
        }
        .package-card.new .package-price {
            @if($changeType === 'upgrade')
                color: #10b981;
            @elseif($changeType === 'downgrade')
                color: #f59e0b;
            @else
                color: #3b82f6;
            @endif
        }
        .arrow {
            font-size: 36px;
            @if($changeType === 'upgrade')
                color: #10b981;
            @elseif($changeType === 'downgrade')
                color: #f59e0b;
            @else
                color: #3b82f6;
            @endif
        }
        .features-comparison {
            margin: 30px 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .features-list {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
        }
        .features-list h4 {
            margin: 0 0 15px 0;
            color: #374151;
        }
        .features-list ul {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        .features-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
        }
        .features-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-left: 10px;
        }
        .subscription-details {
            background-color: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #374151;
        }
        .detail-value {
            color: #1f2937;
        }
        .next-steps {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
        }
        .next-steps h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .package-comparison {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            .arrow {
                transform: rotate(90deg);
            }
            .features-grid {
                grid-template-columns: 1fr;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon">
                @if($changeType === 'upgrade')
                    ⬆️
                @elseif($changeType === 'downgrade')
                    ⬇️
                @else
                    🔄
                @endif
            </div>
            <h1>
                @if($changeType === 'upgrade')
                    تم ترقية الباقة بنجاح
                @elseif($changeType === 'downgrade')
                    تم تخفيض الباقة بنجاح
                @else
                    تم تغيير الباقة بنجاح
                @endif
            </h1>
            <div class="subtitle">
                @if($changeType === 'upgrade')
                    Package Upgraded Successfully
                @elseif($changeType === 'downgrade')
                    Package Downgraded Successfully
                @else
                    Package Changed Successfully
                @endif
            </div>
        </div>

        <div class="content">
            <p>عزيزي/عزيزتي {{ $admin->name }}،</p>
            <p>Dear {{ $admin->name }},</p>
            
            <p>
                @if($changeType === 'upgrade')
                    نشكرك على ترقية باقة الاشتراك الخاصة بك. تم تفعيل الميزات الجديدة بنجاح.
                @elseif($changeType === 'downgrade')
                    تم تخفيض باقة الاشتراك الخاصة بك كما طلبت. تم تطبيق التغييرات بنجاح.
                @else
                    تم تغيير باقة الاشتراك الخاصة بك بنجاح. تم تطبيق التغييرات الجديدة.
                @endif
            </p>
            <p>
                @if($changeType === 'upgrade')
                    Thank you for upgrading your subscription package. The new features have been activated successfully.
                @elseif($changeType === 'downgrade')
                    Your subscription package has been downgraded as requested. The changes have been applied successfully.
                @else
                    Your subscription package has been changed successfully. The new changes have been applied.
                @endif
            </p>

            <div class="building-info">
                <h2 style="margin: 0 0 10px 0;">{{ $building->name }}</h2>
                @if($building->address)
                    <p style="margin: 0; color: #6b7280;">{{ $building->address }}</p>
                @endif
            </div>

            <div class="package-comparison">
                <div class="package-card old">
                    <h3>الباقة السابقة<br>Previous Package</h3>
                    <div class="package-price">{{ number_format($oldPackage->price, 2) }} ريال</div>
                    <p style="margin: 5px 0 0 0; font-size: 14px;">{{ $oldPackage->name }}</p>
                </div>
                
                <div class="arrow">→</div>
                
                <div class="package-card new">
                    <h3>الباقة الجديدة<br>New Package</h3>
                    <div class="package-price">{{ number_format($newPackage->price, 2) }} ريال</div>
                    <p style="margin: 5px 0 0 0; font-size: 14px;">{{ $newPackage->name }}</p>
                </div>
            </div>

            <div class="features-comparison">
                <h3 style="text-align: center; color: #1f2937;">مقارنة الميزات / Features Comparison</h3>
                <div class="features-grid">
                    <div class="features-list">
                        <h4>الميزات الجديدة / New Features</h4>
                        <ul>
                            @if($newPackage->max_neighbors > $oldPackage->max_neighbors)
                                <li>
                                    @if($newPackage->hasUnlimitedNeighbors())
                                        عدد لا محدود من الجيران / Unlimited neighbors
                                    @else
                                        {{ $newPackage->max_neighbors }} جار كحد أقصى / Max {{ $newPackage->max_neighbors }} neighbors
                                    @endif
                                </li>
                            @endif
                            @if($newPackage->email_notifications_enabled && !$oldPackage->email_notifications_enabled)
                                <li>إشعارات البريد الإلكتروني / Email notifications</li>
                            @endif
                            @if($newPackage->sms_notifications_enabled && !$oldPackage->sms_notifications_enabled)
                                <li>إشعارات الرسائل النصية / SMS notifications</li>
                            @endif
                            @if($newPackage->file_attachments_enabled && !$oldPackage->file_attachments_enabled)
                                <li>مرفقات الملفات / File attachments</li>
                            @endif
                            @if($newPackage->priority_support && !$oldPackage->priority_support)
                                <li>دعم فني مميز / Priority support</li>
                            @endif
                            @if($newPackage->advanced_reporting && !$oldPackage->advanced_reporting)
                                <li>تقارير متقدمة / Advanced reporting</li>
                            @endif
                        </ul>
                    </div>
                    
                    <div class="features-list">
                        <h4>تفاصيل التخزين / Storage Details</h4>
                        <ul>
                            <li>
                                @if($newPackage->hasUnlimitedStorage())
                                    مساحة تخزين لا محدودة / Unlimited storage
                                @else
                                    {{ $newPackage->storage_limit_gb }} جيجابايت / {{ $newPackage->storage_limit_gb }} GB storage
                                @endif
                            </li>
                            @if($newPackage->file_attachments_enabled)
                                <li>حد أقصى {{ $newPackage->max_file_size_mb }} ميجابايت للملف / Max {{ $newPackage->max_file_size_mb }} MB per file</li>
                                <li>{{ $newPackage->max_files_per_record }} ملفات لكل سجل / {{ $newPackage->max_files_per_record }} files per record</li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>

            <div class="subscription-details">
                <h3 style="margin: 0 0 15px 0; color: #1e40af;">تفاصيل الاشتراك / Subscription Details</h3>
                
                <div class="detail-row">
                    <span class="detail-label">تاريخ التفعيل / Activation Date:</span>
                    <span class="detail-value">{{ $subscription->created_at->format('Y-m-d H:i') }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">تاريخ انتهاء الصلاحية / Expiry Date:</span>
                    <span class="detail-value">{{ $subscription->expires_at?->format('Y-m-d') ?? 'N/A' }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">دورة الفوترة / Billing Cycle:</span>
                    <span class="detail-value">{{ ucfirst($subscription->billing_cycle) }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">حالة الاشتراك / Status:</span>
                    <span class="detail-value">{{ ucfirst($subscription->status) }}</span>
                </div>
            </div>

            <div class="next-steps">
                <h3>الخطوات التالية / Next Steps:</h3>
                <ul>
                    <li>تم تفعيل جميع الميزات الجديدة فوراً / All new features are activated immediately</li>
                    <li>يمكنك الوصول إلى الميزات الجديدة من لوحة التحكم / Access new features from your dashboard</li>
                    <li>ستتلقى فاتورة محدثة قريباً / You will receive an updated invoice soon</li>
                    <li>في حالة وجود أي استفسار، يرجى التواصل معنا / For any questions, please contact us</li>
                </ul>
            </div>

            <p>شكراً لك على ثقتك في نظام إدارة العمارات أمارة.</p>
            <p>Thank you for your trust in Amara Building Management System.</p>
        </div>

        <div class="footer">
            <p>هذا البريد الإلكتروني تم إرساله من نظام إدارة العمارات أمارة</p>
            <p>This email was sent from Amara Building Management System</p>
            <p>إذا كان لديك أي أسئلة، يرجى <a href="mailto:<EMAIL>">التواصل مع فريق الدعم</a></p>
            <p>If you have any questions, please <a href="mailto:<EMAIL>">contact our support team</a></p>
            <p>&copy; {{ date('Y') }} Amara Building Management. جميع الحقوق محفوظة / All rights reserved.</p>
        </div>
    </div>
</body>
</html>
