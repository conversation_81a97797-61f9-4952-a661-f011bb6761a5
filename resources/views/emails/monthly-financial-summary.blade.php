<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير المالي الشهري - Monthly Financial Summary</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .building-info {
            background-color: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .summary-card {
            background-color: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .summary-card.income {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .summary-card.expense {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        .summary-card.balance {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: bold;
        }
        .summary-card.income h3 {
            color: #065f46;
        }
        .summary-card.expense h3 {
            color: #991b1b;
        }
        .summary-card.balance h3 {
            color: #1e40af;
        }
        .summary-card .amount {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .summary-card.income .amount {
            color: #10b981;
        }
        .summary-card.expense .amount {
            color: #ef4444;
        }
        .summary-card.balance .amount {
            color: #3b82f6;
        }
        .details-section {
            margin: 30px 0;
        }
        .details-section h3 {
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .detail-table th,
        .detail-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        .detail-table tr:hover {
            background-color: #f9fafb;
        }
        .payment-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-overdue {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .chart-placeholder {
            background-color: #f3f4f6;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6b7280;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .summary-grid {
                grid-template-columns: 1fr;
            }
            .detail-table {
                font-size: 14px;
            }
            .detail-table th,
            .detail-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>التقرير المالي الشهري</h1>
            <div class="subtitle">Monthly Financial Summary</div>
            <div class="subtitle">{{ $monthNameAr }} - {{ $monthName }}</div>
        </div>

        <div class="content">
            <p>عزيزي/عزيزتي {{ $admin->name }}،</p>
            <p>Dear {{ $admin->name }},</p>
            
            <p>نرسل إليك التقرير المالي الشهري لعمارة {{ $building->name }} لشهر {{ $monthNameAr }}.</p>
            <p>Here is the monthly financial summary for {{ $building->name }} for {{ $monthName }}.</p>

            <div class="building-info">
                <h2 style="margin: 0 0 10px 0;">{{ $building->name }}</h2>
                @if($building->address)
                    <p style="margin: 0; color: #6b7280;">{{ $building->address }}</p>
                @endif
            </div>

            <div class="summary-grid">
                <div class="summary-card income">
                    <h3>إجمالي الإيرادات<br>Total Income</h3>
                    <div class="amount">{{ number_format($summaryData['total_income'] ?? 0, 2) }} ريال</div>
                    <p style="margin: 5px 0 0 0; font-size: 14px; color: #065f46;">
                        {{ $summaryData['income_count'] ?? 0 }} معاملة / transactions
                    </p>
                </div>
                
                <div class="summary-card expense">
                    <h3>إجمالي المصروفات<br>Total Expenses</h3>
                    <div class="amount">{{ number_format($summaryData['total_expenses'] ?? 0, 2) }} ريال</div>
                    <p style="margin: 5px 0 0 0; font-size: 14px; color: #991b1b;">
                        {{ $summaryData['expense_count'] ?? 0 }} معاملة / transactions
                    </p>
                </div>
                
                <div class="summary-card balance">
                    <h3>الرصيد الصافي<br>Net Balance</h3>
                    <div class="amount">{{ number_format(($summaryData['total_income'] ?? 0) - ($summaryData['total_expenses'] ?? 0), 2) }} ريال</div>
                    <p style="margin: 5px 0 0 0; font-size: 14px; color: #1e40af;">
                        @if(($summaryData['total_income'] ?? 0) > ($summaryData['total_expenses'] ?? 0))
                            فائض / Surplus
                        @elseif(($summaryData['total_income'] ?? 0) < ($summaryData['total_expenses'] ?? 0))
                            عجز / Deficit
                        @else
                            متوازن / Balanced
                        @endif
                    </p>
                </div>
            </div>

            @if(isset($summaryData['payment_status']))
            <div class="details-section">
                <h3>حالة المدفوعات / Payment Status</h3>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>مدفوع / Paid</h3>
                        <div class="amount" style="color: #10b981;">{{ $summaryData['payment_status']['paid'] ?? 0 }}</div>
                    </div>
                    <div class="summary-card">
                        <h3>معلق / Pending</h3>
                        <div class="amount" style="color: #f59e0b;">{{ $summaryData['payment_status']['pending'] ?? 0 }}</div>
                    </div>
                    <div class="summary-card">
                        <h3>متأخر / Overdue</h3>
                        <div class="amount" style="color: #ef4444;">{{ $summaryData['payment_status']['overdue'] ?? 0 }}</div>
                    </div>
                </div>
            </div>
            @endif

            @if(isset($summaryData['top_expenses']) && count($summaryData['top_expenses']) > 0)
            <div class="details-section">
                <h3>أكبر المصروفات / Top Expenses</h3>
                <table class="detail-table">
                    <thead>
                        <tr>
                            <th>نوع المصروف / Expense Type</th>
                            <th>المبلغ / Amount</th>
                            <th>التاريخ / Date</th>
                            <th>الحالة / Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($summaryData['top_expenses'] as $expense)
                        <tr>
                            <td>{{ $expense['type'] ?? 'N/A' }}</td>
                            <td>{{ number_format($expense['amount'] ?? 0, 2) }} ريال</td>
                            <td>{{ $expense['date'] ?? 'N/A' }}</td>
                            <td>
                                <span class="payment-status status-{{ $expense['status'] ?? 'pending' }}">
                                    {{ ucfirst($expense['status'] ?? 'pending') }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif

            <div class="details-section">
                <h3>ملاحظات مهمة / Important Notes</h3>
                <ul>
                    <li>هذا التقرير يغطي الفترة من {{ $year }}-{{ str_pad($month, 2, '0', STR_PAD_LEFT) }}-01 إلى {{ $year }}-{{ str_pad($month, 2, '0', STR_PAD_LEFT) }}-{{ date('t', mktime(0, 0, 0, $month, 1, $year)) }}</li>
                    <li>This report covers the period from {{ $year }}-{{ str_pad($month, 2, '0', STR_PAD_LEFT) }}-01 to {{ $year }}-{{ str_pad($month, 2, '0', STR_PAD_LEFT) }}-{{ date('t', mktime(0, 0, 0, $month, 1, $year)) }}</li>
                    <li>جميع المبالغ بالريال السعودي / All amounts are in Saudi Riyals</li>
                    <li>يمكنك الوصول إلى التقارير التفصيلية من خلال لوحة التحكم / Detailed reports are available through your dashboard</li>
                </ul>
            </div>

            <p>شكراً لك على استخدام نظام إدارة العمارات أمارة.</p>
            <p>Thank you for using Amara Building Management System.</p>
        </div>

        <div class="footer">
            <p>هذا البريد الإلكتروني تم إرساله تلقائياً من نظام إدارة العمارات أمارة</p>
            <p>This email was sent automatically from Amara Building Management System</p>
            <p>إذا كان لديك أي أسئلة، يرجى <a href="mailto:<EMAIL>">التواصل مع فريق الدعم</a></p>
            <p>If you have any questions, please <a href="mailto:<EMAIL>">contact our support team</a></p>
            <p>&copy; {{ date('Y') }} Amara Building Management. جميع الحقوق محفوظة / All rights reserved.</p>
        </div>
    </div>
</body>
</html>
