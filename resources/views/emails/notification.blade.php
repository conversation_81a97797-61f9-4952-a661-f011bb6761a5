<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $notification->title }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #3b82f6;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 30px;
        }
        .notification-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .type-payment_reminder {
            background-color: #fef3c7;
            color: #92400e;
        }
        .type-expense_created {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .type-income_received {
            background-color: #d1fae5;
            color: #065f46;
        }
        .type-general_announcement {
            background-color: #e5e7eb;
            color: #374151;
        }
        .type-payment_received {
            background-color: #d1fae5;
            color: #065f46;
        }
        .type-overdue_payment {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .priority-high {
            border-right: 4px solid #ef4444;
        }
        .priority-medium {
            border-right: 4px solid #f59e0b;
        }
        .priority-low {
            border-right: 4px solid #10b981;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
            margin-bottom: 20px;
        }
        .details {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .details h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-size: 14px;
        }
        .details p {
            margin: 5px 0;
            font-size: 14px;
            color: #6b7280;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }
        .building-info {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>لجنة العمارة</h1>
            <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">
                @if($building)
                    {{ $building->name }}
                @endif
            </p>
        </div>

        <div class="content priority-{{ $notification->priority }}">
            <div class="notification-type type-{{ $notification->type }}">
                @switch($notification->type)
                    @case('payment_reminder')
                        تذكير بالدفع
                        @break
                    @case('expense_created')
                        مصروف جديد
                        @break
                    @case('income_received')
                        دخل مسجل
                        @break
                    @case('general_announcement')
                        إعلان عام
                        @break
                    @case('payment_received')
                        دفعة مستلمة
                        @break
                    @case('overdue_payment')
                        دفعة متأخرة
                        @break
                    @default
                        إشعار
                @endswitch
            </div>

            @if($building)
                <div class="building-info">
                    <strong>العمارة:</strong> {{ $building->name }}
                    @if($building->address)
                        - {{ $building->address }}
                    @endif
                </div>
            @endif

            <h2 style="color: #1f2937; margin: 0 0 15px 0;">{{ $notification->title }}</h2>
            
            <div class="message">
                {{ $notification->message }}
            </div>

            @if($notification->data && count($notification->data) > 0)
                <div class="details">
                    <h3>تفاصيل إضافية:</h3>
                    
                    @if(isset($notification->data['amount']))
                        <p><strong>المبلغ:</strong> {{ number_format($notification->data['amount'], 2) }} ريال</p>
                    @endif
                    
                    @if(isset($notification->data['due_date']))
                        <p><strong>تاريخ الاستحقاق:</strong> {{ \Carbon\Carbon::parse($notification->data['due_date'])->format('Y-m-d') }}</p>
                    @endif
                    
                    @if(isset($notification->data['payment_date']))
                        <p><strong>تاريخ الدفع:</strong> {{ \Carbon\Carbon::parse($notification->data['payment_date'])->format('Y-m-d') }}</p>
                    @endif
                    
                    @if(isset($notification->data['payment_method']))
                        <p><strong>طريقة الدفع:</strong> 
                            @switch($notification->data['payment_method'])
                                @case('cash')
                                    نقداً
                                    @break
                                @case('bank_transfer')
                                    تحويل بنكي
                                    @break
                                @case('check')
                                    شيك
                                    @break
                                @default
                                    {{ $notification->data['payment_method'] }}
                            @endswitch
                        </p>
                    @endif
                    
                    @if(isset($notification->data['days_overdue']))
                        <p><strong>أيام التأخير:</strong> {{ $notification->data['days_overdue'] }} يوم</p>
                    @endif
                </div>
            @endif

            <p style="font-size: 14px; color: #6b7280; margin-top: 20px;">
                تم إرسال هذا الإشعار في: {{ $notification->created_at->format('Y-m-d H:i') }}
            </p>
        </div>

        <div class="footer">
            <p>هذا إشعار تلقائي من نظام إدارة العمارة. يرجى عدم الرد على هذا البريد الإلكتروني.</p>
            <p style="margin-top: 10px;">
                إذا كان لديك أي استفسارات، يرجى التواصل مع إدارة العمارة.
            </p>
        </div>
    </div>
</body>
</html>
