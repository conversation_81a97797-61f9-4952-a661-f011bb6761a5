<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }} - إعلان النظام</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 650px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            @switch($announcementType)
                @case('maintenance')
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    @break
                @case('update')
                    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                    @break
                @case('security')
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    @break
                @case('feature')
                    background: linear-gradient(135deg, #10b981, #059669);
                    @break
                @default
                    background: linear-gradient(135deg, #6366f1, #4f46e5);
            @endswitch
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .announcement-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .announcement-type {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 20px;
        }
        .type-maintenance {
            background-color: #fef3c7;
            color: #92400e;
        }
        .type-update {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .type-security {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .type-feature {
            background-color: #d1fae5;
            color: #065f46;
        }
        .type-general {
            background-color: #e0e7ff;
            color: #3730a3;
        }
        .building-info {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .message-content {
            background-color: #f9fafb;
            border-left: 4px solid;
            @switch($announcementType)
                @case('maintenance')
                    border-color: #f59e0b;
                    @break
                @case('update')
                    border-color: #3b82f6;
                    @break
                @case('security')
                    border-color: #ef4444;
                    @break
                @case('feature')
                    border-color: #10b981;
                    @break
                @default
                    border-color: #6366f1;
            @endswitch
            padding: 20px;
            margin: 20px 0;
            font-size: 16px;
            line-height: 1.7;
        }
        .additional-info {
            background-color: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .info-item {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .info-item h4 {
            margin: 0 0 8px 0;
            color: #374151;
            font-size: 14px;
            font-weight: bold;
        }
        .info-item p {
            margin: 0;
            color: #1f2937;
            font-size: 16px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .action-button {
            display: inline-block;
            padding: 12px 24px;
            @switch($announcementType)
                @case('maintenance')
                    background-color: #f59e0b;
                    @break
                @case('update')
                    background-color: #3b82f6;
                    @break
                @case('security')
                    background-color: #ef4444;
                    @break
                @case('feature')
                    background-color: #10b981;
                    @break
                @default
                    background-color: #6366f1;
            @endswitch
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 0 10px;
        }
        .action-button:hover {
            opacity: 0.9;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .priority-banner {
            @if(isset($additionalData['priority']) && $additionalData['priority'] === 'high')
                background-color: #fee2e2;
                border: 2px solid #ef4444;
                color: #991b1b;
            @elseif(isset($additionalData['priority']) && $additionalData['priority'] === 'medium')
                background-color: #fef3c7;
                border: 2px solid #f59e0b;
                color: #92400e;
            @else
                background-color: #dbeafe;
                border: 2px solid #3b82f6;
                color: #1e40af;
            @endif
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .action-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="announcement-icon">
                @switch($announcementType)
                    @case('maintenance')
                        🔧
                        @break
                    @case('update')
                        🆕
                        @break
                    @case('security')
                        🔒
                        @break
                    @case('feature')
                        ✨
                        @break
                    @default
                        📢
                @endswitch
            </div>
            <h1>{{ $title }}</h1>
            <div class="subtitle">إعلان النظام / System Announcement</div>
        </div>

        <div class="content">
            <div class="announcement-type type-{{ $announcementType }}">
                @switch($announcementType)
                    @case('maintenance')
                        صيانة النظام / System Maintenance
                        @break
                    @case('update')
                        تحديث النظام / System Update
                        @break
                    @case('security')
                        إشعار أمني / Security Notice
                        @break
                    @case('feature')
                        ميزة جديدة / New Feature
                        @break
                    @default
                        إعلان عام / General Announcement
                @endswitch
            </div>

            <p>عزيزي/عزيزتي {{ $user->name }}،</p>
            <p>Dear {{ $user->name }},</p>

            @if($building)
            <div class="building-info">
                <strong>العمارة / Building:</strong> {{ $building->name }}
                @if($building->address)
                    <br><strong>العنوان / Address:</strong> {{ $building->address }}
                @endif
            </div>
            @endif

            @if(isset($additionalData['priority']))
            <div class="priority-banner">
                @if($additionalData['priority'] === 'high')
                    ⚠️ إشعار عالي الأولوية / HIGH PRIORITY NOTICE ⚠️
                @elseif($additionalData['priority'] === 'medium')
                    📋 إشعار متوسط الأولوية / MEDIUM PRIORITY NOTICE 📋
                @else
                    ℹ️ إشعار عادي / NORMAL PRIORITY NOTICE ℹ️
                @endif
            </div>
            @endif

            <div class="message-content">
                {!! nl2br(e($message)) !!}
            </div>

            @if(!empty($additionalData))
            <div class="additional-info">
                <h3 style="margin: 0 0 15px 0; color: #1e40af;">معلومات إضافية / Additional Information</h3>
                
                <div class="info-grid">
                    @if(isset($additionalData['effective_date']))
                    <div class="info-item">
                        <h4>تاريخ التطبيق / Effective Date</h4>
                        <p>{{ $additionalData['effective_date'] }}</p>
                    </div>
                    @endif
                    
                    @if(isset($additionalData['duration']))
                    <div class="info-item">
                        <h4>المدة المتوقعة / Expected Duration</h4>
                        <p>{{ $additionalData['duration'] }}</p>
                    </div>
                    @endif
                    
                    @if(isset($additionalData['affected_services']))
                    <div class="info-item">
                        <h4>الخدمات المتأثرة / Affected Services</h4>
                        <p>{{ $additionalData['affected_services'] }}</p>
                    </div>
                    @endif
                    
                    @if(isset($additionalData['contact_info']))
                    <div class="info-item">
                        <h4>معلومات التواصل / Contact Information</h4>
                        <p>{{ $additionalData['contact_info'] }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            @if(isset($additionalData['action_required']) && $additionalData['action_required'])
            <div class="action-buttons">
                @if(isset($additionalData['action_url']))
                    <a href="{{ $additionalData['action_url'] }}" class="action-button">
                        {{ $additionalData['action_text'] ?? 'اتخاذ إجراء / Take Action' }}
                    </a>
                @endif
                @if(isset($additionalData['dashboard_url']))
                    <a href="{{ $additionalData['dashboard_url'] }}" class="action-button">
                        الذهاب إلى لوحة التحكم / Go to Dashboard
                    </a>
                @endif
            </div>
            @endif

            <p>شكراً لك على اهتمامك واستخدام نظام إدارة العمارات أمارة.</p>
            <p>Thank you for your attention and for using Amara Building Management System.</p>
        </div>

        <div class="footer">
            <p>هذا البريد الإلكتروني تم إرساله من نظام إدارة العمارات أمارة</p>
            <p>This email was sent from Amara Building Management System</p>
            <p>إذا كان لديك أي أسئلة، يرجى <a href="mailto:<EMAIL>">التواصل مع فريق الدعم</a></p>
            <p>If you have any questions, please <a href="mailto:<EMAIL>">contact our support team</a></p>
            <p>&copy; {{ date('Y') }} Amara Building Management. جميع الحقوق محفوظة / All rights reserved.</p>
        </div>
    </div>
</body>
</html>
