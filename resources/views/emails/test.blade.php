<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $testData['subject'] ?? 'Test Email' }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .success-badge {
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
        }
        .info-box {
            background-color: #f0f9ff;
            border: 1px solid #3b82f6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
            margin-top: 30px;
            font-size: 14px;
            color: #6b7280;
        }
        .timestamp {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏢 نظام إدارة العمارة</div>
            <div>Amara Building Management System</div>
        </div>

        <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px;">اختبار البريد الإلكتروني / Email Test</h2>
            
            <div class="success-badge">
                ✅ تم إرسال البريد الإلكتروني بنجاح!<br>
                Email sent successfully!
            </div>

            <div class="info-box">
                <h3 style="margin-top: 0; color: #3b82f6;">معلومات الاختبار / Test Information:</h3>
                <p><strong>الرسالة:</strong> {{ $testData['message'] ?? 'Test email message' }}</p>
                <p><strong>Message:</strong> This email confirms that your email configuration is working correctly.</p>
                
                @if(isset($testData['timestamp']))
                <p><strong>وقت الإرسال / Sent at:</strong> {{ $testData['timestamp'] }}</p>
                @endif
            </div>

            <p>إذا تلقيت هذا البريد الإلكتروني، فهذا يعني أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
            <p>If you received this email, it means your email configuration is working properly.</p>

            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 5px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #92400e;">ملاحظة مهمة / Important Note:</h4>
                <p style="margin-bottom: 0; color: #92400e;">
                    هذا بريد إلكتروني تجريبي. لا حاجة لاتخاذ أي إجراء.<br>
                    This is a test email. No action is required.
                </p>
            </div>
        </div>

        <div class="footer">
            <p>نظام إدارة العمارة - Amara Building Management</p>
            <p>{{ config('app.url') }}</p>
            <div class="timestamp">
                تم إنشاء هذا البريد الإلكتروني تلقائياً في {{ now()->format('Y-m-d H:i:s') }}<br>
                This email was generated automatically at {{ now()->format('Y-m-d H:i:s') }}
            </div>
        </div>
    </div>
</body>
</html>
