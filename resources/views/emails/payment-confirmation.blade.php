<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الدفع - Payment Confirmation</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .success-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .payment-details {
            background-color: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #10b981;
        }
        .detail-label {
            font-weight: bold;
            color: #374151;
        }
        .detail-value {
            color: #1f2937;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #10b981;
        }
        .building-info {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .receipt-number {
            background-color: #eff6ff;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            padding: 10px;
            margin: 15px 0;
            text-align: center;
            font-family: monospace;
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
        }
        .next-steps {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
        }
        .next-steps h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✅</div>
            <h1>تم تأكيد الدفع بنجاح</h1>
            <div class="subtitle">Payment Confirmed Successfully</div>
        </div>

        <div class="content">
            <p>عزيزي/عزيزتي {{ $user->name }}،</p>
            <p>Dear {{ $user->name }},</p>
            
            <p>نشكرك على دفع المستحقات. تم استلام دفعتك بنجاح ومعالجتها.</p>
            <p>Thank you for your payment. We have successfully received and processed your payment.</p>

            @if($building)
            <div class="building-info">
                <strong>العمارة / Building:</strong> {{ $building->name }}
                @if($building->address)
                    <br><strong>العنوان / Address:</strong> {{ $building->address }}
                @endif
            </div>
            @endif

            <div class="receipt-number">
                <strong>رقم الإيصال / Receipt Number:</strong><br>
                #{{ $payment->id }}-{{ date('Y') }}
            </div>

            <div class="payment-details">
                <h3 style="margin: 0 0 15px 0; color: #10b981;">تفاصيل الدفع / Payment Details</h3>
                
                <div class="detail-row">
                    <span class="detail-label">نوع المصروف / Expense Type:</span>
                    <span class="detail-value">{{ $expense->expenseType->name ?? 'N/A' }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">تاريخ الدفع / Payment Date:</span>
                    <span class="detail-value">{{ $payment->created_at->format('Y-m-d H:i') }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">طريقة الدفع / Payment Method:</span>
                    <span class="detail-value">{{ ucfirst($payment->payment_method) }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">الحالة / Status:</span>
                    <span class="detail-value">
                        <span class="status-badge status-completed">{{ ucfirst($payment->status) }}</span>
                    </span>
                </div>
                
                @if($payment->reference_number)
                <div class="detail-row">
                    <span class="detail-label">رقم المرجع / Reference Number:</span>
                    <span class="detail-value">{{ $payment->reference_number }}</span>
                </div>
                @endif
                
                <div class="detail-row">
                    <span class="detail-label">المبلغ المدفوع / Amount Paid:</span>
                    <span class="detail-value amount">{{ number_format($payment->amount, 2) }} ريال</span>
                </div>
            </div>

            @if($expense && $expense->notes)
            <div class="next-steps">
                <h3>ملاحظات / Notes:</h3>
                <p>{{ $expense->notes }}</p>
            </div>
            @endif

            <div class="next-steps">
                <h3>الخطوات التالية / Next Steps:</h3>
                <ul>
                    <li>احتفظ بهذا الإيصال لسجلاتك / Keep this receipt for your records</li>
                    <li>يمكنك مراجعة تاريخ المدفوعات في حسابك / You can review payment history in your account</li>
                    <li>في حالة وجود أي استفسار، يرجى التواصل معنا / For any questions, please contact us</li>
                </ul>
            </div>

            <p>شكراً لك على استخدام نظام إدارة العمارات أمارة.</p>
            <p>Thank you for using Amara Building Management System.</p>
        </div>

        <div class="footer">
            <p>هذا البريد الإلكتروني تم إرساله من نظام إدارة العمارات أمارة</p>
            <p>This email was sent from Amara Building Management System</p>
            <p>إذا كان لديك أي أسئلة، يرجى <a href="mailto:<EMAIL>">التواصل مع فريق الدعم</a></p>
            <p>If you have any questions, please <a href="mailto:<EMAIL>">contact our support team</a></p>
            <p>&copy; {{ date('Y') }} Amara Building Management. جميع الحقوق محفوظة / All rights reserved.</p>
        </div>
    </div>
</body>
</html>
