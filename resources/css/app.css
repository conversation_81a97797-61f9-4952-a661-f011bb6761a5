@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';
@source '../**/*.vue';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

button{
  cursor: pointer;
  direction: ltr;
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* RTL-specific spacing adjustments */
[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* RTL margin adjustments */
[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .ml-10 {
  margin-left: 0;
  margin-right: 2.5rem;
}

[dir="rtl"] .ml-64 {
  margin-left: 0;
  margin-right: 16rem;
}

/* RTL text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL flex adjustments */
[dir="rtl"] .justify-between {
  flex-direction: row-reverse;
}

/* Arabic font support */
[dir="rtl"] {
  font-family: 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
}

/* Phone numbers should always be LTR */
.phone-number,
[dir="ltr"] .phone-number,
[dir="rtl"] .phone-number,
div.phone-number,
a.phone-number {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: embed !important;
  display: inline-block !important;
}

/* Force LTR for any element with dir="ltr" */
[dir="ltr"] {
  direction: ltr !important;
  text-align: left !important;
}

/* RTL form adjustments */
[dir="rtl"] .relative .absolute.inset-y-0.left-0 {
  left: 0;
  right: auto;
}

[dir="rtl"] .pl-8 {
  padding-left: 0.75rem;
  padding-right: 2rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

/* RTL table adjustments */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL button spacing */
[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

/* RTL sidebar adjustments */
[dir="rtl"] .w-64 {
  left: auto;
}

[dir="rtl"] .ml-64 {
  margin-left: 0;
  margin-right: 16rem;
}

/* RTL navigation adjustments */
[dir="rtl"] .justify-between {
  flex-direction: row;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

[dir="rtl"] .lg\:ml-64{
  margin-right: calc(var(--spacing) * 64);
  margin-left: 0;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  /* Ensure text doesn't get too small on mobile */
  .text-xs {
    font-size: 0.75rem;
  }

  /* Better touch targets for mobile */
  button, .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve form inputs on mobile */
  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile cards */
  .mobile-card {
    padding: 1rem;
    margin-bottom: 0.5rem;
  }

  /* Ensure modals don't overflow on small screens */
  .modal-content {
    max-height: 90vh;
    overflow-y: auto;
  }
  [dir="rtl"] .lg\:ml-64{
    margin: 0;
  }
  [dir="rtl"] .w-64 {
    left: 0;
  }
}

/* Improve table responsiveness */
@media (max-width: 1024px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* PWA and Mobile Enhancements */
@media (max-width: 768px) {
  /* PWA safe area support */
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-inset-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-friendly interactions */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus (iOS) */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
  }

  /* Mobile-optimized form controls */
  .mobile-form-control {
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
    font-size: 16px;
    transition: border-color 0.2s ease;
  }

  .mobile-form-control:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Mobile button styles */
  .mobile-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .mobile-btn:active {
    transform: scale(0.98);
  }

  /* Mobile card styles */
  .mobile-card {
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: white;
  }

  /* Mobile navigation adjustments */
  .mobile-nav-item {
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .mobile-nav-item:active {
    background-color: rgba(59, 130, 246, 0.1);
  }

  /* Swipe gestures support */
  .swipeable {
    touch-action: pan-x;
  }

  /* Pull-to-refresh indicator */
  .pull-to-refresh {
    transform: translateY(-100%);
    transition: transform 0.3s ease;
  }

  .pull-to-refresh.active {
    transform: translateY(0);
  }
}

/* Dark mode support for PWA */
@media (prefers-color-scheme: dark) {
  .mobile-card {
    background: #1f2937;
    color: white;
  }

  .mobile-form-control {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-navigation nav {
    padding: 4px 0;
  }

  .mobile-navigation .text-xs {
    display: none;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
.hover\:bg-white:hover {
  color:var(--color-blue-700)
}
.bg-opacity-50.bg-gray-600{
  background-color: rgba(0, 0, 0, 0.5);
}